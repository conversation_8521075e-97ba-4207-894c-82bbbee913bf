server.port=8080
endpoints.enabled=false
server.forward-headers-strategy=framework
#custom corss-domain headers, split by ','
cross.domain.headers=
#spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration

base.package=com.pulse
# ?????
#datasource
spring.datasource.url=*******************************************
spring.datasource.username=pulse
spring.datasource.password=u7xhnEE6
spring.datasource.type=org.apache.tomcat.jdbc.pool.DataSource

# Druid Data Source Config
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
hibernate.dialect=org.hibernate.dialect.Oracle10gDialect
vs.sqlmapper.dialect=oracle
vs.sqlmapper.showSql=true
hibernate.show_sql=true
spring.datasource.tomcat.max-age=3600000
spring.jpa.open-in-view=false
spring.redis.host=${REDIS_HOST:redis.byteawake.com}
# hibernate interceptor
hibernate.interceptor.clz=com.pulse.log.service.interceptor.OperationLogInterceptor

# flyway
spring.flyway.enabled=false
spring.flyway.outOfOrder=true
spring.flyway.baselineOnMigrate=true

mybatis.configuration.map-underscore-to-camel-case=true

#rocketmq
rocketmq.name-server=${ROCKETMQ_HOST:**********:9876;**********:9876;**********:9876}
rocketmq.topic=${MYSQL_DATABASE:hande_test}
rocketmq.consumerGroup=CID_handeTest
rocketmq.tag=*
rocketmq.producer.group=PID_handeTest

spring.main.allow-circular-references=true
spring.login.security.csrf=false

spring.main.allow-bean-definition-overriding=true
spring.mvc.pathmatch.matching-strategy=ant_path_matcher

#elasticsearch
#essql.hosts=${OPENSEARCH_HOST:**********,**********,**********}
#essql.port=9200
#essql.username=${OPENSEARCH_USER:admin}
#essql.password=${OPENSEARCH_PASSWORD:dnZkaNVK}
#essql.scheme=${OPENSEARCH_SCHEME:https}

#get user config
get_user_uri={}

#request header
out.request.headers=
#response header
out.response.headers=Content-Type
#eg:https://vsstudio.teitui.com
out.host=

#xxljob
xxl.job.admin.addresses=${XXL_JOB_ADMIN_ADDRESS:https://xxljob.teitui.com/xxl-job-admin}
xxl.job.executor.port=9999
xxl.job.executor.appname=handeTest
xxl.job.executor.title=handeTest
xxl.job.accessToken=default_token
xxl.job.admin.username=${XXL_JOB_ADMIN_USER:admin}
xxl.job.admin.password=${XXL_JOB_ADMIN_PASSWORD:123456}

#redis
redis-config.pool.hostAndPort=${REDIS_HOST:redis.byteawake.com:6379}
redis-config.pool.password=${REDIS_PASSWORD:}
redis-config.pool.maxTotal=100
redis-config.pool.maxIdle=10
redis-config.pool.minIdle=10
redis-config.pool.maxWaitMillis=10000
redis-config.pool.softMinEvictableIdleTimeMillis=10000
redis-config.pool.testOnBorrow=true
redis-config.pool.testOnReturn=true
redis-config.pool.testWhileIdle=true
redis-config.pool.timeBetweenEvictionRunsMillis=30000
redis-config.pool.minEvictableIdleTimeMillis=1800000
redis-config.pool.numTestsPerEvictionRun=3
redis-config.pool.blockWhenExhausted=true
redis-config.pool.jmxEnabled=true
redis-config.pool.lifo=true

#flow config
liteflow.rule-source=el_json:com.vs.flow.FlowRuleSource
liteflow.print-banner=false
liteflow.monitor.enable-log=true

# Nacos配置
nacos.config.server-addr=${NACOS_SERVER_ADDR:**********:8848}
nacos.config.namespace=${NACOS_NAMESPACE:pulse}
nacos.config.group=${NACOS_GROUP:DEFAULT_GROUP}
nacos.config.username=${NACOS_USERNAME:pulse}
nacos.config.password=${NACOS_PASSWORD:pulse1024}

# Operation log config: default config, it should be overridden by nacos
# 黑名单配置，支持通配符(?：匹配单个字符；*：匹配零个或多个字符)，多个值用逗号分隔，优先级高于白名单
operation.log.business.scene.blacklist=OperationLog*
# 白名单配置，支持通配符，多个值用逗号分隔
#operation.log.business.scene.whitelist=

#create default table auto
vs.db.basetable.create=true

# 文件存储配置
# 启用 Spring Boot 的 multipart 处理
spring.servlet.multipart.enabled=true
# 设置单个文件的最大大小 (例如 10MB) - 根据您的需求调整
spring.servlet.multipart.max-file-size=10MB
# 设置整个请求的最大大小 (例如 10MB) - 根据您的需求调整
spring.servlet.multipart.max-request-size=10MB
# 可选: 文件大小阈值，超过该值则写入磁盘 (例如 0 表示直接写磁盘)
# spring.servlet.multipart.file-size-threshold=0
# 可选: 临时文件存储位置
# spring.servlet.multipart.location=/tmp
# 推荐: 启用延迟解析，如果存在可能干扰的 Filter
#spring.servlet.multipart.resolve-lazily=false
# 默认存储类型: LOCAL, MINIO, AWS_S3, ALIYUN_OSS
file.storage.default-type=MINIO

# 本地存储配置
# 本地存储根路径
file.storage.local.root-path=/data/files
# 访问URL前缀
file.storage.local.url-prefix=http://localhost:8080/files

# MinIO存储配置
# 服务端点
file.storage.min-io.endpoint=http://**********:9000
# 访问密钥
file.storage.min-io.access-key=admin
# 密钥
file.storage.min-io.secret-key=ceyACwLs050
# 存储桶名称
file.storage.min-io.bucket-name=pulse
# 是否安全连接
#file.storage.min-io.secure=true
# 区域
#file.storage.min-io.region=us-east-1

# AWS S3存储配置
# 服务端点（可选，使用默认AWS端点时可不设置）
file.storage.aws-s3.endpoint=https://s3.amazonaws.com
# 访问密钥
file.storage.aws-s3.access-key=your-access-key
# 密钥
file.storage.aws-s3.secret-key=your-secret-key
# 存储桶名称
file.storage.aws-s3.bucket-name=pulse-files
# 区域
file.storage.aws-s3.region=us-east-1

# 阿里云OSS存储配置
# 服务端点
file.storage.aliyun-oss.endpoint=https://oss-cn-hangzhou.aliyuncs.com
# 访问密钥
file.storage.aliyun-oss.access-key=your-access-key
# 密钥
file.storage.aliyun-oss.secret-key=your-secret-key
# 存储桶名称
file.storage.aliyun-oss.bucket-name=pulse-files

login.token.key.private=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALZt39fdRTfe90DgH/KycD7jk2xPX4Ip5uSn7AanVsSd6VEE8Ukgsng5onkHHRmsFvmkgdpSzmmDU+5PXfi5JeWnXTNhYxsfAwGCdPKAivHPzGF83o4MbCHyo8qcQUtkMoGJ0zgPftPmxah+Wj4Rc4qOsqagWq2umj6BKMaH+r9DAgMBAAECgYAovxXWMzf4qLZ1KRo5rR1hDpepA3mDqzRFMjSHr+yzccnhLMdv2/vA2q6tdadMSqG/FL1kNEaiTpD6k4ObwE70QftW9P1pOvhfCIQzDMWCH60RER8itxKVfK9ZdifjMlwB9i0UVSFZr5j1ht1HcrNSH+Zb8ErfNkvf4BQczZZA4QJBAN+gcAryMf59PsmjWN6b7uPpYBgBOnOe5C1R6mHaIsAkbVxhX5nkByc+IHOwuSpSdwhLpdGTR+NA+W+SKNxR8ksCQQDQ1qo31FAPHFapO3MSBtC+OGhLXMmJh2GroboTjezTN/DztrUO/IM1f0HPXH5K3xgn2Y6hQlEsE80zqBbWdAvpAkBr6HcoUQZezwDlO7U+dfAoegysiKsz75AbblOzNBdgsDM1SKdhEu+AuDHgoX44NeLKVfaF0pP1zPyQIe8loUVRAkEAkL2XMCaYbkCn15b6g+3xU0VpE4J1tDn+eaQXgWIGV8YX2/IvvvlIPxW6GOv0JBRHe6cPcbiy5ae7uyeu3GeFsQJBAJkCNKe5PbD3RhQnuv2jxtMkQGP6PMqIyBIAn5z80eHH4Xa71/CZ4VdjJIUqcwEFPMkaMz5FyOn2Hu9Vs5kBaHc=
login.token.key.public=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2bd/X3UU33vdA4B/ysnA+45NsT1+CKebkp+wGp1bEnelRBPFJILJ4OaJ5Bx0ZrBb5pIHaUs5pg1PuT134uSXlp10zYWMbHwMBgnTygIrxz8xhfN6ODGwh8qPKnEFLZDKBidM4D37T5sWoflo+EXOKjrKmoFqtrpo+gSjGh/q/QwIDAQAB
