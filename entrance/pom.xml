<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pulse</groupId>
        <artifactId>pulse</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>pulse-entrance</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <dependencies>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>pulse-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>organization-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>organization-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>dictionary_basic-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>dictionary_basic-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>user-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>user-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>parameter-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>parameter-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>dictionary_business-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>dictionary_business-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>consulting_room-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>consulting_room-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>billing_public_config-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>billing_public_config-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>application-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>application-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>permission-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>permission-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_dictionary-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_dictionary-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>file-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>file-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>patient_information-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>patient_information-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_purchasing_plan-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_purchasing_plan-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_inventory-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_inventory-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_quality-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_quality-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>pharmacy_warehouse_setting-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>pharmacy_warehouse_setting-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_report-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_report-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>patient_safety-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>patient_safety-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_financial-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_financial-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>special_drug-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>special_drug-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_permission-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_permission-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_circulation-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>drug_circulation-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>pivas_preparation-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>pivas_preparation-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>medication_dispensing-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>medication_dispensing-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>medication_dispensing_review-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>medication_dispensing_review-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>medication_return-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>medication_return-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>appointment_schedule-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>appointment_schedule-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>appointment_booking-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>appointment_booking-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>visit-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>visit-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>certificate-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>certificate-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>treatment-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>treatment-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>tag-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>tag-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>log-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>log-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>diagnosis-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>diagnosis-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>rule_engine-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>rule_engine-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>rule_engine_common-entrance-web</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.pulse</groupId>
            <artifactId>rule_engine_common-entrance-rpc</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <modules>
        <module>mq</module>
        <module>web</module>
    </modules>
</project>