# HIS系统语义化版本管理工具类

## 概述

本工具类为HIS系统提供了完整的语义化版本管理功能，遵循语义化版本规范（Semantic Versioning），支持版本号的生成、验证、比较、排序等操作。

## 核心组件

### 1. VersionChangeType 枚举类
定义了三种版本变更类型：
- **MAJOR（主版本号）**：用于重大变更或不兼容更新，如 "1.0.0" → "2.0.0"
- **MINOR（次版本号）**：用于添加新功能或小规模变更，如 "1.0.0" → "1.1.0"  
- **PATCH（修订号）**：用于bug修复或小调整，如 "1.0.0" → "1.0.1"

### 2. SemanticVersion 实体类
表示一个语义化版本号，提供：
- 版本号解析和创建
- 版本号比较和排序
- 版本号递增操作
- 版本兼容性判断

### 3. SemanticVersionUtils 工具类
提供静态方法进行版本管理操作：
- 版本号生成和解析
- 版本号验证
- 版本号比较和排序
- 版本兼容性检查

## 主要功能

### 版本号生成
```java
// 创建指定版本号
SemanticVersion version = SemanticVersionUtils.createVersion(2, 1, 3);

// 创建初始版本号（1.0.0）
SemanticVersion initial = SemanticVersionUtils.createInitialVersion();

// 创建零版本号（0.0.0）
SemanticVersion zero = SemanticVersionUtils.createZeroVersion();

// 解析版本号字符串
SemanticVersion parsed = SemanticVersionUtils.parseVersion("2.1.3");
```

### 版本号递增
```java
String currentVersion = "2.1.3";

// 递增主版本号（重大变更）
SemanticVersion major = SemanticVersionUtils.incrementMajorVersion(currentVersion);
// 结果: 3.0.0

// 递增次版本号（新功能）
SemanticVersion minor = SemanticVersionUtils.incrementMinorVersion(currentVersion);
// 结果: 2.2.0

// 递增修订号（bug修复）
SemanticVersion patch = SemanticVersionUtils.incrementPatchVersion(currentVersion);
// 结果: 2.1.4

// 使用枚举类型递增
SemanticVersion enumVersion = SemanticVersionUtils.incrementVersion(currentVersion, VersionChangeType.MINOR);
```

### 版本号验证
```java
// 验证版本号格式
boolean isValid = SemanticVersionUtils.isValidVersion("2.1.3"); // true
boolean isInvalid = SemanticVersionUtils.isValidVersion("invalid"); // false

// 检查是否为预发布版本（主版本号为0）
boolean isPreRelease = SemanticVersionUtils.isPreReleaseVersion("0.1.0"); // true

// 检查是否为稳定版本（主版本号大于0）
boolean isStable = SemanticVersionUtils.isStableVersion("2.1.3"); // true

// 仅检查格式（不解析）
boolean formatValid = SemanticVersionUtils.isValidVersionFormat("2.1.3"); // true
```

### 版本号比较
```java
// 比较版本号
int result = SemanticVersionUtils.compareVersions("2.1.4", "2.1.3"); // 1

// 便捷比较方法
boolean isGreater = SemanticVersionUtils.isVersionGreater("2.1.4", "2.1.3"); // true
boolean isEqual = SemanticVersionUtils.isVersionEqual("2.1.3", "2.1.3"); // true
boolean isLess = SemanticVersionUtils.isVersionLess("2.1.3", "2.1.4"); // true
```

### 版本号排序
```java
List<String> versions = Arrays.asList("2.1.3", "1.0.0", "2.2.0", "3.0.0");

// 升序排序
List<String> ascending = SemanticVersionUtils.sortVersionsAscending(versions);
// 结果: [1.0.0, 2.1.3, 2.2.0, 3.0.0]

// 降序排序
List<String> descending = SemanticVersionUtils.sortVersionsDescending(versions);
// 结果: [3.0.0, 2.2.0, 2.1.3, 1.0.0]

// 获取最新/最旧版本
String latest = SemanticVersionUtils.getLatestVersion(versions); // 3.0.0
String oldest = SemanticVersionUtils.getOldestVersion(versions); // 1.0.0
```

### 版本号筛选
```java
List<String> versions = Arrays.asList("0.1.0", "0.2.0", "1.0.0", "1.1.0", "2.0.0", "2.1.0");

// 按主版本号筛选
List<String> version1 = SemanticVersionUtils.filterByMajorVersion(versions, 1);
// 结果: [1.0.0, 1.1.0]

// 筛选预发布版本
List<String> preReleaseVersions = SemanticVersionUtils.filterPreReleaseVersions(versions);
// 结果: [0.1.0, 0.2.0]

// 筛选稳定版本
List<String> stableVersions = SemanticVersionUtils.filterStableVersions(versions);
// 结果: [1.0.0, 1.1.0, 2.0.0, 2.1.0]
```

### 版本兼容性检查
```java
// 检查版本兼容性（主版本号相同且第一个版本不低于第二个版本）
boolean compatible = SemanticVersionUtils.isVersionCompatible("2.1.4", "2.1.3"); // true
boolean incompatible = SemanticVersionUtils.isVersionCompatible("3.0.0", "2.1.3"); // false
```

## HIS系统应用场景

### 1. 模块版本管理
```java
// 患者信息管理模块版本演进
String patientModuleVersion = "1.2.5";

// 修复患者信息查询bug
SemanticVersion bugFixVersion = SemanticVersionUtils.incrementPatchVersion(patientModuleVersion);
// 结果: 1.2.6

// 新增患者风险评估功能
SemanticVersion featureVersion = SemanticVersionUtils.incrementMinorVersion(patientModuleVersion);
// 结果: 1.3.0

// 重构患者数据结构（不兼容变更）
SemanticVersion majorVersion = SemanticVersionUtils.incrementMajorVersion(patientModuleVersion);
// 结果: 2.0.0
```

### 2. 模块依赖兼容性检查
```java
String coreSystemVersion = "2.1.0";
List<String> moduleVersions = Arrays.asList("2.1.5", "2.2.0", "2.0.8", "3.0.0");

for (String moduleVersion : moduleVersions) {
    boolean compatible = SemanticVersionUtils.isVersionCompatible(moduleVersion, coreSystemVersion);
    System.out.printf("模块版本 %s: %s%n", moduleVersion, compatible ? "✓ 兼容" : "✗ 不兼容");
}
```

### 3. 版本发布管理
```java
List<String> releaseVersions = Arrays.asList(
    "1.0.0", "1.0.1", "1.1.0", "1.1.1", "1.2.0", "2.0.0", "2.0.1", "2.1.0"
);

String currentRelease = SemanticVersionUtils.getLatestVersion(releaseVersions);
// 当前发布版本: 2.1.0

// 计划下一个版本
String nextPatch = SemanticVersionUtils.incrementPatchVersion(currentRelease).toString(); // 2.1.1
String nextMinor = SemanticVersionUtils.incrementMinorVersion(currentRelease).toString(); // 2.2.0
String nextMajor = SemanticVersionUtils.incrementMajorVersion(currentRelease).toString(); // 3.0.0
```

## 版本变更指导原则

### MAJOR版本递增场景
- API接口的不兼容变更
- 数据结构的重大调整
- 系统架构的重构
- 删除已废弃的功能

### MINOR版本递增场景
- 新增API接口
- 新增业务功能
- 性能优化
- 向下兼容的功能增强

### PATCH版本递增场景
- 修复已知缺陷
- 代码优化
- 文档更新
- 安全补丁

## 异常处理

工具类使用 `IllegalArgumentException` 来处理无效的版本号格式：

```java
try {
    SemanticVersion version = SemanticVersionUtils.parseVersion("invalid.version");
} catch (IllegalArgumentException e) {
    System.out.println("版本号格式错误: " + e.getMessage());
}
```

## 测试覆盖

提供了完整的单元测试 `SemanticVersionUtilsTest`，覆盖所有功能点：
- 版本号创建和解析
- 版本号递增
- 版本号验证（包括稳定版本和预发布版本）
- 版本号比较
- 版本号排序
- 版本号筛选
- 版本兼容性检查
- 枚举类型功能
- 参数验证和异常处理

## 注意事项

1. **版本号格式**：必须遵循 `MAJOR.MINOR.PATCH` 格式
2. **非负整数**：所有版本号组件必须为非负整数
3. **预发布版本**：主版本号为0的版本被视为预发布版本
4. **版本兼容性**：基于主版本号相同的原则
5. **线程安全**：工具类为静态方法，线程安全
6. **不可变性**：SemanticVersion类是不可变的，所有操作返回新实例
7. **参数验证**：所有方法都进行严格的参数验证
8. **性能优化**：使用Stream API优化排序和筛选操作

## 文件结构

```
common/src/main/java/com/pulse/pulse/common/
├── enums/
│   └── VersionChangeType.java          # 版本变更类型枚举
└── utils/
    ├── SemanticVersion.java            # 语义化版本实体类
    ├── SemanticVersionUtils.java       # 版本管理工具类
    └── SemanticVersionExample.java     # 使用示例
```

## 总结

本语义化版本管理工具类为HIS系统提供了标准化、规范化的版本管理解决方案，支持模块版本演进、依赖兼容性检查、版本发布管理等核心场景，有助于提升系统的可维护性和稳定性。
