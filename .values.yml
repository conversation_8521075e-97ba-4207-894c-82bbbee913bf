_commit: 3e3717c
_url: https://code.byteawake.com/templates/common-springboot-template.git
applicationName: pulse-server
artifactId: pulse
dbDatabase: pulse
dbHost: **********
dbPassword: 843cpqP0FetvJEjn
dbPort: "3306"
dbType: dm
dbUser: pulse_user
elasticsearch_host: **********,**********,**********
elasticsearch_password: dnZkaNVK
elasticsearch_port: "9200"
elasticsearch_scheme: https
elasticsearch_username: admin
gitUrl: ssh://*********************:23123/vs-server-generate/pulse/pulse.git
groupId: com.pulse
package: com.pulse.pulse
packagePath: com/pulse/pulse
projectId: ad09c67e-87d7-47a0-bff1-fde6f72fdd1d
projectName: pulse
redis_address: redis.byteawake.com:6379
redis_maxIdle: "10"
redis_maxTotal: "100"
redis_minIdle: "10"
redis_password: ""
ref: main
rocketmq_address: **********:9876;**********:9876;**********:9876
rocketmq_consumerGroup: CID_pulse
rocketmq_producerGroup: PID_pulse
rocketmq_topic: pulse
url: https://code.byteawake.com/templates/common-springboot-template.git
version: 1.0-SNAPSHOT
vsVersion: 1.0.0
xxljob_accessToken: default_token
xxljob_address: https://xxljob.teitui.com/xxl-job-admin
xxljob_password: "123456"
xxljob_username: admin

