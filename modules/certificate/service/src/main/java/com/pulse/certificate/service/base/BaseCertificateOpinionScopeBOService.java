package com.pulse.certificate.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.certificate.manager.bo.*;
import com.pulse.certificate.manager.bo.CertificateOpinionScopeBO;
import com.pulse.certificate.persist.dos.CertificateOpinion;
import com.pulse.certificate.persist.dos.CertificateOpinionScope;
import com.pulse.certificate.service.base.BaseCertificateOpinionScopeBOService.CreateCertificateOpinionScopeBoResult;
import com.pulse.certificate.service.base.BaseCertificateOpinionScopeBOService.DeleteCertificateOpinionScopeBoResult;
import com.pulse.certificate.service.base.BaseCertificateOpinionScopeBOService.UpdateCertificateOpinionScopeBoResult;
import com.pulse.certificate.service.base.BaseCertificateOpinionScopeBOService.UpdateCertificateOpinionScopeEnableFlagBoResult;
import com.pulse.certificate.service.bto.CreateCertificateOpinionScopeBto;
import com.pulse.certificate.service.bto.DeleteCertificateOpinionScopeBto;
import com.pulse.certificate.service.bto.UpdateCertificateOpinionScopeBto;
import com.pulse.certificate.service.bto.UpdateCertificateOpinionScopeEnableFlagBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "a40ffd02-5f22-314d-814e-98709230d4d7")
public class BaseCertificateOpinionScopeBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 创建对象CertificateOpinionBto */
    @AutoGenerated(locked = true)
    private void createCertificateOpinionBto(
            BaseCertificateOpinionScopeBOService.CreateCertificateOpinionScopeBoResult boResult,
            CreateCertificateOpinionScopeBto createCertificateOpinionScopeBto,
            CertificateOpinionScopeBO certificateOpinionScopeBO) {
        if (CollectionUtil.isNotEmpty(
                createCertificateOpinionScopeBto.getCertificateOpinionBtoList())) {
            for (CreateCertificateOpinionScopeBto.CertificateOpinionBto item :
                    createCertificateOpinionScopeBto.getCertificateOpinionBtoList()) {
                CertificateOpinionBO subBo = new CertificateOpinionBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "opinionContent")) {
                    subBo.setOpinionContent(item.getOpinionContent());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "sortNumber")) {
                    subBo.setSortNumber(item.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                subBo.setCertificateOpinionScopeBO(certificateOpinionScopeBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("certificate_opinion")));
                certificateOpinionScopeBO.getCertificateOpinionBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:CertificateOpinionBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createCertificateOpinionBtoOnDuplicateUpdate(
            UpdateCertificateOpinionScopeBoResult boResult,
            UpdateCertificateOpinionScopeBto updateCertificateOpinionScopeBto,
            CertificateOpinionScopeBO certificateOpinionScopeBO) {
        if (CollectionUtil.isEmpty(
                updateCertificateOpinionScopeBto.getCertificateOpinionBtoList())) {
            updateCertificateOpinionScopeBto.setCertificateOpinionBtoList(List.of());
        }
        certificateOpinionScopeBO
                .getCertificateOpinionBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    updateCertificateOpinionScopeBto
                                            .getCertificateOpinionBtoList()
                                            .stream()
                                            .filter(
                                                    certificateOpinionBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (certificateOpinionBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            certificateOpinionBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToCertificateOpinion());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                updateCertificateOpinionScopeBto.getCertificateOpinionBtoList())) {
            for (UpdateCertificateOpinionScopeBto.CertificateOpinionBto item :
                    updateCertificateOpinionScopeBto.getCertificateOpinionBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<CertificateOpinionBO> any =
                        certificateOpinionScopeBO.getCertificateOpinionBOSet().stream()
                                .filter(
                                        certificateOpinionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                certificateOpinionBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        CertificateOpinionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToCertificateOpinion());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "opinionContent")) {
                            bo.setOpinionContent(item.getOpinionContent());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        CertificateOpinionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToCertificateOpinion());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "opinionContent")) {
                            bo.setOpinionContent(item.getOpinionContent());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    CertificateOpinionBO subBo = new CertificateOpinionBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "opinionContent")) {
                        subBo.setOpinionContent(item.getOpinionContent());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setCertificateOpinionScopeBO(certificateOpinionScopeBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("certificate_opinion")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    certificateOpinionScopeBO.getCertificateOpinionBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建多院区医疗证明建议 */
    @AutoGenerated(locked = true)
    protected CreateCertificateOpinionScopeBoResult createCertificateOpinionScopeBase(
            CreateCertificateOpinionScopeBto createCertificateOpinionScopeBto) {
        if (createCertificateOpinionScopeBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateCertificateOpinionScopeBoResult boResult =
                new CreateCertificateOpinionScopeBoResult();
        CertificateOpinionScopeBO certificateOpinionScopeBO =
                createCreateCertificateOpinionScope(boResult, createCertificateOpinionScopeBto);
        if (certificateOpinionScopeBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createCertificateOpinionScopeBto, "__$validPropertySet"),
                    "certificateOpinionBtoList")) {
                createCertificateOpinionBto(
                        boResult, createCertificateOpinionScopeBto, certificateOpinionScopeBO);
            }
        }
        boResult.setRootBo(certificateOpinionScopeBO);
        return boResult;
    }

    /** 数据库创建一行 */
    @AutoGenerated(locked = true)
    private CertificateOpinionScopeBO createCreateCertificateOpinionScope(
            CreateCertificateOpinionScopeBoResult boResult,
            CreateCertificateOpinionScopeBto createCertificateOpinionScopeBto) {
        CertificateOpinionScopeBO certificateOpinionScopeBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        if (certificateOpinionScopeBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!",
                        certificateOpinionScopeBO.getId(),
                        "certificate_opinion_scope");
                throw new IgnoredException(400, "证明建议范围已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "certificate_opinion_scope",
                        certificateOpinionScopeBO.getId());
                throw new IgnoredException(400, "证明建议范围已存在");
            }
        } else {
            certificateOpinionScopeBO = new CertificateOpinionScopeBO();
            if (pkExist) {
                certificateOpinionScopeBO.setId(
                        String.valueOf(this.idGenerator.allocateId("certificate_opinion_scope")));
            } else {
                certificateOpinionScopeBO.setId(
                        String.valueOf(this.idGenerator.allocateId("certificate_opinion_scope")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createCertificateOpinionScopeBto, "__$validPropertySet"),
                    "certificateTypeId")) {
                certificateOpinionScopeBO.setCertificateTypeId(
                        createCertificateOpinionScopeBto.getCertificateTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createCertificateOpinionScopeBto, "__$validPropertySet"),
                    "campusOrganizationIdList")) {
                certificateOpinionScopeBO.setCampusOrganizationIdList(
                        createCertificateOpinionScopeBto.getCampusOrganizationIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createCertificateOpinionScopeBto, "__$validPropertySet"),
                    "useScopeList")) {
                certificateOpinionScopeBO.setUseScopeList(
                        createCertificateOpinionScopeBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createCertificateOpinionScopeBto, "__$validPropertySet"),
                    "precautions")) {
                certificateOpinionScopeBO.setPrecautions(
                        createCertificateOpinionScopeBto.getPrecautions());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createCertificateOpinionScopeBto, "__$validPropertySet"),
                    "enableFlag")) {
                certificateOpinionScopeBO.setEnableFlag(
                        createCertificateOpinionScopeBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createCertificateOpinionScopeBto, "__$validPropertySet"),
                    "createdBy")) {
                certificateOpinionScopeBO.setCreatedBy(
                        createCertificateOpinionScopeBto.getCreatedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createCertificateOpinionScopeBto);
            addedBto.setBo(certificateOpinionScopeBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return certificateOpinionScopeBO;
    }

    /** 删除多院区医疗证明建议 */
    @AutoGenerated(locked = true)
    protected DeleteCertificateOpinionScopeBoResult deleteCertificateOpinionScopeBase(
            DeleteCertificateOpinionScopeBto deleteCertificateOpinionScopeBto) {
        if (deleteCertificateOpinionScopeBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteCertificateOpinionScopeBoResult boResult =
                new DeleteCertificateOpinionScopeBoResult();
        CertificateOpinionScopeBO certificateOpinionScopeBO =
                deleteDeleteCertificateOpinionScopeOnMissThrowEx(
                        boResult, deleteCertificateOpinionScopeBto);
        boResult.setRootBo(certificateOpinionScopeBO);
        return boResult;
    }

    /** 删除对象:deleteCertificateOpinionScope,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private CertificateOpinionScopeBO deleteDeleteCertificateOpinionScopeOnMissThrowEx(
            BaseCertificateOpinionScopeBOService.DeleteCertificateOpinionScopeBoResult boResult,
            DeleteCertificateOpinionScopeBto deleteCertificateOpinionScopeBto) {
        CertificateOpinionScopeBO certificateOpinionScopeBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteCertificateOpinionScopeBto.getId() == null);
        if (!allNull && !found) {
            certificateOpinionScopeBO =
                    CertificateOpinionScopeBO.getById(deleteCertificateOpinionScopeBto.getId());
            found = true;
        }
        if (certificateOpinionScopeBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(certificateOpinionScopeBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteCertificateOpinionScopeBto);
            deletedBto.setEntity(certificateOpinionScopeBO.convertToCertificateOpinionScope());
            boResult.getDeletedBtoList().add(deletedBto);
            return certificateOpinionScopeBO;
        }
    }

    /** 更新多院区医疗证明建议 */
    @AutoGenerated(locked = true)
    protected UpdateCertificateOpinionScopeBoResult updateCertificateOpinionScopeBase(
            UpdateCertificateOpinionScopeBto updateCertificateOpinionScopeBto) {
        if (updateCertificateOpinionScopeBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateCertificateOpinionScopeBoResult boResult =
                new UpdateCertificateOpinionScopeBoResult();
        CertificateOpinionScopeBO certificateOpinionScopeBO =
                updateUpdateCertificateOpinionScopeOnMissThrowEx(
                        boResult, updateCertificateOpinionScopeBto);
        if (certificateOpinionScopeBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeBto, "__$validPropertySet"),
                    "certificateOpinionBtoList")) {
                createCertificateOpinionBtoOnDuplicateUpdate(
                        boResult, updateCertificateOpinionScopeBto, certificateOpinionScopeBO);
            }
        }
        boResult.setRootBo(certificateOpinionScopeBO);
        return boResult;
    }

    /** 更新多院区医疗证明建议启用状态 */
    @AutoGenerated(locked = true)
    protected UpdateCertificateOpinionScopeEnableFlagBoResult
            updateCertificateOpinionScopeEnableFlagBase(
                    UpdateCertificateOpinionScopeEnableFlagBto
                            updateCertificateOpinionScopeEnableFlagBto) {
        if (updateCertificateOpinionScopeEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateCertificateOpinionScopeEnableFlagBoResult boResult =
                new UpdateCertificateOpinionScopeEnableFlagBoResult();
        CertificateOpinionScopeBO certificateOpinionScopeBO =
                updateUpdateCertificateOpinionScopeEnableFlagOnMissThrowEx(
                        boResult, updateCertificateOpinionScopeEnableFlagBto);
        boResult.setRootBo(certificateOpinionScopeBO);
        return boResult;
    }

    /** 更新对象:updateCertificateOpinionScopeEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private CertificateOpinionScopeBO updateUpdateCertificateOpinionScopeEnableFlagOnMissThrowEx(
            BaseCertificateOpinionScopeBOService.UpdateCertificateOpinionScopeEnableFlagBoResult
                    boResult,
            UpdateCertificateOpinionScopeEnableFlagBto updateCertificateOpinionScopeEnableFlagBto) {
        CertificateOpinionScopeBO certificateOpinionScopeBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateCertificateOpinionScopeEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            certificateOpinionScopeBO =
                    CertificateOpinionScopeBO.getById(
                            updateCertificateOpinionScopeEnableFlagBto.getId());
            found = true;
        }
        if (certificateOpinionScopeBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(certificateOpinionScopeBO.convertToCertificateOpinionScope());
            updatedBto.setBto(updateCertificateOpinionScopeEnableFlagBto);
            updatedBto.setBo(certificateOpinionScopeBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeEnableFlagBto,
                                    "__$validPropertySet"),
                    "enableFlag")) {
                certificateOpinionScopeBO.setEnableFlag(
                        updateCertificateOpinionScopeEnableFlagBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeEnableFlagBto,
                                    "__$validPropertySet"),
                    "updatedBy")) {
                certificateOpinionScopeBO.setUpdatedBy(
                        updateCertificateOpinionScopeEnableFlagBto.getUpdatedBy());
            }
            return certificateOpinionScopeBO;
        }
    }

    /** 更新对象:updateCertificateOpinionScope,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private CertificateOpinionScopeBO updateUpdateCertificateOpinionScopeOnMissThrowEx(
            BaseCertificateOpinionScopeBOService.UpdateCertificateOpinionScopeBoResult boResult,
            UpdateCertificateOpinionScopeBto updateCertificateOpinionScopeBto) {
        CertificateOpinionScopeBO certificateOpinionScopeBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateCertificateOpinionScopeBto.getId() == null);
        if (!allNull && !found) {
            certificateOpinionScopeBO =
                    CertificateOpinionScopeBO.getById(updateCertificateOpinionScopeBto.getId());
            found = true;
        }
        if (certificateOpinionScopeBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(certificateOpinionScopeBO.convertToCertificateOpinionScope());
            updatedBto.setBto(updateCertificateOpinionScopeBto);
            updatedBto.setBo(certificateOpinionScopeBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeBto, "__$validPropertySet"),
                    "certificateTypeId")) {
                certificateOpinionScopeBO.setCertificateTypeId(
                        updateCertificateOpinionScopeBto.getCertificateTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeBto, "__$validPropertySet"),
                    "campusOrganizationIdList")) {
                certificateOpinionScopeBO.setCampusOrganizationIdList(
                        updateCertificateOpinionScopeBto.getCampusOrganizationIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeBto, "__$validPropertySet"),
                    "useScopeList")) {
                certificateOpinionScopeBO.setUseScopeList(
                        updateCertificateOpinionScopeBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeBto, "__$validPropertySet"),
                    "precautions")) {
                certificateOpinionScopeBO.setPrecautions(
                        updateCertificateOpinionScopeBto.getPrecautions());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeBto, "__$validPropertySet"),
                    "enableFlag")) {
                certificateOpinionScopeBO.setEnableFlag(
                        updateCertificateOpinionScopeBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateCertificateOpinionScopeBto, "__$validPropertySet"),
                    "updatedBy")) {
                certificateOpinionScopeBO.setUpdatedBy(
                        updateCertificateOpinionScopeBto.getUpdatedBy());
            }
            return certificateOpinionScopeBO;
        }
    }

    public static class CreateCertificateOpinionScopeBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public CertificateOpinionScopeBO getRootBo() {
            return (CertificateOpinionScopeBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateCertificateOpinionScopeBto.CertificateOpinionBto,
                        CertificateOpinionBO>
                getCreatedBto(
                        CreateCertificateOpinionScopeBto.CertificateOpinionBto
                                certificateOpinionBto) {
            return this.getAddedResult(certificateOpinionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateCertificateOpinionScopeBto, CertificateOpinionScopeBO> getCreatedBto(
                CreateCertificateOpinionScopeBto createCertificateOpinionScopeBto) {
            return this.getAddedResult(createCertificateOpinionScopeBto);
        }

        @AutoGenerated(locked = true)
        public CertificateOpinion getDeleted_CertificateOpinion() {
            return (CertificateOpinion)
                    CollectionUtil.getFirst(this.getDeletedEntityList(CertificateOpinion.class));
        }

        @AutoGenerated(locked = true)
        public CertificateOpinionScope getDeleted_CertificateOpinionScope() {
            return (CertificateOpinionScope)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(CertificateOpinionScope.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateCertificateOpinionScopeBto.CertificateOpinionBto,
                        CertificateOpinion,
                        CertificateOpinionBO>
                getUpdatedBto(
                        CreateCertificateOpinionScopeBto.CertificateOpinionBto
                                certificateOpinionBto) {
            return super.getUpdatedResult(certificateOpinionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateCertificateOpinionScopeBto,
                        CertificateOpinionScope,
                        CertificateOpinionScopeBO>
                getUpdatedBto(CreateCertificateOpinionScopeBto createCertificateOpinionScopeBto) {
            return super.getUpdatedResult(createCertificateOpinionScopeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateCertificateOpinionScopeBto.CertificateOpinionBto,
                        CertificateOpinionBO>
                getUnmodifiedBto(
                        CreateCertificateOpinionScopeBto.CertificateOpinionBto
                                certificateOpinionBto) {
            return super.getUnmodifiedResult(certificateOpinionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateCertificateOpinionScopeBto, CertificateOpinionScopeBO>
                getUnmodifiedBto(
                        CreateCertificateOpinionScopeBto createCertificateOpinionScopeBto) {
            return super.getUnmodifiedResult(createCertificateOpinionScopeBto);
        }
    }

    public static class DeleteCertificateOpinionScopeBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public CertificateOpinionScopeBO getRootBo() {
            return (CertificateOpinionScopeBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteCertificateOpinionScopeBto, CertificateOpinionScopeBO> getCreatedBto(
                DeleteCertificateOpinionScopeBto deleteCertificateOpinionScopeBto) {
            return this.getAddedResult(deleteCertificateOpinionScopeBto);
        }

        @AutoGenerated(locked = true)
        public CertificateOpinionScope getDeleted_CertificateOpinionScope() {
            return (CertificateOpinionScope)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(CertificateOpinionScope.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteCertificateOpinionScopeBto,
                        CertificateOpinionScope,
                        CertificateOpinionScopeBO>
                getUpdatedBto(DeleteCertificateOpinionScopeBto deleteCertificateOpinionScopeBto) {
            return super.getUpdatedResult(deleteCertificateOpinionScopeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteCertificateOpinionScopeBto, CertificateOpinionScopeBO>
                getUnmodifiedBto(
                        DeleteCertificateOpinionScopeBto deleteCertificateOpinionScopeBto) {
            return super.getUnmodifiedResult(deleteCertificateOpinionScopeBto);
        }
    }

    public static class UpdateCertificateOpinionScopeBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public CertificateOpinionScopeBO getRootBo() {
            return (CertificateOpinionScopeBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateCertificateOpinionScopeBto.CertificateOpinionBto,
                        CertificateOpinionBO>
                getCreatedBto(
                        UpdateCertificateOpinionScopeBto.CertificateOpinionBto
                                certificateOpinionBto) {
            return this.getAddedResult(certificateOpinionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateCertificateOpinionScopeBto, CertificateOpinionScopeBO> getCreatedBto(
                UpdateCertificateOpinionScopeBto updateCertificateOpinionScopeBto) {
            return this.getAddedResult(updateCertificateOpinionScopeBto);
        }

        @AutoGenerated(locked = true)
        public CertificateOpinion getDeleted_CertificateOpinion() {
            return (CertificateOpinion)
                    CollectionUtil.getFirst(this.getDeletedEntityList(CertificateOpinion.class));
        }

        @AutoGenerated(locked = true)
        public CertificateOpinionScope getDeleted_CertificateOpinionScope() {
            return (CertificateOpinionScope)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(CertificateOpinionScope.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateCertificateOpinionScopeBto.CertificateOpinionBto,
                        CertificateOpinion,
                        CertificateOpinionBO>
                getUpdatedBto(
                        UpdateCertificateOpinionScopeBto.CertificateOpinionBto
                                certificateOpinionBto) {
            return super.getUpdatedResult(certificateOpinionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateCertificateOpinionScopeBto,
                        CertificateOpinionScope,
                        CertificateOpinionScopeBO>
                getUpdatedBto(UpdateCertificateOpinionScopeBto updateCertificateOpinionScopeBto) {
            return super.getUpdatedResult(updateCertificateOpinionScopeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateCertificateOpinionScopeBto.CertificateOpinionBto,
                        CertificateOpinionBO>
                getUnmodifiedBto(
                        UpdateCertificateOpinionScopeBto.CertificateOpinionBto
                                certificateOpinionBto) {
            return super.getUnmodifiedResult(certificateOpinionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateCertificateOpinionScopeBto, CertificateOpinionScopeBO>
                getUnmodifiedBto(
                        UpdateCertificateOpinionScopeBto updateCertificateOpinionScopeBto) {
            return super.getUnmodifiedResult(updateCertificateOpinionScopeBto);
        }
    }

    public static class UpdateCertificateOpinionScopeEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public CertificateOpinionScopeBO getRootBo() {
            return (CertificateOpinionScopeBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateCertificateOpinionScopeEnableFlagBto, CertificateOpinionScopeBO>
                getCreatedBto(
                        UpdateCertificateOpinionScopeEnableFlagBto
                                updateCertificateOpinionScopeEnableFlagBto) {
            return this.getAddedResult(updateCertificateOpinionScopeEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public CertificateOpinionScope getDeleted_CertificateOpinionScope() {
            return (CertificateOpinionScope)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(CertificateOpinionScope.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateCertificateOpinionScopeEnableFlagBto,
                        CertificateOpinionScope,
                        CertificateOpinionScopeBO>
                getUpdatedBto(
                        UpdateCertificateOpinionScopeEnableFlagBto
                                updateCertificateOpinionScopeEnableFlagBto) {
            return super.getUpdatedResult(updateCertificateOpinionScopeEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateCertificateOpinionScopeEnableFlagBto, CertificateOpinionScopeBO>
                getUnmodifiedBto(
                        UpdateCertificateOpinionScopeEnableFlagBto
                                updateCertificateOpinionScopeEnableFlagBto) {
            return super.getUnmodifiedResult(updateCertificateOpinionScopeEnableFlagBto);
        }
    }
}
