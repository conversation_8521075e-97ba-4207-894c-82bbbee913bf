package com.pulse.certificate.service.converter;

import com.pulse.certificate.manager.dto.CertificateBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "e7db22ce-3992-335e-ab04-b2c4f7655637")
public class CertificateBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<CertificateBaseDto> CertificateBaseDtoConverter(
            List<CertificateBaseDto> certificateBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return certificateBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
