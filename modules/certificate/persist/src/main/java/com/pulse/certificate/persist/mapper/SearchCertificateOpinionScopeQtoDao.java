package com.pulse.certificate.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.pulse.certificate.persist.qto.SearchCertificateOpinionScopeQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "ea866803-21c9-4460-968f-f4ad8163699d|QTO|DAO")
public class SearchCertificateOpinionScopeQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询医疗证明范围及其建议明细 */
    @AutoGenerated(locked = false, uuid = "ea866803-21c9-4460-968f-f4ad8163699d-count")
    public Integer count(SearchCertificateOpinionScopeQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(certificate_opinion_scope.id) FROM certificate_opinion_scope WHERE"
                    + " certificate_opinion_scope.certificate_type_id = #certificateTypeIdIs AND"
                    + " #campusOrganizationIdListHas AND certificate_opinion_scope.enable_flag ="
                    + " #enableFlagIs AND #useScopeListHas ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getCampusOrganizationIdListHas())) {
            conditionToRemove.add("#campusOrganizationIdListHas");
        }
        if (qto.getCertificateTypeIdIs() == null) {
            conditionToRemove.add("#certificateTypeIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getUseScopeListHas())) {
            conditionToRemove.add("#useScopeListHas");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("certificate_opinion_scope");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql = sql.replace("#certificateTypeIdIs", "?").replace("#enableFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#campusOrganizationIdListHas")) {
                parsedSql =
                        StrUtil.replace(
                                parsedSql,
                                "#campusOrganizationIdListHas",
                                QtoUtil.buildHasSql(
                                        "certificate_opinion_scope.campus_organization_id_list",
                                        qto.getCampusOrganizationIdListHas().size()));
                sqlParams.addAll(
                        qto.getCampusOrganizationIdListHas().stream()
                                .map(item -> String.valueOf(item))
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#certificateTypeIdIs")) {
                sqlParams.add(qto.getCertificateTypeIdIs());
            } else if (paramName.equalsIgnoreCase("#useScopeListHas")) {
                parsedSql =
                        StrUtil.replace(
                                parsedSql,
                                "#useScopeListHas",
                                QtoUtil.buildHasSql(
                                        "certificate_opinion_scope.use_scope_list",
                                        qto.getUseScopeListHas().size()));
                sqlParams.addAll(
                        qto.getUseScopeListHas().stream()
                                .map(item -> String.valueOf(item))
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询医疗证明范围及其建议明细 */
    @AutoGenerated(locked = false, uuid = "ea866803-21c9-4460-968f-f4ad8163699d-query-all")
    public List<String> query(SearchCertificateOpinionScopeQto qto) {
        qto.setSize(1000);
        qto.setFrom(0);
        return this.queryPaged(qto);
    }

    /** 查询医疗证明范围及其建议明细 */
    @AutoGenerated(locked = false, uuid = "ea866803-21c9-4460-968f-f4ad8163699d-query-paginate")
    public List<String> queryPaged(SearchCertificateOpinionScopeQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT certificate_opinion_scope.id FROM certificate_opinion_scope WHERE"
                    + " certificate_opinion_scope.certificate_type_id = #certificateTypeIdIs AND"
                    + " #campusOrganizationIdListHas AND certificate_opinion_scope.enable_flag ="
                    + " #enableFlagIs AND #useScopeListHas ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getCampusOrganizationIdListHas())) {
            conditionToRemove.add("#campusOrganizationIdListHas");
        }
        if (qto.getCertificateTypeIdIs() == null) {
            conditionToRemove.add("#certificateTypeIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getUseScopeListHas())) {
            conditionToRemove.add("#useScopeListHas");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("certificate_opinion_scope");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql = sql.replace("#certificateTypeIdIs", "?").replace("#enableFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#campusOrganizationIdListHas")) {
                parsedSql =
                        StrUtil.replace(
                                parsedSql,
                                "#campusOrganizationIdListHas",
                                QtoUtil.buildHasSql(
                                        "certificate_opinion_scope.campus_organization_id_list",
                                        qto.getCampusOrganizationIdListHas().size()));
                sqlParams.addAll(
                        qto.getCampusOrganizationIdListHas().stream()
                                .map(item -> String.valueOf(item))
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#certificateTypeIdIs")) {
                sqlParams.add(qto.getCertificateTypeIdIs());
            } else if (paramName.equalsIgnoreCase("#useScopeListHas")) {
                parsedSql =
                        StrUtil.replace(
                                parsedSql,
                                "#useScopeListHas",
                                QtoUtil.buildHasSql(
                                        "certificate_opinion_scope.use_scope_list",
                                        qto.getUseScopeListHas().size()));
                sqlParams.addAll(
                        qto.getUseScopeListHas().stream()
                                .map(item -> String.valueOf(item))
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  certificate_opinion_scope.created_at desc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
