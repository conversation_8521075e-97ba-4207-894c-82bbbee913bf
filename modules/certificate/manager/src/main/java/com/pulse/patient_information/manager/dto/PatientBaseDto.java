package com.pulse.patient_information.manager.dto;

import com.pulse.patient_information.common.enums.PatientStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "442ca92c-1737-45bd-ac3d-e8ee80ef0047|DTO|DEFINITION")
public class PatientBaseDto {
    /** 患者头像 */
    @AutoGenerated(locked = true, uuid = "bfb94936-ebfe-4c00-be1f-1b4be90f1c3b")
    private String avatar;

    /** 出生地 */
    @AutoGenerated(locked = true, uuid = "d4e8192c-47d5-4233-9bb8-2b404bd03a37")
    private String birthAddress;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "b7ea68bb-e228-48ea-b8da-35dbc7394a86")
    private Date birthday;

    /** 献血证标识 */
    @AutoGenerated(locked = true, uuid = "284e1050-d01a-439c-b767-9a045d77f642")
    private Boolean bloodCardFlag;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "3877a985-0b5e-4a30-899b-e9115c587f55")
    private String cellphone;

    /** 子女统筹标志 */
    @AutoGenerated(locked = true, uuid = "1437e263-91bf-499a-82f9-9ffd17d0c031")
    private Boolean childrenCoordinatedFlag;

    /** 子女统筹有效期 */
    @AutoGenerated(locked = true, uuid = "81dfa415-c81b-470a-b414-f75790de812b")
    private Date childrenCoordinatedValidDate;

    /** 商保标志 */
    @AutoGenerated(locked = true, uuid = "89e22a35-c189-414c-85c1-89911bd9c11e")
    private Boolean commercialInsuranceFlag;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "76abcbd5-385b-499e-afcf-c519d5e63bda")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "987501a4-ce2b-4d28-a55b-c9f2fd4e47b9")
    private String createdBy;

    /** 默认费别 */
    @AutoGenerated(locked = true, uuid = "1cb406cf-2bb6-4fce-8630-80daef8b77f4")
    private String defaultChargeType;

    /** 残疾人证标识 */
    @AutoGenerated(locked = true, uuid = "bc34cb87-0b49-479a-9cea-71393e56c1d7")
    private Boolean disabilityFlag;

    /** 用于显示的id */
    @AutoGenerated(locked = true, uuid = "24af84bc-2b79-4d47-aa7d-f4669365ad38")
    private String displayId;

    /** 生理性别 */
    @AutoGenerated(locked = true, uuid = "926e7a9f-32cd-4f8e-96fc-9e7e6b6212a0")
    private String gender;

    /** 绿色通道标志 */
    @AutoGenerated(locked = true, uuid = "53796bd8-1269-43c0-b499-c5672b8a9769")
    private Boolean greenChannelFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "454c8e15-7f3a-4fc0-9bcd-8887258a1bdd")
    private String id;

    /** 主证件号 */
    @AutoGenerated(locked = true, uuid = "9b005b08-cdb9-4823-ab55-855ebf394720")
    private String idNumber;

    /** 主证件类型 */
    @AutoGenerated(locked = true, uuid = "e75d9133-c8eb-4bb3-83bb-3c1bb59fff48")
    private String idType;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "0c3655c7-fd56-4a98-8ebe-ecb2e2f6d3cb")
    private String identityCode;

    /** 医保卡号 */
    @AutoGenerated(locked = true, uuid = "59917c09-b646-4400-b042-af34009cb176")
    private String insuranceCardNumber;

    /** 医保账号 */
    @AutoGenerated(locked = true, uuid = "35f3180a-2489-43ea-8030-ffa9c538652c")
    private String insuranceNumber;

    /** 医保类型ID */
    @AutoGenerated(locked = true, uuid = "bff64777-d43d-4e2e-b401-8427ddf34898")
    private String insuranceTypeId;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "0917dccb-b399-48df-a8a3-637ac5d46568")
    private Long lockVersion;

    /** 主账标志 */
    @AutoGenerated(locked = true, uuid = "45bbba6e-7657-48f7-a62e-4b6e0321eb52")
    private Boolean mainAccountFlag;

    /** 劳模标志 */
    @AutoGenerated(locked = true, uuid = "7ace895d-4c79-47d1-85eb-b9eef4e643f2")
    private Boolean modelWorkerFlag;

    /** 姓名 */
    @AutoGenerated(locked = true, uuid = "10d95338-b717-42d5-8f50-7c9c5d45065f")
    private String name;

    /** 姓名输入码 */
    @AutoGenerated(locked = true, uuid = "f63bdfd8-5357-4ae2-97b3-a976a4f83891")
    private String nameInputCode;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "71eda7bf-f161-48b0-901b-bb103a46375d")
    private String phoneNumber;

    /** 公费级别 */
    @AutoGenerated(locked = true, uuid = "6385aff9-7ee6-4c0a-81df-6e9d6f7d4da3")
    private Long publicFundedLevel;

    /** 公费单位 */
    @AutoGenerated(locked = true, uuid = "c063a2f6-7b1d-4a9e-ad9a-c84164e1314f")
    private String publicFundedUnit;

    /** 公费证号 */
    @AutoGenerated(locked = true, uuid = "2f0b587c-6057-49f0-a8c3-474c19de6dfd")
    private String publicMedicalExpensesCertificateNumber;

    /** 状态码 */
    @AutoGenerated(locked = true, uuid = "07656fa3-72d8-4b42-bb21-098cb01d4064")
    private PatientStatusEnum status;

    /** 译名 */
    @AutoGenerated(locked = true, uuid = "4dcb7798-18b6-41ce-99c9-70af7581ce76")
    private String translateName;

    /** 无名患者标识 */
    @AutoGenerated(locked = true, uuid = "3a15e5c5-fa89-421d-a307-e88e52f91c9c")
    private Boolean unknownFlag;

    /** 无名患者类型 */
    @AutoGenerated(locked = true, uuid = "698c5a26-c3d0-4633-8c0d-e7dded596b20")
    private String unknownType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "fb519165-7019-4217-abe4-5233b086b795")
    private Date updatedAt;

    /** 操作员 */
    @AutoGenerated(locked = true, uuid = "9ec5694d-f791-48bf-a24f-481306f41fc1")
    private String updatedBy;

    /** 退伍军人标志 */
    @AutoGenerated(locked = true, uuid = "d670bfed-c271-4f30-aa45-da2dc1ad2e83")
    private Boolean veteranFlag;

    /** VIP标识 */
    @AutoGenerated(locked = true, uuid = "e2c73bf9-32ed-4541-a6a8-5d3b2c38ce9b")
    private Boolean vipFlag;
}
