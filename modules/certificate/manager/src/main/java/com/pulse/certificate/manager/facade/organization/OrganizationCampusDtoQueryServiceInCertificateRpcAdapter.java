package com.pulse.certificate.manager.facade.organization;

import com.pulse.certificate.manager.facade.organization.base.OrganizationCampusDtoQueryServiceInCertificateBaseRpcAdapter;
import com.pulse.organization.manager.dto.OrganizationCampusDto;
import com.pulse.organization.persist.qto.SearchOrganizationCampusQto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "39770e57-47d7-48fd-b96b-a3a6e064edff")
@AutoGenerated(locked = false, uuid = "10ecca02-e749-38c3-b6f0-91d738535489")
public class OrganizationCampusDtoQueryServiceInCertificateRpcAdapter
        extends OrganizationCampusDtoQueryServiceInCertificateBaseRpcAdapter {

    @RpcRefer(id = "2db7381e-0d49-4a88-8ce1-e4f10f9e5503", version = "1747363849302")
    @AutoGenerated(locked = false, uuid = "2db7381e-0d49-4a88-8ce1-e4f10f9e5503|RPC|ADAPTER")
    public List<OrganizationCampusDto> searchOrganizationCampus(SearchOrganizationCampusQto qto) {
        return super.searchOrganizationCampus(qto);
    }
}
