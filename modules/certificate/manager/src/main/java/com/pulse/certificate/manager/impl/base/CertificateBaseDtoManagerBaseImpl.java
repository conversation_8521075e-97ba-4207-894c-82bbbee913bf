package com.pulse.certificate.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.certificate.manager.CertificateBaseDtoManager;
import com.pulse.certificate.manager.converter.CertificateBaseDtoConverter;
import com.pulse.certificate.manager.dto.CertificateBaseDto;
import com.pulse.certificate.persist.dos.Certificate;
import com.pulse.certificate.persist.mapper.CertificateDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "803d280d-7655-47ae-9deb-8d3bfe06769b|DTO|BASE_MANAGER_IMPL")
public abstract class CertificateBaseDtoManagerBaseImpl implements CertificateBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private CertificateBaseDtoConverter certificateBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private CertificateDao certificateDao;

    @AutoGenerated(locked = true, uuid = "31ea53d3-0ed9-38e2-8d89-42d44512c5a8")
    @Override
    public List<CertificateBaseDto> getByOutpVisitIds(List<String> outpVisitId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpVisitId)) {
            return Collections.emptyList();
        }

        List<Certificate> certificateList = certificateDao.getByOutpVisitIds(outpVisitId);
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        return doConvertFromCertificateToCertificateBaseDto(certificateList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5bb0db53-a787-3e24-bdda-1b4a33db93c2")
    @Override
    public List<CertificateBaseDto> getByPatientIds(List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(patientId)) {
            return Collections.emptyList();
        }

        List<Certificate> certificateList = certificateDao.getByPatientIds(patientId);
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        return doConvertFromCertificateToCertificateBaseDto(certificateList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8708d26a-8a90-3dec-8db8-96715db22cc8")
    public List<CertificateBaseDto> doConvertFromCertificateToCertificateBaseDto(
            List<Certificate> certificateList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        Map<String, CertificateBaseDto> dtoMap =
                certificateBaseDtoConverter
                        .convertFromCertificateToCertificateBaseDto(certificateList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        CertificateBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<CertificateBaseDto> certificateBaseDtoList = new ArrayList<>();
        for (Certificate i : certificateList) {
            CertificateBaseDto certificateBaseDto = dtoMap.get(i.getId());
            if (certificateBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            certificateBaseDtoList.add(certificateBaseDto);
        }
        return certificateBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "983a3f1d-d57e-326f-9158-11ed4cacea51")
    @Override
    public CertificateBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CertificateBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        CertificateBaseDto certificateBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return certificateBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b179589a-4f44-3c02-b0aa-deaf9f3488be")
    @Override
    public List<CertificateBaseDto> getByOutpVisitId(String outpVisitId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CertificateBaseDto> certificateBaseDtoList =
                getByOutpVisitIds(Arrays.asList(outpVisitId));
        return certificateBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ba0aaf29-cb6a-3f12-9a75-13deee70e6e1")
    @Override
    public List<CertificateBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Certificate> certificateList = certificateDao.getByIds(id);
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Certificate> certificateMap =
                certificateList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        certificateList =
                id.stream()
                        .map(i -> certificateMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromCertificateToCertificateBaseDto(certificateList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d4c46e64-0ae3-3b79-a579-d179939f75d1")
    @Override
    public List<CertificateBaseDto> getByPatientId(String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CertificateBaseDto> certificateBaseDtoList = getByPatientIds(Arrays.asList(patientId));
        return certificateBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
