package com.pulse.certificate.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.certificate.manager.dto.CertificateBaseDto;
import com.pulse.certificate.persist.dos.Certificate;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "803d280d-7655-47ae-9deb-8d3bfe06769b|DTO|BASE_CONVERTER")
public class CertificateBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public CertificateBaseDto convertFromCertificateToCertificateBaseDto(Certificate certificate) {
        return convertFromCertificateToCertificateBaseDto(List.of(certificate)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<CertificateBaseDto> convertFromCertificateToCertificateBaseDto(
            List<Certificate> certificateList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(certificateList)) {
            return new ArrayList<>();
        }
        List<CertificateBaseDto> certificateBaseDtoList = new ArrayList<>();
        for (Certificate certificate : certificateList) {
            if (certificate == null) {
                continue;
            }
            CertificateBaseDto certificateBaseDto = new CertificateBaseDto();
            certificateBaseDto.setId(certificate.getId());
            certificateBaseDto.setOutpVisitId(certificate.getOutpVisitId());
            certificateBaseDto.setErpVisitId(certificate.getErpVisitId());
            certificateBaseDto.setInpVisitId(certificate.getInpVisitId());
            certificateBaseDto.setPatientId(certificate.getPatientId());
            certificateBaseDto.setCertificateTypeId(certificate.getCertificateTypeId());
            certificateBaseDto.setMedicalCardNumber(certificate.getMedicalCardNumber());
            certificateBaseDto.setName(certificate.getName());
            certificateBaseDto.setGender(certificate.getGender());
            certificateBaseDto.setDisplayAge(certificate.getDisplayAge());
            certificateBaseDto.setIdNumber(certificate.getIdNumber());
            certificateBaseDto.setNativeAddress(certificate.getNativeAddress());
            certificateBaseDto.setWorkUnit(certificate.getWorkUnit());
            certificateBaseDto.setMedicalDiagnosis(certificate.getMedicalDiagnosis());
            certificateBaseDto.setHandlingSuggestions(certificate.getHandlingSuggestions());
            certificateBaseDto.setRestDuration(certificate.getRestDuration());
            certificateBaseDto.setRestDurationUnit(certificate.getRestDurationUnit());
            certificateBaseDto.setMemo(certificate.getMemo());
            certificateBaseDto.setOpenDate(certificate.getOpenDate());
            certificateBaseDto.setOpenCampusOrganizationId(
                    certificate.getOpenCampusOrganizationId());
            certificateBaseDto.setOpenDoctorId(certificate.getOpenDoctorId());
            certificateBaseDto.setPrintFlag(certificate.getPrintFlag());
            certificateBaseDto.setPrintTimes(certificate.getPrintTimes());
            certificateBaseDto.setLastPrintBy(certificate.getLastPrintBy());
            certificateBaseDto.setLastPrintDateTime(certificate.getLastPrintDateTime());
            certificateBaseDto.setDepartmentId(certificate.getDepartmentId());
            certificateBaseDto.setCreatedBy(certificate.getCreatedBy());
            certificateBaseDto.setUpdatedBy(certificate.getUpdatedBy());
            certificateBaseDto.setReviewStatus(certificate.getReviewStatus());
            certificateBaseDto.setReviewerId(certificate.getReviewerId());
            certificateBaseDto.setReviewTime(certificate.getReviewTime());
            certificateBaseDto.setReviewResult(certificate.getReviewResult());
            certificateBaseDto.setReviewComment(certificate.getReviewComment());
            certificateBaseDto.setDeletedBy(certificate.getDeletedBy());
            certificateBaseDto.setLockVersion(certificate.getLockVersion());
            certificateBaseDto.setCreatedAt(certificate.getCreatedAt());
            certificateBaseDto.setUpdatedAt(certificate.getUpdatedAt());
            certificateBaseDto.setDeletedAt(certificate.getDeletedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            certificateBaseDtoList.add(certificateBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return certificateBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
