package com.pulse.certificate.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.certificate.manager.dto.CertificateProcessingOpinionBaseDto;
import com.pulse.certificate.persist.dos.CertificateOpinionScope;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "c495f5d8-1fc9-4759-967d-5e0f1ebf8ada|DTO|BASE_CONVERTER")
public class CertificateProcessingOpinionBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public CertificateProcessingOpinionBaseDto
            convertFromCertificateOpinionScopeToCertificateProcessingOpinionBaseDto(
                    CertificateOpinionScope certificateOpinionScope) {
        return convertFromCertificateOpinionScopeToCertificateProcessingOpinionBaseDto(
                        List.of(certificateOpinionScope))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<CertificateProcessingOpinionBaseDto>
            convertFromCertificateOpinionScopeToCertificateProcessingOpinionBaseDto(
                    List<CertificateOpinionScope> certificateOpinionScopeList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(certificateOpinionScopeList)) {
            return new ArrayList<>();
        }
        List<CertificateProcessingOpinionBaseDto> certificateProcessingOpinionBaseDtoList =
                new ArrayList<>();
        for (CertificateOpinionScope certificateOpinionScope : certificateOpinionScopeList) {
            if (certificateOpinionScope == null) {
                continue;
            }
            CertificateProcessingOpinionBaseDto certificateProcessingOpinionBaseDto =
                    new CertificateProcessingOpinionBaseDto();
            certificateProcessingOpinionBaseDto.setId(certificateOpinionScope.getId());
            certificateProcessingOpinionBaseDto.setCertificateTypeId(
                    certificateOpinionScope.getCertificateTypeId());
            certificateProcessingOpinionBaseDto.setCampusOrganizationIdList(
                    certificateOpinionScope.getCampusOrganizationIdList());
            certificateProcessingOpinionBaseDto.setUseScopeList(
                    certificateOpinionScope.getUseScopeList());
            certificateProcessingOpinionBaseDto.setPrecautions(
                    certificateOpinionScope.getPrecautions());
            certificateProcessingOpinionBaseDto.setEnableFlag(
                    certificateOpinionScope.getEnableFlag());
            certificateProcessingOpinionBaseDto.setCreatedBy(
                    certificateOpinionScope.getCreatedBy());
            certificateProcessingOpinionBaseDto.setUpdatedBy(
                    certificateOpinionScope.getUpdatedBy());
            certificateProcessingOpinionBaseDto.setDeletedBy(
                    certificateOpinionScope.getDeletedBy());
            certificateProcessingOpinionBaseDto.setLockVersion(
                    certificateOpinionScope.getLockVersion());
            certificateProcessingOpinionBaseDto.setCreatedAt(
                    certificateOpinionScope.getCreatedAt());
            certificateProcessingOpinionBaseDto.setUpdatedAt(
                    certificateOpinionScope.getUpdatedAt());
            certificateProcessingOpinionBaseDto.setDeletedAt(
                    certificateOpinionScope.getDeletedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            certificateProcessingOpinionBaseDtoList.add(certificateProcessingOpinionBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return certificateProcessingOpinionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
