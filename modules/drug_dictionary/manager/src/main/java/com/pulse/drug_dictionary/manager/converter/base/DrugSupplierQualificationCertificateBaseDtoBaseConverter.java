package com.pulse.drug_dictionary.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugSupplierQualificationCertificateBaseDto;
import com.pulse.drug_dictionary.persist.dos.DrugSupplierQualificationCertificate;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "a0a15b04-d11c-4f72-9595-7b86170ab889|DTO|BASE_CONVERTER")
public class DrugSupplierQualificationCertificateBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugSupplierQualificationCertificateBaseDto
            convertFromDrugSupplierQualificationCertificateToDrugSupplierQualificationCertificateBaseDto(
                    DrugSupplierQualificationCertificate drugSupplierQualificationCertificate) {
        return convertFromDrugSupplierQualificationCertificateToDrugSupplierQualificationCertificateBaseDto(
                        List.of(drugSupplierQualificationCertificate))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugSupplierQualificationCertificateBaseDto>
            convertFromDrugSupplierQualificationCertificateToDrugSupplierQualificationCertificateBaseDto(
                    List<DrugSupplierQualificationCertificate>
                            drugSupplierQualificationCertificateList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugSupplierQualificationCertificateList)) {
            return new ArrayList<>();
        }
        List<DrugSupplierQualificationCertificateBaseDto>
                drugSupplierQualificationCertificateBaseDtoList = new ArrayList<>();
        for (DrugSupplierQualificationCertificate drugSupplierQualificationCertificate :
                drugSupplierQualificationCertificateList) {
            if (drugSupplierQualificationCertificate == null) {
                continue;
            }
            DrugSupplierQualificationCertificateBaseDto
                    drugSupplierQualificationCertificateBaseDto =
                            new DrugSupplierQualificationCertificateBaseDto();
            drugSupplierQualificationCertificateBaseDto.setId(
                    drugSupplierQualificationCertificate.getId());
            drugSupplierQualificationCertificateBaseDto.setSupplierId(
                    drugSupplierQualificationCertificate.getSupplierId());
            drugSupplierQualificationCertificateBaseDto.setDrugOriginCode(
                    drugSupplierQualificationCertificate.getDrugOriginCode());
            drugSupplierQualificationCertificateBaseDto.setCertificateCode(
                    drugSupplierQualificationCertificate.getCertificateCode());
            drugSupplierQualificationCertificateBaseDto.setCertificateNameCode(
                    drugSupplierQualificationCertificate.getCertificateNameCode());
            drugSupplierQualificationCertificateBaseDto.setExpirationDate(
                    drugSupplierQualificationCertificate.getExpirationDate());
            drugSupplierQualificationCertificateBaseDto.setQualificationWarningDate(
                    drugSupplierQualificationCertificate.getQualificationWarningDate());
            drugSupplierQualificationCertificateBaseDto.setRemark(
                    drugSupplierQualificationCertificate.getRemark());
            drugSupplierQualificationCertificateBaseDto.setUpdatedBy(
                    drugSupplierQualificationCertificate.getUpdatedBy());
            drugSupplierQualificationCertificateBaseDto.setCreatedBy(
                    drugSupplierQualificationCertificate.getCreatedBy());
            drugSupplierQualificationCertificateBaseDto.setEnableFlag(
                    drugSupplierQualificationCertificate.getEnableFlag());
            drugSupplierQualificationCertificateBaseDto.setPicture(
                    drugSupplierQualificationCertificate.getPicture());
            drugSupplierQualificationCertificateBaseDto.setLockVersion(
                    drugSupplierQualificationCertificate.getLockVersion());
            drugSupplierQualificationCertificateBaseDto.setCreatedAt(
                    drugSupplierQualificationCertificate.getCreatedAt());
            drugSupplierQualificationCertificateBaseDto.setUpdatedAt(
                    drugSupplierQualificationCertificate.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugSupplierQualificationCertificateBaseDtoList.add(
                    drugSupplierQualificationCertificateBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugSupplierQualificationCertificateBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
