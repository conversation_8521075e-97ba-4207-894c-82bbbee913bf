package com.pulse.drug_dictionary.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.drug_dictionary.manager.DrugOriginForDetailDtoManager;
import com.pulse.drug_dictionary.manager.DrugOriginSpecificationBaseDtoManager;
import com.pulse.drug_dictionary.manager.DrugOriginSpecificationForDropDownDtoManager;
import com.pulse.drug_dictionary.manager.converter.DrugOriginSpecificationBaseDtoConverter;
import com.pulse.drug_dictionary.manager.converter.DrugOriginSpecificationForDropDownDtoConverter;
import com.pulse.drug_dictionary.manager.dto.DrugOriginForDetailDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationForDropDownDto;
import com.pulse.drug_dictionary.manager.facade.drug_inventory.DrugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter;
import com.pulse.drug_dictionary.persist.dos.DrugOriginSpecification;
import com.pulse.drug_dictionary.persist.mapper.DrugOriginSpecificationDao;
import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryWithSpecificationDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "e32904ac-5186-4e1d-b9b8-89647e6ec0ad|DTO|BASE_MANAGER_IMPL")
public abstract class DrugOriginSpecificationForDropDownDtoManagerBaseImpl
        implements DrugOriginSpecificationForDropDownDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginForDetailDtoManager drugOriginForDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter
            drugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationBaseDtoConverter drugOriginSpecificationBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationBaseDtoManager drugOriginSpecificationBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationDao drugOriginSpecificationDao;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationForDropDownDtoConverter
            drugOriginSpecificationForDropDownDtoConverter;

    @AutoGenerated(locked = true, uuid = "132d4dd5-8a78-3792-bb57-13df91993aaa")
    @Override
    public List<DrugOriginSpecificationForDropDownDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugOriginSpecification> drugOriginSpecificationMap =
                drugOriginSpecificationList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugOriginSpecificationList =
                id.stream()
                        .map(i -> drugOriginSpecificationMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationForDropDownDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "37d4a1d8-88f1-3c4e-83b1-70d79d725ad5")
    @Override
    public List<DrugOriginSpecificationForDropDownDto> getByDrugSpecificationDetailIds(
            List<String> drugSpecificationDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugSpecificationDetailId)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao.getByDrugSpecificationDetailIds(
                        drugSpecificationDetailId);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationForDropDownDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3b53dd1e-171c-30d3-90ed-e5c747b00cea")
    @Override
    public DrugOriginSpecificationForDropDownDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationForDropDownDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugOriginSpecificationForDropDownDto drugOriginSpecificationForDropDownDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugOriginSpecificationForDropDownDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "79eccfb8-a715-3eb6-a7ac-4f56ad10c9e7")
    @Override
    public List<DrugOriginSpecificationForDropDownDto>
            getByDrugOriginCodesAndSpecificationTypesAndDrugSpecificationsAndUnitsAndAmountPerPackages(
                    List<
                                    DrugOriginSpecification
                                            .AmountPerPackageAndDrugOriginCodeAndDrugSpecificationAndSpecificationTypeAndUnit>
                            var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(var)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao
                        .getByDrugOriginCodesAndSpecificationTypesAndDrugSpecificationsAndUnitsAndAmountPerPackages(
                                var);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationForDropDownDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8dfab32f-eb9a-32b5-8a55-24876eba57ba")
    @Override
    public List<DrugOriginSpecificationForDropDownDto> getByDrugSpecificationDetailId(
            String drugSpecificationDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationForDropDownDto> drugOriginSpecificationForDropDownDtoList =
                getByDrugSpecificationDetailIds(Arrays.asList(drugSpecificationDetailId));
        return drugOriginSpecificationForDropDownDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "90f2296c-20a6-3b49-90fe-fa975dbaff79")
    @Override
    public List<DrugOriginSpecificationForDropDownDto> getByDrugOriginCodes(
            List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginCode)) {
            return Collections.emptyList();
        }

        List<DrugOriginSpecification> drugOriginSpecificationList =
                drugOriginSpecificationDao.getByDrugOriginCodes(drugOriginCode);
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugOriginSpecificationToDrugOriginSpecificationForDropDownDto(
                drugOriginSpecificationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b0a8c8ba-ee2e-3cd6-9292-e3babf116f56")
    @Override
    public DrugOriginSpecificationForDropDownDto
            getByDrugOriginCodeAndSpecificationTypeAndDrugSpecificationAndUnitAndAmountPerPackage(
                    DrugOriginSpecification
                                    .AmountPerPackageAndDrugOriginCodeAndDrugSpecificationAndSpecificationTypeAndUnit
                            var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationForDropDownDto> ret =
                getByDrugOriginCodesAndSpecificationTypesAndDrugSpecificationsAndUnitsAndAmountPerPackages(
                        Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugOriginSpecificationForDropDownDto drugOriginSpecificationForDropDownDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugOriginSpecificationForDropDownDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c150d4a9-b7ed-3fd7-9726-b2e5c1e6cd96")
    @Override
    public List<DrugOriginSpecificationForDropDownDto> getByDrugOriginCode(String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugOriginSpecificationForDropDownDto> drugOriginSpecificationForDropDownDtoList =
                getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        return drugOriginSpecificationForDropDownDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ff1bccb7-3b2f-31fd-b8db-0749f4a5be7d")
    public List<DrugOriginSpecificationForDropDownDto>
            doConvertFromDrugOriginSpecificationToDrugOriginSpecificationForDropDownDto(
                    List<DrugOriginSpecification> drugOriginSpecificationList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginSpecificationList)) {
            return Collections.emptyList();
        }

        Map<String, String> drugOriginCodeMap =
                drugOriginSpecificationList.stream()
                        .filter(i -> i.getDrugOriginCode() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugOriginSpecification::getId,
                                        DrugOriginSpecification::getDrugOriginCode));
        List<DrugOriginForDetailDto> drugOriginCodeDrugOriginForDetailDtoList =
                drugOriginForDetailDtoManager.getByDrugOriginCodes(
                        new ArrayList<>(new HashSet<>(drugOriginCodeMap.values())));
        Map<String, DrugOriginForDetailDto> drugOriginCodeDrugOriginForDetailDtoMapRaw =
                drugOriginCodeDrugOriginForDetailDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugOriginForDetailDto::getDrugOriginCode, i -> i));
        Map<String, DrugOriginForDetailDto> drugOriginCodeDrugOriginForDetailDtoMap =
                drugOriginCodeMap.entrySet().stream()
                        .filter(
                                i ->
                                        drugOriginCodeDrugOriginForDetailDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                drugOriginCodeDrugOriginForDetailDtoMapRaw.get(
                                                        i.getValue())));

        List<DrugOriginInventoryWithSpecificationDto> drugOriginInventoryWithSpecificationDtoList =
                drugOriginInventoryWithSpecificationDtoServiceInDrugDictionaryRpcAdapter
                        .getByDrugOriginSpecificationIds(
                                drugOriginSpecificationList.stream()
                                        .map(i -> i.getId())
                                        .collect(Collectors.toList()));
        Map<String, List<DrugOriginInventoryWithSpecificationDto>>
                idDrugOriginInventoryWithSpecificationDtoListMap =
                        drugOriginInventoryWithSpecificationDtoList.stream()
                                .collect(
                                        Collectors.toMap(
                                                i -> i.getDrugOriginSpecification().getId(),
                                                Lists::newArrayList,
                                                (l1, l2) -> {
                                                    l1.addAll(l2);
                                                    return l1;
                                                }));

        List<DrugOriginSpecificationBaseDto> baseDtoList =
                drugOriginSpecificationBaseDtoConverter
                        .convertFromDrugOriginSpecificationToDrugOriginSpecificationBaseDto(
                                drugOriginSpecificationList);
        Map<String, DrugOriginSpecificationForDropDownDto> dtoMap =
                drugOriginSpecificationForDropDownDtoConverter
                        .convertFromDrugOriginSpecificationBaseDtoToDrugOriginSpecificationForDropDownDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugOriginSpecificationForDropDownDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugOriginSpecificationForDropDownDto> drugOriginSpecificationForDropDownDtoList =
                new ArrayList<>();
        for (DrugOriginSpecification i : drugOriginSpecificationList) {
            DrugOriginSpecificationForDropDownDto drugOriginSpecificationForDropDownDto =
                    dtoMap.get(i.getId());
            if (drugOriginSpecificationForDropDownDto == null) {
                continue;
            }

            if (null != i.getDrugOriginCode()) {
                drugOriginSpecificationForDropDownDto.setDrugOrigin(
                        drugOriginCodeDrugOriginForDetailDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getId()) {
                drugOriginSpecificationForDropDownDto.setDrugOriginInventoryWithSpecificationList(
                        idDrugOriginInventoryWithSpecificationDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugOriginSpecificationForDropDownDtoList.add(drugOriginSpecificationForDropDownDto);
        }
        return drugOriginSpecificationForDropDownDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
