package com.pulse.drug_dictionary.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.persist.dos.DrugProducerDictionary;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "d5e7d99e-dfb0-4ef5-8318-fcc10736d538|DTO|BASE_CONVERTER")
public class DrugProducerDictionaryBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugProducerDictionaryBaseDto
            convertFromDrugProducerDictionaryToDrugProducerDictionaryBaseDto(
                    DrugProducerDictionary drugProducerDictionary) {
        return convertFromDrugProducerDictionaryToDrugProducerDictionaryBaseDto(
                        List.of(drugProducerDictionary))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugProducerDictionaryBaseDto>
            convertFromDrugProducerDictionaryToDrugProducerDictionaryBaseDto(
                    List<DrugProducerDictionary> drugProducerDictionaryList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugProducerDictionaryList)) {
            return new ArrayList<>();
        }
        List<DrugProducerDictionaryBaseDto> drugProducerDictionaryBaseDtoList = new ArrayList<>();
        for (DrugProducerDictionary drugProducerDictionary : drugProducerDictionaryList) {
            if (drugProducerDictionary == null) {
                continue;
            }
            DrugProducerDictionaryBaseDto drugProducerDictionaryBaseDto =
                    new DrugProducerDictionaryBaseDto();
            drugProducerDictionaryBaseDto.setId(drugProducerDictionary.getId());
            drugProducerDictionaryBaseDto.setProducerName(drugProducerDictionary.getProducerName());
            drugProducerDictionaryBaseDto.setProducerNameAlias(
                    drugProducerDictionary.getProducerNameAlias());
            drugProducerDictionaryBaseDto.setProducerType(drugProducerDictionary.getProducerType());
            drugProducerDictionaryBaseDto.setDrugType(drugProducerDictionary.getDrugType());
            drugProducerDictionaryBaseDto.setInputCode(drugProducerDictionary.getInputCode());
            drugProducerDictionaryBaseDto.setEnableFlag(drugProducerDictionary.getEnableFlag());
            drugProducerDictionaryBaseDto.setUpdatedBy(drugProducerDictionary.getUpdatedBy());
            drugProducerDictionaryBaseDto.setCreatedBy(drugProducerDictionary.getCreatedBy());
            drugProducerDictionaryBaseDto.setProvinceCode(drugProducerDictionary.getProvinceCode());
            drugProducerDictionaryBaseDto.setStandardCode(drugProducerDictionary.getStandardCode());
            drugProducerDictionaryBaseDto.setExternalFlag(drugProducerDictionary.getExternalFlag());
            drugProducerDictionaryBaseDto.setRemark(drugProducerDictionary.getRemark());
            drugProducerDictionaryBaseDto.setCreatedAt(drugProducerDictionary.getCreatedAt());
            drugProducerDictionaryBaseDto.setUpdatedAt(drugProducerDictionary.getUpdatedAt());
            drugProducerDictionaryBaseDto.setLockVersion(drugProducerDictionary.getLockVersion());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugProducerDictionaryBaseDtoList.add(drugProducerDictionaryBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugProducerDictionaryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
