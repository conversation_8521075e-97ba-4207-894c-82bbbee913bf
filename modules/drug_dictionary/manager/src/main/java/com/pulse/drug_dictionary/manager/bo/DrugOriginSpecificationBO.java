package com.pulse.drug_dictionary.manager.bo;

import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.pulse.drug_dictionary.persist.dos.DrugOriginSpecification;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE drug_origin_specification  SET deleted_at = (EXTRACT(DAY FROM"
                    + " (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 24 * 60 * 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP"
                    + " - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60"
                    + " * 1000 + EXTRACT(MINUTE FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM"
                    + " (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 1000) WHERE id = ?")
@Getter
@Setter
@Table(name = "drug_origin_specification")
@Entity
@AutoGenerated(locked = true, uuid = "6e616895-a787-453e-988f-d0a4b0e70196|BO|DEFINITION")
public class DrugOriginSpecificationBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 拆分系数 当前规格可以拆分出多少个最小规格 */
    @Column(name = "amount_per_package")
    @AutoGenerated(locked = true, uuid = "40bd858b-3491-4922-afec-48199c083757")
    private Long amountPerPackage;

    /** 招标进价 */
    @Column(name = "bid_purchase_price")
    @AutoGenerated(locked = true, uuid = "3c3ea787-5d12-4b31-8cc1-3912a63ecd5b")
    private BigDecimal bidPurchasePrice;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "9702427b-1e46-5bd0-a92a-4961636f85d9")
    private Date createdAt;

    /** 是否标准规格 药库/药房默认使用规格 */
    @Column(name = "default_used_flag")
    @AutoGenerated(locked = true, uuid = "a2370f3a-7449-4c68-8816-238ac828cb4b")
    private Boolean defaultUsedFlag;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "8f280349-1b3f-547f-bc8d-c3e6b525ace7")
    private Long deletedAt = 0L;

    @ManyToOne
    @JoinColumn(name = "drug_origin_code", referencedColumnName = "drug_origin_code")
    @AutoGenerated(locked = true)
    private DrugOriginBO drugOriginBO;

    /** 规格 */
    @Column(name = "drug_specification")
    @AutoGenerated(locked = true, uuid = "9fcab074-7207-4076-903d-482c659f0c11")
    private String drugSpecification;

    /** 药品规格明细id */
    @Column(name = "drug_specification_detail_id")
    @AutoGenerated(locked = true, uuid = "6a9d2c6e-3f6a-412d-b2c9-13e1ce4fc6ce")
    private String drugSpecificationDetailId;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "6d8ab880-0577-42b9-9ec2-c4a726681e81")
    @Id
    private String id;

    /** 医保支付价 */
    @Column(name = "insurance_pay_price")
    @AutoGenerated(locked = true, uuid = "cfa4e0af-e926-41be-b8e9-169588030d95")
    private BigDecimal insurancePayPrice;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 价表项目编码 价表唯一编码（非id） */
    @Column(name = "price_item_code")
    @AutoGenerated(locked = true, uuid = "3524c361-b545-4601-97f9-d320788fcde9")
    private String priceItemCode;

    /** 参考零售价五 */
    @Column(name = "reference_retail_price_five")
    @AutoGenerated(locked = true, uuid = "79a0bd35-48ce-42c3-bf74-f4313ab32148")
    private BigDecimal referenceRetailPriceFive;

    /** 参考零售价四 */
    @Column(name = "reference_retail_price_four")
    @AutoGenerated(locked = true, uuid = "f10a0420-4778-4a6d-a2a0-978d3debb525")
    private BigDecimal referenceRetailPriceFour;

    /** 参考零售价一 */
    @Column(name = "reference_retail_price_one")
    @AutoGenerated(locked = true, uuid = "ca93f573-c1e0-4fb6-9aa5-7fb2e877a05b")
    private BigDecimal referenceRetailPriceOne;

    /** 参考零售价三 */
    @Column(name = "reference_retail_price_three")
    @AutoGenerated(locked = true, uuid = "fddecb82-1ef5-444e-bd72-054ca63d4a50")
    private BigDecimal referenceRetailPriceThree;

    /** 参考零售价二 */
    @Column(name = "reference_retail_price_tow")
    @AutoGenerated(locked = true, uuid = "37f26216-18f1-483a-bb93-632649165b21")
    private BigDecimal referenceRetailPriceTow;

    /** 规格类型 包装规格、最小规格、其他拆分规格 */
    @Column(name = "specification_type")
    @AutoGenerated(locked = true, uuid = "5b5ff7e5-7a42-4c3d-a025-5aff68cadbdd")
    @Enumerated(EnumType.STRING)
    private SpecificationTypeEnum specificationType;

    /** 单位 */
    @Column(name = "unit")
    @AutoGenerated(locked = true, uuid = "4449b742-a6a1-41d4-a975-ccf127e8f356")
    private String unit;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "e4ef570b-0424-50c4-9b6a-1875767d0df2")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "3de0640d-9018-4a6c-a0ac-f5884e05af1e|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public DrugOriginSpecification convertToDrugOriginSpecification() {
        DrugOriginSpecification entity = new DrugOriginSpecification();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "drugSpecificationDetailId",
                "priceItemCode",
                "specificationType",
                "drugSpecification",
                "unit",
                "amountPerPackage",
                "defaultUsedFlag",
                "bidPurchasePrice",
                "referenceRetailPriceOne",
                "referenceRetailPriceTow",
                "referenceRetailPriceThree",
                "referenceRetailPriceFour",
                "referenceRetailPriceFive",
                "insurancePayPrice",
                "createdAt",
                "updatedAt",
                "deletedAt");
        DrugOriginBO drugOriginBO = this.getDrugOriginBO();
        entity.setDrugOriginCode(drugOriginBO.getDrugOriginCode());
        return entity;
    }

    @AutoGenerated(locked = true)
    public Long getAmountPerPackage() {
        return this.amountPerPackage;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getBidPurchasePrice() {
        return this.bidPurchasePrice;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Boolean getDefaultUsedFlag() {
        return this.defaultUsedFlag;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public DrugOriginBO getDrugOriginBO() {
        return this.drugOriginBO;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginCode() {
        return this.getDrugOriginBO().getDrugOriginCode();
    }

    @AutoGenerated(locked = true)
    public String getDrugSpecification() {
        return this.drugSpecification;
    }

    @AutoGenerated(locked = true)
    public String getDrugSpecificationDetailId() {
        return this.drugSpecificationDetailId;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getInsurancePayPrice() {
        return this.insurancePayPrice;
    }

    @AutoGenerated(locked = true)
    public String getPriceItemCode() {
        return this.priceItemCode;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getReferenceRetailPriceFive() {
        return this.referenceRetailPriceFive;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getReferenceRetailPriceFour() {
        return this.referenceRetailPriceFour;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getReferenceRetailPriceOne() {
        return this.referenceRetailPriceOne;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getReferenceRetailPriceThree() {
        return this.referenceRetailPriceThree;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getReferenceRetailPriceTow() {
        return this.referenceRetailPriceTow;
    }

    @AutoGenerated(locked = true)
    public SpecificationTypeEnum getSpecificationType() {
        return this.specificationType;
    }

    @AutoGenerated(locked = true)
    public String getUnit() {
        return this.unit;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setAmountPerPackage(Long amountPerPackage) {
        this.amountPerPackage = amountPerPackage;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setBidPurchasePrice(BigDecimal bidPurchasePrice) {
        this.bidPurchasePrice = bidPurchasePrice;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setDefaultUsedFlag(Boolean defaultUsedFlag) {
        this.defaultUsedFlag = defaultUsedFlag;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setDrugOriginBO(DrugOriginBO drugOriginBO) {
        this.drugOriginBO = drugOriginBO;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setDrugSpecification(String drugSpecification) {
        this.drugSpecification = drugSpecification;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setDrugSpecificationDetailId(
            String drugSpecificationDetailId) {
        this.drugSpecificationDetailId = drugSpecificationDetailId;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setId(String id) {
        this.id = id;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setInsurancePayPrice(BigDecimal insurancePayPrice) {
        this.insurancePayPrice = insurancePayPrice;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setPriceItemCode(String priceItemCode) {
        this.priceItemCode = priceItemCode;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setReferenceRetailPriceFive(
            BigDecimal referenceRetailPriceFive) {
        this.referenceRetailPriceFive = referenceRetailPriceFive;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setReferenceRetailPriceFour(
            BigDecimal referenceRetailPriceFour) {
        this.referenceRetailPriceFour = referenceRetailPriceFour;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setReferenceRetailPriceOne(
            BigDecimal referenceRetailPriceOne) {
        this.referenceRetailPriceOne = referenceRetailPriceOne;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setReferenceRetailPriceThree(
            BigDecimal referenceRetailPriceThree) {
        this.referenceRetailPriceThree = referenceRetailPriceThree;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setReferenceRetailPriceTow(
            BigDecimal referenceRetailPriceTow) {
        this.referenceRetailPriceTow = referenceRetailPriceTow;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setSpecificationType(SpecificationTypeEnum specificationType) {
        this.specificationType = specificationType;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setUnit(String unit) {
        this.unit = unit;
        return (DrugOriginSpecificationBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginSpecificationBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugOriginSpecificationBO) this;
    }
}
