package com.pulse.drug_dictionary.manager.facade.pharmacy_warehouse_setting.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.pharmacy_warehouse_setting.manager.dto.ExportImportWayContrastBaseDto;
import com.pulse.pharmacy_warehouse_setting.persist.qto.ListWayContrastByWayIdQto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "2968d495-b88f-3ed9-8d07-e2b62d948857")
public class ExportImportWayContrastBaseDtoQueryServiceInDrugDictionaryBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "f9b1ed92-815c-458a-b9d6-3c2fb9f2eea1|RPC|BASE_ADAPTER")
    public List<ExportImportWayContrastBaseDto> listWayContrastByWayId(
            ListWayContrastByWayIdQto qto) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("qto", qto);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("qto", ListWayContrastByWayIdQto.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/pharmacy_warehouse_setting/f9b1ed92-815c-458a-b9d6-3c2fb9f2eea1/ExportImportWayContrastBaseDtoQueryService-listWayContrastByWayId",
                        "com.pulse.pharmacy_warehouse_setting.service.query.ExportImportWayContrastBaseDtoQueryService",
                        "listWayContrastByWayId",
                        paramMap,
                        paramTypeMap,
                        "8abe848a-da76-4713-8181-5bc6832bf785",
                        "c47ef390-e56c-4ad4-bf5b-04a1d408c462"),
                new TypeReference<>() {});
    }
}
