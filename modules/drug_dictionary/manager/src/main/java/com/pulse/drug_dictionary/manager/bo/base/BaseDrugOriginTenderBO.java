package com.pulse.drug_dictionary.manager.bo.base;

import com.pulse.drug_dictionary.manager.bo.DrugOriginTenderBO;
import com.pulse.drug_dictionary.persist.dos.DrugOriginTender;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_origin_tender")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "d0cf9bb4-652e-3943-b20d-eb81f75f02ce")
public abstract class BaseDrugOriginTenderBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "eccedbcb-8a85-36e0-b99c-8769118e0722")
    private Date createdAt;

    /** 创建者id */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "d007e03b-b090-32ae-a044-a278fa7ffcb3")
    private String createdBy;

    /** 扣率 */
    @Column(name = "discount_rate")
    @AutoGenerated(locked = true, uuid = "df21538f-d7f7-38d1-ad84-22e62c86d626")
    private BigDecimal discountRate;

    /** 药品商品编码 */
    @Column(name = "drug_origin_code")
    @AutoGenerated(locked = true, uuid = "b8ff3685-a83d-359a-bb25-5847798b4a31")
    private String drugOriginCode;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "f93187a2-7c7f-3df2-be54-2120b01c3d50")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "5be1609d-b16b-43c8-8017-8494f90ddd95")
    @Version
    private Long lockVersion;

    /** 进价 */
    @Column(name = "purchase_price")
    @AutoGenerated(locked = true, uuid = "be13eff8-5c2f-3458-8c8c-bd82f3df9fde")
    private BigDecimal purchasePrice;

    /** 供应商ID */
    @Column(name = "supplier_id")
    @AutoGenerated(locked = true, uuid = "cbd0389c-64ea-42cb-a06f-a2fae876958f")
    private String supplierId;

    /** 招标编码 */
    @Column(name = "tender_code")
    @AutoGenerated(locked = true, uuid = "cec5a53b-54de-3514-9ad7-0c280089d948")
    private String tenderCode;

    /** 招标单位编码 */
    @Column(name = "tender_unit_code")
    @AutoGenerated(locked = true, uuid = "7359ab0a-3a52-36e6-b367-d4508c2485ea")
    private String tenderUnitCode;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "af460400-9620-36ef-983c-f7976fb75cdf")
    private Date updatedAt;

    /** 修改人id */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "ef05041b-2b74-373c-ba98-823c93ddf4f6")
    private String updatedBy;

    /** 使用人 */
    @Column(name = "use_staff_id")
    @AutoGenerated(locked = true, uuid = "10384911-fcb6-3c31-bec7-0fd2473e50f2")
    private String useStaffId;

    /** 使用状态 */
    @Column(name = "use_status")
    @AutoGenerated(locked = true, uuid = "67b97fa5-a532-3826-b9b9-8faae9fdb59c")
    private String useStatus;

    @AutoGenerated(locked = true)
    public DrugOriginTender convertToDrugOriginTender() {
        DrugOriginTender entity = new DrugOriginTender();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "drugOriginCode",
                "tenderCode",
                "tenderUnitCode",
                "purchasePrice",
                "discountRate",
                "useStaffId",
                "useStatus",
                "updatedBy",
                "createdBy",
                "supplierId",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static DrugOriginTenderBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugOriginTenderBO drugOriginTender =
                (DrugOriginTenderBO)
                        session.createQuery("from DrugOriginTenderBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugOriginTender;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getDiscountRate() {
        return this.discountRate;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginCode() {
        return this.drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getPurchasePrice() {
        return this.purchasePrice;
    }

    @AutoGenerated(locked = true)
    public String getSupplierId() {
        return this.supplierId;
    }

    @AutoGenerated(locked = true)
    public String getTenderCode() {
        return this.tenderCode;
    }

    @AutoGenerated(locked = true)
    public String getTenderUnitCode() {
        return this.tenderUnitCode;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public String getUseStaffId() {
        return this.useStaffId;
    }

    @AutoGenerated(locked = true)
    public String getUseStatus() {
        return this.useStatus;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setDrugOriginCode(String drugOriginCode) {
        this.drugOriginCode = drugOriginCode;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setId(String id) {
        this.id = id;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setSupplierId(String supplierId) {
        this.supplierId = supplierId;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setTenderCode(String tenderCode) {
        this.tenderCode = tenderCode;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setTenderUnitCode(String tenderUnitCode) {
        this.tenderUnitCode = tenderUnitCode;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setUseStaffId(String useStaffId) {
        this.useStaffId = useStaffId;
        return (DrugOriginTenderBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugOriginTenderBO setUseStatus(String useStatus) {
        this.useStatus = useStatus;
        return (DrugOriginTenderBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
