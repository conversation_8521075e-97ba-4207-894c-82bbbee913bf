package com.pulse.drug_dictionary.persist.qto;

import com.pulse.drug_dictionary.persist.qto.SearchDrugOriginSpecificationForDropDownSelectQto.Filter;
import com.pulse.pharmacy_warehouse_setting.common.enums.SystemExportImportWayEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "d02c4272-c1bf-4ea5-b7a2-98945b0ac357|QTO|DEFINITION")
public class SearchDrugOriginSpecificationForDropDownSelectQto {
    /** 出入库方式 */
    @AutoGenerated(locked = true, uuid = "78410899-9e35-4cd0-bd55-7434a4cfaf29")
    private String exportImportWay;

    /** filter条件 */
    @AutoGenerated(locked = true)
    private Filter filter = new Filter();

    /** 主键 drug_origin_specification.id */
    @AutoGenerated(locked = true, uuid = "439f3db1-e7d4-40a3-902f-0eb22d7264db")
    @NotNull(message = "主键不能为空")
    private String idIs;

    /** 系统出入库方式 */
    @AutoGenerated(locked = true, uuid = "18b25e55-3ffa-49d8-a226-da5ccfd6a6b5")
    private SystemExportImportWayEnum systemExportImportWay;

    @Getter
    @Setter
    public static class Filter {
        /** 库房编码 drug_location.storage_code */
        @AutoGenerated(locked = true, uuid = "078c3add-f261-4b69-9205-3cf633829e1a")
        private String storageCodeIs;
    }
}
