package com.pulse.drug_dictionary.persist.mapper;

import com.pulse.drug_dictionary.persist.qto.ListFirmQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "c76afb1c-2104-4f53-8db3-19780e0dc6f3|QTO|DAO")
public class ListFirmQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 功能： 1、药品基本信息维护-厂家/供应商下拉 2、基本数据维护-药品产地列表、供货单位列表 实现步骤：获取厂家/供应商列表 */
    @AutoGenerated(locked = false, uuid = "c76afb1c-2104-4f53-8db3-19780e0dc6f3-count")
    public Integer count(ListFirmQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(drug_producer_dictionary.id) FROM drug_producer_dictionary WHERE"
                    + " drug_producer_dictionary.enable_flag = #stopFlagIs AND ("
                    + " JSON_VALUE(drug_producer_dictionary.input_code, '$.pinyin') like"
                    + " #inputCodeLike OR drug_producer_dictionary.producer_name like"
                    + " #inputCodeLike ) AND drug_producer_dictionary.enable_flag = #enableFlagIs"
                    + " AND drug_producer_dictionary.producer_name like #firmNameLike ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodeLike() == null) {
            conditionToRemove.add("#inputCodeLike");
        }
        if (qto.getInputCodeLike() == null) {
            conditionToRemove.add("#inputCodeLike");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getFirmNameLike() == null) {
            conditionToRemove.add("#firmNameLike");
        }
        if (qto.getStopFlagIs() == null) {
            conditionToRemove.add("#stopFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#inputCodeLike", "?")
                        .replace("#inputCodeLike", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#firmNameLike", "?")
                        .replace("#stopFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodeLike")) {
                sqlParams.add("%" + qto.getInputCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeLike")) {
                sqlParams.add("%" + qto.getInputCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#firmNameLike")) {
                sqlParams.add("%" + qto.getFirmNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#stopFlagIs")) {
                sqlParams.add(qto.getStopFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 功能： 1、药品基本信息维护-厂家/供应商下拉 2、基本数据维护-药品产地列表、供货单位列表 实现步骤：获取厂家/供应商列表 */
    @AutoGenerated(locked = false, uuid = "c76afb1c-2104-4f53-8db3-19780e0dc6f3-query-all")
    public List<String> query(ListFirmQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_producer_dictionary.id FROM drug_producer_dictionary WHERE"
                    + " drug_producer_dictionary.enable_flag = #stopFlagIs AND ("
                    + " JSON_VALUE(drug_producer_dictionary.input_code, '$.pinyin') like"
                    + " #inputCodeLike OR drug_producer_dictionary.producer_name like"
                    + " #inputCodeLike ) AND drug_producer_dictionary.enable_flag = #enableFlagIs"
                    + " AND drug_producer_dictionary.producer_name like #firmNameLike ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodeLike() == null) {
            conditionToRemove.add("#inputCodeLike");
        }
        if (qto.getInputCodeLike() == null) {
            conditionToRemove.add("#inputCodeLike");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getFirmNameLike() == null) {
            conditionToRemove.add("#firmNameLike");
        }
        if (qto.getStopFlagIs() == null) {
            conditionToRemove.add("#stopFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#inputCodeLike", "?")
                        .replace("#inputCodeLike", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#firmNameLike", "?")
                        .replace("#stopFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodeLike")) {
                sqlParams.add("%" + qto.getInputCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeLike")) {
                sqlParams.add("%" + qto.getInputCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#firmNameLike")) {
                sqlParams.add("%" + qto.getFirmNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#stopFlagIs")) {
                sqlParams.add(qto.getStopFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_producer_dictionary.input_code asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 功能： 1、药品基本信息维护-厂家/供应商下拉 2、基本数据维护-药品产地列表、供货单位列表 实现步骤：获取厂家/供应商列表 */
    @AutoGenerated(locked = false, uuid = "c76afb1c-2104-4f53-8db3-19780e0dc6f3-query-paginate")
    public List<String> queryPaged(ListFirmQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_producer_dictionary.id FROM drug_producer_dictionary WHERE"
                    + " drug_producer_dictionary.enable_flag = #stopFlagIs AND ("
                    + " JSON_VALUE(drug_producer_dictionary.input_code, '$.pinyin') like"
                    + " #inputCodeLike OR drug_producer_dictionary.producer_name like"
                    + " #inputCodeLike ) AND drug_producer_dictionary.enable_flag = #enableFlagIs"
                    + " AND drug_producer_dictionary.producer_name like #firmNameLike ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodeLike() == null) {
            conditionToRemove.add("#inputCodeLike");
        }
        if (qto.getInputCodeLike() == null) {
            conditionToRemove.add("#inputCodeLike");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getFirmNameLike() == null) {
            conditionToRemove.add("#firmNameLike");
        }
        if (qto.getStopFlagIs() == null) {
            conditionToRemove.add("#stopFlagIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#inputCodeLike", "?")
                        .replace("#inputCodeLike", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#firmNameLike", "?")
                        .replace("#stopFlagIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodeLike")) {
                sqlParams.add("%" + qto.getInputCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeLike")) {
                sqlParams.add("%" + qto.getInputCodeLike() + "%");
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#firmNameLike")) {
                sqlParams.add("%" + qto.getFirmNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#stopFlagIs")) {
                sqlParams.add(qto.getStopFlagIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_producer_dictionary.input_code asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
