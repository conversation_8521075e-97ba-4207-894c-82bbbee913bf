package com.pulse.drug_dictionary.persist.mapper;

import com.pulse.drug_dictionary.persist.qto.ListDrugSupplierQualityEvaluationQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "4a6b7c2c-6a5b-4e6a-83d5-7e43c3a41b20|QTO|DAO")
public class ListDrugSupplierQualityEvaluationQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 基础数据维护-供货单位质量评价 */
    @AutoGenerated(locked = false, uuid = "4a6b7c2c-6a5b-4e6a-83d5-7e43c3a41b20-count")
    public Integer count(ListDrugSupplierQualityEvaluationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(drug_supplier_quality_evaluation.id) FROM"
                    + " drug_supplier_quality_evaluation WHERE"
                    + " drug_supplier_quality_evaluation.supplier_id = #supplierIdIs AND"
                    + " drug_supplier_quality_evaluation.invalid_flag = #invalidFlagIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInvalidFlagIs() == null) {
            conditionToRemove.add("#invalidFlagIs");
        }
        if (qto.getSupplierIdIs() == null) {
            conditionToRemove.add("#supplierIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#invalidFlagIs", "?").replace("#supplierIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#invalidFlagIs")) {
                sqlParams.add(qto.getInvalidFlagIs());
            } else if (paramName.equalsIgnoreCase("#supplierIdIs")) {
                sqlParams.add(qto.getSupplierIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 基础数据维护-供货单位质量评价 */
    @AutoGenerated(locked = false, uuid = "4a6b7c2c-6a5b-4e6a-83d5-7e43c3a41b20-query-all")
    public List<String> query(ListDrugSupplierQualityEvaluationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_supplier_quality_evaluation.id FROM drug_supplier_quality_evaluation"
                    + " WHERE drug_supplier_quality_evaluation.supplier_id = #supplierIdIs AND"
                    + " drug_supplier_quality_evaluation.invalid_flag = #invalidFlagIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInvalidFlagIs() == null) {
            conditionToRemove.add("#invalidFlagIs");
        }
        if (qto.getSupplierIdIs() == null) {
            conditionToRemove.add("#supplierIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#invalidFlagIs", "?").replace("#supplierIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#invalidFlagIs")) {
                sqlParams.add(qto.getInvalidFlagIs());
            } else if (paramName.equalsIgnoreCase("#supplierIdIs")) {
                sqlParams.add(qto.getSupplierIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_supplier_quality_evaluation.id asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 基础数据维护-供货单位质量评价 */
    @AutoGenerated(locked = false, uuid = "4a6b7c2c-6a5b-4e6a-83d5-7e43c3a41b20-query-paginate")
    public List<String> queryPaged(ListDrugSupplierQualityEvaluationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_supplier_quality_evaluation.id FROM drug_supplier_quality_evaluation"
                    + " WHERE drug_supplier_quality_evaluation.supplier_id = #supplierIdIs AND"
                    + " drug_supplier_quality_evaluation.invalid_flag = #invalidFlagIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInvalidFlagIs() == null) {
            conditionToRemove.add("#invalidFlagIs");
        }
        if (qto.getSupplierIdIs() == null) {
            conditionToRemove.add("#supplierIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql = sql.replace("#invalidFlagIs", "?").replace("#supplierIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#invalidFlagIs")) {
                sqlParams.add(qto.getInvalidFlagIs());
            } else if (paramName.equalsIgnoreCase("#supplierIdIs")) {
                sqlParams.add(qto.getSupplierIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_supplier_quality_evaluation.id asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
