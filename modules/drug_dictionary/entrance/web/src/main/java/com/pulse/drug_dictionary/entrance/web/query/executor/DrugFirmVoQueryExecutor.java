package com.pulse.drug_dictionary.entrance.web.query.executor;

import com.pulse.drug_dictionary.entrance.web.converter.DrugFirmVoConverter;
import com.pulse.drug_dictionary.entrance.web.query.assembler.DrugFirmVoDataAssembler;
import com.pulse.drug_dictionary.entrance.web.vo.DrugFirmVo;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.persist.qto.ListFirmQto;
import com.pulse.drug_dictionary.service.DrugProducerDictionaryBaseDtoService;
import com.pulse.drug_dictionary.service.index.entity.ListFirmQtoService;
import com.vs.code.AutoGenerated;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/** DrugFirmVo查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "f9a34845-9312-3ea1-847b-56b6659e9dd4")
public class DrugFirmVoQueryExecutor {
    @AutoGenerated(locked = true)
    @Resource
    private DrugFirmVoConverter drugFirmVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugFirmVoDataAssembler drugFirmVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugProducerDictionaryBaseDtoService drugProducerDictionaryBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ListFirmQtoService listFirmQtoService;

    /** 根据ListFirmQto查询DrugFirmVo列表,瀑布流 */
    @AutoGenerated(locked = false, uuid = "74f8bd38-39e3-35df-a402-df79bfd51dc2")
    public VSQueryResult<DrugFirmVo> queryByListFirmWaterfall(@NotNull ListFirmQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listFirmQtoService.queryWaterfall(qto);
        Map<String, DrugFirmVo> idVoMap = toIdVoMap(ids);
        drugFirmVoDataAssembler.assembleData(idVoMap);
        List<DrugFirmVo> voList = new ArrayList<>(idVoMap.values());
        VSQueryResult result = new VSQueryResult();
        result.setResult(voList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        result.setHasMore(listFirmQtoService.hasMore(qto));
        result.setScrollId(qto.getScrollId());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为Map<ID, VO> */
    @AutoGenerated(locked = true, uuid = "801bae89-a6f5-3e24-bd05-327beb3474c6")
    private Map<String, DrugFirmVo> toIdVoMap(List<String> ids) {
        List<DrugProducerDictionaryBaseDto> rootBaseDtoList =
                drugProducerDictionaryBaseDtoService.getByIds(ids);
        Map<String, DrugProducerDictionaryBaseDto> baseDtoMap =
                rootBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugProducerDictionaryBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        Map<DrugProducerDictionaryBaseDto, DrugFirmVo> voMap =
                drugFirmVoConverter.convertToDrugFirmVoMap(new ArrayList<>(baseDtoMap.values()));
        Map<String, DrugFirmVo> idVoMap =
                baseDtoMap.values().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugProducerDictionaryBaseDto::getId,
                                        baseDto -> voMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        return ids.stream()
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                id -> idVoMap.get(id),
                                (o1, o2) -> o1,
                                LinkedHashMap::new));
    }

    /** 根据ListFirmQto查询DrugFirmVo列表,分页 */
    @AutoGenerated(locked = false, uuid = "9398caef-9e73-34ba-90bd-eaec15c96322")
    public VSQueryResult<DrugFirmVo> queryByListFirmPaged(@NotNull ListFirmQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listFirmQtoService.queryPaged(qto);
        Map<String, DrugFirmVo> idVoMap = toIdVoMap(ids);
        drugFirmVoDataAssembler.assembleData(idVoMap);
        List<DrugFirmVo> voList = new ArrayList<>(idVoMap.values());
        VSQueryResult result = new VSQueryResult();
        result.setCount(listFirmQtoService.count(qto));
        result.setResult(voList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
