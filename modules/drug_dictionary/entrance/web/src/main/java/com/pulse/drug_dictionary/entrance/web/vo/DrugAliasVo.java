package com.pulse.drug_dictionary.entrance.web.vo;

import com.pulse.drug_dictionary.common.enums.NameTypeEnum;
import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.pulse.drug_dictionary.entrance.web.vo.DrugAliasVo.DrugOriginVo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "9a330dc5-9ca4-47a3-ba8a-a4f57e4454c5|VO|DEFINITION")
public class DrugAliasVo {
    /** 药品名称 */
    @AutoGenerated(locked = true, uuid = "e29fcb78-7457-4729-b377-62e978b8f544")
    private String drugName;

    /** 药品产地 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b74c141b-9a9e-4a16-8c7f-f3a3f6e3b8cc")
    private DrugOriginVo drugOrigin;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "91a0db49-f9a6-4e01-b5c7-63ea501f685e")
    private String id;

    /** 名称类型 通用名、化学名、英文名、商品名、别名 */
    @AutoGenerated(locked = true, uuid = "28a20b7c-c929-46d8-ad57-1bd39f503b40")
    private NameTypeEnum nameType;

    @Setter
    @Getter
    public static class DrugOriginExtensionVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "b1ea027d-f3fa-4dd3-ad6c-45e161454620")
        private String id;

        /**
         * 药械平台药品id
         * 药械平台id，药械平台对应药品主键，做显示使用（药品检索方案也会显示），方便药师查找药械平台对应药品。这个字段浙二应该没有维护（对接完药械后会有一张医院药品和平台药品的字典对照表）
         */
        @AutoGenerated(locked = true, uuid = "ff25196e-d542-4828-81eb-19f1167aac01")
        private String purchasePlatformDrugId;
    }

    @Setter
    @Getter
    public static class DrugOriginSpecificationVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "a460e5b2-b4d6-44f4-b05e-06a9de50d514")
        private String id;

        /** 招标进价 */
        @AutoGenerated(locked = true, uuid = "98571a26-33f6-4f56-a9d6-2321a2313f2e")
        private BigDecimal bidPurchasePrice;

        /** 参考零售价一 */
        @AutoGenerated(locked = true, uuid = "28d76899-c8f3-4427-b6c3-56a409f62908")
        private BigDecimal referenceRetailPriceOne;

        /** 药品规格明细id */
        @AutoGenerated(locked = true, uuid = "34700b0d-45d3-4179-b46b-f25b0339e5f0")
        private String drugSpecificationDetailId;

        /** 规格类型 包装规格、最小规格、其他拆分规格 */
        @AutoGenerated(locked = true, uuid = "853125b1-e5cc-4dc0-a7e3-b3fb2d750bd7")
        private SpecificationTypeEnum specificationType;

        /** 规格 */
        @AutoGenerated(locked = true, uuid = "c65b0ad7-ecb9-4b3d-b5e3-ef7884f2231c")
        private String drugSpecification;
    }

    @Setter
    @Getter
    public static class DrugProducerDictionaryVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "7b787f64-30d6-4e80-b032-214437189ac4")
        private String id;

        /** 生产商名称 */
        @AutoGenerated(locked = true, uuid = "dc223952-83ae-42ef-b809-250fd4f91f6f")
        private String producerName;
    }

    @Setter
    @Getter
    public static class DrugOriginVo {
        /** 药品产地名称 */
        @AutoGenerated(locked = true, uuid = "d377f1f7-4b4a-4c7f-bc14-7992a0bde8ea")
        private String drugOriginName;

        /** 药品编码 */
        @AutoGenerated(locked = true, uuid = "3e8b575a-22e1-47bd-90a4-a83354910137")
        private String drugCode;

        /** 药品规格id */
        @AutoGenerated(locked = true, uuid = "fdb57cbd-bfe1-4e2c-a84a-9dab59e6d99a")
        private String drugSpecificationId;

        /** 药品生产商ID */
        @Valid
        @AutoGenerated(locked = true, uuid = "57f6989e-5b69-41ce-9ad4-20218985d02b")
        private DrugProducerDictionaryVo drugProducer;

        /** 包装规格 冗余存 */
        @AutoGenerated(locked = true, uuid = "8a8a101b-2c8e-4adb-abf3-d941473cdfcf")
        private String packageSpecification;

        /** 包装单位 冗余存 */
        @AutoGenerated(locked = true, uuid = "3034ed88-b388-4d17-bde2-73cf298fb3c0")
        private String packageUnit;

        /** GMP标志 良好生产规范 */
        @AutoGenerated(locked = true, uuid = "2c27fb56-f938-4236-872c-c130d4bf3a9c")
        private Boolean gmpFlag;

        @Valid
        @AutoGenerated(locked = true, uuid = "c7c2e596-64c7-4904-a6e4-f08b263349b6")
        private List<DrugAliasVo.DrugOriginSpecificationVo> drugOriginSpecificationList;

        /** 包装参考进价 */
        @AutoGenerated(locked = true, uuid = "05c0625b-3314-4c04-a2d9-101e96beeffc")
        private BigDecimal packagePurchasePrice;

        /** 包装参考零售价 */
        @AutoGenerated(locked = true, uuid = "28d5d642-7c2f-4743-b0ac-85403c752aea")
        private BigDecimal packageRetailPrice;

        @Valid
        @AutoGenerated(locked = true, uuid = "19d56d3e-a6d5-425d-9911-a44e7d48df2f")
        private DrugOriginExtensionVo drugOriginExtension;
    }
}
