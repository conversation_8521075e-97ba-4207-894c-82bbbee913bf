package com.pulse.drug_dictionary.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> DrugSupplierQualityEvaluation
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "3cf58b6e-5ba1-496a-ad6d-c0abfee65128|BTO|DEFINITION")
public class MergeDrugSupplierQualityEvaluationBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 售后服务 */
    @AutoGenerated(locked = true, uuid = "63a26552-f039-448e-ae97-37f1ea9556cb")
    private String afterSaleService;

    /** 条形化管理 */
    @AutoGenerated(locked = true, uuid = "1c28171d-fe76-49dd-976c-d399ec6897a4")
    private String barcodeManage;

    /** 配送服务 */
    @AutoGenerated(locked = true, uuid = "335d3cbf-6d08-4786-bc93-e62defb22cc2")
    private String distributeService;

    /** 企业信用 */
    @AutoGenerated(locked = true, uuid = "a58f9225-cb3a-459a-b70a-91c7be37c647")
    private String firmCredit;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "626d36b1-de41-48b0-9f89-88f844728a7b")
    private String id;

    /** 资质材料 */
    @AutoGenerated(locked = true, uuid = "108317dc-9fe8-4c7f-8d29-a30bad6e97eb")
    private String qualificationMaterial;

    /** 质量评估 */
    @AutoGenerated(locked = true, uuid = "1276e055-6149-45e1-90d7-79d53fddad58")
    private String qualityEvaluation;

    /** 供货商id */
    @AutoGenerated(locked = true, uuid = "c0e0bbed-7985-41ad-b2c3-750e5b56ed3f")
    private String supplierId;

    /** 运输条件 */
    @AutoGenerated(locked = true, uuid = "b3dbe577-19a1-41b8-bee5-a4b620b026cf")
    private String transportCondition;

    /** 修改人 */
    @AutoGenerated(locked = true, uuid = "40d43cba-c96c-4197-a24a-857407cde2b9")
    private String updateBy;

    @AutoGenerated(locked = true)
    public void setAfterSaleService(String afterSaleService) {
        this.__$validPropertySet.add("afterSaleService");
        this.afterSaleService = afterSaleService;
    }

    @AutoGenerated(locked = true)
    public void setBarcodeManage(String barcodeManage) {
        this.__$validPropertySet.add("barcodeManage");
        this.barcodeManage = barcodeManage;
    }

    @AutoGenerated(locked = true)
    public void setDistributeService(String distributeService) {
        this.__$validPropertySet.add("distributeService");
        this.distributeService = distributeService;
    }

    @AutoGenerated(locked = true)
    public void setFirmCredit(String firmCredit) {
        this.__$validPropertySet.add("firmCredit");
        this.firmCredit = firmCredit;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setQualificationMaterial(String qualificationMaterial) {
        this.__$validPropertySet.add("qualificationMaterial");
        this.qualificationMaterial = qualificationMaterial;
    }

    @AutoGenerated(locked = true)
    public void setQualityEvaluation(String qualityEvaluation) {
        this.__$validPropertySet.add("qualityEvaluation");
        this.qualityEvaluation = qualityEvaluation;
    }

    @AutoGenerated(locked = true)
    public void setSupplierId(String supplierId) {
        this.__$validPropertySet.add("supplierId");
        this.supplierId = supplierId;
    }

    @AutoGenerated(locked = true)
    public void setTransportCondition(String transportCondition) {
        this.__$validPropertySet.add("transportCondition");
        this.transportCondition = transportCondition;
    }

    @AutoGenerated(locked = true)
    public void setUpdateBy(String updateBy) {
        this.__$validPropertySet.add("updateBy");
        this.updateBy = updateBy;
    }
}
