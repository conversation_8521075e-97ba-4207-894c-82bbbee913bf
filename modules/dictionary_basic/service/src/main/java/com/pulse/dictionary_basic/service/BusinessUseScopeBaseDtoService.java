package com.pulse.dictionary_basic.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_basic.manager.BusinessUseScopeBaseDtoManager;
import com.pulse.dictionary_basic.manager.dto.BusinessUseScopeBaseDto;
import com.pulse.dictionary_basic.persist.dos.BusinessUseScope.EntityIdAndEntityType;
import com.pulse.dictionary_basic.persist.eo.UkUseScopeEntityEo;
import com.pulse.dictionary_basic.service.converter.BusinessUseScopeBaseDtoServiceConverter;
import com.pulse.dictionary_basic.service.converter.voConverter.BusinessUseScopeUkUseScopeEntityConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "ce13313e-6bfd-46c7-be4c-47e357844161|DTO|SERVICE")
public class BusinessUseScopeBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private BusinessUseScopeBaseDtoManager businessUseScopeBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private BusinessUseScopeBaseDtoServiceConverter businessUseScopeBaseDtoServiceConverter;

    @PublicInterface(id = "b04c4f32-49e1-4c6e-bace-a225c4890347", module = "dictionary_basic")
    @AutoGenerated(locked = false, uuid = "0ab97400-8bb6-36a3-9362-1141d2cadc14")
    public BusinessUseScopeBaseDto getById(@NotNull(message = "主键不能为空") Long id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<BusinessUseScopeBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "d4f82840-c88f-4422-9370-41783615c34c", module = "dictionary_basic")
    @AutoGenerated(locked = false, uuid = "2b9b11f4-c5ad-3883-801d-3b4f7d963af5")
    public List<BusinessUseScopeBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<Long> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<BusinessUseScopeBaseDto> businessUseScopeBaseDtoList =
                businessUseScopeBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return businessUseScopeBaseDtoServiceConverter.BusinessUseScopeBaseDtoConverter(
                businessUseScopeBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0a896ad5-7b72-4dad-84bc-1f9704507215", module = "dictionary_basic")
    @AutoGenerated(locked = false, uuid = "90280617-77fa-36c0-a899-d350b048928f")
    public List<BusinessUseScopeBaseDto> getByEntityTypesAndEntityIds(
            @Valid @NotNull List<UkUseScopeEntityEo> ukUseScopeEntityEo) {
        List<EntityIdAndEntityType> entityIdAndEntityType =
                ukUseScopeEntityEo.stream()
                        .map(
                                BusinessUseScopeUkUseScopeEntityConverter
                                        ::convertFromUkUseScopeEntityToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<BusinessUseScopeBaseDto> businessUseScopeBaseDtoList =
                businessUseScopeBaseDtoManager.getByEntityTypesAndEntityIds(entityIdAndEntityType);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return businessUseScopeBaseDtoServiceConverter.BusinessUseScopeBaseDtoConverter(
                businessUseScopeBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f8d1acec-ef89-4e8e-a892-29b8814469e9", module = "dictionary_basic")
    @AutoGenerated(locked = false, uuid = "c89ae8ae-0b16-3c92-b7f4-37d7e38c19cb")
    public BusinessUseScopeBaseDto getByEntityTypeAndEntityId(
            @Valid @NotNull UkUseScopeEntityEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<BusinessUseScopeBaseDto> ret = getByEntityTypesAndEntityIds(Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
