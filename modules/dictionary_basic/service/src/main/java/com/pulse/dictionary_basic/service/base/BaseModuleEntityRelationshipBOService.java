package com.pulse.dictionary_basic.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_basic.manager.bo.*;
import com.pulse.dictionary_basic.manager.bo.ModuleEntityRelationshipBO;
import com.pulse.dictionary_basic.persist.dos.ModuleEntityRelationship;
import com.pulse.dictionary_basic.service.base.BaseModuleEntityRelationshipBOService.MergeModuleEntityRelationshipBoResult;
import com.pulse.dictionary_basic.service.bto.MergeModuleEntityRelationshipBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "c4e5dfe0-e65d-333a-9b16-726b055ff926")
public class BaseModuleEntityRelationshipBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ModuleEntityRelationshipBO createMergeModuleEntityRelationshipOnDuplicateUpdate(
            BaseModuleEntityRelationshipBOService.MergeModuleEntityRelationshipBoResult boResult,
            MergeModuleEntityRelationshipBto mergeModuleEntityRelationshipBto) {
        ModuleEntityRelationshipBO moduleEntityRelationshipBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeModuleEntityRelationshipBto.getTableName() == null);
        if (!allNull && !found) {
            moduleEntityRelationshipBO =
                    ModuleEntityRelationshipBO.getByTableName(
                            mergeModuleEntityRelationshipBto.getTableName());
            if (moduleEntityRelationshipBO != null) {
                matchedUkName += "(";
                matchedUkName += "'table_name'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (moduleEntityRelationshipBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        moduleEntityRelationshipBO.convertToModuleEntityRelationship());
                updatedBto.setBto(mergeModuleEntityRelationshipBto);
                updatedBto.setBo(moduleEntityRelationshipBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "moduleId")) {
                    moduleEntityRelationshipBO.setModuleId(
                            mergeModuleEntityRelationshipBto.getModuleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "moduleName")) {
                    moduleEntityRelationshipBO.setModuleName(
                            mergeModuleEntityRelationshipBto.getModuleName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "entityCnName")) {
                    moduleEntityRelationshipBO.setEntityCnName(
                            mergeModuleEntityRelationshipBto.getEntityCnName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "entityEnName")) {
                    moduleEntityRelationshipBO.setEntityEnName(
                            mergeModuleEntityRelationshipBto.getEntityEnName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "tableName")) {
                    moduleEntityRelationshipBO.setTableName(
                            mergeModuleEntityRelationshipBto.getTableName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "description")) {
                    moduleEntityRelationshipBO.setDescription(
                            mergeModuleEntityRelationshipBto.getDescription());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        moduleEntityRelationshipBO.convertToModuleEntityRelationship());
                updatedBto.setBto(mergeModuleEntityRelationshipBto);
                updatedBto.setBo(moduleEntityRelationshipBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "moduleId")) {
                    moduleEntityRelationshipBO.setModuleId(
                            mergeModuleEntityRelationshipBto.getModuleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "moduleName")) {
                    moduleEntityRelationshipBO.setModuleName(
                            mergeModuleEntityRelationshipBto.getModuleName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "entityCnName")) {
                    moduleEntityRelationshipBO.setEntityCnName(
                            mergeModuleEntityRelationshipBto.getEntityCnName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "entityEnName")) {
                    moduleEntityRelationshipBO.setEntityEnName(
                            mergeModuleEntityRelationshipBto.getEntityEnName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "tableName")) {
                    moduleEntityRelationshipBO.setTableName(
                            mergeModuleEntityRelationshipBto.getTableName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                        "description")) {
                    moduleEntityRelationshipBO.setDescription(
                            mergeModuleEntityRelationshipBto.getDescription());
                }
            }
        } else {
            moduleEntityRelationshipBO = new ModuleEntityRelationshipBO();
            if (pkExist) {
                moduleEntityRelationshipBO.setId(
                        String.valueOf(this.idGenerator.allocateId("module_entity_relationship")));
            } else {
                moduleEntityRelationshipBO.setId(
                        String.valueOf(this.idGenerator.allocateId("module_entity_relationship")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                    "moduleId")) {
                moduleEntityRelationshipBO.setModuleId(
                        mergeModuleEntityRelationshipBto.getModuleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                    "moduleName")) {
                moduleEntityRelationshipBO.setModuleName(
                        mergeModuleEntityRelationshipBto.getModuleName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                    "entityCnName")) {
                moduleEntityRelationshipBO.setEntityCnName(
                        mergeModuleEntityRelationshipBto.getEntityCnName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                    "entityEnName")) {
                moduleEntityRelationshipBO.setEntityEnName(
                        mergeModuleEntityRelationshipBto.getEntityEnName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                    "tableName")) {
                moduleEntityRelationshipBO.setTableName(
                        mergeModuleEntityRelationshipBto.getTableName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeModuleEntityRelationshipBto, "__$validPropertySet"),
                    "description")) {
                moduleEntityRelationshipBO.setDescription(
                        mergeModuleEntityRelationshipBto.getDescription());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeModuleEntityRelationshipBto);
            addedBto.setBo(moduleEntityRelationshipBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return moduleEntityRelationshipBO;
    }

    /** 保存模块实体关系 */
    @AutoGenerated(locked = true)
    protected MergeModuleEntityRelationshipBoResult mergeModuleEntityRelationshipBase(
            MergeModuleEntityRelationshipBto mergeModuleEntityRelationshipBto) {
        if (mergeModuleEntityRelationshipBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeModuleEntityRelationshipBoResult boResult =
                new MergeModuleEntityRelationshipBoResult();
        ModuleEntityRelationshipBO moduleEntityRelationshipBO =
                createMergeModuleEntityRelationshipOnDuplicateUpdate(
                        boResult, mergeModuleEntityRelationshipBto);
        boResult.setRootBo(moduleEntityRelationshipBO);
        return boResult;
    }

    public static class MergeModuleEntityRelationshipBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ModuleEntityRelationshipBO getRootBo() {
            return (ModuleEntityRelationshipBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeModuleEntityRelationshipBto, ModuleEntityRelationshipBO> getCreatedBto(
                MergeModuleEntityRelationshipBto mergeModuleEntityRelationshipBto) {
            return this.getAddedResult(mergeModuleEntityRelationshipBto);
        }

        @AutoGenerated(locked = true)
        public ModuleEntityRelationship getDeleted_ModuleEntityRelationship() {
            return (ModuleEntityRelationship)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ModuleEntityRelationship.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeModuleEntityRelationshipBto,
                        ModuleEntityRelationship,
                        ModuleEntityRelationshipBO>
                getUpdatedBto(MergeModuleEntityRelationshipBto mergeModuleEntityRelationshipBto) {
            return super.getUpdatedResult(mergeModuleEntityRelationshipBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeModuleEntityRelationshipBto, ModuleEntityRelationshipBO>
                getUnmodifiedBto(
                        MergeModuleEntityRelationshipBto mergeModuleEntityRelationshipBto) {
            return super.getUnmodifiedResult(mergeModuleEntityRelationshipBto);
        }
    }
}
