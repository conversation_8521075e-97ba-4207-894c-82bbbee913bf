package com.pulse.dictionary_basic.entrance.web.controller;

import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_basic.service.mq.consumer.DeleteEntityTagMoConsumer;
import com.pulse.dictionary_basic.service.mq.consumer.DeleteTagMoConsumer;
import com.pulse.tag.manager.mo.DeleteEntityTagMo;
import com.pulse.tag.manager.mo.DeleteTagMo;
import com.vs.code.AutoGenerated;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

@Controller
@AutoGenerated(locked = true, uuid = "302a1877-4c33-3121-81f6-1d598db272dd")
public class DictionaryBasicMessageConsumerMocker {
    @AutoGenerated(locked = true)
    @Resource
    private DeleteEntityTagMoConsumer deleteEntityTagMoConsumer;

    @AutoGenerated(locked = true)
    @Resource
    private DeleteTagMoConsumer deleteTagMoConsumer;

    @AutoGenerated(locked = true)
    @Value("${mock.enabled:false}")
    private Boolean mockEnabled;

    @AutoGenerated(locked = true)
    @RequestMapping("/api/mocker/message/dictionary_basic/delete_entity_tag_mo")
    public Boolean mockDeleteEntityTagMoConsumer(@RequestBody DeleteEntityTagMo deleteEntityTagMo) {
        if (this.mockEnabled) {
            return ReflectUtil.invoke(
                    deleteEntityTagMoConsumer, "handleMessage", deleteEntityTagMo);
        } else {
            throw new RuntimeException("非法请求!");
        }
    }

    @AutoGenerated(locked = true)
    @RequestMapping("/api/mocker/message/dictionary_basic/delete_tag_mo")
    public Boolean mockDeleteTagMoConsumer(@RequestBody DeleteTagMo deleteTagMo) {
        if (this.mockEnabled) {
            return ReflectUtil.invoke(deleteTagMoConsumer, "handleMessage", deleteTagMo);
        } else {
            throw new RuntimeException("非法请求!");
        }
    }
}
