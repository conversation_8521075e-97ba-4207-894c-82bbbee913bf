package com.pulse.dictionary_basic.manager.bo.base;

import com.pulse.dictionary_basic.manager.bo.AttributeValueBO;
import com.pulse.dictionary_basic.persist.dos.AttributeValue;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "attribute_value")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "f610c062-f59f-3b74-8d0c-726aedb01c1d")
public abstract class BaseAttributeValueBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 属性ID */
    @Column(name = "attribute_id")
    @AutoGenerated(locked = true, uuid = "00c204e5-5a2b-4f9c-81a8-25180130482f")
    private String attributeId;

    /** 布尔类型值 */
    @Column(name = "boolean_value")
    @AutoGenerated(locked = true, uuid = "1333e7ef-57b3-46f0-8267-817c9de71053")
    private Boolean booleanValue;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "d9649f88-f09e-5182-92bd-af7ff232b7c2")
    private Date createdAt;

    /** 日期类型值 */
    @Column(name = "date_value")
    @AutoGenerated(locked = true, uuid = "81b1d84c-684e-484d-9cf8-ca74def6013b")
    private Date dateValue;

    /** 日期时间类型值 */
    @Column(name = "datetime_value")
    @AutoGenerated(locked = true, uuid = "17098652-16c8-4697-a2a5-06a930a194ff")
    private Date datetimeValue;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "f09aa567-3407-5ec9-9c9f-799967a32580")
    private Long deletedAt = 0L;

    /** 实体ID */
    @Column(name = "entity_id")
    @AutoGenerated(locked = true, uuid = "b936dab2-cc55-49b6-a493-acc7db5128e2")
    private String entityId;

    /** 关联实体类型 */
    @Column(name = "entity_type")
    @AutoGenerated(locked = true, uuid = "26b39f5d-a2df-40b2-9d5d-1f0ea2238576")
    private String entityType;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "f2b6d094-87db-4641-8ad0-f9f5f257fe62")
    @Id
    private String id;

    /** 复杂结构值 */
    @Column(name = "json_value")
    @AutoGenerated(locked = true, uuid = "21caf24f-1f8c-43eb-96fd-6b86805f21e9")
    private String jsonValue;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "4085bd60-8dd5-4de0-9bd0-8a16f0450108")
    @Version
    private Long lockVersion;

    /** 数字类型值 */
    @Column(name = "number_value")
    @AutoGenerated(locked = true, uuid = "40ae799a-52ff-4b67-b4b9-bceed17bb877")
    private Long numberValue;

    /** 字符串类型值 */
    @Column(name = "string_value")
    @AutoGenerated(locked = true, uuid = "ef2845a3-063c-4ce6-9d17-1c03a3acb0d4")
    private String stringValue;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "5c81559d-dd8f-5a52-94b0-29a5968139fe")
    private Date updatedAt;

    /** 值 通用的值字段，和类型无关，统一为字符串形式 */
    @Column(name = "value")
    @AutoGenerated(locked = true, uuid = "3a8c4fa9-d460-448c-8238-f93b0bf0557d")
    private String value;

    /** 版本 */
    @Column(name = "version")
    @AutoGenerated(locked = true, uuid = "9b358428-5aba-46a5-b39c-21a9c080056d")
    private String version;

    @AutoGenerated(locked = true)
    public AttributeValue convertToAttributeValue() {
        AttributeValue entity = new AttributeValue();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "entityType",
                "entityId",
                "attributeId",
                "value",
                "stringValue",
                "numberValue",
                "dateValue",
                "datetimeValue",
                "booleanValue",
                "jsonValue",
                "version",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public String getAttributeId() {
        return this.attributeId;
    }

    @AutoGenerated(locked = true)
    public Boolean getBooleanValue() {
        return this.booleanValue;
    }

    @AutoGenerated(locked = true)
    public static AttributeValueBO getByEntityTypeAndEntityIdAndAttributeId(
            String entityType, String entityId, String attributeId) {
        Session session = TransactionalSessionFactory.getSession();
        AttributeValueBO attributeValue =
                (AttributeValueBO)
                        session.createQuery(
                                        "from AttributeValueBO where entityType =: entityType  and"
                                            + " entityId =: entityId  and attributeId =:"
                                            + " attributeId ")
                                .setParameter("entityType", entityType)
                                .setParameter("entityId", entityId)
                                .setParameter("attributeId", attributeId)
                                .uniqueResult();
        return attributeValue;
    }

    @AutoGenerated(locked = true)
    public static AttributeValueBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        AttributeValueBO attributeValue =
                (AttributeValueBO)
                        session.createQuery("from AttributeValueBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return attributeValue;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Date getDateValue() {
        return this.dateValue;
    }

    @AutoGenerated(locked = true)
    public Date getDatetimeValue() {
        return this.datetimeValue;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getEntityId() {
        return this.entityId;
    }

    @AutoGenerated(locked = true)
    public String getEntityType() {
        return this.entityType;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getJsonValue() {
        return this.jsonValue;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public Long getNumberValue() {
        return this.numberValue;
    }

    @AutoGenerated(locked = true)
    public String getStringValue() {
        return this.stringValue;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getValue() {
        return this.value;
    }

    @AutoGenerated(locked = true)
    public String getVersion() {
        return this.version;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setAttributeId(String attributeId) {
        this.attributeId = attributeId;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setBooleanValue(Boolean booleanValue) {
        this.booleanValue = booleanValue;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setDateValue(Date dateValue) {
        this.dateValue = dateValue;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setDatetimeValue(Date datetimeValue) {
        this.datetimeValue = datetimeValue;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setEntityId(String entityId) {
        this.entityId = entityId;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setEntityType(String entityType) {
        this.entityType = entityType;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setId(String id) {
        this.id = id;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setJsonValue(String jsonValue) {
        this.jsonValue = jsonValue;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setNumberValue(Long numberValue) {
        this.numberValue = numberValue;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setStringValue(String stringValue) {
        this.stringValue = stringValue;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setValue(String value) {
        this.value = value;
        return (AttributeValueBO) this;
    }

    @AutoGenerated(locked = true)
    public AttributeValueBO setVersion(String version) {
        this.version = version;
        return (AttributeValueBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
