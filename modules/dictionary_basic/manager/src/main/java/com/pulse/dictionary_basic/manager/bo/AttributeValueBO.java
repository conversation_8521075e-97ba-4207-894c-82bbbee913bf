package com.pulse.dictionary_basic.manager.bo;

import com.pulse.dictionary_basic.manager.bo.base.BaseAttributeValueBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE attribute_value  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ? and lock_version = ?")
@Table(name = "attribute_value")
@Entity
@AutoGenerated(locked = false, uuid = "34bfb495-9400-4a86-8f82-ff7de7d03eb2|BO|DEFINITION")
public class AttributeValueBO extends BaseAttributeValueBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "34bfb495-9400-4a86-8f82-ff7de7d03eb2|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
