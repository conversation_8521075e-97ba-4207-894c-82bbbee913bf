package com.pulse.appointment_schedule.service.bto;

import com.pulse.appointment_schedule.common.enums.AppointmentStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> SchedulingSlotDetail
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "38eef5cd-051e-41c1-a1c3-7819fc32f143|BTO|DEFINITION")
public class UpdateSchedulingSlotDetailStatusBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 号源状态 */
    @AutoGenerated(locked = true, uuid = "263e2ff9-6fab-4465-869d-973c12d5cc85")
    private AppointmentStatusEnum appointmentStatus;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "badcc8f8-3c62-4ab9-bd51-23803e462e0e")
    private String id;

    @AutoGenerated(locked = true)
    public void setAppointmentStatus(AppointmentStatusEnum appointmentStatus) {
        this.__$validPropertySet.add("appointmentStatus");
        this.appointmentStatus = appointmentStatus;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }
}
