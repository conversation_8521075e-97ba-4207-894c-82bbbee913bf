package com.pulse.appointment_booking.manager.facade.visit.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.visit.manager.dto.OutpVisitEncounterBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "7f5aa339-4bbd-3e86-adaa-48dc194e8d6e")
public class OutpVisitEncounterBaseDtoServiceInAppointmentBookingBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "03c1f88d-3ab3-4d73-9b9b-2d06efd18446|RPC|BASE_ADAPTER")
    public List<OutpVisitEncounterBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/visit/03c1f88d-3ab3-4d73-9b9b-2d06efd18446/OutpVisitEncounterBaseDtoService-getByIds",
                        "com.pulse.visit.service.OutpVisitEncounterBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
                        "20a9027a-a071-49bb-93db-29c8446b432c"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "b1cfd09f-0bb4-4e45-9c9f-0ae0323e0796|RPC|BASE_ADAPTER")
    public List<OutpVisitEncounterBaseDto> getByOutpVisitIds(List<String> outpVisitId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("outp_visit_id", outpVisitId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("outp_visit_id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/visit/b1cfd09f-0bb4-4e45-9c9f-0ae0323e0796/OutpVisitEncounterBaseDtoService-getByOutpVisitIds",
                        "com.pulse.visit.service.OutpVisitEncounterBaseDtoService",
                        "getByOutpVisitIds",
                        paramMap,
                        paramTypeMap,
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
                        "20a9027a-a071-49bb-93db-29c8446b432c"),
                new TypeReference<>() {});
    }
}
