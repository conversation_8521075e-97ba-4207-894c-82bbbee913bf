package com.pulse.appointment_schedule.service.bto;

import com.pulse.appointment_schedule.common.enums.AppointmentStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> SchedulingSlotDetail
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "ddfd08f5-c422-470e-ae5c-3dd6a8e68e54|BTO|DEFINITION")
public class UpdateScheduleSlotDetailBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 号源状态 */
    @AutoGenerated(locked = true, uuid = "4a1e100b-e72b-46d7-919f-4289f1b06793")
    private AppointmentStatusEnum appointmentStatus;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9f099337-5b30-46b9-998a-24c311b6b47a")
    private String id;

    @AutoGenerated(locked = true)
    public void setAppointmentStatus(AppointmentStatusEnum appointmentStatus) {
        this.__$validPropertySet.add("appointmentStatus");
        this.appointmentStatus = appointmentStatus;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }
}
