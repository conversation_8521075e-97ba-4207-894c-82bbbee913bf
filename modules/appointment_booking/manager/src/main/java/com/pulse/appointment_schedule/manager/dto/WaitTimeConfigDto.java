package com.pulse.appointment_schedule.manager.dto;

import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "6149d7d3-b5e8-4ae1-b5f2-6c6c45c9effa|DTO|DEFINITION")
public class WaitTimeConfigDto {
    /** 排班ID */
    @AutoGenerated(locked = true, uuid = "c2b0585f-d044-4469-9f57-a27e14d3ff6f")
    private String appointmentScheduleId;

    /** 结束编号 */
    @AutoGenerated(locked = true, uuid = "da61d10b-348d-416c-8220-fa60a9c2b7d7")
    private Long endNumber;

    /** 结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "91f36606-65a6-4567-bc4b-83d68e1ba22a")
    private TimeEo endTime;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "708285a5-3396-461b-b66f-2c645a56f948")
    private String id;

    /** 排班计划ID */
    @AutoGenerated(locked = true, uuid = "8c296852-eabd-4a0b-b201-14dd0a195973")
    private String schedlePlanId;

    /** 开始编号 */
    @AutoGenerated(locked = true, uuid = "f73855c1-f207-4fab-ba13-f6fe25f259b5")
    private Long startNumber;

    /** 开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "316e75e3-9bdd-4e34-bedd-b47f7b792806")
    private TimeEo startTime;
}
