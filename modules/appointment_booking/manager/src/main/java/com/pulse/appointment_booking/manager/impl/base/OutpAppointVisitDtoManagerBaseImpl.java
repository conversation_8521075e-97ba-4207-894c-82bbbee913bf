package com.pulse.appointment_booking.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpAppointBaseDtoManager;
import com.pulse.appointment_booking.manager.OutpAppointVisitDtoManager;
import com.pulse.appointment_booking.manager.converter.OutpAppointBaseDtoConverter;
import com.pulse.appointment_booking.manager.converter.OutpAppointVisitDtoConverter;
import com.pulse.appointment_booking.manager.dto.OutpAppointBaseDto;
import com.pulse.appointment_booking.manager.dto.OutpAppointVisitDto;
import com.pulse.appointment_booking.manager.facade.appointment_schedule.AppointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.persist.dos.OutpAppoint;
import com.pulse.appointment_booking.persist.mapper.OutpAppointDao;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "4426342b-7f98-49b0-835c-cfbfac8f196f|DTO|BASE_MANAGER_IMPL")
public abstract class OutpAppointVisitDtoManagerBaseImpl implements OutpAppointVisitDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter
            appointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointBaseDtoConverter outpAppointBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointBaseDtoManager outpAppointBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointDao outpAppointDao;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointVisitDtoConverter outpAppointVisitDtoConverter;

    @AutoGenerated(locked = true, uuid = "0fb250a0-4bd9-3be5-9e60-d1d2e036402c")
    public List<OutpAppointVisitDto> doConvertFromOutpAppointToOutpAppointVisitDto(
            List<OutpAppoint> outpAppointList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpAppointList)) {
            return Collections.emptyList();
        }

        Map<String, String> appointmentScheduleIdMap =
                outpAppointList.stream()
                        .filter(i -> i.getAppointmentScheduleId() != null)
                        .collect(
                                Collectors.toMap(
                                        OutpAppoint::getId, OutpAppoint::getAppointmentScheduleId));
        List<AppointmentScheduleBaseDto> appointmentScheduleIdAppointmentScheduleBaseDtoList =
                appointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(appointmentScheduleIdMap.values())));
        Map<String, AppointmentScheduleBaseDto>
                appointmentScheduleIdAppointmentScheduleBaseDtoMapRaw =
                        appointmentScheduleIdAppointmentScheduleBaseDtoList.stream()
                                .collect(
                                        Collectors.toMap(
                                                AppointmentScheduleBaseDto::getId, i -> i));
        Map<String, AppointmentScheduleBaseDto> appointmentScheduleIdAppointmentScheduleBaseDtoMap =
                appointmentScheduleIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        appointmentScheduleIdAppointmentScheduleBaseDtoMapRaw.get(
                                                        i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                appointmentScheduleIdAppointmentScheduleBaseDtoMapRaw
                                                        .get(i.getValue())));

        List<OutpAppointBaseDto> baseDtoList =
                outpAppointBaseDtoConverter.convertFromOutpAppointToOutpAppointBaseDto(
                        outpAppointList);
        Map<String, OutpAppointVisitDto> dtoMap =
                outpAppointVisitDtoConverter
                        .convertFromOutpAppointBaseDtoToOutpAppointVisitDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        OutpAppointVisitDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<OutpAppointVisitDto> outpAppointVisitDtoList = new ArrayList<>();
        for (OutpAppoint i : outpAppointList) {
            OutpAppointVisitDto outpAppointVisitDto = dtoMap.get(i.getId());
            if (outpAppointVisitDto == null) {
                continue;
            }

            if (null != i.getAppointmentScheduleId()) {
                outpAppointVisitDto.setAppointmentSchedule(
                        appointmentScheduleIdAppointmentScheduleBaseDtoMap.getOrDefault(
                                i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            outpAppointVisitDtoList.add(outpAppointVisitDto);
        }
        return outpAppointVisitDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "26019703-0c77-3dc7-b1e3-c48488f212d1")
    @Override
    public List<OutpAppointVisitDto> getByClinicRegisterTypeId(String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointVisitDto> outpAppointVisitDtoList =
                getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        return outpAppointVisitDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "40a678f7-b90d-3ec3-acf2-a76a4e0123c9")
    @Override
    public List<OutpAppointVisitDto> getByDepartmentIds(List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(departmentId)) {
            return Collections.emptyList();
        }

        List<OutpAppoint> outpAppointList = outpAppointDao.getByDepartmentIds(departmentId);
        if (CollectionUtil.isEmpty(outpAppointList)) {
            return Collections.emptyList();
        }

        return doConvertFromOutpAppointToOutpAppointVisitDto(outpAppointList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "43233a61-9087-3fb1-90f8-63e9301f44d6")
    @Override
    public OutpAppointVisitDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointVisitDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        OutpAppointVisitDto outpAppointVisitDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return outpAppointVisitDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4c0be4e2-a2e0-32f0-b641-8e8957988fbf")
    @Override
    public List<OutpAppointVisitDto> getByAppointmentScheduleId(String appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointVisitDto> outpAppointVisitDtoList =
                getByAppointmentScheduleIds(Arrays.asList(appointmentScheduleId));
        return outpAppointVisitDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4e9664a9-64b4-3454-9cc9-b522ea1909fd")
    @Override
    public List<OutpAppointVisitDto> getByDepartmentId(String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointVisitDto> outpAppointVisitDtoList =
                getByDepartmentIds(Arrays.asList(departmentId));
        return outpAppointVisitDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5692f3c0-e4fb-376f-a321-3c659c11c1a0")
    @Override
    public List<OutpAppointVisitDto> getByPatientId(String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointVisitDto> outpAppointVisitDtoList =
                getByPatientIds(Arrays.asList(patientId));
        return outpAppointVisitDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9bcc8717-9be0-309a-9787-1e14706fd427")
    @Override
    public List<OutpAppointVisitDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicRegisterTypeId)) {
            return Collections.emptyList();
        }

        List<OutpAppoint> outpAppointList =
                outpAppointDao.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        if (CollectionUtil.isEmpty(outpAppointList)) {
            return Collections.emptyList();
        }

        return doConvertFromOutpAppointToOutpAppointVisitDto(outpAppointList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a32de061-daa6-305c-9b87-855638bf3988")
    @Override
    public List<OutpAppointVisitDto> getByAppointmentScheduleIds(
            List<String> appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(appointmentScheduleId)) {
            return Collections.emptyList();
        }

        List<OutpAppoint> outpAppointList =
                outpAppointDao.getByAppointmentScheduleIds(appointmentScheduleId);
        if (CollectionUtil.isEmpty(outpAppointList)) {
            return Collections.emptyList();
        }

        return doConvertFromOutpAppointToOutpAppointVisitDto(outpAppointList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "aab27a61-80be-3652-8a5b-a53e057825a7")
    @Override
    public List<OutpAppointVisitDto> getByPatientIds(List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(patientId)) {
            return Collections.emptyList();
        }

        List<OutpAppoint> outpAppointList = outpAppointDao.getByPatientIds(patientId);
        if (CollectionUtil.isEmpty(outpAppointList)) {
            return Collections.emptyList();
        }

        return doConvertFromOutpAppointToOutpAppointVisitDto(outpAppointList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "fcb54535-b861-391f-91aa-21d1d26b669b")
    @Override
    public List<OutpAppointVisitDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<OutpAppoint> outpAppointList = outpAppointDao.getByIds(id);
        if (CollectionUtil.isEmpty(outpAppointList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, OutpAppoint> outpAppointMap =
                outpAppointList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        outpAppointList =
                id.stream()
                        .map(i -> outpAppointMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromOutpAppointToOutpAppointVisitDto(outpAppointList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
