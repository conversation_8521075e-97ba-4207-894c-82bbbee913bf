package com.pulse.patient_information.manager.dto;

import com.pulse.patient_information.common.enums.PatientStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "7dd9f015-3498-45e7-a86b-03ebb312e5c0|DTO|DEFINITION")
public class PatientVisitDto {
    /** 患者头像 */
    @AutoGenerated(locked = true, uuid = "6578f16a-b6af-4443-88d1-4b05c4afdbcc")
    private String avatar;

    /** 出生地 */
    @AutoGenerated(locked = true, uuid = "e5ce484c-bb08-4167-aca8-833263caf6a5")
    private String birthAddress;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "6e3c82a8-ea5d-4d4a-8604-dc6d2aec85a6")
    private Date birthday;

    /** 献血证标识 */
    @AutoGenerated(locked = true, uuid = "dc60805c-59a7-473b-8de9-4fbcba2d60c6")
    private Boolean bloodCardFlag;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "7db1c250-1337-4870-9698-39ed6980d0d7")
    private String cellphone;

    /** 子女统筹标志 */
    @AutoGenerated(locked = true, uuid = "381adfc9-6201-4748-9cd2-acefca91f2ae")
    private Boolean childrenCoordinatedFlag;

    /** 子女统筹有效期 */
    @AutoGenerated(locked = true, uuid = "d1cc2e3f-1ef3-45c0-8c73-e421504d5dbe")
    private Date childrenCoordinatedValidDate;

    /** 商保标志 */
    @AutoGenerated(locked = true, uuid = "b7f82f00-9b92-42ad-92ba-bbf8b534ec7a")
    private Boolean commercialInsuranceFlag;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "5fc2fc57-c59f-4a05-aecd-2dd8f7b03fc5")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "bb8f6e79-925b-4c55-845b-fb28a335cc1e")
    private String createdBy;

    /** 默认费别 */
    @AutoGenerated(locked = true, uuid = "92ba2a88-b6df-47f8-85b7-44cc90460362")
    private String defaultChargeType;

    /** 残疾人证标识 */
    @AutoGenerated(locked = true, uuid = "276bda0e-ef2a-41a4-95bd-0a5cada3ee98")
    private Boolean disabilityFlag;

    /** 用于显示的id */
    @AutoGenerated(locked = true, uuid = "1099d01d-298d-4042-be13-f8e7e3dca9dd")
    private String displayId;

    /** 生理性别 */
    @AutoGenerated(locked = true, uuid = "368d525d-6080-4675-ba35-a697ee9a578c")
    private String gender;

    /** 绿色通道标志 */
    @AutoGenerated(locked = true, uuid = "c5f08f87-8af9-4f62-8696-e44e73d240fe")
    private Boolean greenChannelFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "7b3cc9d0-3ca5-40da-a492-f14c40219da0")
    private String id;

    /** 主证件号 */
    @AutoGenerated(locked = true, uuid = "60606c6e-9c41-4b80-ba3f-a618887dd3f2")
    private String idNumber;

    /** 主证件类型 */
    @AutoGenerated(locked = true, uuid = "3c3ce93a-69bb-4a93-8f63-2de1c01cd426")
    private String idType;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "b96ed07a-5a23-4a86-850b-16737d2c03c3")
    private String identityCode;

    /** 医保卡号 */
    @AutoGenerated(locked = true, uuid = "1c8fd049-da47-4117-b3bd-af152cc4c12c")
    private String insuranceCardNumber;

    /** 医保账号 */
    @AutoGenerated(locked = true, uuid = "********-e702-4a19-92a6-d5079834e041")
    private String insuranceNumber;

    /** 医保类型ID */
    @AutoGenerated(locked = true, uuid = "405a724e-7bbc-4276-b24f-fef19bfc0840")
    private String insuranceTypeId;

    /** 主账标志 */
    @AutoGenerated(locked = true, uuid = "97688f76-5cf9-4858-b944-4e45edaee4a9")
    private Boolean mainAccountFlag;

    /** 劳模标志 */
    @AutoGenerated(locked = true, uuid = "8c35e57e-9be4-4844-b9bf-a6954f80d893")
    private Boolean modelWorkerFlag;

    /** 姓名 */
    @AutoGenerated(locked = true, uuid = "385a43aa-9cba-4b03-90d2-28146859487e")
    private String name;

    /** 姓名输入码 */
    @AutoGenerated(locked = true, uuid = "f01d39e3-ed5a-4bbb-8986-d87587874ab7")
    private String nameInputCode;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f2f8d730-f605-4649-b580-d95b07ce5573")
    private List<PatientIdentificationBaseDto> patientIdentificationList;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "1927e103-7377-4276-85aa-276085c44299")
    private PatientProfileBaseDto patientProfile;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "1db97099-605e-4824-bac3-32b25f334283")
    private String phoneNumber;

    /** 公费级别 */
    @AutoGenerated(locked = true, uuid = "0668b943-43b3-48ad-a3fb-81eb9288de94")
    private Long publicFundedLevel;

    /** 公费单位 */
    @AutoGenerated(locked = true, uuid = "41237aab-e594-4032-93ce-32f9352dc290")
    private String publicFundedUnit;

    /** 公费证号 */
    @AutoGenerated(locked = true, uuid = "ff32712e-4794-4a08-bbaf-434c364933fd")
    private String publicMedicalExpensesCertificateNumber;

    /** 状态码 */
    @AutoGenerated(locked = true, uuid = "47622a66-f0a7-48ac-82de-491e3710c7b0")
    private PatientStatusEnum status;

    /** 译名 */
    @AutoGenerated(locked = true, uuid = "de3389be-86de-45af-aadd-d2199f5d0c1b")
    private String translateName;

    /** 无名患者标识 */
    @AutoGenerated(locked = true, uuid = "658526b4-1309-4170-ad17-89db8b15e667")
    private Boolean unknownFlag;

    /** 无名患者类型 */
    @AutoGenerated(locked = true, uuid = "e8cf9071-452e-468d-8bc3-982f683b9b68")
    private String unknownType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "ae6263d5-6429-4699-b812-6334e070b86e")
    private Date updatedAt;

    /** 操作员 */
    @AutoGenerated(locked = true, uuid = "7ec35863-fb3c-474d-9ec3-81aac2c11641")
    private String updatedBy;

    /** 退伍军人标志 */
    @AutoGenerated(locked = true, uuid = "44f9073a-85aa-421b-93f6-7d8dce10be95")
    private Boolean veteranFlag;

    /** VIP标识 */
    @AutoGenerated(locked = true, uuid = "c4dd5aac-d298-4d33-b6bb-cb5999da3a02")
    private Boolean vipFlag;
}
