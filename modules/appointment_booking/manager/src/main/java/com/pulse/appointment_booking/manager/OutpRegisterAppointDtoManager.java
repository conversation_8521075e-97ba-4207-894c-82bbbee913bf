package com.pulse.appointment_booking.manager;

import com.pulse.appointment_booking.manager.dto.OutpRegisterAppointDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "c138b18c-2894-4844-8d80-c0d7d5b77887|DTO|MANAGER")
public interface OutpRegisterAppointDtoManager {

    @AutoGenerated(locked = true, uuid = "276ea94c-8b4f-36dd-9b2b-a4949470087d")
    List<OutpRegisterAppointDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "3a17103e-4caa-3007-b72f-221339a274a1")
    OutpRegisterAppointDto getById(String id);

    @AutoGenerated(locked = true, uuid = "3d5bb6a5-aeb3-3acc-b82c-45e44f51dac0")
    List<OutpRegisterAppointDto> getByOutpAppointId(String outpAppointId);

    @AutoGenerated(locked = true, uuid = "a3a2ddc0-4253-38dc-ac20-5a4dd93a395a")
    List<OutpRegisterAppointDto> getByOutpAppointIds(List<String> outpAppointId);
}
