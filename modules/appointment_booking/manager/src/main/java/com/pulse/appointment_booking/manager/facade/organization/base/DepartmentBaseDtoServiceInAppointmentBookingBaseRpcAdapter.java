package com.pulse.appointment_booking.manager.facade.organization.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "c13f785c-fe1e-34d2-a437-15a37f6c3384")
public class DepartmentBaseDtoServiceInAppointmentBookingBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "21d5fadb-14fe-4f56-ad8a-1dce46c7cd07|RPC|BASE_ADAPTER")
    public List<DepartmentBaseDto> getByOrganizationIds(List<String> organizationId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("organization_id", organizationId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("organization_id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/organization/21d5fadb-14fe-4f56-ad8a-1dce46c7cd07/DepartmentBaseDtoService-getByOrganizationIds",
                        "com.pulse.organization.service.DepartmentBaseDtoService",
                        "getByOrganizationIds",
                        paramMap,
                        paramTypeMap,
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
                        "a3b95408-2257-4bfa-aafe-59cc5547e63c"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "63d8aa47-a13b-4710-ba23-240868328fa3|RPC|BASE_ADAPTER")
    public DepartmentBaseDto getByOrganizationId(String organizationId) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("organization_id", organizationId);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("organization_id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/organization/63d8aa47-a13b-4710-ba23-240868328fa3/DepartmentBaseDtoService-getByOrganizationId",
                        "com.pulse.organization.service.DepartmentBaseDtoService",
                        "getByOrganizationId",
                        paramMap,
                        paramTypeMap,
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
                        "a3b95408-2257-4bfa-aafe-59cc5547e63c"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "dcc9406c-cce0-4a6b-83a2-1eb27db1be39|RPC|BASE_ADAPTER")
    public List<DepartmentBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/organization/dcc9406c-cce0-4a6b-83a2-1eb27db1be39/DepartmentBaseDtoService-getByIds",
                        "com.pulse.organization.service.DepartmentBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
                        "a3b95408-2257-4bfa-aafe-59cc5547e63c"),
                new TypeReference<>() {});
    }
}
