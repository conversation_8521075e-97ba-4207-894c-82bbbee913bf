package com.pulse.appointment_booking.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.persist.qto.SearchExpertDoctorQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "797ca5c0-e668-4674-b962-937e3ab99d3e|QTO|DAO")
public class SearchExpertDoctorQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询专家排班 */
    @AutoGenerated(locked = false, uuid = "797ca5c0-e668-4674-b962-937e3ab99d3e-count")
    public Integer count(SearchExpertDoctorQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(outp_appoint.id) FROM outp_appoint LEFT JOIN appointment_schedule"
                    + " \"appointmentSchedule\" on outp_appoint.appointment_schedule_id ="
                    + " \"appointmentSchedule\".id WHERE \"appointmentSchedule\".id ="
                    + " #appointmentScheduleIdIs AND outp_appoint.clinic_register_type_id in"
                    + " #clinicRegisterTypeIdIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getAppointmentScheduleIdIs() == null) {
            conditionToRemove.add("#appointmentScheduleIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())) {
            conditionToRemove.add("#clinicRegisterTypeIdIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#appointmentScheduleIdIs", "?")
                        .replace(
                                "#clinicRegisterTypeIdIn",
                                CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getClinicRegisterTypeIdIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#appointmentScheduleIdIs")) {
                sqlParams.add(qto.getAppointmentScheduleIdIs());
            } else if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIn")) {
                sqlParams.addAll(qto.getClinicRegisterTypeIdIn());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询专家排班 */
    @AutoGenerated(locked = false, uuid = "797ca5c0-e668-4674-b962-937e3ab99d3e-query-all")
    public List<String> query(SearchExpertDoctorQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT outp_appoint.id FROM outp_appoint LEFT JOIN appointment_schedule"
                    + " \"appointmentSchedule\" on outp_appoint.appointment_schedule_id ="
                    + " \"appointmentSchedule\".id WHERE \"appointmentSchedule\".id ="
                    + " #appointmentScheduleIdIs AND outp_appoint.clinic_register_type_id in"
                    + " #clinicRegisterTypeIdIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getAppointmentScheduleIdIs() == null) {
            conditionToRemove.add("#appointmentScheduleIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())) {
            conditionToRemove.add("#clinicRegisterTypeIdIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#appointmentScheduleIdIs", "?")
                        .replace(
                                "#clinicRegisterTypeIdIn",
                                CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getClinicRegisterTypeIdIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#appointmentScheduleIdIs")) {
                sqlParams.add(qto.getAppointmentScheduleIdIs());
            } else if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIn")) {
                sqlParams.addAll(qto.getClinicRegisterTypeIdIn());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  outp_appoint.id asc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 500 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
