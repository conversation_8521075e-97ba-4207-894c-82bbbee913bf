package com.pulse.appointment_booking.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.entrance.web.converter.AppointmentScheduleExportVoConverter;
import com.pulse.appointment_booking.entrance.web.converter.BookDepartmentVoConverter;
import com.pulse.appointment_booking.entrance.web.converter.BookOrganizationDepartmentVoConverter;
import com.pulse.appointment_booking.entrance.web.converter.BookOutpRegisterVisitVoConverter;
import com.pulse.appointment_booking.entrance.web.converter.BookOutpVisitEncounterBaseVoConverter;
import com.pulse.appointment_booking.entrance.web.converter.BookOutpVisitEncounterVoConverter;
import com.pulse.appointment_booking.entrance.web.converter.OutpAppointAllVoConverter;
import com.pulse.appointment_booking.entrance.web.query.assembler.OutpAppointAllVoDataAssembler.OutpAppointAllVoDataHolder;
import com.pulse.appointment_booking.entrance.web.vo.AppointmentScheduleExportVo;
import com.pulse.appointment_booking.entrance.web.vo.BookDepartmentVo;
import com.pulse.appointment_booking.entrance.web.vo.BookOrganizationDepartmentVo;
import com.pulse.appointment_booking.entrance.web.vo.BookOutpRegisterVisitVo;
import com.pulse.appointment_booking.entrance.web.vo.BookOutpVisitEncounterBaseVo;
import com.pulse.appointment_booking.entrance.web.vo.BookOutpVisitEncounterVo;
import com.pulse.appointment_booking.manager.converter.OutpRegisterVisitDtoConverter;
import com.pulse.appointment_booking.manager.dto.OutpAppointAllDto;
import com.pulse.appointment_booking.manager.dto.OutpAppointBaseDto;
import com.pulse.appointment_booking.manager.dto.OutpRegisterBaseDto;
import com.pulse.appointment_booking.manager.dto.OutpRegisterVisitDto;
import com.pulse.appointment_booking.manager.facade.appointment_schedule.AppointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.organization.DepartmentBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.organization.DepartmentDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.organization.OrganizationBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.organization.OrganizationDepartmentDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.visit.OutpVisitBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.visit.OutpVisitEncounterBaseDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.manager.facade.visit.OutpVisitEncounterDtoServiceInAppointmentBookingRpcAdapter;
import com.pulse.appointment_booking.service.OutpAppointBaseDtoService;
import com.pulse.appointment_booking.service.OutpRegisterBaseDtoService;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.pulse.organization.manager.dto.DepartmentBaseDto;
import com.pulse.organization.manager.dto.DepartmentDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.visit.manager.dto.OutpVisitBaseDto;
import com.pulse.visit.manager.dto.OutpVisitEncounterBaseDto;
import com.pulse.visit.manager.dto.OutpVisitEncounterDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装OutpAppointAllVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "77c3a3ee-4e4d-36ab-b1c1-2c790fda0810")
public class OutpAppointAllVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter
            appointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleExportVoConverter appointmentScheduleExportVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private BookDepartmentVoConverter bookDepartmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private BookOrganizationDepartmentVoConverter bookOrganizationDepartmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private BookOutpRegisterVisitVoConverter bookOutpRegisterVisitVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private BookOutpVisitEncounterBaseVoConverter bookOutpVisitEncounterBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private BookOutpVisitEncounterVoConverter bookOutpVisitEncounterVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentBaseDtoServiceInAppointmentBookingRpcAdapter
            departmentBaseDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DepartmentDtoServiceInAppointmentBookingRpcAdapter
            departmentDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoServiceInAppointmentBookingRpcAdapter
            organizationBaseDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentDtoServiceInAppointmentBookingRpcAdapter
            organizationDepartmentDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OutpAppointAllVoConverter outpAppointAllVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OutpAppointAllVoDataCollector outpAppointAllVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private OutpAppointBaseDtoService outpAppointBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterBaseDtoService outpRegisterBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OutpRegisterVisitDtoConverter outpRegisterVisitDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OutpVisitBaseDtoServiceInAppointmentBookingRpcAdapter
            outpVisitBaseDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OutpVisitEncounterBaseDtoServiceInAppointmentBookingRpcAdapter
            outpVisitEncounterBaseDtoServiceInAppointmentBookingRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OutpVisitEncounterDtoServiceInAppointmentBookingRpcAdapter
            outpVisitEncounterDtoServiceInAppointmentBookingRpcAdapter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "55329e1e-a062-3ebd-934e-62c42251786b")
    private void fillDataWhenNecessary(OutpAppointAllVoDataHolder dataHolder) {
        List<OutpAppointBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.outpRegisterVisitList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OutpAppointBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OutpRegisterBaseDto> baseDtoList =
                    outpRegisterBaseDtoService.getByOutpAppointIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OutpRegisterBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<OutpRegisterBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(OutpRegisterBaseDto::getOutpAppointId));
            Map<String, OutpRegisterVisitDto> outpRegisterVisitDtoMap =
                    outpRegisterVisitDtoConverter
                            .convertFromOutpRegisterBaseDtoToOutpRegisterVisitDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            OutpRegisterVisitDto::getId, Function.identity()));
            Map<OutpRegisterVisitDto, BookOutpRegisterVisitVo> dtoVoMap =
                    bookOutpRegisterVisitVoConverter.convertToBookOutpRegisterVisitVoMap(
                            new ArrayList<>(outpRegisterVisitDtoMap.values()));
            Map<OutpRegisterBaseDto, BookOutpRegisterVisitVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> outpRegisterVisitDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            outpRegisterVisitDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.outpRegisterVisitList =
                    rootDtoList.stream()
                            .map(OutpAppointBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.appointmentSchedule == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OutpAppointBaseDto::getAppointmentScheduleId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<AppointmentScheduleBaseDto> baseDtoList =
                    appointmentScheduleBaseDtoServiceInAppointmentBookingRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(AppointmentScheduleBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, AppointmentScheduleBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            AppointmentScheduleBaseDto::getId,
                                            Function.identity()));
            Map<AppointmentScheduleBaseDto, AppointmentScheduleExportVo> dtoVoMap =
                    appointmentScheduleExportVoConverter.convertToAppointmentScheduleExportVoMap(
                            baseDtoList);
            Map<AppointmentScheduleBaseDto, AppointmentScheduleExportVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.appointmentSchedule =
                    rootDtoList.stream()
                            .map(OutpAppointBaseDto::getAppointmentScheduleId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.department == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(OutpAppointBaseDto::getDepartmentId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoServiceInAppointmentBookingRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<String, OrganizationDepartmentDto> organizationDepartmentDtoMap =
                    organizationDepartmentDtoServiceInAppointmentBookingRpcAdapter
                            .getByIds(
                                    baseDtoList.stream()
                                            .map(OrganizationBaseDto::getId)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationDepartmentDto::getId, Function.identity()));
            Map<OrganizationDepartmentDto, BookOrganizationDepartmentVo> dtoVoMap =
                    bookOrganizationDepartmentVoConverter.convertToBookOrganizationDepartmentVoMap(
                            new ArrayList<>(organizationDepartmentDtoMap.values()));
            Map<OrganizationBaseDto, BookOrganizationDepartmentVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            organizationDepartmentDtoMap.containsKey(
                                                    baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            organizationDepartmentDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.department =
                    rootDtoList.stream()
                            .map(OutpAppointBaseDto::getDepartmentId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.outpRegisterVisitList2OutpVisitEncounterList == null) {
            Set<String> ids =
                    dataHolder.outpRegisterVisitList.keySet().stream()
                            .map(OutpRegisterBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OutpVisitBaseDto> baseDtoList =
                    outpVisitBaseDtoServiceInAppointmentBookingRpcAdapter
                            .getByOutpRegisterIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(OutpVisitBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<OutpVisitBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(OutpVisitBaseDto::getOutpRegisterId));
            Map<String, OutpVisitEncounterDto> outpVisitEncounterDtoMap =
                    outpVisitEncounterDtoServiceInAppointmentBookingRpcAdapter
                            .getByOutpRegisterIds(
                                    baseDtoList.stream()
                                            .map(OutpVisitBaseDto::getOutpRegisterId)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            OutpVisitEncounterDto::getId, Function.identity()));
            Map<OutpVisitEncounterDto, BookOutpVisitEncounterVo> dtoVoMap =
                    bookOutpVisitEncounterVoConverter.convertToBookOutpVisitEncounterVoMap(
                            new ArrayList<>(outpVisitEncounterDtoMap.values()));
            Map<OutpVisitBaseDto, BookOutpVisitEncounterVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            outpVisitEncounterDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            outpVisitEncounterDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.outpRegisterVisitList2OutpVisitEncounterList =
                    dataHolder.outpRegisterVisitList.keySet().stream()
                            .map(OutpRegisterBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.department2Department == null) {
            Set<String> ids =
                    dataHolder.department.keySet().stream()
                            .map(OrganizationBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DepartmentBaseDto> baseDtoList =
                    departmentBaseDtoServiceInAppointmentBookingRpcAdapter
                            .getByOrganizationIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DepartmentBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DepartmentBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DepartmentBaseDto::getOrganizationId));
            Map<String, DepartmentDto> departmentDtoMap =
                    departmentDtoServiceInAppointmentBookingRpcAdapter
                            .getByOrganizationIds(
                                    baseDtoList.stream()
                                            .map(DepartmentBaseDto::getOrganizationId)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));
            Map<DepartmentDto, BookDepartmentVo> dtoVoMap =
                    bookDepartmentVoConverter.convertToBookDepartmentVoMap(
                            new ArrayList<>(departmentDtoMap.values()));
            Map<DepartmentBaseDto, BookDepartmentVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> departmentDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            departmentDtoMap.get(baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.department2Department =
                    dataHolder.department.keySet().stream()
                            .map(OrganizationBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterList
                == null) {
            Set<String> ids =
                    dataHolder.outpRegisterVisitList2OutpVisitEncounterList.keySet().stream()
                            .map(OutpVisitBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OutpVisitEncounterBaseDto> baseDtoList =
                    outpVisitEncounterBaseDtoServiceInAppointmentBookingRpcAdapter
                            .getByOutpVisitIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(OutpVisitEncounterBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<OutpVisitEncounterBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            OutpVisitEncounterBaseDto::getOutpVisitId));
            Map<OutpVisitEncounterBaseDto, BookOutpVisitEncounterBaseVo> dtoVoMap =
                    bookOutpVisitEncounterBaseVoConverter.convertToBookOutpVisitEncounterBaseVoMap(
                            baseDtoList);
            Map<OutpVisitEncounterBaseDto, BookOutpVisitEncounterBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterList =
                    dataHolder.outpRegisterVisitList2OutpVisitEncounterList.keySet().stream()
                            .map(OutpVisitBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取OutpAppointAllDto数据填充OutpAppointAllVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "b10f55c2-535c-3194-849f-fec7891f791b")
    public void collectDataWithDtoData(
            List<OutpAppointAllDto> dtoList, OutpAppointAllVoDataHolder dataHolder) {
        Map<OutpRegisterBaseDto, OutpRegisterVisitDto> outpRegisterVisitListBaseDtoDtoMap =
                new LinkedHashMap<>();
        Map<OutpVisitBaseDto, OutpVisitEncounterDto>
                outpRegisterVisitList2OutpVisitEncounterListBaseDtoDtoMap = new LinkedHashMap<>();
        List<OutpVisitEncounterBaseDto>
                outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListList =
                        new ArrayList<>();
        List<AppointmentScheduleBaseDto> appointmentScheduleList = new ArrayList<>();
        Map<OrganizationBaseDto, OrganizationDepartmentDto> departmentBaseDtoDtoMap =
                new LinkedHashMap<>();
        Map<DepartmentBaseDto, DepartmentDto> department2DepartmentBaseDtoDtoMap =
                new LinkedHashMap<>();

        for (OutpAppointAllDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getOutpRegisterVisitList())) {
                Map<String, OutpRegisterBaseDto> outpRegisterVisitListBaseDtoMap =
                        outpRegisterVisitDtoConverter
                                .convertFromOutpRegisterVisitDtoToOutpRegisterBaseDto(
                                        rootDto.getOutpRegisterVisitList())
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                OutpRegisterBaseDto::getId, Function.identity()));
                for (OutpRegisterVisitDto outpRegisterVisitListDto :
                        rootDto.getOutpRegisterVisitList()) {
                    OutpRegisterBaseDto outpRegisterVisitListBaseDto =
                            outpRegisterVisitListBaseDtoMap.get(outpRegisterVisitListDto.getId());
                    outpRegisterVisitListBaseDto.setOutpAppointId(rootDto.getId());
                    outpRegisterVisitListBaseDtoDtoMap.put(
                            outpRegisterVisitListBaseDto, outpRegisterVisitListDto);
                    if (CollectionUtil.isNotEmpty(
                            outpRegisterVisitListDto.getOutpVisitEncounterList())) {
                        for (OutpVisitEncounterDto outpRegisterVisitList2OutpVisitEncounterListDto :
                                outpRegisterVisitListDto.getOutpVisitEncounterList()) {
                            OutpVisitBaseDto outpRegisterVisitList2OutpVisitEncounterListBaseDto =
                                    outpVisitBaseDtoServiceInAppointmentBookingRpcAdapter
                                            .getByIds(
                                                    List.of(
                                                            outpRegisterVisitList2OutpVisitEncounterListDto
                                                                    .getId()))
                                            .stream()
                                            .findAny()
                                            .get();
                            outpRegisterVisitList2OutpVisitEncounterListBaseDto.setOutpRegisterId(
                                    outpRegisterVisitListDto.getId());
                            outpRegisterVisitList2OutpVisitEncounterListBaseDtoDtoMap.put(
                                    outpRegisterVisitList2OutpVisitEncounterListBaseDto,
                                    outpRegisterVisitList2OutpVisitEncounterListDto);
                            if (CollectionUtil.isNotEmpty(
                                    outpRegisterVisitList2OutpVisitEncounterListDto
                                            .getOutpVisitEncounterList())) {
                                for (OutpVisitEncounterBaseDto
                                        outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListDto :
                                                outpRegisterVisitList2OutpVisitEncounterListDto
                                                        .getOutpVisitEncounterList()) {
                                    outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListList
                                            .add(
                                                    outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListDto);
                                }
                            }
                        }
                    }
                }
            }
            AppointmentScheduleBaseDto appointmentScheduleDto = rootDto.getAppointmentSchedule();
            if (appointmentScheduleDto != null) {
                appointmentScheduleList.add(appointmentScheduleDto);
            }
            OrganizationDepartmentDto departmentDto = rootDto.getDepartment();
            if (departmentDto != null) {
                OrganizationBaseDto departmentBaseDto =
                        organizationBaseDtoServiceInAppointmentBookingRpcAdapter
                                .getByIds(List.of(departmentDto.getId()))
                                .stream()
                                .findAny()
                                .get();
                departmentBaseDtoDtoMap.put(departmentBaseDto, departmentDto);
                DepartmentDto department2DepartmentDto = departmentDto.getDepartment();
                if (department2DepartmentDto != null) {
                    DepartmentBaseDto department2DepartmentBaseDto =
                            departmentBaseDtoServiceInAppointmentBookingRpcAdapter
                                    .getByIds(List.of(department2DepartmentDto.getId()))
                                    .stream()
                                    .findAny()
                                    .get();
                    department2DepartmentBaseDto.setOrganizationId(departmentDto.getId());
                    department2DepartmentBaseDtoDtoMap.put(
                            department2DepartmentBaseDto, department2DepartmentDto);
                }
            }
        }

        // access outpRegisterVisitList
        Map<OutpRegisterVisitDto, BookOutpRegisterVisitVo> outpRegisterVisitListVoMap =
                bookOutpRegisterVisitVoConverter.convertToBookOutpRegisterVisitVoMap(
                        new ArrayList<>(outpRegisterVisitListBaseDtoDtoMap.values()));
        dataHolder.outpRegisterVisitList =
                outpRegisterVisitListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                outpRegisterVisitListVoMap.get(
                                                        outpRegisterVisitListBaseDtoDtoMap.get(
                                                                baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access outpRegisterVisitList2OutpVisitEncounterList
        Map<OutpVisitEncounterDto, BookOutpVisitEncounterVo>
                outpRegisterVisitList2OutpVisitEncounterListVoMap =
                        bookOutpVisitEncounterVoConverter.convertToBookOutpVisitEncounterVoMap(
                                new ArrayList<>(
                                        outpRegisterVisitList2OutpVisitEncounterListBaseDtoDtoMap
                                                .values()));
        dataHolder.outpRegisterVisitList2OutpVisitEncounterList =
                outpRegisterVisitList2OutpVisitEncounterListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                outpRegisterVisitList2OutpVisitEncounterListVoMap
                                                        .get(
                                                                outpRegisterVisitList2OutpVisitEncounterListBaseDtoDtoMap
                                                                        .get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterList
        Map<OutpVisitEncounterBaseDto, BookOutpVisitEncounterBaseVo>
                outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListVoMap =
                        bookOutpVisitEncounterBaseVoConverter
                                .convertToBookOutpVisitEncounterBaseVoMap(
                                        outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListList);
        dataHolder.outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterList =
                outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                outpRegisterVisitList2OutpVisitEncounterList2OutpVisitEncounterListVoMap
                                                        .get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access appointmentSchedule
        Map<AppointmentScheduleBaseDto, AppointmentScheduleExportVo> appointmentScheduleVoMap =
                appointmentScheduleExportVoConverter.convertToAppointmentScheduleExportVoMap(
                        appointmentScheduleList);
        dataHolder.appointmentSchedule =
                appointmentScheduleList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> appointmentScheduleVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access department
        Map<OrganizationDepartmentDto, BookOrganizationDepartmentVo> departmentVoMap =
                bookOrganizationDepartmentVoConverter.convertToBookOrganizationDepartmentVoMap(
                        new ArrayList<>(departmentBaseDtoDtoMap.values()));
        dataHolder.department =
                departmentBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                departmentVoMap.get(
                                                        departmentBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access department2Department
        Map<DepartmentDto, BookDepartmentVo> department2DepartmentVoMap =
                bookDepartmentVoConverter.convertToBookDepartmentVoMap(
                        new ArrayList<>(department2DepartmentBaseDtoDtoMap.values()));
        dataHolder.department2Department =
                department2DepartmentBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                department2DepartmentVoMap.get(
                                                        department2DepartmentBaseDtoDtoMap.get(
                                                                baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "c068753e-e3ca-3f9b-bad1-dfd0df23c573")
    public void collectDataDefault(OutpAppointAllVoDataHolder dataHolder) {
        outpAppointAllVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
