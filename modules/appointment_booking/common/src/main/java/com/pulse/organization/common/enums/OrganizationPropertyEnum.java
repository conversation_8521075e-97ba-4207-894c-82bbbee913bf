package com.pulse.organization.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "ed7d02f8-dd41-4f9c-8264-26bf6c09212e|ENUM|DEFINITION")
public enum OrganizationPropertyEnum {

    /** 临床 */
    CLINIC(),

    /** 辅诊 */
    AUXILIARY_DIAGNOSIS(),

    /** 收费 */
    CHARGE(),

    /** 行政后勤 */
    ADMINISTRATIVE_LOGISTICS(),

    /** 其他 */
    OTHER(),

    /** 药剂 */
    DRUG();

    @AutoGenerated(locked = true)
    OrganizationPropertyEnum() {}
}
