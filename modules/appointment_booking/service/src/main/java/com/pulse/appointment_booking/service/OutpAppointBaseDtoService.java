package com.pulse.appointment_booking.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpAppointBaseDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpAppointBaseDto;
import com.pulse.appointment_booking.service.converter.OutpAppointBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "3835da06-0c23-4819-8e4b-c1074b3082c4|DTO|SERVICE")
public class OutpAppointBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointBaseDtoManager outpAppointBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointBaseDtoServiceConverter outpAppointBaseDtoServiceConverter;

    @PublicInterface(
            id = "741b7b45-f74b-4fe9-813e-fc8b40bb2d46",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992882")
    @AutoGenerated(locked = false, uuid = "106db4f3-11bb-374e-a11b-cd2d64a75a3f")
    public List<OutpAppointBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpAppointBaseDto> outpAppointBaseDtoList = outpAppointBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointBaseDtoServiceConverter.OutpAppointBaseDtoConverter(
                outpAppointBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "04f9c22d-d614-4fd1-b219-bf1596cb9a41",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992909")
    @AutoGenerated(locked = false, uuid = "33cf4676-0e9b-3d26-9335-4c66da02b7d9")
    public List<OutpAppointBaseDto> getByAppointmentScheduleId(
            @NotNull(message = "排班ID不能为空") String appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAppointmentScheduleIds(Arrays.asList(appointmentScheduleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "1e05ea2b-74e1-4ea2-8130-9706c0464883",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992914")
    @AutoGenerated(locked = false, uuid = "4686ab67-fac2-35a9-bf5e-64786b5a1147")
    public List<OutpAppointBaseDto> getByAppointmentScheduleIds(
            @Valid @NotNull(message = "排班ID不能为空") List<String> appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        appointmentScheduleId = new ArrayList<>(new HashSet<>(appointmentScheduleId));
        List<OutpAppointBaseDto> outpAppointBaseDtoList =
                outpAppointBaseDtoManager.getByAppointmentScheduleIds(appointmentScheduleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointBaseDtoServiceConverter.OutpAppointBaseDtoConverter(
                outpAppointBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "b7f45301-b80a-4414-9871-27c8b76793d7",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992893")
    @AutoGenerated(locked = false, uuid = "9b4864fd-336e-30a5-b67b-8b3f46674da9")
    public List<OutpAppointBaseDto> getByPatientIds(
            @Valid @NotNull(message = "患者ID不能为空") List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        patientId = new ArrayList<>(new HashSet<>(patientId));
        List<OutpAppointBaseDto> outpAppointBaseDtoList =
                outpAppointBaseDtoManager.getByPatientIds(patientId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointBaseDtoServiceConverter.OutpAppointBaseDtoConverter(
                outpAppointBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "57ef0d63-86a2-4165-8ca7-b89d48e52f45",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992925")
    @AutoGenerated(locked = false, uuid = "9ddd6a7d-9d53-3058-b8b2-96c1ec90d7f2")
    public List<OutpAppointBaseDto> getByDepartmentIds(
            @Valid @NotNull(message = "科室ID不能为空") List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        departmentId = new ArrayList<>(new HashSet<>(departmentId));
        List<OutpAppointBaseDto> outpAppointBaseDtoList =
                outpAppointBaseDtoManager.getByDepartmentIds(departmentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointBaseDtoServiceConverter.OutpAppointBaseDtoConverter(
                outpAppointBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "bd143a95-18f0-469b-875d-3f3f26a5cd3d",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992920")
    @AutoGenerated(locked = false, uuid = "b34e72bf-63c3-3428-8278-05e988c205cb")
    public List<OutpAppointBaseDto> getByDepartmentId(
            @NotNull(message = "科室ID不能为空") String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDepartmentIds(Arrays.asList(departmentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "89babc9f-6ade-48cb-890c-25ffe825a0a5",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992877")
    @AutoGenerated(locked = false, uuid = "b8ff8b85-e6c0-3cef-928c-b63104535523")
    public OutpAppointBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "d1b03a2f-4d88-4b9d-ab6f-5cfbacc4ac32",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992888")
    @AutoGenerated(locked = false, uuid = "bf20a759-c7f2-3426-9f58-6e6a19270060")
    public List<OutpAppointBaseDto> getByPatientId(
            @NotNull(message = "患者ID不能为空") String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByPatientIds(Arrays.asList(patientId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "59e3ed41-6de6-4012-b0d6-28537915acbf",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992904")
    @AutoGenerated(locked = false, uuid = "dcdfa95e-ea13-34bf-b0ca-de804e428502")
    public List<OutpAppointBaseDto> getByClinicRegisterTypeIds(
            @Valid @NotNull(message = "挂号类别ID不能为空") List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        clinicRegisterTypeId = new ArrayList<>(new HashSet<>(clinicRegisterTypeId));
        List<OutpAppointBaseDto> outpAppointBaseDtoList =
                outpAppointBaseDtoManager.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointBaseDtoServiceConverter.OutpAppointBaseDtoConverter(
                outpAppointBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "d1b1c1c7-2bb2-4f21-9346-f51cea5e93d0",
            module = "appointment_booking",
            moduleId = "8b42e5e5-7065-48d5-9c9b-9234ef78ae96",
            pubRpc = true,
            version = "1748918992899")
    @AutoGenerated(locked = false, uuid = "f557a9b3-6b6d-3463-b32a-8ad43563f4ec")
    public List<OutpAppointBaseDto> getByClinicRegisterTypeId(
            @NotNull(message = "挂号类别ID不能为空") String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
