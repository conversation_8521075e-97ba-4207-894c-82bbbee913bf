package com.pulse.appointment_booking.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_booking.manager.OutpAppointRegisterDtoManager;
import com.pulse.appointment_booking.manager.dto.OutpAppointRegisterDto;
import com.pulse.appointment_booking.service.converter.OutpAppointRegisterDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "6ded3f54-5f2e-4ef8-a834-2ed2d6753084|DTO|SERVICE")
public class OutpAppointRegisterDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointRegisterDtoManager outpAppointRegisterDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpAppointRegisterDtoServiceConverter outpAppointRegisterDtoServiceConverter;

    @PublicInterface(id = "65f3b994-17d6-4c56-b26a-239223204dfb", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "106db4f3-11bb-374e-a11b-cd2d64a75a3f")
    public List<OutpAppointRegisterDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpAppointRegisterDto> outpAppointRegisterDtoList =
                outpAppointRegisterDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointRegisterDtoServiceConverter.OutpAppointRegisterDtoConverter(
                outpAppointRegisterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "446fba7e-6400-4c1f-ab35-7d0d6868a7e1", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "33cf4676-0e9b-3d26-9335-4c66da02b7d9")
    public List<OutpAppointRegisterDto> getByAppointmentScheduleId(
            @NotNull(message = "排班ID不能为空") String appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAppointmentScheduleIds(Arrays.asList(appointmentScheduleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1ee382f9-0e99-44ea-9441-9584d2191910", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "4686ab67-fac2-35a9-bf5e-64786b5a1147")
    public List<OutpAppointRegisterDto> getByAppointmentScheduleIds(
            @Valid @NotNull(message = "排班ID不能为空") List<String> appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        appointmentScheduleId = new ArrayList<>(new HashSet<>(appointmentScheduleId));
        List<OutpAppointRegisterDto> outpAppointRegisterDtoList =
                outpAppointRegisterDtoManager.getByAppointmentScheduleIds(appointmentScheduleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointRegisterDtoServiceConverter.OutpAppointRegisterDtoConverter(
                outpAppointRegisterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "7dce6ab7-5b9f-4674-841d-9ada1d23d451", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "9b4864fd-336e-30a5-b67b-8b3f46674da9")
    public List<OutpAppointRegisterDto> getByPatientIds(
            @Valid @NotNull(message = "患者ID不能为空") List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        patientId = new ArrayList<>(new HashSet<>(patientId));
        List<OutpAppointRegisterDto> outpAppointRegisterDtoList =
                outpAppointRegisterDtoManager.getByPatientIds(patientId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointRegisterDtoServiceConverter.OutpAppointRegisterDtoConverter(
                outpAppointRegisterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "81577cec-a5b8-4b71-9905-9270a8023fd1", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "9ddd6a7d-9d53-3058-b8b2-96c1ec90d7f2")
    public List<OutpAppointRegisterDto> getByDepartmentIds(
            @Valid @NotNull(message = "科室ID不能为空") List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        departmentId = new ArrayList<>(new HashSet<>(departmentId));
        List<OutpAppointRegisterDto> outpAppointRegisterDtoList =
                outpAppointRegisterDtoManager.getByDepartmentIds(departmentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointRegisterDtoServiceConverter.OutpAppointRegisterDtoConverter(
                outpAppointRegisterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "3166c134-9922-4222-a81d-a6c1f425fe2a", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "b34e72bf-63c3-3428-8278-05e988c205cb")
    public List<OutpAppointRegisterDto> getByDepartmentId(
            @NotNull(message = "科室ID不能为空") String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDepartmentIds(Arrays.asList(departmentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "936a286c-2970-4f2a-a721-2abc07fde92d", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "b8ff8b85-e6c0-3cef-928c-b63104535523")
    public OutpAppointRegisterDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpAppointRegisterDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "b5b222b5-8508-4226-9e4a-30d7942aef9c", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "bf20a759-c7f2-3426-9f58-6e6a19270060")
    public List<OutpAppointRegisterDto> getByPatientId(
            @NotNull(message = "患者ID不能为空") String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByPatientIds(Arrays.asList(patientId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "679a5487-42bf-4ab5-829d-************", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "dcdfa95e-ea13-34bf-b0ca-de804e428502")
    public List<OutpAppointRegisterDto> getByClinicRegisterTypeIds(
            @Valid @NotNull(message = "挂号类别ID不能为空") List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        clinicRegisterTypeId = new ArrayList<>(new HashSet<>(clinicRegisterTypeId));
        List<OutpAppointRegisterDto> outpAppointRegisterDtoList =
                outpAppointRegisterDtoManager.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpAppointRegisterDtoServiceConverter.OutpAppointRegisterDtoConverter(
                outpAppointRegisterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0cadd6d6-efe5-480d-97f2-0b6b90249d89", module = "appointment_booking")
    @AutoGenerated(locked = false, uuid = "f557a9b3-6b6d-3463-b32a-8ad43563f4ec")
    public List<OutpAppointRegisterDto> getByClinicRegisterTypeId(
            @NotNull(message = "挂号类别ID不能为空") String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
