package com.pulse.appointment_booking.service.converter;

import com.pulse.appointment_booking.manager.dto.OutpAppointAllDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "a3c645db-5394-3238-b3f5-24e3c4d077c0")
public class OutpAppointAllDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OutpAppointAllDto> OutpAppointAllDtoConverter(
            List<OutpAppointAllDto> outpAppointAllDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return outpAppointAllDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
