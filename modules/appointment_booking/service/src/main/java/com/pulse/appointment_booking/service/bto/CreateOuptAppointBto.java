package com.pulse.appointment_booking.service.bto;

import com.pulse.appointment_booking.common.enums.AppointStatusEnum;
import com.pulse.dictionary_basic.common.enums.ClinicVisitTypeEnum;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;

import javax.validation.Valid;

/**
 * <b>[源自]</b> OutpAppoint
 *
 * <p><b>[操作]</b> CREATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "3521ad80-17d8-48fa-8edd-9de88bca1552|BTO|DEFINITION")
public class CreateOuptAppointBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 应用ID */
    @AutoGenerated(locked = true, uuid = "8c473bfe-2d83-4042-b98c-8639220d083a")
    private String appId;

    /** 预约凭证号 */
    @AutoGenerated(locked = true, uuid = "4dded204-abfa-4e8f-87bc-3ad212234c4c")
    private String appointCertificationNumber;

    /** 预约状态 */
    @AutoGenerated(locked = true, uuid = "d5612d7f-a4a3-46be-8fd3-250223d98cf1")
    private AppointStatusEnum appointStatus;

    /** 预约类别ID */
    @AutoGenerated(locked = true, uuid = "a1c80009-04e9-4367-b140-6a5610ee42d0")
    private String appointmentCategoryId;

    /** 排班ID */
    @AutoGenerated(locked = true, uuid = "44cb8d46-a0dc-4e14-9f3f-d5998fe65d08")
    private String appointmentScheduleId;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "1c9d9c62-1e4b-4641-a188-bbdfc94f4f20")
    private Date birthDate;

    /** 院区id */
    @AutoGenerated(locked = true, uuid = "30062373-29e4-47d4-9649-5d5d3bfadd69")
    private String campusId;

    /** 取消日期 */
    @AutoGenerated(locked = true, uuid = "2812ed4a-5e73-44b7-84ab-d81a2d7bd81f")
    private Date cancelDate;

    /** 取消人员ID */
    @AutoGenerated(locked = true, uuid = "c3e6f179-8826-499e-84df-b280372f829d")
    private String cancelOperatorId;

    /** 取消原因 */
    @AutoGenerated(locked = true, uuid = "2604cc92-10ff-4806-973d-1eb3e93dbb26")
    private String cancelReason;

    /** 取消预约类型 */
    @AutoGenerated(locked = true, uuid = "bc2f7d26-668c-4b6d-bf25-23073986704b")
    private String cancelType;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "80f990e1-a26f-4c56-9587-d5dce53877aa")
    private String cellphone;

    /** 是否收费 */
    @AutoGenerated(locked = true, uuid = "8ca94b58-0b67-47e4-b323-4092407149b4")
    private Boolean chargeIs;

    /** 挂号类别ID */
    @AutoGenerated(locked = true, uuid = "7f3594e0-27ba-4303-a91a-95e6113ddd8a")
    private String clinicRegisterTypeId;

    /** 科室ID */
    @AutoGenerated(locked = true, uuid = "36751a0e-97ce-4f06-ab9d-8ab3189a1b41")
    private String departmentId;

    /** 病案号 */
    @AutoGenerated(locked = true, uuid = "f3b65573-dce7-4b2f-ad93-c7b1d8cc4e59")
    private String displayId;

    /** 医生ID */
    @AutoGenerated(locked = true, uuid = "daaa19df-beff-4397-82a0-bf36e9bc17f2")
    private String doctorId;

    /** 预约编号 取号时,预约编号+取号密码作为凭证 */
    @AutoGenerated(locked = true, uuid = "77024e21-2418-4210-a63b-c6bc74ac4046")
    private String externalRegisterId;

    /** 加号标志 */
    @AutoGenerated(locked = true, uuid = "fb34e1f8-4a31-4d8e-936f-e0b253d228f8")
    private Boolean extraRegistrationFlag;

    /** 家庭地址 */
    @AutoGenerated(locked = true, uuid = "4fbeb643-f150-40d0-8ddd-3eef8db05bcd")
    private String homeAddress;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "cbf360f8-d880-414c-9ad4-aa9004afef0c")
    private String id;

    /** 患者预约实名认证证件类型 */
    @AutoGenerated(locked = true, uuid = "29d5e50b-91d2-42c0-bdbb-fccbb0e45377")
    private String identificationClassId;

    /** 实名认证证件号码 */
    @AutoGenerated(locked = true, uuid = "de8daa8f-e2f5-42eb-a568-d1ca160afa0d")
    private String identificationNumber;

    /** 互联网收费标志 */
    @AutoGenerated(locked = true, uuid = "419061e4-ef53-453b-83ff-1f0f23d5ad33")
    private Boolean internetPaymentFlag;

    /** MDT诊疗费类别 */
    @AutoGenerated(locked = true, uuid = "39383c2a-8dc0-42d0-8f46-43e844a59c30")
    private String mdtFeeType;

    /** 预约日期 */
    @AutoGenerated(locked = true, uuid = "81f50155-1ffa-46df-b2a2-787ae7adf98e")
    private Date operateDate;

    /** 操作科室 */
    @AutoGenerated(locked = true, uuid = "4781ecb7-9a17-4ad6-87cf-a13ae53a5b0c")
    private String operatorDepartmentId;

    /** 预约人员ID */
    @AutoGenerated(locked = true, uuid = "776ab086-1fe3-4df9-9ea1-74df71706b5d")
    private String operatorId;

    /** 门诊住院标志 */
    @AutoGenerated(locked = true, uuid = "bebc8c3b-481f-469d-be05-14ee37a1485a")
    private ClinicVisitTypeEnum outpInpType;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "b2697030-dea7-44c7-a8a1-2467237ac3e2")
    private String patientId;

    /** 患者名称 未建档患者使用 */
    @AutoGenerated(locked = true, uuid = "d2a25fc6-64cd-43b1-a1a1-7149d0fd9546")
    private String patientName;

    /** 省预约平台上传标志 */
    @AutoGenerated(locked = true, uuid = "c3c68f16-bd60-42c4-becc-bd7ad6b77b3f")
    private Boolean provinceUploadFlag;

    /** 代理挂号标志 */
    @AutoGenerated(locked = true, uuid = "4bebcb81-6430-4266-bfae-76a91be2be81")
    private Boolean proxyRegistrationFlag;

    /** 提醒手机 */
    @AutoGenerated(locked = true, uuid = "5c17b467-59b4-41f3-bdfb-0e7741cc0851")
    private String reminderMobile;

    /** 号源序号 */
    @AutoGenerated(locked = true, uuid = "1a7c8529-4f9f-4c67-b6b1-a699bffaf6a1")
    private Long serialNumber;

    /** 来源ID */
    @AutoGenerated(locked = true, uuid = "304f4096-341b-4335-a7d0-c7088ab50eb3")
    private String sourceId;

    /** 就诊卡ID */
    @AutoGenerated(locked = true, uuid = "8c4bee06-e6ca-443d-91e4-3f3dbb3d1f82")
    private String visitCardId;

    /** 就诊日期 */
    @AutoGenerated(locked = true, uuid = "276fb10b-b7af-4cf6-8af4-21c4c4c3b0de")
    private Date visitDate;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "9161375d-f9d6-469c-9568-c348709b43fc")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c4ac30d9-a849-4424-908d-bd74800a37f1")
    private TimeEo waitingStartTime;

    @AutoGenerated(locked = true)
    public void setAppId(String appId) {
        this.__$validPropertySet.add("appId");
        this.appId = appId;
    }

    @AutoGenerated(locked = true)
    public void setAppointCertificationNumber(String appointCertificationNumber) {
        this.__$validPropertySet.add("appointCertificationNumber");
        this.appointCertificationNumber = appointCertificationNumber;
    }

    @AutoGenerated(locked = true)
    public void setAppointStatus(AppointStatusEnum appointStatus) {
        this.__$validPropertySet.add("appointStatus");
        this.appointStatus = appointStatus;
    }

    @AutoGenerated(locked = true)
    public void setAppointmentCategoryId(String appointmentCategoryId) {
        this.__$validPropertySet.add("appointmentCategoryId");
        this.appointmentCategoryId = appointmentCategoryId;
    }

    @AutoGenerated(locked = true)
    public void setAppointmentScheduleId(String appointmentScheduleId) {
        this.__$validPropertySet.add("appointmentScheduleId");
        this.appointmentScheduleId = appointmentScheduleId;
    }

    @AutoGenerated(locked = true)
    public void setBirthDate(Date birthDate) {
        this.__$validPropertySet.add("birthDate");
        this.birthDate = birthDate;
    }

    @AutoGenerated(locked = true)
    public void setCampusId(String campusId) {
        this.__$validPropertySet.add("campusId");
        this.campusId = campusId;
    }

    @AutoGenerated(locked = true)
    public void setCancelDate(Date cancelDate) {
        this.__$validPropertySet.add("cancelDate");
        this.cancelDate = cancelDate;
    }

    @AutoGenerated(locked = true)
    public void setCancelOperatorId(String cancelOperatorId) {
        this.__$validPropertySet.add("cancelOperatorId");
        this.cancelOperatorId = cancelOperatorId;
    }

    @AutoGenerated(locked = true)
    public void setCancelReason(String cancelReason) {
        this.__$validPropertySet.add("cancelReason");
        this.cancelReason = cancelReason;
    }

    @AutoGenerated(locked = true)
    public void setCancelType(String cancelType) {
        this.__$validPropertySet.add("cancelType");
        this.cancelType = cancelType;
    }

    @AutoGenerated(locked = true)
    public void setCellphone(String cellphone) {
        this.__$validPropertySet.add("cellphone");
        this.cellphone = cellphone;
    }

    @AutoGenerated(locked = true)
    public void setChargeIs(Boolean chargeIs) {
        this.__$validPropertySet.add("chargeIs");
        this.chargeIs = chargeIs;
    }

    @AutoGenerated(locked = true)
    public void setClinicRegisterTypeId(String clinicRegisterTypeId) {
        this.__$validPropertySet.add("clinicRegisterTypeId");
        this.clinicRegisterTypeId = clinicRegisterTypeId;
    }

    @AutoGenerated(locked = true)
    public void setDepartmentId(String departmentId) {
        this.__$validPropertySet.add("departmentId");
        this.departmentId = departmentId;
    }

    @AutoGenerated(locked = true)
    public void setDisplayId(String displayId) {
        this.__$validPropertySet.add("displayId");
        this.displayId = displayId;
    }

    @AutoGenerated(locked = true)
    public void setDoctorId(String doctorId) {
        this.__$validPropertySet.add("doctorId");
        this.doctorId = doctorId;
    }

    @AutoGenerated(locked = true)
    public void setExternalRegisterId(String externalRegisterId) {
        this.__$validPropertySet.add("externalRegisterId");
        this.externalRegisterId = externalRegisterId;
    }

    @AutoGenerated(locked = true)
    public void setExtraRegistrationFlag(Boolean extraRegistrationFlag) {
        this.__$validPropertySet.add("extraRegistrationFlag");
        this.extraRegistrationFlag = extraRegistrationFlag;
    }

    @AutoGenerated(locked = true)
    public void setHomeAddress(String homeAddress) {
        this.__$validPropertySet.add("homeAddress");
        this.homeAddress = homeAddress;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setIdentificationClassId(String identificationClassId) {
        this.__$validPropertySet.add("identificationClassId");
        this.identificationClassId = identificationClassId;
    }

    @AutoGenerated(locked = true)
    public void setIdentificationNumber(String identificationNumber) {
        this.__$validPropertySet.add("identificationNumber");
        this.identificationNumber = identificationNumber;
    }

    @AutoGenerated(locked = true)
    public void setInternetPaymentFlag(Boolean internetPaymentFlag) {
        this.__$validPropertySet.add("internetPaymentFlag");
        this.internetPaymentFlag = internetPaymentFlag;
    }

    @AutoGenerated(locked = true)
    public void setMdtFeeType(String mdtFeeType) {
        this.__$validPropertySet.add("mdtFeeType");
        this.mdtFeeType = mdtFeeType;
    }

    @AutoGenerated(locked = true)
    public void setOperateDate(Date operateDate) {
        this.__$validPropertySet.add("operateDate");
        this.operateDate = operateDate;
    }

    @AutoGenerated(locked = true)
    public void setOperatorDepartmentId(String operatorDepartmentId) {
        this.__$validPropertySet.add("operatorDepartmentId");
        this.operatorDepartmentId = operatorDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setOperatorId(String operatorId) {
        this.__$validPropertySet.add("operatorId");
        this.operatorId = operatorId;
    }

    @AutoGenerated(locked = true)
    public void setOutpInpType(ClinicVisitTypeEnum outpInpType) {
        this.__$validPropertySet.add("outpInpType");
        this.outpInpType = outpInpType;
    }

    @AutoGenerated(locked = true)
    public void setPatientId(String patientId) {
        this.__$validPropertySet.add("patientId");
        this.patientId = patientId;
    }

    @AutoGenerated(locked = true)
    public void setPatientName(String patientName) {
        this.__$validPropertySet.add("patientName");
        this.patientName = patientName;
    }

    @AutoGenerated(locked = true)
    public void setProvinceUploadFlag(Boolean provinceUploadFlag) {
        this.__$validPropertySet.add("provinceUploadFlag");
        this.provinceUploadFlag = provinceUploadFlag;
    }

    @AutoGenerated(locked = true)
    public void setProxyRegistrationFlag(Boolean proxyRegistrationFlag) {
        this.__$validPropertySet.add("proxyRegistrationFlag");
        this.proxyRegistrationFlag = proxyRegistrationFlag;
    }

    @AutoGenerated(locked = true)
    public void setReminderMobile(String reminderMobile) {
        this.__$validPropertySet.add("reminderMobile");
        this.reminderMobile = reminderMobile;
    }

    @AutoGenerated(locked = true)
    public void setSerialNumber(Long serialNumber) {
        this.__$validPropertySet.add("serialNumber");
        this.serialNumber = serialNumber;
    }

    @AutoGenerated(locked = true)
    public void setSourceId(String sourceId) {
        this.__$validPropertySet.add("sourceId");
        this.sourceId = sourceId;
    }

    @AutoGenerated(locked = true)
    public void setVisitCardId(String visitCardId) {
        this.__$validPropertySet.add("visitCardId");
        this.visitCardId = visitCardId;
    }

    @AutoGenerated(locked = true)
    public void setVisitDate(Date visitDate) {
        this.__$validPropertySet.add("visitDate");
        this.visitDate = visitDate;
    }

    @AutoGenerated(locked = true)
    public void setWaitingEndTime(TimeEo waitingEndTime) {
        this.__$validPropertySet.add("waitingEndTime");
        this.waitingEndTime = waitingEndTime;
    }

    @AutoGenerated(locked = true)
    public void setWaitingStartTime(TimeEo waitingStartTime) {
        this.__$validPropertySet.add("waitingStartTime");
        this.waitingStartTime = waitingStartTime;
    }
}
