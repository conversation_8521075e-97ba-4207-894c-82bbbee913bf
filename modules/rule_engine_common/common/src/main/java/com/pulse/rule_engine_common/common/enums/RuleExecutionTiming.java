package com.pulse.rule_engine_common.common.enums;

/**
 * 规则执行时机枚举
 *
 * <p>定义规则在方法执行过程中的触发时机
 *
 * <AUTHOR>
 */
public enum RuleExecutionTiming {

    /** 方法执行前触发规则 */
    BEFORE("方法执行前"),

    /** 方法执行后触发规则 */
    AFTER("方法执行后"),

    /** 方法执行前后都触发规则 */
    AROUND("方法执行前后"),

    /** 方法抛出异常时触发规则 */
    AFTER_THROWING("方法异常时");

    private final String description;

    RuleExecutionTiming(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
