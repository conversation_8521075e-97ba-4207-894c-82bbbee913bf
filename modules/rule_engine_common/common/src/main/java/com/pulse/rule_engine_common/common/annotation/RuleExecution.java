package com.pulse.rule_engine_common.common.annotation;

import com.pulse.rule_engine_common.common.enums.RuleExecutionTiming;

import java.lang.annotation.*;

/**
 * 规则执行注解
 *
 * <p>在方法上使用此注解，可以自动执行指定的规则或规则组
 *
 * <p>支持按规则代码列表和规则组代码列表两种方式执行规则
 *
 * <p>支持配置执行时机、异常处理等参数
 *
 * <p>使用示例：
 *
 * <pre>
 * &#64;RuleExecution(
 *     ruleGroupCodes = {"PATIENT_ADMISSION_RULES", "BILLING_VALIDATION_RULES"},
 *     timing = RuleExecutionTiming.BEFORE,
 *     throwOnFailure = true
 * )
 * public void admitPatient(PatientAdmissionRequest request) {
 *     // 业务逻辑
 * }
 * </pre>
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RuleExecution {

    /**
     * 要执行的规则代码列表
     *
     * <p>指定具体的规则代码，将执行这些规则的最新版本
     *
     * <p>如果同时指定了ruleGroupCodes，两者都会被执行
     *
     * @return 规则代码数组
     */
    String[] ruleCodes() default {};

    /**
     * 要执行的规则组代码列表
     *
     * <p>指定规则组代码，将执行规则组中包含的所有有效规则
     *
     * <p>如果同时指定了ruleCodes，两者都会被执行
     *
     * @return 规则组代码数组
     */
    String[] ruleGroupCodes() default {};

    /**
     * 规则执行时机
     *
     * <p>定义规则在方法执行过程中的触发时机
     *
     * <p>默认为方法执行前
     *
     * @return 执行时机
     */
    RuleExecutionTiming timing() default RuleExecutionTiming.BEFORE;

    /**
     * 规则执行失败时是否抛出异常
     *
     * <p>true: 规则执行失败时抛出异常，阻止方法继续执行
     *
     * <p>false: 规则执行失败时仅记录日志，不影响方法执行
     *
     * <p>默认为false
     *
     * @return 是否抛出异常
     */
    boolean throwOnFailure() default false;

    /**
     * 规则执行结果为false时是否抛出异常
     *
     * <p>true: 规则执行结果为false时抛出异常
     *
     * <p>false: 规则执行结果为false时仅记录日志
     *
     * <p>默认为false
     *
     * @return 是否在结果为false时抛出异常
     */
    boolean throwOnFalseResult() default false;

    /**
     * 业务数据提取表达式
     *
     * <p>使用SpEL表达式从方法参数中提取业务数据
     *
     * <p>如果为空，将使用方法的第一个参数作为业务数据
     *
     * <p>业务数据将被转换为JSON字符串传递给规则引擎
     *
     * <p>示例：
     *
     * <ul>
     *   <li>"#request" - 使用名为request的参数
     *   <li>"#args[0]" - 使用第一个参数
     *   <li>"#request.patientInfo" - 使用request参数的patientInfo属性
     * </ul>
     *
     * @return SpEL表达式
     */
    String businessDataExpression() default "";

    /**
     * 自定义错误消息
     *
     * <p>当规则执行失败或结果为false时的错误消息
     *
     * <p>如果为空，将使用默认错误消息
     *
     * @return 错误消息
     */
    String errorMessage() default "";

    /**
     * 是否启用规则执行
     *
     * <p>可以通过此参数临时禁用规则执行，用于调试或特殊场景
     *
     * <p>默认为true
     *
     * @return 是否启用
     */
    boolean enabled() default true;
}
