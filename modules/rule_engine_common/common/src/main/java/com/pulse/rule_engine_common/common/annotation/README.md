# 规则执行注解使用指南

## 概述

`@RuleExecution` 注解提供了一种声明式的方式来在业务方法中自动执行规则引擎的规则。通过在方法上添加此注解，可以在方法执行的不同时机自动触发规则执行，无需在业务代码中显式调用规则引擎服务。

## 功能特性

- **多种执行时机**：支持方法执行前、后、前后、异常时触发规则
- **灵活的规则配置**：支持按规则代码列表和规则组代码列表执行规则
- **SpEL表达式支持**：支持使用SpEL表达式从方法参数中提取业务数据
- **异常处理控制**：支持配置规则执行失败时的异常处理策略
- **性能监控**：自动记录规则执行时间和结果

## 使用示例

### 基础用法

```java
@Service
public class PatientAdmissionService {

    /**
     * 患者入院处理
     * 在方法执行前验证入院规则
     */
    @RuleExecution(
        ruleGroupCodes = {"PATIENT_ADMISSION_RULES"},
        timing = RuleExecutionTiming.BEFORE,
        throwOnFalseResult = true,
        errorMessage = "患者入院规则验证失败"
    )
    public void admitPatient(PatientAdmissionRequest request) {
        // 业务逻辑：处理患者入院
        patientRepository.save(request.toPatient());
    }
}
```

### 高级用法

```java
@Service
public class BillingService {

    /**
     * 费用结算处理
     * 使用SpEL表达式提取业务数据，在方法执行前后都执行规则
     */
    @RuleExecution(
        ruleCodes = {"BILLING_VALIDATION", "INSURANCE_CHECK"},
        ruleGroupCodes = {"BILLING_RULES", "AUDIT_RULES"},
        timing = RuleExecutionTiming.AROUND,
        businessDataExpression = "#request.billingInfo",
        throwOnFailure = true,
        errorMessage = "费用结算规则验证失败"
    )
    public BillingResult processBilling(BillingRequest request) {
        // 业务逻辑：处理费用结算
        return billingProcessor.process(request);
    }

    /**
     * 异常处理时执行审计规则
     */
    @RuleExecution(
        ruleGroupCodes = {"AUDIT_RULES", "ERROR_HANDLING_RULES"},
        timing = RuleExecutionTiming.AFTER_THROWING,
        businessDataExpression = "#args[0]"
    )
    public void handleBillingError(BillingRequest request) {
        // 可能抛出异常的业务逻辑
        throw new RuntimeException("模拟业务异常");
    }
}
```

## 注解参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `ruleCodes` | String[] | {} | 要执行的规则代码列表 |
| `ruleGroupCodes` | String[] | {} | 要执行的规则组代码列表 |
| `timing` | RuleExecutionTiming | BEFORE | 规则执行时机 |
| `throwOnFailure` | boolean | false | 规则执行失败时是否抛出异常 |
| `throwOnFalseResult` | boolean | false | 规则执行结果为false时是否抛出异常 |
| `businessDataExpression` | String | "" | 业务数据提取的SpEL表达式 |
| `errorMessage` | String | "" | 自定义错误消息 |
| `enabled` | boolean | true | 是否启用规则执行 |

## 执行时机说明

- **BEFORE**：方法执行前触发规则，适用于前置验证场景
- **AFTER**：方法执行后触发规则，适用于后置检查场景
- **AROUND**：方法执行前后都触发规则，适用于全流程监控场景
- **AFTER_THROWING**：方法抛出异常时触发规则，适用于异常处理和审计场景

## SpEL表达式示例

```java
// 使用第一个参数
businessDataExpression = "#args[0]"

// 使用具名参数（参数名为request）
businessDataExpression = "#request"

// 使用参数的属性
businessDataExpression = "#request.patientInfo"

// 使用复杂表达式
businessDataExpression = "new java.util.HashMap(#{patientId: #request.patientId, amount: #request.amount})"
```

## 注意事项

1. **性能考虑**：规则执行会增加方法调用的耗时，建议在关键业务节点使用
2. **异常处理**：合理配置 `throwOnFailure` 和 `throwOnFalseResult` 参数
3. **业务数据**：确保业务数据能够正确序列化为JSON格式
4. **规则配置**：至少配置 `ruleCodes` 或 `ruleGroupCodes` 中的一个
5. **循环依赖**：避免在规则执行过程中再次触发相同的规则，防止无限循环

## 集成要求

1. 在项目中引入 `rule_engine_common` 依赖
2. 确保Spring AOP功能已启用
3. 确保规则引擎服务正常运行
4. 在Spring配置中启用AspectJ自动代理：`@EnableAspectJAutoProxy`
