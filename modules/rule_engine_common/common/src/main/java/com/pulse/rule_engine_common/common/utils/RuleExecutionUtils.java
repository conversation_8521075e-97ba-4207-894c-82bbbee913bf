package com.pulse.rule_engine_common.common.utils;

import com.pulse.rule_engine_common.common.annotation.RuleExecution;
import com.pulse.rule_engine_common.common.enums.RuleExecutionTiming;

import java.lang.annotation.Annotation;

/**
 * 规则执行工具类
 *
 * <p>提供规则执行相关的工具方法，帮助其他模块更方便地使用规则执行功能
 *
 * <AUTHOR>
 */
public class RuleExecutionUtils {

    /**
     * 创建规则执行注解的动态实现
     *
     * <p>用于在运行时动态创建@RuleExecution注解实例
     *
     * @param ruleCodes 规则代码列表
     * @param ruleGroupCodes 规则组代码列表
     * @param timing 执行时机
     * @param throwOnFailure 执行失败时是否抛出异常
     * @param throwOnFalseResult 结果为false时是否抛出异常
     * @param businessDataExpression 业务数据提取表达式
     * @param errorMessage 错误消息
     * @param enabled 是否启用
     * @return 规则执行注解实例
     */
    public static RuleExecution createRuleExecution(
            String[] ruleCodes,
            String[] ruleGroupCodes,
            RuleExecutionTiming timing,
            boolean throwOnFailure,
            boolean throwOnFalseResult,
            String businessDataExpression,
            String errorMessage,
            boolean enabled) {

        return new RuleExecution() {
            @Override
            public String[] ruleCodes() {
                return ruleCodes != null ? ruleCodes : new String[0];
            }

            @Override
            public String[] ruleGroupCodes() {
                return ruleGroupCodes != null ? ruleGroupCodes : new String[0];
            }

            @Override
            public RuleExecutionTiming timing() {
                return timing != null ? timing : RuleExecutionTiming.BEFORE;
            }

            @Override
            public boolean throwOnFailure() {
                return throwOnFailure;
            }

            @Override
            public boolean throwOnFalseResult() {
                return throwOnFalseResult;
            }

            @Override
            public String businessDataExpression() {
                return businessDataExpression != null ? businessDataExpression : "";
            }

            @Override
            public String errorMessage() {
                return errorMessage != null ? errorMessage : "";
            }

            @Override
            public boolean enabled() {
                return enabled;
            }

            @Override
            public Class<? extends Annotation> annotationType() {
                return RuleExecution.class;
            }
        };
    }

    /**
     * 创建简单的规则执行注解
     *
     * <p>使用默认配置创建规则执行注解
     *
     * @param ruleGroupCodes 规则组代码列表
     * @return 规则执行注解实例
     */
    public static RuleExecution createSimpleRuleExecution(String... ruleGroupCodes) {
        return createRuleExecution(
                new String[0],
                ruleGroupCodes,
                RuleExecutionTiming.BEFORE,
                false,
                false,
                "",
                "",
                true);
    }

    /**
     * 创建验证型规则执行注解
     *
     * <p>创建用于验证的规则执行注解，结果为false时抛出异常
     *
     * @param ruleGroupCodes 规则组代码列表
     * @param errorMessage 错误消息
     * @return 规则执行注解实例
     */
    public static RuleExecution createValidationRuleExecution(
            String[] ruleGroupCodes, String errorMessage) {
        return createRuleExecution(
                new String[0],
                ruleGroupCodes,
                RuleExecutionTiming.BEFORE,
                true,
                true,
                "",
                errorMessage,
                true);
    }

    /**
     * 创建审计型规则执行注解
     *
     * <p>创建用于审计的规则执行注解，在方法执行后触发
     *
     * @param ruleGroupCodes 规则组代码列表
     * @return 规则执行注解实例
     */
    public static RuleExecution createAuditRuleExecution(String... ruleGroupCodes) {
        return createRuleExecution(
                new String[0],
                ruleGroupCodes,
                RuleExecutionTiming.AFTER,
                false,
                false,
                "",
                "",
                true);
    }

    /**
     * 检查规则执行注解是否有效
     *
     * @param ruleExecution 规则执行注解
     * @return 是否有效
     */
    public static boolean isValidRuleExecution(RuleExecution ruleExecution) {
        if (ruleExecution == null || !ruleExecution.enabled()) {
            return false;
        }

        // 检查是否配置了规则代码或规则组代码
        boolean hasRuleCodes =
                ruleExecution.ruleCodes() != null && ruleExecution.ruleCodes().length > 0;
        boolean hasRuleGroupCodes =
                ruleExecution.ruleGroupCodes() != null && ruleExecution.ruleGroupCodes().length > 0;

        return hasRuleCodes || hasRuleGroupCodes;
    }

    /**
     * 获取规则执行注解的描述信息
     *
     * @param ruleExecution 规则执行注解
     * @return 描述信息
     */
    public static String getRuleExecutionDescription(RuleExecution ruleExecution) {
        if (ruleExecution == null) {
            return "null";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("RuleExecution{");

        if (ruleExecution.ruleCodes().length > 0) {
            sb.append("ruleCodes=")
                    .append(String.join(",", ruleExecution.ruleCodes()))
                    .append(", ");
        }

        if (ruleExecution.ruleGroupCodes().length > 0) {
            sb.append("ruleGroupCodes=")
                    .append(String.join(",", ruleExecution.ruleGroupCodes()))
                    .append(", ");
        }

        sb.append("timing=").append(ruleExecution.timing());
        sb.append(", enabled=").append(ruleExecution.enabled());
        sb.append("}");

        return sb.toString();
    }

    /** 私有构造函数，防止实例化 */
    private RuleExecutionUtils() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
}
