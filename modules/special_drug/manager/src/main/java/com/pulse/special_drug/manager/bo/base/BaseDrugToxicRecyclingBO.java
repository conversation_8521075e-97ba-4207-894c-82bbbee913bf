package com.pulse.special_drug.manager.bo.base;

import com.pulse.special_drug.common.enums.RecyclingStatusEnum;
import com.pulse.special_drug.manager.bo.DrugToxicRecyclingBO;
import com.pulse.special_drug.persist.dos.DrugToxicRecycling;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_toxic_recycling")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "f356edc0-5349-3d7b-ae0c-d6dbeecd403f")
public abstract class BaseDrugToxicRecyclingBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 接收时间 */
    @Column(name = "accept_date_time")
    @AutoGenerated(locked = true, uuid = "95745a51-1112-3801-ae2f-afe5fcd8bc10")
    private Date acceptDateTime;

    /** 接收人 */
    @Column(name = "accept_staff_id")
    @AutoGenerated(locked = true, uuid = "273ce3ef-a38c-3613-ad9a-80a70d10a564")
    private String acceptStaffId;

    /** 代办人 */
    @Column(name = "agent_name")
    @AutoGenerated(locked = true, uuid = "6150b198-d7c7-3051-8a79-9adeb014b8cc")
    private String agentName;

    /** 回收数量 */
    @Column(name = "amount")
    @AutoGenerated(locked = true, uuid = "60440897-16fe-3389-8240-938573bd2d8b")
    private Long amount;

    /** 应用ID */
    @Column(name = "application_id")
    @AutoGenerated(locked = true, uuid = "75058f5d-f80f-4ec4-8e84-790027e8c56f")
    private String applicationId;

    /** 审核时间 */
    @Column(name = "audit_date_time")
    @AutoGenerated(locked = true, uuid = "cf0c0261-f419-39c9-8571-54338985ce1e")
    private Date auditDateTime;

    /** 审核人 */
    @Column(name = "audit_staff_id")
    @AutoGenerated(locked = true, uuid = "5d26f86c-0144-346d-801a-13a05333377a")
    private String auditStaffId;

    /** 批次id */
    @Column(name = "batch_inventory_id")
    @AutoGenerated(locked = true, uuid = "e3218904-f736-32ff-90d4-2d905749007f")
    private String batchInventoryId;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "48732461-8dfe-3fa5-b3de-6cca1595810a")
    private Date createdAt;

    /** 销毁时间 */
    @Column(name = "destroyed_date_time")
    @AutoGenerated(locked = true, uuid = "09f58575-e08d-3633-8437-19525eee1277")
    private Date destroyedDateTime;

    /** 销毁人 */
    @Column(name = "destroyed_staff_id")
    @AutoGenerated(locked = true, uuid = "ed5a7396-f61a-3f49-af5c-50630993dc1a")
    private String destroyedStaffId;

    /** 药品产地规格id */
    @Column(name = "drug_origin_specification_id")
    @AutoGenerated(locked = true, uuid = "1c4b29c1-9e57-39dc-8b88-f6563b28ade9")
    private String drugOriginSpecificationId;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "897a019d-fcf3-389a-99d1-7bc90808e1e0")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "399b7126-86ab-4a46-a05e-4be53ff92140")
    @Version
    private Long lockVersion;

    /** 患者id */
    @Column(name = "patient_id")
    @AutoGenerated(locked = true, uuid = "5fa48aa3-e7af-3f24-991b-f236a67b064e")
    private String patientId;

    /** 回收时间 */
    @Column(name = "recycling_date_time")
    @AutoGenerated(locked = true, uuid = "4a5fd66d-8056-32e8-ad2a-4136e2988549")
    private Date recyclingDateTime;

    /** 回收人 */
    @Column(name = "recycling_staff_id")
    @AutoGenerated(locked = true, uuid = "2df0c454-fadc-334d-8c79-a1914d62f1e3")
    private String recyclingStaffId;

    /** 状态 已登记、已审核、已接收、已销毁 */
    @Column(name = "status")
    @AutoGenerated(locked = true, uuid = "b0e493ec-a654-3d03-a565-82fbcc4de2c5")
    @Enumerated(EnumType.STRING)
    private RecyclingStatusEnum status;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "bac40810-8808-3c52-bd11-4b2ce5c9505c")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public DrugToxicRecycling convertToDrugToxicRecycling() {
        DrugToxicRecycling entity = new DrugToxicRecycling();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "lockVersion",
                "drugOriginSpecificationId",
                "batchInventoryId",
                "amount",
                "status",
                "recyclingStaffId",
                "recyclingDateTime",
                "patientId",
                "agentName",
                "auditStaffId",
                "auditDateTime",
                "acceptStaffId",
                "acceptDateTime",
                "destroyedStaffId",
                "destroyedDateTime",
                "applicationId",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public Date getAcceptDateTime() {
        return this.acceptDateTime;
    }

    @AutoGenerated(locked = true)
    public String getAcceptStaffId() {
        return this.acceptStaffId;
    }

    @AutoGenerated(locked = true)
    public String getAgentName() {
        return this.agentName;
    }

    @AutoGenerated(locked = true)
    public Long getAmount() {
        return this.amount;
    }

    @AutoGenerated(locked = true)
    public String getApplicationId() {
        return this.applicationId;
    }

    @AutoGenerated(locked = true)
    public Date getAuditDateTime() {
        return this.auditDateTime;
    }

    @AutoGenerated(locked = true)
    public String getAuditStaffId() {
        return this.auditStaffId;
    }

    @AutoGenerated(locked = true)
    public String getBatchInventoryId() {
        return this.batchInventoryId;
    }

    @AutoGenerated(locked = true)
    public static DrugToxicRecyclingBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugToxicRecyclingBO drugToxicRecycling =
                (DrugToxicRecyclingBO)
                        session.createQuery("from DrugToxicRecyclingBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugToxicRecycling;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Date getDestroyedDateTime() {
        return this.destroyedDateTime;
    }

    @AutoGenerated(locked = true)
    public String getDestroyedStaffId() {
        return this.destroyedStaffId;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginSpecificationId() {
        return this.drugOriginSpecificationId;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getPatientId() {
        return this.patientId;
    }

    @AutoGenerated(locked = true)
    public Date getRecyclingDateTime() {
        return this.recyclingDateTime;
    }

    @AutoGenerated(locked = true)
    public String getRecyclingStaffId() {
        return this.recyclingStaffId;
    }

    @AutoGenerated(locked = true)
    public RecyclingStatusEnum getStatus() {
        return this.status;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setAcceptDateTime(Date acceptDateTime) {
        this.acceptDateTime = acceptDateTime;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setAcceptStaffId(String acceptStaffId) {
        this.acceptStaffId = acceptStaffId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setAgentName(String agentName) {
        this.agentName = agentName;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setAmount(Long amount) {
        this.amount = amount;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setApplicationId(String applicationId) {
        this.applicationId = applicationId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setAuditDateTime(Date auditDateTime) {
        this.auditDateTime = auditDateTime;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setAuditStaffId(String auditStaffId) {
        this.auditStaffId = auditStaffId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setBatchInventoryId(String batchInventoryId) {
        this.batchInventoryId = batchInventoryId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setDestroyedDateTime(Date destroyedDateTime) {
        this.destroyedDateTime = destroyedDateTime;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setDestroyedStaffId(String destroyedStaffId) {
        this.destroyedStaffId = destroyedStaffId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setDrugOriginSpecificationId(String drugOriginSpecificationId) {
        this.drugOriginSpecificationId = drugOriginSpecificationId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setId(String id) {
        this.id = id;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setPatientId(String patientId) {
        this.patientId = patientId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setRecyclingDateTime(Date recyclingDateTime) {
        this.recyclingDateTime = recyclingDateTime;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setRecyclingStaffId(String recyclingStaffId) {
        this.recyclingStaffId = recyclingStaffId;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setStatus(RecyclingStatusEnum status) {
        this.status = status;
        return (DrugToxicRecyclingBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugToxicRecyclingBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugToxicRecyclingBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
