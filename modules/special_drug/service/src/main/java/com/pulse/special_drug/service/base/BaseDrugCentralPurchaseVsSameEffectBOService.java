package com.pulse.special_drug.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.special_drug.manager.bo.*;
import com.pulse.special_drug.manager.bo.DrugCentralPurchaseVsSameEffectBO;
import com.pulse.special_drug.persist.dos.DrugCentralPurchaseVsSameEffect;
import com.pulse.special_drug.persist.dos.DrugSameEffectWhitelist;
import com.pulse.special_drug.service.base.BaseDrugCentralPurchaseVsSameEffectBOService.DeleteDrugCentralPurchaseVsSameEffectBoResult;
import com.pulse.special_drug.service.base.BaseDrugCentralPurchaseVsSameEffectBOService.SaveDrugCentralPurchaseVsSameEffectBoResult;
import com.pulse.special_drug.service.bto.DeleteDrugCentralPurchaseVsSameEffectBto;
import com.pulse.special_drug.service.bto.SaveDrugCentralPurchaseVsSameEffectBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "6e5e840a-f87b-3a2e-9d0f-f26e4375bb72")
public class BaseDrugCentralPurchaseVsSameEffectBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 创建对象:DrugSameEffectWhitelistBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugSameEffectWhitelistBtoOnDuplicateUpdate(
            BaseDrugCentralPurchaseVsSameEffectBOService.SaveDrugCentralPurchaseVsSameEffectBoResult
                    boResult,
            SaveDrugCentralPurchaseVsSameEffectBto saveDrugCentralPurchaseVsSameEffectBto,
            DrugCentralPurchaseVsSameEffectBO drugCentralPurchaseVsSameEffectBO) {
        if (CollectionUtil.isEmpty(
                saveDrugCentralPurchaseVsSameEffectBto.getDrugSameEffectWhitelistBtoList())) {
            saveDrugCentralPurchaseVsSameEffectBto.setDrugSameEffectWhitelistBtoList(List.of());
        }
        drugCentralPurchaseVsSameEffectBO
                .getDrugSameEffectWhitelistBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveDrugCentralPurchaseVsSameEffectBto
                                            .getDrugSameEffectWhitelistBtoList()
                                            .stream()
                                            .filter(
                                                    drugSameEffectWhitelistBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (drugSameEffectWhitelistBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            drugSameEffectWhitelistBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToDrugSameEffectWhitelist());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveDrugCentralPurchaseVsSameEffectBto.getDrugSameEffectWhitelistBtoList())) {
            for (SaveDrugCentralPurchaseVsSameEffectBto.DrugSameEffectWhitelistBto item :
                    saveDrugCentralPurchaseVsSameEffectBto.getDrugSameEffectWhitelistBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugSameEffectWhitelistBO> any =
                        drugCentralPurchaseVsSameEffectBO.getDrugSameEffectWhitelistBOSet().stream()
                                .filter(
                                        drugSameEffectWhitelistBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugSameEffectWhitelistBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugSameEffectWhitelistBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugSameEffectWhitelist());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "personType")) {
                            bo.setPersonType(item.getPersonType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "doctorId")) {
                            bo.setDoctorId(item.getDoctorId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientId")) {
                            bo.setPatientId(item.getPatientId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invalidFlag")) {
                            bo.setInvalidFlag(item.getInvalidFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "warningFlag")) {
                            bo.setWarningFlag(item.getWarningFlag());
                        }
                    } else {
                        DrugSameEffectWhitelistBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugSameEffectWhitelist());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "personType")) {
                            bo.setPersonType(item.getPersonType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "doctorId")) {
                            bo.setDoctorId(item.getDoctorId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientId")) {
                            bo.setPatientId(item.getPatientId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invalidFlag")) {
                            bo.setInvalidFlag(item.getInvalidFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "warningFlag")) {
                            bo.setWarningFlag(item.getWarningFlag());
                        }
                    }
                } else {
                    DrugSameEffectWhitelistBO subBo = new DrugSameEffectWhitelistBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "personType")) {
                        subBo.setPersonType(item.getPersonType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "doctorId")) {
                        subBo.setDoctorId(item.getDoctorId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "patientId")) {
                        subBo.setPatientId(item.getPatientId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "invalidFlag")) {
                        subBo.setInvalidFlag(item.getInvalidFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "warningFlag")) {
                        subBo.setWarningFlag(item.getWarningFlag());
                    }
                    subBo.setDrugCentralPurchaseVsSameEffectBO(drugCentralPurchaseVsSameEffectBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("drug_same_effect_whitelist")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugCentralPurchaseVsSameEffectBO.getDrugSameEffectWhitelistBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugCentralPurchaseVsSameEffectBO
            createSaveDrugCentralPurchaseVsSameEffectOnDuplicateUpdate(
                    SaveDrugCentralPurchaseVsSameEffectBoResult boResult,
                    SaveDrugCentralPurchaseVsSameEffectBto saveDrugCentralPurchaseVsSameEffectBto) {
        DrugCentralPurchaseVsSameEffectBO drugCentralPurchaseVsSameEffectBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveDrugCentralPurchaseVsSameEffectBto.getId() == null);
        if (!allNull && !found) {
            drugCentralPurchaseVsSameEffectBO =
                    DrugCentralPurchaseVsSameEffectBO.getById(
                            saveDrugCentralPurchaseVsSameEffectBto.getId());
            if (drugCentralPurchaseVsSameEffectBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugCentralPurchaseVsSameEffectBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        drugCentralPurchaseVsSameEffectBO
                                .convertToDrugCentralPurchaseVsSameEffect());
                updatedBto.setBto(saveDrugCentralPurchaseVsSameEffectBto);
                updatedBto.setBo(drugCentralPurchaseVsSameEffectBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "centralPurchaseOriginCode")) {
                    drugCentralPurchaseVsSameEffectBO.setCentralPurchaseOriginCode(
                            saveDrugCentralPurchaseVsSameEffectBto.getCentralPurchaseOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "sameEffectOriginCode")) {
                    drugCentralPurchaseVsSameEffectBO.setSameEffectOriginCode(
                            saveDrugCentralPurchaseVsSameEffectBto.getSameEffectOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "createdBy")) {
                    drugCentralPurchaseVsSameEffectBO.setCreatedBy(
                            saveDrugCentralPurchaseVsSameEffectBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "updatedBy")) {
                    drugCentralPurchaseVsSameEffectBO.setUpdatedBy(
                            saveDrugCentralPurchaseVsSameEffectBto.getUpdatedBy());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        drugCentralPurchaseVsSameEffectBO
                                .convertToDrugCentralPurchaseVsSameEffect());
                updatedBto.setBto(saveDrugCentralPurchaseVsSameEffectBto);
                updatedBto.setBo(drugCentralPurchaseVsSameEffectBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "centralPurchaseOriginCode")) {
                    drugCentralPurchaseVsSameEffectBO.setCentralPurchaseOriginCode(
                            saveDrugCentralPurchaseVsSameEffectBto.getCentralPurchaseOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "sameEffectOriginCode")) {
                    drugCentralPurchaseVsSameEffectBO.setSameEffectOriginCode(
                            saveDrugCentralPurchaseVsSameEffectBto.getSameEffectOriginCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "createdBy")) {
                    drugCentralPurchaseVsSameEffectBO.setCreatedBy(
                            saveDrugCentralPurchaseVsSameEffectBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveDrugCentralPurchaseVsSameEffectBto,
                                        "__$validPropertySet"),
                        "updatedBy")) {
                    drugCentralPurchaseVsSameEffectBO.setUpdatedBy(
                            saveDrugCentralPurchaseVsSameEffectBto.getUpdatedBy());
                }
            }
        } else {
            drugCentralPurchaseVsSameEffectBO = new DrugCentralPurchaseVsSameEffectBO();
            if (pkExist) {
                drugCentralPurchaseVsSameEffectBO.setId(
                        saveDrugCentralPurchaseVsSameEffectBto.getId());
            } else {
                drugCentralPurchaseVsSameEffectBO.setId(
                        String.valueOf(
                                this.idGenerator.allocateId(
                                        "drug_central_purchase_vs_same_effect")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugCentralPurchaseVsSameEffectBto, "__$validPropertySet"),
                    "centralPurchaseOriginCode")) {
                drugCentralPurchaseVsSameEffectBO.setCentralPurchaseOriginCode(
                        saveDrugCentralPurchaseVsSameEffectBto.getCentralPurchaseOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugCentralPurchaseVsSameEffectBto, "__$validPropertySet"),
                    "sameEffectOriginCode")) {
                drugCentralPurchaseVsSameEffectBO.setSameEffectOriginCode(
                        saveDrugCentralPurchaseVsSameEffectBto.getSameEffectOriginCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugCentralPurchaseVsSameEffectBto, "__$validPropertySet"),
                    "createdBy")) {
                drugCentralPurchaseVsSameEffectBO.setCreatedBy(
                        saveDrugCentralPurchaseVsSameEffectBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugCentralPurchaseVsSameEffectBto, "__$validPropertySet"),
                    "updatedBy")) {
                drugCentralPurchaseVsSameEffectBO.setUpdatedBy(
                        saveDrugCentralPurchaseVsSameEffectBto.getUpdatedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveDrugCentralPurchaseVsSameEffectBto);
            addedBto.setBo(drugCentralPurchaseVsSameEffectBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugCentralPurchaseVsSameEffectBO;
    }

    /** 删除对象:deleteDrugCentralPurchaseVsSameEffect,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugCentralPurchaseVsSameEffectBO
            deleteDeleteDrugCentralPurchaseVsSameEffectOnMissThrowEx(
                    BaseDrugCentralPurchaseVsSameEffectBOService
                                    .DeleteDrugCentralPurchaseVsSameEffectBoResult
                            boResult,
                    DeleteDrugCentralPurchaseVsSameEffectBto
                            deleteDrugCentralPurchaseVsSameEffectBto) {
        DrugCentralPurchaseVsSameEffectBO drugCentralPurchaseVsSameEffectBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteDrugCentralPurchaseVsSameEffectBto.getId() == null);
        if (!allNull && !found) {
            drugCentralPurchaseVsSameEffectBO =
                    DrugCentralPurchaseVsSameEffectBO.getById(
                            deleteDrugCentralPurchaseVsSameEffectBto.getId());
            found = true;
        }
        if (drugCentralPurchaseVsSameEffectBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugCentralPurchaseVsSameEffectBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteDrugCentralPurchaseVsSameEffectBto);
            deletedBto.setEntity(
                    drugCentralPurchaseVsSameEffectBO.convertToDrugCentralPurchaseVsSameEffect());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugCentralPurchaseVsSameEffectBO;
        }
    }

    /** 功能：删除集采药品竞品对照 */
    @AutoGenerated(locked = true)
    protected DeleteDrugCentralPurchaseVsSameEffectBoResult
            deleteDrugCentralPurchaseVsSameEffectBase(
                    DeleteDrugCentralPurchaseVsSameEffectBto
                            deleteDrugCentralPurchaseVsSameEffectBto) {
        if (deleteDrugCentralPurchaseVsSameEffectBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteDrugCentralPurchaseVsSameEffectBoResult boResult =
                new DeleteDrugCentralPurchaseVsSameEffectBoResult();
        DrugCentralPurchaseVsSameEffectBO drugCentralPurchaseVsSameEffectBO =
                deleteDeleteDrugCentralPurchaseVsSameEffectOnMissThrowEx(
                        boResult, deleteDrugCentralPurchaseVsSameEffectBto);
        boResult.setRootBo(drugCentralPurchaseVsSameEffectBO);
        return boResult;
    }

    /** 功能：保存集采药品竞品对照 */
    @AutoGenerated(locked = true)
    protected SaveDrugCentralPurchaseVsSameEffectBoResult saveDrugCentralPurchaseVsSameEffectBase(
            SaveDrugCentralPurchaseVsSameEffectBto saveDrugCentralPurchaseVsSameEffectBto) {
        if (saveDrugCentralPurchaseVsSameEffectBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveDrugCentralPurchaseVsSameEffectBoResult boResult =
                new SaveDrugCentralPurchaseVsSameEffectBoResult();
        DrugCentralPurchaseVsSameEffectBO drugCentralPurchaseVsSameEffectBO =
                createSaveDrugCentralPurchaseVsSameEffectOnDuplicateUpdate(
                        boResult, saveDrugCentralPurchaseVsSameEffectBto);
        if (drugCentralPurchaseVsSameEffectBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveDrugCentralPurchaseVsSameEffectBto, "__$validPropertySet"),
                    "drugSameEffectWhitelistBtoList")) {
                createDrugSameEffectWhitelistBtoOnDuplicateUpdate(
                        boResult,
                        saveDrugCentralPurchaseVsSameEffectBto,
                        drugCentralPurchaseVsSameEffectBO);
            }
        }
        boResult.setRootBo(drugCentralPurchaseVsSameEffectBO);
        return boResult;
    }

    public static class SaveDrugCentralPurchaseVsSameEffectBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugCentralPurchaseVsSameEffectBO getRootBo() {
            return (DrugCentralPurchaseVsSameEffectBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveDrugCentralPurchaseVsSameEffectBto, DrugCentralPurchaseVsSameEffectBO>
                getCreatedBto(
                        SaveDrugCentralPurchaseVsSameEffectBto
                                saveDrugCentralPurchaseVsSameEffectBto) {
            return this.getAddedResult(saveDrugCentralPurchaseVsSameEffectBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveDrugCentralPurchaseVsSameEffectBto.DrugSameEffectWhitelistBto,
                        DrugSameEffectWhitelistBO>
                getCreatedBto(
                        SaveDrugCentralPurchaseVsSameEffectBto.DrugSameEffectWhitelistBto
                                drugSameEffectWhitelistBto) {
            return this.getAddedResult(drugSameEffectWhitelistBto);
        }

        @AutoGenerated(locked = true)
        public DrugCentralPurchaseVsSameEffect getDeleted_DrugCentralPurchaseVsSameEffect() {
            return (DrugCentralPurchaseVsSameEffect)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugCentralPurchaseVsSameEffect.class));
        }

        @AutoGenerated(locked = true)
        public DrugSameEffectWhitelist getDeleted_DrugSameEffectWhitelist() {
            return (DrugSameEffectWhitelist)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugSameEffectWhitelist.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveDrugCentralPurchaseVsSameEffectBto,
                        DrugCentralPurchaseVsSameEffect,
                        DrugCentralPurchaseVsSameEffectBO>
                getUpdatedBto(
                        SaveDrugCentralPurchaseVsSameEffectBto
                                saveDrugCentralPurchaseVsSameEffectBto) {
            return super.getUpdatedResult(saveDrugCentralPurchaseVsSameEffectBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveDrugCentralPurchaseVsSameEffectBto.DrugSameEffectWhitelistBto,
                        DrugSameEffectWhitelist,
                        DrugSameEffectWhitelistBO>
                getUpdatedBto(
                        SaveDrugCentralPurchaseVsSameEffectBto.DrugSameEffectWhitelistBto
                                drugSameEffectWhitelistBto) {
            return super.getUpdatedResult(drugSameEffectWhitelistBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveDrugCentralPurchaseVsSameEffectBto, DrugCentralPurchaseVsSameEffectBO>
                getUnmodifiedBto(
                        SaveDrugCentralPurchaseVsSameEffectBto
                                saveDrugCentralPurchaseVsSameEffectBto) {
            return super.getUnmodifiedResult(saveDrugCentralPurchaseVsSameEffectBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveDrugCentralPurchaseVsSameEffectBto.DrugSameEffectWhitelistBto,
                        DrugSameEffectWhitelistBO>
                getUnmodifiedBto(
                        SaveDrugCentralPurchaseVsSameEffectBto.DrugSameEffectWhitelistBto
                                drugSameEffectWhitelistBto) {
            return super.getUnmodifiedResult(drugSameEffectWhitelistBto);
        }
    }

    public static class DeleteDrugCentralPurchaseVsSameEffectBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugCentralPurchaseVsSameEffectBO getRootBo() {
            return (DrugCentralPurchaseVsSameEffectBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDrugCentralPurchaseVsSameEffectBto, DrugCentralPurchaseVsSameEffectBO>
                getCreatedBto(
                        DeleteDrugCentralPurchaseVsSameEffectBto
                                deleteDrugCentralPurchaseVsSameEffectBto) {
            return this.getAddedResult(deleteDrugCentralPurchaseVsSameEffectBto);
        }

        @AutoGenerated(locked = true)
        public DrugCentralPurchaseVsSameEffect getDeleted_DrugCentralPurchaseVsSameEffect() {
            return (DrugCentralPurchaseVsSameEffect)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugCentralPurchaseVsSameEffect.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteDrugCentralPurchaseVsSameEffectBto,
                        DrugCentralPurchaseVsSameEffect,
                        DrugCentralPurchaseVsSameEffectBO>
                getUpdatedBto(
                        DeleteDrugCentralPurchaseVsSameEffectBto
                                deleteDrugCentralPurchaseVsSameEffectBto) {
            return super.getUpdatedResult(deleteDrugCentralPurchaseVsSameEffectBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        DeleteDrugCentralPurchaseVsSameEffectBto, DrugCentralPurchaseVsSameEffectBO>
                getUnmodifiedBto(
                        DeleteDrugCentralPurchaseVsSameEffectBto
                                deleteDrugCentralPurchaseVsSameEffectBto) {
            return super.getUnmodifiedResult(deleteDrugCentralPurchaseVsSameEffectBto);
        }
    }
}
