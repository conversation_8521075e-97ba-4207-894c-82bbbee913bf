package com.pulse.diagnosis.service.converter;

import com.pulse.diagnosis.manager.dto.DiagnosisRecordDictionaryExtDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "25cd47fb-1057-34d6-a335-0af102c8aefd")
public class DiagnosisRecordDictionaryExtDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DiagnosisRecordDictionaryExtDto> DiagnosisRecordDictionaryExtDtoConverter(
            List<DiagnosisRecordDictionaryExtDto> diagnosisRecordDictionaryExtDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return diagnosisRecordDictionaryExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
