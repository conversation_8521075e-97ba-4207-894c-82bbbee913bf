package com.pulse.diagnosis.manager.bo;

import com.pulse.diagnosis.manager.bo.base.BaseDiagnosisRecordBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Table(name = "diagnosis_record")
@Entity
@AutoGenerated(locked = false, uuid = "1d0a5c41-7bff-4b0d-91a0-4d211a9906ad|BO|DEFINITION")
public class DiagnosisRecordBO extends BaseDiagnosisRecordBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "1d0a5c41-7bff-4b0d-91a0-4d211a9906ad|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
