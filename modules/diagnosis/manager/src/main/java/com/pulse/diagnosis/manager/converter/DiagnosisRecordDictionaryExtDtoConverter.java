package com.pulse.diagnosis.manager.converter;

import com.pulse.diagnosis.manager.converter.base.DiagnosisRecordDictionaryExtDtoBaseConverter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

@Component
@AutoGenerated(locked = false, uuid = "99877868-307a-4643-b00c-94da06237045|DTO|CONVERTER")
public class DiagnosisRecordDictionaryExtDtoConverter
        extends DiagnosisRecordDictionaryExtDtoBaseConverter {}
