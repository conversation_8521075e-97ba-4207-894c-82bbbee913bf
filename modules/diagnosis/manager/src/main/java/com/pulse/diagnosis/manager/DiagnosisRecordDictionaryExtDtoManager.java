package com.pulse.diagnosis.manager;

import com.pulse.diagnosis.manager.dto.DiagnosisRecordDictionaryExtDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "99877868-307a-4643-b00c-94da06237045|DTO|MANAGER")
public interface DiagnosisRecordDictionaryExtDtoManager {

    @AutoGenerated(locked = true, uuid = "17ffffbb-d246-34f9-9015-8f97fe56b324")
    DiagnosisRecordDictionaryExtDto getById(String id);

    @AutoGenerated(locked = true, uuid = "549ad691-60dc-3f42-8fed-c4ef0403c767")
    List<DiagnosisRecordDictionaryExtDto> getByDiagnosisIds(List<String> diagnosisId);

    @AutoGenerated(locked = true, uuid = "82ed1571-0822-31b7-897a-cbf0114cb537")
    List<DiagnosisRecordDictionaryExtDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "ac91d615-cc3f-31da-ae3c-32d9875c445a")
    List<DiagnosisRecordDictionaryExtDto> getByDiagnosisId(String diagnosisId);
}
