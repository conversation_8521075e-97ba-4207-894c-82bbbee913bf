package com.pulse.dictionary_business.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.common.enums.DiagnosisTypeEnum;
import com.pulse.dictionary_business.common.enums.UseScopeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "46cf0ecb-05ec-4ed3-8213-deedc655acd5|DTO|DEFINITION")
public class DiagnosisDictionaryBaseDto {
    /** 别名 */
    @AutoGenerated(locked = true, uuid = "d2c78541-68ed-4923-80a0-a03c958c5c99")
    private String alias;

    /** 版本号 */
    @AutoGenerated(locked = true, uuid = "f4e4619e-644b-48c9-b10c-71df303a8e0c")
    private String codeVersion;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "1a32e907-a9e5-4943-b482-7d572cc0bcac")
    private Date createdAt;

    /** 创建者ID */
    @AutoGenerated(locked = true, uuid = "2f9e84ce-2231-4ad4-b513-3e7bf3f53ee4")
    private String createdBy;

    /** 诊断分类 */
    @AutoGenerated(locked = true, uuid = "a02ddfe9-4889-4bb1-aeb1-c1f7d2fc4ff6")
    private String diagnosisCategory;

    /** 诊断代码 */
    @AutoGenerated(locked = true, uuid = "2bf2b7b9-d02a-4bec-8126-55503bdad621")
    private String diagnosisCode;

    /** 诊断名称 */
    @AutoGenerated(locked = true, uuid = "cd4b9ab0-eed4-43a7-ad1b-ab0bb1282d26")
    private String diagnosisName;

    /** 诊断类型 */
    @AutoGenerated(locked = true, uuid = "0559c79f-11f1-4f94-9177-271f2ec39e38")
    private DiagnosisTypeEnum diagnosisType;

    /** 诊断使用范围 */
    @AutoGenerated(locked = true, uuid = "e96068db-c59c-4a28-8147-fce2b405cdb7")
    private UseScopeEnum diagnosisUseScope;

    /** 禁用原因 */
    @AutoGenerated(locked = true, uuid = "058fd39e-7fd4-47ce-ba3b-06ddeb7ee6ca")
    private String disabledReason;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "8c3afeb0-56d6-4b28-a8b3-d391904f771c")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "21d97341-0039-4fc4-8c0b-21c187f42ace")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c599d89a-c948-4874-9f89-0700192fbfa2")
    private InputCodeEo inputCode;

    /** 医保诊断编码 */
    @AutoGenerated(locked = true, uuid = "491b017f-0388-40ab-acf5-30803d0b7f08")
    private String insuranceDiagnosisCode;

    /** 医保诊断名称 */
    @AutoGenerated(locked = true, uuid = "1daa0d20-9794-46f3-b829-a33bfcd3a18b")
    private String insuranceDiagnosisName;

    /** 重点病种类别标识 */
    @AutoGenerated(locked = true, uuid = "a3ab8eb6-d6cf-4ea2-9aa4-08d3a4bda518")
    private Boolean keyDiseaseTypeFlag;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "7ec0cc3a-ae56-4bea-9c58-116e9a8042cf")
    private Long lockVersion;

    /** 不允许主诊断标志 （HB6-16715） */
    @AutoGenerated(locked = true, uuid = "064256c9-87a8-4910-8f2a-d02daba7e6d6")
    private Boolean primaryDiagnosisFlag;

    /** 报卡方式 */
    @Valid
    @AutoGenerated(locked = true, uuid = "ce27b0ef-cb52-4722-95cd-bcfad178e178")
    private List<String> reportCardMethodList;

    /** 报卡类型 */
    @AutoGenerated(locked = true, uuid = "775c0fad-8658-44ca-933e-8557c2ac59d5")
    private String reportCardType;

    /** 顺序号 */
    @AutoGenerated(locked = true, uuid = "6ab20e29-3da4-4d63-8846-2f2e6e99c68a")
    private Long sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b88581fe-5873-4511-84c7-5fd52d401063")
    private Date updatedAt;

    /** 更新者ID */
    @AutoGenerated(locked = true, uuid = "a4f63735-a12f-4223-ade3-69317ff0ed0b")
    private String updatedBy;
}
