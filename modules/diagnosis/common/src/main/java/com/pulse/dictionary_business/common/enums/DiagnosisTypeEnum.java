package com.pulse.dictionary_business.common.enums;

import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "a6f77d64-443c-37fc-8f14-5cfb8f4a2998|ENUM|DEFINITION")
public enum DiagnosisTypeEnum {

    /** 西医疾病 */
    WESTERN_MEDICINE_DISEASE(),

    /** 中毒损伤 */
    TOXIC_INJURY(),

    /** 病理 */
    PATHOLOGY(),

    /** 中医疾病 */
    TCM_DISEASE(),

    /** 证型 */
    TCM_SYNDROME_TYPE(),

    /** 治法 */
    THERAPY();

    @AutoGenerated(locked = true)
    DiagnosisTypeEnum() {}
}
