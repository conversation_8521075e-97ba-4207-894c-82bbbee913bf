package com.pulse.diagnosis.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.diagnosis.persist.dos.DiagnosisRecord;
import com.pulse.diagnosis.persist.mapper.DiagnosisRecordDao;
import com.pulse.diagnosis.persist.mapper.mybatis.DiagnosisRecordMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "296dbc71-e6d4-420c-b8c3-a06d485377cd|ENTITY|DAO")
public class DiagnosisRecordDaoImpl implements DiagnosisRecordDao {
    @AutoGenerated(locked = true)
    @Resource
    private DiagnosisRecordMapper diagnosisRecordMapper;

    @AutoGenerated(locked = true, uuid = "3471caa4-9f5f-3b87-af91-2e49d318bdb9")
    @Override
    public List<DiagnosisRecord> getByDiagnosisId(String diagnosisId) {
        QueryWrapper<DiagnosisRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("diagnosis_id", diagnosisId).orderByAsc("id");
        return diagnosisRecordMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "916056e4-664f-368d-ac75-ea49be39076b")
    @Override
    public List<DiagnosisRecord> getByIds(List<String> id) {
        QueryWrapper<DiagnosisRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return diagnosisRecordMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b2edf6f1-c89a-3d29-bb8e-3e275684f3fa")
    @Override
    public List<DiagnosisRecord> getByDiagnosisIds(List<String> diagnosisId) {
        QueryWrapper<DiagnosisRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("diagnosis_id", diagnosisId).orderByAsc("id");
        return diagnosisRecordMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d3df42fd-f8bf-36da-b039-74d3a30b84c4")
    @Override
    public DiagnosisRecord getById(String id) {
        QueryWrapper<DiagnosisRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return diagnosisRecordMapper.selectOne(queryWrapper);
    }
}
