<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.pulse</groupId>
  <artifactId>diagnosis</artifactId>
  <version>3.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <parent>
    <groupId>com.pulse</groupId>
    <artifactId>pulse</artifactId>
    <version>1.0-SNAPSHOT</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>
  <dependencies>
    <dependency>
      <groupId>com.toco</groupId>
      <artifactId>toco-all</artifactId>
      <type>pom</type>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.pulse</groupId>
        <artifactId>pulse-common</artifactId>
        <version>1.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.pulse</groupId>
        <artifactId>diagnosis-common</artifactId>
        <version>3.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.pulse</groupId>
        <artifactId>diagnosis-service</artifactId>
        <version>3.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.pulse</groupId>
        <artifactId>diagnosis-persist</artifactId>
        <version>3.0-SNAPSHOT</version>
      </dependency>
      <dependency>
        <groupId>com.pulse</groupId>
        <artifactId>diagnosis-manager</artifactId>
        <version>3.0-SNAPSHOT</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-archetype-plugin</artifactId>
        <version>3.2.1</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.3.0</version>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <configuration>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <modules>
    <module>persist</module>
    <module>service</module>
    <module>common</module>
    <module>manager</module>
    <module>entrance</module>
  </modules>
</project>
