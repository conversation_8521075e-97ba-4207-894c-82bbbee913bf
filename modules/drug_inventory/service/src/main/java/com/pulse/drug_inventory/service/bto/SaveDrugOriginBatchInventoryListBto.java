package com.pulse.drug_inventory.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> DrugOriginInventory
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "9bf93b74-d4a7-4bd8-ab3d-77031d0992cb|BTO|DEFINITION")
public class SaveDrugOriginBatchInventoryListBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 数量 库存数量，按最小规格单位存储 */
    @AutoGenerated(locked = true, uuid = "656371d6-39d2-47c4-b40a-de3628ac4c3e")
    private BigDecimal amountIncr;

    @Valid
    @AutoGenerated(locked = true, uuid = "5998de9c-a880-4d13-b0f1-fece604f0a66")
    private List<SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto>
            drugOriginBatchInventoryBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4f2b41de-64a9-4fa6-b53f-f33168b288f3")
    private String id;

    /** 在途数量 药库已出库，当前库房未入库的数量 */
    @AutoGenerated(locked = true, uuid = "de5bb743-bb9e-4acf-b056-574695d513f5")
    private BigDecimal inTransitAmountIncr;

    /** 预占数量 待发药数量 */
    @AutoGenerated(locked = true, uuid = "0732b665-5a59-4d71-8582-dba3dc18bbb4")
    private BigDecimal preOccupiedAmountIncr;

    /**
     * 虚库存数量 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
     * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
     * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
     */
    @AutoGenerated(locked = true, uuid = "c2b70fd1-cd92-4d30-b90b-f084cbce7a87")
    private BigDecimal virtualAmountIncr;

    @AutoGenerated(locked = true)
    public void setAmountIncr(BigDecimal amountIncr) {
        this.__$validPropertySet.add("amountIncr");
        this.amountIncr = amountIncr;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginBatchInventoryBtoList(
            List<SaveDrugOriginBatchInventoryListBto.DrugOriginBatchInventoryBto>
                    drugOriginBatchInventoryBtoList) {
        this.__$validPropertySet.add("drugOriginBatchInventoryBtoList");
        this.drugOriginBatchInventoryBtoList = drugOriginBatchInventoryBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInTransitAmountIncr(BigDecimal inTransitAmountIncr) {
        this.__$validPropertySet.add("inTransitAmountIncr");
        this.inTransitAmountIncr = inTransitAmountIncr;
    }

    @AutoGenerated(locked = true)
    public void setPreOccupiedAmountIncr(BigDecimal preOccupiedAmountIncr) {
        this.__$validPropertySet.add("preOccupiedAmountIncr");
        this.preOccupiedAmountIncr = preOccupiedAmountIncr;
    }

    @AutoGenerated(locked = true)
    public void setVirtualAmountIncr(BigDecimal virtualAmountIncr) {
        this.__$validPropertySet.add("virtualAmountIncr");
        this.virtualAmountIncr = virtualAmountIncr;
    }

    /**
     * <b>[源自]</b> DrugOriginBatchInventory
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_IGNORE
     */
    @Getter
    @NoArgsConstructor
    public static class DrugOriginBatchInventoryBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "5c71480a-aeb4-4554-9bef-c46cbf969478")
        private String id;

        /** 库房编码 */
        @AutoGenerated(locked = true, uuid = "ec6f008c-5cfa-4247-a9ea-4eecc0c95f93")
        private String storageCode;

        /** 药品产地编码 冗余存 */
        @AutoGenerated(locked = true, uuid = "25cd8df0-2d1a-4ca9-910f-6e9a82445ce1")
        private String drugOriginCode;

        /** 药品产地规格id */
        @AutoGenerated(locked = true, uuid = "b81cfc22-1bb9-4bfc-a719-7c2130cd6627")
        private String drugOriginSpecificationId;

        /** 批号 */
        @AutoGenerated(locked = true, uuid = "4dbfece2-2db0-49ba-8070-145e89578456")
        private String batchNumber;

        /** 有效期 批次效期 */
        @AutoGenerated(locked = true, uuid = "bcfeb43c-403d-4a1c-a191-831cf3683ede")
        private Date expirationDate;

        /** 数量 库存数量，按最小规格单位存储 */
        @AutoGenerated(locked = true, uuid = "6ec6de02-e41d-4ed2-a3e2-0d38b061d8b9")
        private BigDecimal amount;

        /** 可供标识id */
        @AutoGenerated(locked = true, uuid = "f4d93323-9398-4b66-b9f0-5d90388051be")
        private String supplyId;

        /** 批次id */
        @AutoGenerated(locked = true, uuid = "4f16f842-9ae0-4755-a141-b9db2c967524")
        private String batchId;

        /**
         * 虚库存 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
         * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
         * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
         */
        @AutoGenerated(locked = true, uuid = "1a3209c9-2391-464d-8770-bf9f5c7d8a97")
        private BigDecimal virtualAmount;

        /** gcp编码 gcp药品一物一码，开单发药时也按编码开单发药 */
        @AutoGenerated(locked = true, uuid = "a4192350-7f58-4b8f-897c-4c39dea5f1a9")
        private String gcpCode;

        /** 进价 */
        @AutoGenerated(locked = true, uuid = "6a03fcd6-6c5b-42bd-8fbc-83694a7aa475")
        private BigDecimal purchasePrice;

        /** 零售价 */
        @AutoGenerated(locked = true, uuid = "f5f0f8c2-71cf-4992-bb37-29787196c2c7")
        private BigDecimal retailPrice;

        /** 进货日期 */
        @AutoGenerated(locked = true, uuid = "cb468940-d492-42c4-b591-f04e4bd1ccc1")
        private Date importDateTime;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setStorageCode(String storageCode) {
            this.__$validPropertySet.add("storageCode");
            this.storageCode = storageCode;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginCode(String drugOriginCode) {
            this.__$validPropertySet.add("drugOriginCode");
            this.drugOriginCode = drugOriginCode;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginSpecificationId(String drugOriginSpecificationId) {
            this.__$validPropertySet.add("drugOriginSpecificationId");
            this.drugOriginSpecificationId = drugOriginSpecificationId;
        }

        @AutoGenerated(locked = true)
        public void setBatchNumber(String batchNumber) {
            this.__$validPropertySet.add("batchNumber");
            this.batchNumber = batchNumber;
        }

        @AutoGenerated(locked = true)
        public void setExpirationDate(Date expirationDate) {
            this.__$validPropertySet.add("expirationDate");
            this.expirationDate = expirationDate;
        }

        @AutoGenerated(locked = true)
        public void setAmount(BigDecimal amount) {
            this.__$validPropertySet.add("amount");
            this.amount = amount;
        }

        @AutoGenerated(locked = true)
        public void setSupplyId(String supplyId) {
            this.__$validPropertySet.add("supplyId");
            this.supplyId = supplyId;
        }

        @AutoGenerated(locked = true)
        public void setBatchId(String batchId) {
            this.__$validPropertySet.add("batchId");
            this.batchId = batchId;
        }

        @AutoGenerated(locked = true)
        public void setVirtualAmount(BigDecimal virtualAmount) {
            this.__$validPropertySet.add("virtualAmount");
            this.virtualAmount = virtualAmount;
        }

        @AutoGenerated(locked = true)
        public void setGcpCode(String gcpCode) {
            this.__$validPropertySet.add("gcpCode");
            this.gcpCode = gcpCode;
        }

        @AutoGenerated(locked = true)
        public void setPurchasePrice(BigDecimal purchasePrice) {
            this.__$validPropertySet.add("purchasePrice");
            this.purchasePrice = purchasePrice;
        }

        @AutoGenerated(locked = true)
        public void setRetailPrice(BigDecimal retailPrice) {
            this.__$validPropertySet.add("retailPrice");
            this.retailPrice = retailPrice;
        }

        @AutoGenerated(locked = true)
        public void setImportDateTime(Date importDateTime) {
            this.__$validPropertySet.add("importDateTime");
            this.importDateTime = importDateTime;
        }
    }
}
