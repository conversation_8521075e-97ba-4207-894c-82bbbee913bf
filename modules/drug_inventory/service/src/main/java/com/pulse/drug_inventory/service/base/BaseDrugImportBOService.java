package com.pulse.drug_inventory.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.bo.DrugImportBO;
import com.pulse.drug_inventory.persist.dos.DrugImport;
import com.pulse.drug_inventory.persist.dos.DrugImportDetail;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.DeleteDrugImportDetailBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.MergeDrugImportBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.ResetDrugImportDetailSettleBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.ReturnOrderImportDetailBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.UpdateDrugDetailInvoiceNumberBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.UpdateDrugImportBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.UpdateDrugImportDetailListBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.UpdateDrugImportDetailSettleIdBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugImportBOService.UpdateInvoiceCodeBoResult;
import com.pulse.drug_inventory.service.bto.DeleteDrugImportDetailBto;
import com.pulse.drug_inventory.service.bto.MergeDrugImportBto;
import com.pulse.drug_inventory.service.bto.ResetDrugImportDetailSettleBto;
import com.pulse.drug_inventory.service.bto.ReturnOrderImportDetailBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugDetailInvoiceNumberBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugImportBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugImportDetailListBto;
import com.pulse.drug_inventory.service.bto.UpdateDrugImportDetailSettleIdBto;
import com.pulse.drug_inventory.service.bto.UpdateInvoiceCodeBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "9d95a38d-d6fb-3251-8301-9a794bdfdf6a")
public class BaseDrugImportBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 创建对象:DrugImportDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugImportDetailBtoOnDuplicateUpdate(
            BaseDrugImportBOService.MergeDrugImportBoResult boResult,
            MergeDrugImportBto mergeDrugImportBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isEmpty(mergeDrugImportBto.getDrugImportDetailBtoList())) {
            mergeDrugImportBto.setDrugImportDetailBtoList(List.of());
        }
        drugImportBO
                .getDrugImportDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeDrugImportBto.getDrugImportDetailBtoList().stream()
                                            .filter(
                                                    drugImportDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (drugImportDetailBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            drugImportDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        allNull =
                                                                (drugImportDetailBtoList
                                                                                .getDrugOriginCode()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item
                                                                                    .getDrugOriginCode(),
                                                                            drugImportDetailBtoList
                                                                                    .getDrugOriginCode());
                                                            if (found) {
                                                                return found;
                                                            }
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToDrugImportDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeDrugImportBto.getDrugImportDetailBtoList())) {
            for (MergeDrugImportBto.DrugImportDetailBto item :
                    mergeDrugImportBto.getDrugImportDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugImportDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull = (item.getDrugOriginCode() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugImportDetailBOSet
                                                                        .getDrugOriginCode(),
                                                                item.getDrugOriginCode());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'drug_origin_code'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugImportDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugImportDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginName")) {
                            bo.setDrugOriginName(item.getDrugOriginName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugSpecification")) {
                            bo.setDrugSpecification(item.getDrugSpecification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "specificationUnit")) {
                            bo.setSpecificationUnit(item.getSpecificationUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amountPerPackage")) {
                            bo.setAmountPerPackage(item.getAmountPerPackage());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firmId")) {
                            bo.setFirmId(item.getFirmId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountantFlag")) {
                            bo.setAccountantFlag(item.getAccountantFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "productionDate")) {
                            bo.setProductionDate(item.getProductionDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchaseCost")) {
                            bo.setPurchaseCost(item.getPurchaseCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "discountRate")) {
                            bo.setDiscountRate(item.getDiscountRate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailCost")) {
                            bo.setRetailCost(item.getRetailCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "refundImportDetailId")) {
                            bo.setRefundImportDetailId(item.getRefundImportDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "electronicInvoiceCode")) {
                            bo.setElectronicInvoiceCode(item.getElectronicInvoiceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceCode")) {
                            bo.setInvoiceCode(item.getInvoiceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceDateTime")) {
                            bo.setInvoiceDateTime(item.getInvoiceDateTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugPurchaseDetailId")) {
                            bo.setDrugPurchaseDetailId(item.getDrugPurchaseDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugExportDetailId")) {
                            bo.setDrugExportDetailId(item.getDrugExportDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stocktakingDetailId")) {
                            bo.setStocktakingDetailId(item.getStocktakingDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "platformOrderDistributeId")) {
                            bo.setPlatformOrderDistributeId(item.getPlatformOrderDistributeId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "settleId")) {
                            bo.setSettleId(item.getSettleId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountTypeCode")) {
                            bo.setAccountTypeCode(item.getAccountTypeCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplierId")) {
                            bo.setSupplierId(item.getSupplierId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpFlag")) {
                            bo.setGcpFlag(item.getGcpFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnOrderFlag")) {
                            bo.setReturnOrderFlag(item.getReturnOrderFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpCode")) {
                            bo.setGcpCode(item.getGcpCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "tenderFlag")) {
                            bo.setTenderFlag(item.getTenderFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptResult")) {
                            bo.setAcceptResult(item.getAcceptResult());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptStaffId")) {
                            bo.setAcceptStaffId(item.getAcceptStaffId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importPurchaseDate")) {
                            bo.setImportPurchaseDate(item.getImportPurchaseDate());
                        }
                    } else {
                        DrugImportDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugImportDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginName")) {
                            bo.setDrugOriginName(item.getDrugOriginName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugSpecification")) {
                            bo.setDrugSpecification(item.getDrugSpecification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "specificationUnit")) {
                            bo.setSpecificationUnit(item.getSpecificationUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amountPerPackage")) {
                            bo.setAmountPerPackage(item.getAmountPerPackage());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firmId")) {
                            bo.setFirmId(item.getFirmId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountantFlag")) {
                            bo.setAccountantFlag(item.getAccountantFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "productionDate")) {
                            bo.setProductionDate(item.getProductionDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchaseCost")) {
                            bo.setPurchaseCost(item.getPurchaseCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "discountRate")) {
                            bo.setDiscountRate(item.getDiscountRate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailCost")) {
                            bo.setRetailCost(item.getRetailCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "refundImportDetailId")) {
                            bo.setRefundImportDetailId(item.getRefundImportDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "electronicInvoiceCode")) {
                            bo.setElectronicInvoiceCode(item.getElectronicInvoiceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceCode")) {
                            bo.setInvoiceCode(item.getInvoiceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceDateTime")) {
                            bo.setInvoiceDateTime(item.getInvoiceDateTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugPurchaseDetailId")) {
                            bo.setDrugPurchaseDetailId(item.getDrugPurchaseDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugExportDetailId")) {
                            bo.setDrugExportDetailId(item.getDrugExportDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stocktakingDetailId")) {
                            bo.setStocktakingDetailId(item.getStocktakingDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "platformOrderDistributeId")) {
                            bo.setPlatformOrderDistributeId(item.getPlatformOrderDistributeId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "settleId")) {
                            bo.setSettleId(item.getSettleId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountTypeCode")) {
                            bo.setAccountTypeCode(item.getAccountTypeCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplierId")) {
                            bo.setSupplierId(item.getSupplierId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpFlag")) {
                            bo.setGcpFlag(item.getGcpFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnOrderFlag")) {
                            bo.setReturnOrderFlag(item.getReturnOrderFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "gcpCode")) {
                            bo.setGcpCode(item.getGcpCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "tenderFlag")) {
                            bo.setTenderFlag(item.getTenderFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptResult")) {
                            bo.setAcceptResult(item.getAcceptResult());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptStaffId")) {
                            bo.setAcceptStaffId(item.getAcceptStaffId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importPurchaseDate")) {
                            bo.setImportPurchaseDate(item.getImportPurchaseDate());
                        }
                    }
                } else {
                    DrugImportDetailBO subBo = new DrugImportDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginCode")) {
                        subBo.setDrugOriginCode(item.getDrugOriginCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginName")) {
                        subBo.setDrugOriginName(item.getDrugOriginName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugSpecification")) {
                        subBo.setDrugSpecification(item.getDrugSpecification());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "specificationUnit")) {
                        subBo.setSpecificationUnit(item.getSpecificationUnit());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amountPerPackage")) {
                        subBo.setAmountPerPackage(item.getAmountPerPackage());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "firmId")) {
                        subBo.setFirmId(item.getFirmId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amount")) {
                        subBo.setAmount(item.getAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "accountantFlag")) {
                        subBo.setAccountantFlag(item.getAccountantFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "productionDate")) {
                        subBo.setProductionDate(item.getProductionDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchaseCost")) {
                        subBo.setPurchaseCost(item.getPurchaseCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "discountRate")) {
                        subBo.setDiscountRate(item.getDiscountRate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailCost")) {
                        subBo.setRetailCost(item.getRetailCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "remark")) {
                        subBo.setRemark(item.getRemark());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "refundImportDetailId")) {
                        subBo.setRefundImportDetailId(item.getRefundImportDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchInventoryId")) {
                        subBo.setBatchInventoryId(item.getBatchInventoryId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "electronicInvoiceCode")) {
                        subBo.setElectronicInvoiceCode(item.getElectronicInvoiceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "invoiceCode")) {
                        subBo.setInvoiceCode(item.getInvoiceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "invoiceDateTime")) {
                        subBo.setInvoiceDateTime(item.getInvoiceDateTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugPurchaseDetailId")) {
                        subBo.setDrugPurchaseDetailId(item.getDrugPurchaseDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugExportDetailId")) {
                        subBo.setDrugExportDetailId(item.getDrugExportDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stocktakingDetailId")) {
                        subBo.setStocktakingDetailId(item.getStocktakingDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "platformOrderDistributeId")) {
                        subBo.setPlatformOrderDistributeId(item.getPlatformOrderDistributeId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "settleId")) {
                        subBo.setSettleId(item.getSettleId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "accountTypeCode")) {
                        subBo.setAccountTypeCode(item.getAccountTypeCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "supplierId")) {
                        subBo.setSupplierId(item.getSupplierId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "gcpFlag")) {
                        subBo.setGcpFlag(item.getGcpFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "returnOrderFlag")) {
                        subBo.setReturnOrderFlag(item.getReturnOrderFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "gcpCode")) {
                        subBo.setGcpCode(item.getGcpCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "tenderFlag")) {
                        subBo.setTenderFlag(item.getTenderFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptResult")) {
                        subBo.setAcceptResult(item.getAcceptResult());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptStaffId")) {
                        subBo.setAcceptStaffId(item.getAcceptStaffId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "importPurchaseDate")) {
                        subBo.setImportPurchaseDate(item.getImportPurchaseDate());
                    }
                    subBo.setDrugImportBO(drugImportBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("drug_import_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugImportBO.getDrugImportDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugImportBO createMergeDrugImportOnDuplicateUpdate(
            MergeDrugImportBoResult boResult, MergeDrugImportBto mergeDrugImportBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeDrugImportBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(mergeDrugImportBto.getId());
            if (drugImportBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull = (mergeDrugImportBto.getDocumentNumber() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getByDocumentNumber(mergeDrugImportBto.getDocumentNumber());
            if (drugImportBO != null) {
                matchedUkName += "(";
                matchedUkName += "'document_number'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (drugImportBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugImportBO.convertToDrugImport());
                updatedBto.setBto(mergeDrugImportBto);
                updatedBto.setBo(drugImportBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "documentNumber")) {
                    drugImportBO.setDocumentNumber(mergeDrugImportBto.getDocumentNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugImportBO.setStorageCode(mergeDrugImportBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "exportImportCode")) {
                    drugImportBO.setExportImportCode(mergeDrugImportBto.getExportImportCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountantStatus")) {
                    drugImportBO.setAccountantStatus(mergeDrugImportBto.getAccountantStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "payFlag")) {
                    drugImportBO.setPayFlag(mergeDrugImportBto.getPayFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "refundFlag")) {
                    drugImportBO.setRefundFlag(mergeDrugImportBto.getRefundFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "redFlag")) {
                    drugImportBO.setRedFlag(mergeDrugImportBto.getRedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "acceptanceStatus")) {
                    drugImportBO.setAcceptanceStatus(mergeDrugImportBto.getAcceptanceStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugImportBO.setApplyStaffId(mergeDrugImportBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "deliveryDepartmentCode")) {
                    drugImportBO.setDeliveryDepartmentCode(
                            mergeDrugImportBto.getDeliveryDepartmentCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "gcpFlag")) {
                    drugImportBO.setGcpFlag(mergeDrugImportBto.getGcpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountantStaffId")) {
                    drugImportBO.setAccountantStaffId(mergeDrugImportBto.getAccountantStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountantDateTime")) {
                    drugImportBO.setAccountantDateTime(mergeDrugImportBto.getAccountantDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "payDateTime")) {
                    drugImportBO.setPayDateTime(mergeDrugImportBto.getPayDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "cost")) {
                    drugImportBO.setCost(mergeDrugImportBto.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "charge")) {
                    drugImportBO.setCharge(mergeDrugImportBto.getCharge());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "additionalCost")) {
                    drugImportBO.setAdditionalCost(mergeDrugImportBto.getAdditionalCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "supplierId")) {
                    drugImportBO.setSupplierId(mergeDrugImportBto.getSupplierId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "chargeGrade")) {
                    drugImportBO.setChargeGrade(mergeDrugImportBto.getChargeGrade());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "financeDocumentNumber")) {
                    drugImportBO.setFinanceDocumentNumber(
                            mergeDrugImportBto.getFinanceDocumentNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "drugPurchaseId")) {
                    drugImportBO.setDrugPurchaseId(mergeDrugImportBto.getDrugPurchaseId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseNumber")) {
                    drugImportBO.setPurchaseNumber(mergeDrugImportBto.getPurchaseNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "importDate")) {
                    drugImportBO.setImportDate(mergeDrugImportBto.getImportDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "drugExportId")) {
                    drugImportBO.setDrugExportId(mergeDrugImportBto.getDrugExportId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "stocktakingId")) {
                    drugImportBO.setStocktakingId(mergeDrugImportBto.getStocktakingId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "platformOrderId")) {
                    drugImportBO.setPlatformOrderId(mergeDrugImportBto.getPlatformOrderId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseType")) {
                    drugImportBO.setPurchaseType(mergeDrugImportBto.getPurchaseType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseDocumentType")) {
                    drugImportBO.setPurchaseDocumentType(
                            mergeDrugImportBto.getPurchaseDocumentType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseStorageCode")) {
                    drugImportBO.setPurchaseStorageCode(
                            mergeDrugImportBto.getPurchaseStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "refundImportId")) {
                    drugImportBO.setRefundImportId(mergeDrugImportBto.getRefundImportId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "remark")) {
                    drugImportBO.setRemark(mergeDrugImportBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountingPeriodId")) {
                    drugImportBO.setAccountingPeriodId(mergeDrugImportBto.getAccountingPeriodId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugImportBO.convertToDrugImport());
                updatedBto.setBto(mergeDrugImportBto);
                updatedBto.setBo(drugImportBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "documentNumber")) {
                    drugImportBO.setDocumentNumber(mergeDrugImportBto.getDocumentNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugImportBO.setStorageCode(mergeDrugImportBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "exportImportCode")) {
                    drugImportBO.setExportImportCode(mergeDrugImportBto.getExportImportCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountantStatus")) {
                    drugImportBO.setAccountantStatus(mergeDrugImportBto.getAccountantStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "payFlag")) {
                    drugImportBO.setPayFlag(mergeDrugImportBto.getPayFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "refundFlag")) {
                    drugImportBO.setRefundFlag(mergeDrugImportBto.getRefundFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "redFlag")) {
                    drugImportBO.setRedFlag(mergeDrugImportBto.getRedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "acceptanceStatus")) {
                    drugImportBO.setAcceptanceStatus(mergeDrugImportBto.getAcceptanceStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugImportBO.setApplyStaffId(mergeDrugImportBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "deliveryDepartmentCode")) {
                    drugImportBO.setDeliveryDepartmentCode(
                            mergeDrugImportBto.getDeliveryDepartmentCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "gcpFlag")) {
                    drugImportBO.setGcpFlag(mergeDrugImportBto.getGcpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountantStaffId")) {
                    drugImportBO.setAccountantStaffId(mergeDrugImportBto.getAccountantStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountantDateTime")) {
                    drugImportBO.setAccountantDateTime(mergeDrugImportBto.getAccountantDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "payDateTime")) {
                    drugImportBO.setPayDateTime(mergeDrugImportBto.getPayDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "cost")) {
                    drugImportBO.setCost(mergeDrugImportBto.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "charge")) {
                    drugImportBO.setCharge(mergeDrugImportBto.getCharge());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "additionalCost")) {
                    drugImportBO.setAdditionalCost(mergeDrugImportBto.getAdditionalCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "supplierId")) {
                    drugImportBO.setSupplierId(mergeDrugImportBto.getSupplierId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "chargeGrade")) {
                    drugImportBO.setChargeGrade(mergeDrugImportBto.getChargeGrade());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "financeDocumentNumber")) {
                    drugImportBO.setFinanceDocumentNumber(
                            mergeDrugImportBto.getFinanceDocumentNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "drugPurchaseId")) {
                    drugImportBO.setDrugPurchaseId(mergeDrugImportBto.getDrugPurchaseId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseNumber")) {
                    drugImportBO.setPurchaseNumber(mergeDrugImportBto.getPurchaseNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "importDate")) {
                    drugImportBO.setImportDate(mergeDrugImportBto.getImportDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "drugExportId")) {
                    drugImportBO.setDrugExportId(mergeDrugImportBto.getDrugExportId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "stocktakingId")) {
                    drugImportBO.setStocktakingId(mergeDrugImportBto.getStocktakingId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "platformOrderId")) {
                    drugImportBO.setPlatformOrderId(mergeDrugImportBto.getPlatformOrderId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseType")) {
                    drugImportBO.setPurchaseType(mergeDrugImportBto.getPurchaseType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseDocumentType")) {
                    drugImportBO.setPurchaseDocumentType(
                            mergeDrugImportBto.getPurchaseDocumentType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "purchaseStorageCode")) {
                    drugImportBO.setPurchaseStorageCode(
                            mergeDrugImportBto.getPurchaseStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "refundImportId")) {
                    drugImportBO.setRefundImportId(mergeDrugImportBto.getRefundImportId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "remark")) {
                    drugImportBO.setRemark(mergeDrugImportBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugImportBto, "__$validPropertySet"),
                        "accountingPeriodId")) {
                    drugImportBO.setAccountingPeriodId(mergeDrugImportBto.getAccountingPeriodId());
                }
            }
        } else {
            drugImportBO = new DrugImportBO();
            if (pkExist) {
                drugImportBO.setId(mergeDrugImportBto.getId());
            } else {
                drugImportBO.setId(String.valueOf(this.idGenerator.allocateId("drug_import")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "documentNumber")) {
                drugImportBO.setDocumentNumber(mergeDrugImportBto.getDocumentNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "storageCode")) {
                drugImportBO.setStorageCode(mergeDrugImportBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "exportImportCode")) {
                drugImportBO.setExportImportCode(mergeDrugImportBto.getExportImportCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "accountantStatus")) {
                drugImportBO.setAccountantStatus(mergeDrugImportBto.getAccountantStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "payFlag")) {
                drugImportBO.setPayFlag(mergeDrugImportBto.getPayFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "refundFlag")) {
                drugImportBO.setRefundFlag(mergeDrugImportBto.getRefundFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "redFlag")) {
                drugImportBO.setRedFlag(mergeDrugImportBto.getRedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "acceptanceStatus")) {
                drugImportBO.setAcceptanceStatus(mergeDrugImportBto.getAcceptanceStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "applyStaffId")) {
                drugImportBO.setApplyStaffId(mergeDrugImportBto.getApplyStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "deliveryDepartmentCode")) {
                drugImportBO.setDeliveryDepartmentCode(
                        mergeDrugImportBto.getDeliveryDepartmentCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "gcpFlag")) {
                drugImportBO.setGcpFlag(mergeDrugImportBto.getGcpFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "accountantStaffId")) {
                drugImportBO.setAccountantStaffId(mergeDrugImportBto.getAccountantStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "accountantDateTime")) {
                drugImportBO.setAccountantDateTime(mergeDrugImportBto.getAccountantDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "payDateTime")) {
                drugImportBO.setPayDateTime(mergeDrugImportBto.getPayDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "cost")) {
                drugImportBO.setCost(mergeDrugImportBto.getCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "charge")) {
                drugImportBO.setCharge(mergeDrugImportBto.getCharge());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "additionalCost")) {
                drugImportBO.setAdditionalCost(mergeDrugImportBto.getAdditionalCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "supplierId")) {
                drugImportBO.setSupplierId(mergeDrugImportBto.getSupplierId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "chargeGrade")) {
                drugImportBO.setChargeGrade(mergeDrugImportBto.getChargeGrade());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "financeDocumentNumber")) {
                drugImportBO.setFinanceDocumentNumber(
                        mergeDrugImportBto.getFinanceDocumentNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "drugPurchaseId")) {
                drugImportBO.setDrugPurchaseId(mergeDrugImportBto.getDrugPurchaseId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "purchaseNumber")) {
                drugImportBO.setPurchaseNumber(mergeDrugImportBto.getPurchaseNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "importDate")) {
                drugImportBO.setImportDate(mergeDrugImportBto.getImportDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "drugExportId")) {
                drugImportBO.setDrugExportId(mergeDrugImportBto.getDrugExportId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "stocktakingId")) {
                drugImportBO.setStocktakingId(mergeDrugImportBto.getStocktakingId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "platformOrderId")) {
                drugImportBO.setPlatformOrderId(mergeDrugImportBto.getPlatformOrderId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "purchaseType")) {
                drugImportBO.setPurchaseType(mergeDrugImportBto.getPurchaseType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "purchaseDocumentType")) {
                drugImportBO.setPurchaseDocumentType(mergeDrugImportBto.getPurchaseDocumentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "purchaseStorageCode")) {
                drugImportBO.setPurchaseStorageCode(mergeDrugImportBto.getPurchaseStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "refundImportId")) {
                drugImportBO.setRefundImportId(mergeDrugImportBto.getRefundImportId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "remark")) {
                drugImportBO.setRemark(mergeDrugImportBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "accountingPeriodId")) {
                drugImportBO.setAccountingPeriodId(mergeDrugImportBto.getAccountingPeriodId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeDrugImportBto);
            addedBto.setBo(drugImportBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugImportBO;
    }

    /** 删除入库单明细 */
    @AutoGenerated(locked = true)
    protected DeleteDrugImportDetailBoResult deleteDrugImportDetailBase(
            DeleteDrugImportDetailBto deleteDrugImportDetailBto) {
        if (deleteDrugImportDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteDrugImportDetailBoResult boResult = new DeleteDrugImportDetailBoResult();
        DrugImportBO drugImportBO =
                updateDeleteDrugImportDetailOnMissThrowEx(boResult, deleteDrugImportDetailBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteDrugImportDetailBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                deleteDrugImportDetailBtoOnMissThrowEx(
                        boResult, deleteDrugImportDetailBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 删除对象:drugImportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteDrugImportDetailBtoOnMissThrowEx(
            DeleteDrugImportDetailBoResult boResult,
            DeleteDrugImportDetailBto deleteDrugImportDetailBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isNotEmpty(deleteDrugImportDetailBto.getDrugImportDetailBtoList())) {
            for (DeleteDrugImportDetailBto.DrugImportDetailBto item :
                    deleteDrugImportDetailBto.getDrugImportDetailBtoList()) {
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugImportDetailBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    drugImportBO.getDrugImportDetailBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToDrugImportDetail());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 保存药品入库单 */
    @AutoGenerated(locked = true)
    protected MergeDrugImportBoResult mergeDrugImportBase(MergeDrugImportBto mergeDrugImportBto) {
        if (mergeDrugImportBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeDrugImportBoResult boResult = new MergeDrugImportBoResult();
        DrugImportBO drugImportBO =
                createMergeDrugImportOnDuplicateUpdate(boResult, mergeDrugImportBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDrugImportBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                createDrugImportDetailBtoOnDuplicateUpdate(
                        boResult, mergeDrugImportBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 功能：结算单作废时重置入库单明细结算相关状态 */
    @AutoGenerated(locked = true)
    protected ResetDrugImportDetailSettleBoResult resetDrugImportDetailSettleBase(
            ResetDrugImportDetailSettleBto resetDrugImportDetailSettleBto) {
        if (resetDrugImportDetailSettleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ResetDrugImportDetailSettleBoResult boResult = new ResetDrugImportDetailSettleBoResult();
        DrugImportBO drugImportBO =
                updateResetDrugImportDetailSettleOnMissThrowEx(
                        boResult, resetDrugImportDetailSettleBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    resetDrugImportDetailSettleBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                updateDrugImportDetailBtoOnMissThrowEx(
                        boResult, resetDrugImportDetailSettleBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 功能：应付款结算-已结算退单时更新入库单明细退单标志 */
    @AutoGenerated(locked = true)
    protected ReturnOrderImportDetailBoResult returnOrderImportDetailBase(
            ReturnOrderImportDetailBto returnOrderImportDetailBto) {
        if (returnOrderImportDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ReturnOrderImportDetailBoResult boResult = new ReturnOrderImportDetailBoResult();
        DrugImportBO drugImportBO =
                updateReturnOrderImportDetailOnMissThrowEx(boResult, returnOrderImportDetailBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    returnOrderImportDetailBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                updateDrugImportDetailBtoOnMissThrowEx(
                        boResult, returnOrderImportDetailBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 更新对象:deleteDrugImportDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateDeleteDrugImportDetailOnMissThrowEx(
            BaseDrugImportBOService.DeleteDrugImportDetailBoResult boResult,
            DeleteDrugImportDetailBto deleteDrugImportDetailBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteDrugImportDetailBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(deleteDrugImportDetailBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(deleteDrugImportDetailBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugImportBO;
        }
    }

    /** 更新入库单明细发票号 */
    @AutoGenerated(locked = true)
    protected UpdateDrugDetailInvoiceNumberBoResult updateDrugDetailInvoiceNumberBase(
            UpdateDrugDetailInvoiceNumberBto updateDrugDetailInvoiceNumberBto) {
        if (updateDrugDetailInvoiceNumberBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugDetailInvoiceNumberBoResult boResult =
                new UpdateDrugDetailInvoiceNumberBoResult();
        DrugImportBO drugImportBO =
                updateUpdateDrugDetailInvoiceNumberOnMissThrowEx(
                        boResult, updateDrugDetailInvoiceNumberBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugDetailInvoiceNumberBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                updateDrugImportDetailBtoOnMissThrowEx(
                        boResult, updateDrugDetailInvoiceNumberBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 更新药品入库单 */
    @AutoGenerated(locked = true)
    protected UpdateDrugImportBoResult updateDrugImportBase(
            UpdateDrugImportBto updateDrugImportBto) {
        if (updateDrugImportBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugImportBoResult boResult = new UpdateDrugImportBoResult();
        DrugImportBO drugImportBO =
                updateUpdateDrugImportOnMissThrowEx(boResult, updateDrugImportBto);
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 更新对象:drugImportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugImportDetailBtoOnMissThrowEx(
            BaseDrugImportBOService.UpdateInvoiceCodeBoResult boResult,
            UpdateInvoiceCodeBto updateInvoiceCodeBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isNotEmpty(updateInvoiceCodeBto.getDrugImportDetailBtoList())) {
            for (UpdateInvoiceCodeBto.DrugImportDetailBto bto :
                    updateInvoiceCodeBto.getDrugImportDetailBtoList()) {
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugImportDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugImportDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugImportDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "invoiceCode")) {
                        bo.setInvoiceCode(bto.getInvoiceCode());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:drugImportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugImportDetailBtoOnMissThrowEx(
            BaseDrugImportBOService.UpdateDrugImportDetailSettleIdBoResult boResult,
            UpdateDrugImportDetailSettleIdBto updateDrugImportDetailSettleIdBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isNotEmpty(
                updateDrugImportDetailSettleIdBto.getDrugImportDetailBtoList())) {
            for (UpdateDrugImportDetailSettleIdBto.DrugImportDetailBto bto :
                    updateDrugImportDetailSettleIdBto.getDrugImportDetailBtoList()) {
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugImportDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugImportDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugImportDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "settleId")) {
                        bo.setSettleId(bto.getSettleId());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:drugImportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugImportDetailBtoOnMissThrowEx(
            BaseDrugImportBOService.ResetDrugImportDetailSettleBoResult boResult,
            ResetDrugImportDetailSettleBto resetDrugImportDetailSettleBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isNotEmpty(
                resetDrugImportDetailSettleBto.getDrugImportDetailBtoList())) {
            for (ResetDrugImportDetailSettleBto.DrugImportDetailBto bto :
                    resetDrugImportDetailSettleBto.getDrugImportDetailBtoList()) {
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugImportDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugImportDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugImportDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "settleId")) {
                        bo.setSettleId(bto.getSettleId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "returnOrderFlag")) {
                        bo.setReturnOrderFlag(bto.getReturnOrderFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:drugImportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugImportDetailBtoOnMissThrowEx(
            BaseDrugImportBOService.ReturnOrderImportDetailBoResult boResult,
            ReturnOrderImportDetailBto returnOrderImportDetailBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isNotEmpty(returnOrderImportDetailBto.getDrugImportDetailBtoList())) {
            for (ReturnOrderImportDetailBto.DrugImportDetailBto bto :
                    returnOrderImportDetailBto.getDrugImportDetailBtoList()) {
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugImportDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugImportDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugImportDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "returnOrderFlag")) {
                        bo.setReturnOrderFlag(bto.getReturnOrderFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:drugImportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugImportDetailBtoOnMissThrowEx(
            BaseDrugImportBOService.UpdateDrugDetailInvoiceNumberBoResult boResult,
            UpdateDrugDetailInvoiceNumberBto updateDrugDetailInvoiceNumberBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isNotEmpty(
                updateDrugDetailInvoiceNumberBto.getDrugImportDetailBtoList())) {
            for (UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto bto :
                    updateDrugDetailInvoiceNumberBto.getDrugImportDetailBtoList()) {
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugImportDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugImportDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugImportDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "electronicInvoiceCode")) {
                        bo.setElectronicInvoiceCode(bto.getElectronicInvoiceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "invoiceCode")) {
                        bo.setInvoiceCode(bto.getInvoiceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "invoiceDateTime")) {
                        bo.setInvoiceDateTime(bto.getInvoiceDateTime());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:drugImportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDrugImportDetailBtoOnMissThrowEx(
            BaseDrugImportBOService.UpdateDrugImportDetailListBoResult boResult,
            UpdateDrugImportDetailListBto updateDrugImportDetailListBto,
            DrugImportBO drugImportBO) {
        if (CollectionUtil.isNotEmpty(updateDrugImportDetailListBto.getDrugImportDetailBtoList())) {
            for (UpdateDrugImportDetailListBto.DrugImportDetailBto bto :
                    updateDrugImportDetailListBto.getDrugImportDetailBtoList()) {
                Optional<DrugImportDetailBO> any =
                        drugImportBO.getDrugImportDetailBOSet().stream()
                                .filter(
                                        drugImportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                drugImportDetailBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    DrugImportDetailBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToDrugImportDetail());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "batchNumber")) {
                        bo.setBatchNumber(bto.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "batchInventoryId")) {
                        bo.setBatchInventoryId(bto.getBatchInventoryId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "invoiceCode")) {
                        bo.setInvoiceCode(bto.getInvoiceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "invoiceDateTime")) {
                        bo.setInvoiceDateTime(bto.getInvoiceDateTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "importPurchaseDate")) {
                        bo.setImportPurchaseDate(bto.getImportPurchaseDate());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新药品入库单明细列表 */
    @AutoGenerated(locked = true)
    protected UpdateDrugImportDetailListBoResult updateDrugImportDetailListBase(
            UpdateDrugImportDetailListBto updateDrugImportDetailListBto) {
        if (updateDrugImportDetailListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugImportDetailListBoResult boResult = new UpdateDrugImportDetailListBoResult();
        DrugImportBO drugImportBO =
                updateUpdateDrugImportDetailListOnMissThrowEx(
                        boResult, updateDrugImportDetailListBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugImportDetailListBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                updateDrugImportDetailBtoOnMissThrowEx(
                        boResult, updateDrugImportDetailListBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 功能：修改入库单明细结算单id */
    @AutoGenerated(locked = true)
    protected UpdateDrugImportDetailSettleIdBoResult updateDrugImportDetailSettleIdBase(
            UpdateDrugImportDetailSettleIdBto updateDrugImportDetailSettleIdBto) {
        if (updateDrugImportDetailSettleIdBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateDrugImportDetailSettleIdBoResult boResult =
                new UpdateDrugImportDetailSettleIdBoResult();
        DrugImportBO drugImportBO =
                updateUpdateDrugImportDetailSettleIdOnMissThrowEx(
                        boResult, updateDrugImportDetailSettleIdBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateDrugImportDetailSettleIdBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                updateDrugImportDetailBtoOnMissThrowEx(
                        boResult, updateDrugImportDetailSettleIdBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 功能：修改发票号 */
    @AutoGenerated(locked = true)
    protected UpdateInvoiceCodeBoResult updateInvoiceCodeBase(
            UpdateInvoiceCodeBto updateInvoiceCodeBto) {
        if (updateInvoiceCodeBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateInvoiceCodeBoResult boResult = new UpdateInvoiceCodeBoResult();
        DrugImportBO drugImportBO =
                updateUpdateInvoiceCodeOnMissThrowEx(boResult, updateInvoiceCodeBto);
        if (drugImportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateInvoiceCodeBto, "__$validPropertySet"),
                    "drugImportDetailBtoList")) {
                updateDrugImportDetailBtoOnMissThrowEx(
                        boResult, updateInvoiceCodeBto, drugImportBO);
            }
        }
        boResult.setRootBo(drugImportBO);
        return boResult;
    }

    /** 更新对象:resetDrugImportDetailSettle,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateResetDrugImportDetailSettleOnMissThrowEx(
            ResetDrugImportDetailSettleBoResult boResult,
            ResetDrugImportDetailSettleBto resetDrugImportDetailSettleBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (resetDrugImportDetailSettleBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(resetDrugImportDetailSettleBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(resetDrugImportDetailSettleBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugImportBO;
        }
    }

    /** 更新对象:returnOrderImportDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateReturnOrderImportDetailOnMissThrowEx(
            ReturnOrderImportDetailBoResult boResult,
            ReturnOrderImportDetailBto returnOrderImportDetailBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (returnOrderImportDetailBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(returnOrderImportDetailBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(returnOrderImportDetailBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugImportBO;
        }
    }

    /** 更新对象:updateDrugDetailInvoiceNumber,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateUpdateDrugDetailInvoiceNumberOnMissThrowEx(
            UpdateDrugDetailInvoiceNumberBoResult boResult,
            UpdateDrugDetailInvoiceNumberBto updateDrugDetailInvoiceNumberBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugDetailInvoiceNumberBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(updateDrugDetailInvoiceNumberBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(updateDrugDetailInvoiceNumberBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugImportBO;
        }
    }

    /** 更新对象:updateDrugImportDetailList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateUpdateDrugImportDetailListOnMissThrowEx(
            UpdateDrugImportDetailListBoResult boResult,
            UpdateDrugImportDetailListBto updateDrugImportDetailListBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugImportDetailListBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(updateDrugImportDetailListBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(updateDrugImportDetailListBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugImportBO;
        }
    }

    /** 更新对象:updateDrugImportDetailSettleId,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateUpdateDrugImportDetailSettleIdOnMissThrowEx(
            UpdateDrugImportDetailSettleIdBoResult boResult,
            UpdateDrugImportDetailSettleIdBto updateDrugImportDetailSettleIdBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugImportDetailSettleIdBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(updateDrugImportDetailSettleIdBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(updateDrugImportDetailSettleIdBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugImportBO;
        }
    }

    /** 更新对象:updateDrugImport,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateUpdateDrugImportOnMissThrowEx(
            BaseDrugImportBOService.UpdateDrugImportBoResult boResult,
            UpdateDrugImportBto updateDrugImportBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateDrugImportBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(updateDrugImportBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(updateDrugImportBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "accountantStatus")) {
                drugImportBO.setAccountantStatus(updateDrugImportBto.getAccountantStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "payFlag")) {
                drugImportBO.setPayFlag(updateDrugImportBto.getPayFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "acceptanceStatus")) {
                drugImportBO.setAcceptanceStatus(updateDrugImportBto.getAcceptanceStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "accountantStaffId")) {
                drugImportBO.setAccountantStaffId(updateDrugImportBto.getAccountantStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "accountantDateTime")) {
                drugImportBO.setAccountantDateTime(updateDrugImportBto.getAccountantDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "payDateTime")) {
                drugImportBO.setPayDateTime(updateDrugImportBto.getPayDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "financeDocumentNumber")) {
                drugImportBO.setFinanceDocumentNumber(
                        updateDrugImportBto.getFinanceDocumentNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "drugExportId")) {
                drugImportBO.setDrugExportId(updateDrugImportBto.getDrugExportId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "stocktakingId")) {
                drugImportBO.setStocktakingId(updateDrugImportBto.getStocktakingId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "refundImportId")) {
                drugImportBO.setRefundImportId(updateDrugImportBto.getRefundImportId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateDrugImportBto, "__$validPropertySet"),
                    "accountingPeriodId")) {
                drugImportBO.setAccountingPeriodId(updateDrugImportBto.getAccountingPeriodId());
            }
            return drugImportBO;
        }
    }

    /** 更新对象:updateInvoiceCode,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugImportBO updateUpdateInvoiceCodeOnMissThrowEx(
            UpdateInvoiceCodeBoResult boResult, UpdateInvoiceCodeBto updateInvoiceCodeBto) {
        DrugImportBO drugImportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateInvoiceCodeBto.getId() == null);
        if (!allNull && !found) {
            drugImportBO = DrugImportBO.getById(updateInvoiceCodeBto.getId());
            found = true;
        }
        if (drugImportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugImportBO.convertToDrugImport());
            updatedBto.setBto(updateInvoiceCodeBto);
            updatedBto.setBo(drugImportBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugImportBO;
        }
    }

    public static class UpdateInvoiceCodeBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateInvoiceCodeBto.DrugImportDetailBto, DrugImportDetailBO> getCreatedBto(
                UpdateInvoiceCodeBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateInvoiceCodeBto, DrugImportBO> getCreatedBto(
                UpdateInvoiceCodeBto updateInvoiceCodeBto) {
            return this.getAddedResult(updateInvoiceCodeBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateInvoiceCodeBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(UpdateInvoiceCodeBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateInvoiceCodeBto, DrugImport, DrugImportBO> getUpdatedBto(
                UpdateInvoiceCodeBto updateInvoiceCodeBto) {
            return super.getUpdatedResult(updateInvoiceCodeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateInvoiceCodeBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(UpdateInvoiceCodeBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateInvoiceCodeBto, DrugImportBO> getUnmodifiedBto(
                UpdateInvoiceCodeBto updateInvoiceCodeBto) {
            return super.getUnmodifiedResult(updateInvoiceCodeBto);
        }
    }

    public static class UpdateDrugImportDetailSettleIdBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugImportDetailSettleIdBto.DrugImportDetailBto, DrugImportDetailBO>
                getCreatedBto(
                        UpdateDrugImportDetailSettleIdBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugImportDetailSettleIdBto, DrugImportBO> getCreatedBto(
                UpdateDrugImportDetailSettleIdBto updateDrugImportDetailSettleIdBto) {
            return this.getAddedResult(updateDrugImportDetailSettleIdBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugImportDetailSettleIdBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(
                        UpdateDrugImportDetailSettleIdBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateDrugImportDetailSettleIdBto, DrugImport, DrugImportBO>
                getUpdatedBto(UpdateDrugImportDetailSettleIdBto updateDrugImportDetailSettleIdBto) {
            return super.getUpdatedResult(updateDrugImportDetailSettleIdBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateDrugImportDetailSettleIdBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(
                        UpdateDrugImportDetailSettleIdBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugImportDetailSettleIdBto, DrugImportBO> getUnmodifiedBto(
                UpdateDrugImportDetailSettleIdBto updateDrugImportDetailSettleIdBto) {
            return super.getUnmodifiedResult(updateDrugImportDetailSettleIdBto);
        }
    }

    public static class ResetDrugImportDetailSettleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ResetDrugImportDetailSettleBto.DrugImportDetailBto, DrugImportDetailBO>
                getCreatedBto(
                        ResetDrugImportDetailSettleBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<ResetDrugImportDetailSettleBto, DrugImportBO> getCreatedBto(
                ResetDrugImportDetailSettleBto resetDrugImportDetailSettleBto) {
            return this.getAddedResult(resetDrugImportDetailSettleBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ResetDrugImportDetailSettleBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(
                        ResetDrugImportDetailSettleBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ResetDrugImportDetailSettleBto, DrugImport, DrugImportBO> getUpdatedBto(
                ResetDrugImportDetailSettleBto resetDrugImportDetailSettleBto) {
            return super.getUpdatedResult(resetDrugImportDetailSettleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ResetDrugImportDetailSettleBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(
                        ResetDrugImportDetailSettleBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ResetDrugImportDetailSettleBto, DrugImportBO> getUnmodifiedBto(
                ResetDrugImportDetailSettleBto resetDrugImportDetailSettleBto) {
            return super.getUnmodifiedResult(resetDrugImportDetailSettleBto);
        }
    }

    public static class ReturnOrderImportDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ReturnOrderImportDetailBto.DrugImportDetailBto, DrugImportDetailBO>
                getCreatedBto(ReturnOrderImportDetailBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<ReturnOrderImportDetailBto, DrugImportBO> getCreatedBto(
                ReturnOrderImportDetailBto returnOrderImportDetailBto) {
            return this.getAddedResult(returnOrderImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ReturnOrderImportDetailBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(ReturnOrderImportDetailBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ReturnOrderImportDetailBto, DrugImport, DrugImportBO> getUpdatedBto(
                ReturnOrderImportDetailBto returnOrderImportDetailBto) {
            return super.getUpdatedResult(returnOrderImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ReturnOrderImportDetailBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(
                        ReturnOrderImportDetailBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ReturnOrderImportDetailBto, DrugImportBO> getUnmodifiedBto(
                ReturnOrderImportDetailBto returnOrderImportDetailBto) {
            return super.getUnmodifiedResult(returnOrderImportDetailBto);
        }
    }

    public static class MergeDrugImportBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDrugImportBto.DrugImportDetailBto, DrugImportDetailBO> getCreatedBto(
                MergeDrugImportBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDrugImportBto, DrugImportBO> getCreatedBto(
                MergeDrugImportBto mergeDrugImportBto) {
            return this.getAddedResult(mergeDrugImportBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeDrugImportBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(MergeDrugImportBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeDrugImportBto, DrugImport, DrugImportBO> getUpdatedBto(
                MergeDrugImportBto mergeDrugImportBto) {
            return super.getUpdatedResult(mergeDrugImportBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDrugImportBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(MergeDrugImportBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDrugImportBto, DrugImportBO> getUnmodifiedBto(
                MergeDrugImportBto mergeDrugImportBto) {
            return super.getUnmodifiedResult(mergeDrugImportBto);
        }
    }

    public static class DeleteDrugImportDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDrugImportDetailBto.DrugImportDetailBto, DrugImportDetailBO>
                getCreatedBto(DeleteDrugImportDetailBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDrugImportDetailBto, DrugImportBO> getCreatedBto(
                DeleteDrugImportDetailBto deleteDrugImportDetailBto) {
            return this.getAddedResult(deleteDrugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteDrugImportDetailBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(DeleteDrugImportDetailBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteDrugImportDetailBto, DrugImport, DrugImportBO> getUpdatedBto(
                DeleteDrugImportDetailBto deleteDrugImportDetailBto) {
            return super.getUpdatedResult(deleteDrugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDrugImportDetailBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(
                        DeleteDrugImportDetailBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDrugImportDetailBto, DrugImportBO> getUnmodifiedBto(
                DeleteDrugImportDetailBto deleteDrugImportDetailBto) {
            return super.getUnmodifiedResult(deleteDrugImportDetailBto);
        }
    }

    public static class UpdateDrugDetailInvoiceNumberBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto, DrugImportDetailBO>
                getCreatedBto(
                        UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugDetailInvoiceNumberBto, DrugImportBO> getCreatedBto(
                UpdateDrugDetailInvoiceNumberBto updateDrugDetailInvoiceNumberBto) {
            return this.getAddedResult(updateDrugDetailInvoiceNumberBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(
                        UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateDrugDetailInvoiceNumberBto, DrugImport, DrugImportBO> getUpdatedBto(
                UpdateDrugDetailInvoiceNumberBto updateDrugDetailInvoiceNumberBto) {
            return super.getUpdatedResult(updateDrugDetailInvoiceNumberBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(
                        UpdateDrugDetailInvoiceNumberBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugDetailInvoiceNumberBto, DrugImportBO> getUnmodifiedBto(
                UpdateDrugDetailInvoiceNumberBto updateDrugDetailInvoiceNumberBto) {
            return super.getUnmodifiedResult(updateDrugDetailInvoiceNumberBto);
        }
    }

    public static class UpdateDrugImportBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugImportBto, DrugImportBO> getCreatedBto(
                UpdateDrugImportBto updateDrugImportBto) {
            return this.getAddedResult(updateDrugImportBto);
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateDrugImportBto, DrugImport, DrugImportBO> getUpdatedBto(
                UpdateDrugImportBto updateDrugImportBto) {
            return super.getUpdatedResult(updateDrugImportBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugImportBto, DrugImportBO> getUnmodifiedBto(
                UpdateDrugImportBto updateDrugImportBto) {
            return super.getUnmodifiedResult(updateDrugImportBto);
        }
    }

    public static class UpdateDrugImportDetailListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugImportBO getRootBo() {
            return (DrugImportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugImportDetailListBto.DrugImportDetailBto, DrugImportDetailBO>
                getCreatedBto(
                        UpdateDrugImportDetailListBto.DrugImportDetailBto drugImportDetailBto) {
            return this.getAddedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateDrugImportDetailListBto, DrugImportBO> getCreatedBto(
                UpdateDrugImportDetailListBto updateDrugImportDetailListBto) {
            return this.getAddedResult(updateDrugImportDetailListBto);
        }

        @AutoGenerated(locked = true)
        public DrugImportDetail getDeleted_DrugImportDetail() {
            return (DrugImportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugImport getDeleted_DrugImport() {
            return (DrugImport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugImport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateDrugImportDetailListBto.DrugImportDetailBto,
                        DrugImportDetail,
                        DrugImportDetailBO>
                getUpdatedBto(
                        UpdateDrugImportDetailListBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUpdatedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateDrugImportDetailListBto, DrugImport, DrugImportBO> getUpdatedBto(
                UpdateDrugImportDetailListBto updateDrugImportDetailListBto) {
            return super.getUpdatedResult(updateDrugImportDetailListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugImportDetailListBto.DrugImportDetailBto, DrugImportDetailBO>
                getUnmodifiedBto(
                        UpdateDrugImportDetailListBto.DrugImportDetailBto drugImportDetailBto) {
            return super.getUnmodifiedResult(drugImportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateDrugImportDetailListBto, DrugImportBO> getUnmodifiedBto(
                UpdateDrugImportDetailListBto updateDrugImportDetailListBto) {
            return super.getUnmodifiedResult(updateDrugImportDetailListBto);
        }
    }
}
