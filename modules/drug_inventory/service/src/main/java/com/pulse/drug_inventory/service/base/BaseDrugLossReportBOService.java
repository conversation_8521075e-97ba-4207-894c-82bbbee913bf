package com.pulse.drug_inventory.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.bo.DrugLossReportBO;
import com.pulse.drug_inventory.persist.dos.DrugLossReport;
import com.pulse.drug_inventory.persist.dos.DrugLossReportDetail;
import com.pulse.drug_inventory.service.base.BaseDrugLossReportBOService.DeleteLossReportBoResult;
import com.pulse.drug_inventory.service.base.BaseDrugLossReportBOService.MergeLossReportBoResult;
import com.pulse.drug_inventory.service.bto.DeleteLossReportBto;
import com.pulse.drug_inventory.service.bto.MergeLossReportBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "c99eeba8-9f7b-38e1-9b71-17233ec7ab73")
public class BaseDrugLossReportBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 创建对象:DrugLossReportDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugLossReportDetailBtoOnDuplicateUpdate(
            BaseDrugLossReportBOService.MergeLossReportBoResult boResult,
            MergeLossReportBto mergeLossReportBto,
            DrugLossReportBO drugLossReportBO) {
        if (CollectionUtil.isEmpty(mergeLossReportBto.getDrugLossReportDetailBtoList())) {
            mergeLossReportBto.setDrugLossReportDetailBtoList(List.of());
        }
        drugLossReportBO
                .getDrugLossReportDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeLossReportBto.getDrugLossReportDetailBtoList().stream()
                                            .filter(
                                                    drugLossReportDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (drugLossReportDetailBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            drugLossReportDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToDrugLossReportDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeLossReportBto.getDrugLossReportDetailBtoList())) {
            for (MergeLossReportBto.DrugLossReportDetailBto item :
                    mergeLossReportBto.getDrugLossReportDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugLossReportDetailBO> any =
                        drugLossReportBO.getDrugLossReportDetailBOSet().stream()
                                .filter(
                                        drugLossReportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugLossReportDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugLossReportDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugLossReportDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountType")) {
                            bo.setAccountType(item.getAccountType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginName")) {
                            bo.setDrugOriginName(item.getDrugOriginName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "reportingLossReason")) {
                            bo.setReportingLossReason(item.getReportingLossReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockAmount")) {
                            bo.setStockAmount(item.getStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "reportLossType")) {
                            bo.setReportLossType(item.getReportLossType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailCost")) {
                            bo.setRetailCost(item.getRetailCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchaseCost")) {
                            bo.setPurchaseCost(item.getPurchaseCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                    } else {
                        DrugLossReportDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugLossReportDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountType")) {
                            bo.setAccountType(item.getAccountType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginCode")) {
                            bo.setDrugOriginCode(item.getDrugOriginCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginName")) {
                            bo.setDrugOriginName(item.getDrugOriginName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "reportingLossReason")) {
                            bo.setReportingLossReason(item.getReportingLossReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockAmount")) {
                            bo.setStockAmount(item.getStockAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "reportLossType")) {
                            bo.setReportLossType(item.getReportLossType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailCost")) {
                            bo.setRetailCost(item.getRetailCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchaseCost")) {
                            bo.setPurchaseCost(item.getPurchaseCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                    }
                } else {
                    DrugLossReportDetailBO subBo = new DrugLossReportDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "accountType")) {
                        subBo.setAccountType(item.getAccountType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginCode")) {
                        subBo.setDrugOriginCode(item.getDrugOriginCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginName")) {
                        subBo.setDrugOriginName(item.getDrugOriginName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amount")) {
                        subBo.setAmount(item.getAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "reportingLossReason")) {
                        subBo.setReportingLossReason(item.getReportingLossReason());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stockAmount")) {
                        subBo.setStockAmount(item.getStockAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "reportLossType")) {
                        subBo.setReportLossType(item.getReportLossType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailCost")) {
                        subBo.setRetailCost(item.getRetailCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchaseCost")) {
                        subBo.setPurchaseCost(item.getPurchaseCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchInventoryId")) {
                        subBo.setBatchInventoryId(item.getBatchInventoryId());
                    }
                    subBo.setDrugLossReportBO(drugLossReportBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("drug_loss_report_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugLossReportBO.getDrugLossReportDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugLossReportBO createMergeLossReportOnDuplicateUpdate(
            MergeLossReportBoResult boResult, MergeLossReportBto mergeLossReportBto) {
        DrugLossReportBO drugLossReportBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeLossReportBto.getId() == null);
        if (!allNull && !found) {
            drugLossReportBO = DrugLossReportBO.getById(mergeLossReportBto.getId());
            if (drugLossReportBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugLossReportBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugLossReportBO.convertToDrugLossReport());
                updatedBto.setBto(mergeLossReportBto);
                updatedBto.setBo(drugLossReportBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "reportLossNumber")) {
                    drugLossReportBO.setReportLossNumber(mergeLossReportBto.getReportLossNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugLossReportBO.setStorageCode(mergeLossReportBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "exportImportCode")) {
                    drugLossReportBO.setExportImportCode(mergeLossReportBto.getExportImportCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "departmentId")) {
                    drugLossReportBO.setDepartmentId(mergeLossReportBto.getDepartmentId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugLossReportBO.setApplyStaffId(mergeLossReportBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "applyDateTime")) {
                    drugLossReportBO.setApplyDateTime(mergeLossReportBto.getApplyDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "reviewStaffId")) {
                    drugLossReportBO.setReviewStaffId(mergeLossReportBto.getReviewStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "reviewDateTime")) {
                    drugLossReportBO.setReviewDateTime(mergeLossReportBto.getReviewDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountantFlag")) {
                    drugLossReportBO.setAccountantFlag(mergeLossReportBto.getAccountantFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountantStaffId")) {
                    drugLossReportBO.setAccountantStaffId(
                            mergeLossReportBto.getAccountantStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountantDateTime")) {
                    drugLossReportBO.setAccountantDateTime(
                            mergeLossReportBto.getAccountantDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "updatedBy")) {
                    drugLossReportBO.setUpdatedBy(mergeLossReportBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "modifyDateTime")) {
                    drugLossReportBO.setModifyDateTime(mergeLossReportBto.getModifyDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "remark")) {
                    drugLossReportBO.setRemark(mergeLossReportBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "printFlag")) {
                    drugLossReportBO.setPrintFlag(mergeLossReportBto.getPrintFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "documentType")) {
                    drugLossReportBO.setDocumentType(mergeLossReportBto.getDocumentType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountingPeriodId")) {
                    drugLossReportBO.setAccountingPeriodId(
                            mergeLossReportBto.getAccountingPeriodId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugLossReportBO.convertToDrugLossReport());
                updatedBto.setBto(mergeLossReportBto);
                updatedBto.setBo(drugLossReportBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "reportLossNumber")) {
                    drugLossReportBO.setReportLossNumber(mergeLossReportBto.getReportLossNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugLossReportBO.setStorageCode(mergeLossReportBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "exportImportCode")) {
                    drugLossReportBO.setExportImportCode(mergeLossReportBto.getExportImportCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "departmentId")) {
                    drugLossReportBO.setDepartmentId(mergeLossReportBto.getDepartmentId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugLossReportBO.setApplyStaffId(mergeLossReportBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "applyDateTime")) {
                    drugLossReportBO.setApplyDateTime(mergeLossReportBto.getApplyDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "reviewStaffId")) {
                    drugLossReportBO.setReviewStaffId(mergeLossReportBto.getReviewStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "reviewDateTime")) {
                    drugLossReportBO.setReviewDateTime(mergeLossReportBto.getReviewDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountantFlag")) {
                    drugLossReportBO.setAccountantFlag(mergeLossReportBto.getAccountantFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountantStaffId")) {
                    drugLossReportBO.setAccountantStaffId(
                            mergeLossReportBto.getAccountantStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountantDateTime")) {
                    drugLossReportBO.setAccountantDateTime(
                            mergeLossReportBto.getAccountantDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "updatedBy")) {
                    drugLossReportBO.setUpdatedBy(mergeLossReportBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "modifyDateTime")) {
                    drugLossReportBO.setModifyDateTime(mergeLossReportBto.getModifyDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "remark")) {
                    drugLossReportBO.setRemark(mergeLossReportBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "printFlag")) {
                    drugLossReportBO.setPrintFlag(mergeLossReportBto.getPrintFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "documentType")) {
                    drugLossReportBO.setDocumentType(mergeLossReportBto.getDocumentType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeLossReportBto, "__$validPropertySet"),
                        "accountingPeriodId")) {
                    drugLossReportBO.setAccountingPeriodId(
                            mergeLossReportBto.getAccountingPeriodId());
                }
            }
        } else {
            drugLossReportBO = new DrugLossReportBO();
            if (pkExist) {
                drugLossReportBO.setId(mergeLossReportBto.getId());
            } else {
                drugLossReportBO.setId(
                        String.valueOf(this.idGenerator.allocateId("drug_loss_report")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "reportLossNumber")) {
                drugLossReportBO.setReportLossNumber(mergeLossReportBto.getReportLossNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "storageCode")) {
                drugLossReportBO.setStorageCode(mergeLossReportBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "exportImportCode")) {
                drugLossReportBO.setExportImportCode(mergeLossReportBto.getExportImportCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "departmentId")) {
                drugLossReportBO.setDepartmentId(mergeLossReportBto.getDepartmentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "applyStaffId")) {
                drugLossReportBO.setApplyStaffId(mergeLossReportBto.getApplyStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "applyDateTime")) {
                drugLossReportBO.setApplyDateTime(mergeLossReportBto.getApplyDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "reviewStaffId")) {
                drugLossReportBO.setReviewStaffId(mergeLossReportBto.getReviewStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "reviewDateTime")) {
                drugLossReportBO.setReviewDateTime(mergeLossReportBto.getReviewDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "accountantFlag")) {
                drugLossReportBO.setAccountantFlag(mergeLossReportBto.getAccountantFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "accountantStaffId")) {
                drugLossReportBO.setAccountantStaffId(mergeLossReportBto.getAccountantStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "accountantDateTime")) {
                drugLossReportBO.setAccountantDateTime(mergeLossReportBto.getAccountantDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "updatedBy")) {
                drugLossReportBO.setUpdatedBy(mergeLossReportBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "modifyDateTime")) {
                drugLossReportBO.setModifyDateTime(mergeLossReportBto.getModifyDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "remark")) {
                drugLossReportBO.setRemark(mergeLossReportBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "printFlag")) {
                drugLossReportBO.setPrintFlag(mergeLossReportBto.getPrintFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "documentType")) {
                drugLossReportBO.setDocumentType(mergeLossReportBto.getDocumentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "accountingPeriodId")) {
                drugLossReportBO.setAccountingPeriodId(mergeLossReportBto.getAccountingPeriodId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeLossReportBto);
            addedBto.setBo(drugLossReportBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugLossReportBO;
    }

    /** 删除对象:deleteLossReport,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugLossReportBO deleteDeleteLossReportOnMissThrowEx(
            BaseDrugLossReportBOService.DeleteLossReportBoResult boResult,
            DeleteLossReportBto deleteLossReportBto) {
        DrugLossReportBO drugLossReportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteLossReportBto.getId() == null);
        if (!allNull && !found) {
            drugLossReportBO = DrugLossReportBO.getById(deleteLossReportBto.getId());
            found = true;
        }
        if (drugLossReportBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugLossReportBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteLossReportBto);
            deletedBto.setEntity(drugLossReportBO.convertToDrugLossReport());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugLossReportBO;
        }
    }

    @AutoGenerated(locked = true)
    protected DeleteLossReportBoResult deleteLossReportBase(
            DeleteLossReportBto deleteLossReportBto) {
        if (deleteLossReportBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteLossReportBoResult boResult = new DeleteLossReportBoResult();
        DrugLossReportBO drugLossReportBO =
                deleteDeleteLossReportOnMissThrowEx(boResult, deleteLossReportBto);
        boResult.setRootBo(drugLossReportBO);
        return boResult;
    }

    /** merge报损单（按标识是否记账） */
    @AutoGenerated(locked = true)
    protected MergeLossReportBoResult mergeLossReportBase(MergeLossReportBto mergeLossReportBto) {
        if (mergeLossReportBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeLossReportBoResult boResult = new MergeLossReportBoResult();
        DrugLossReportBO drugLossReportBO =
                createMergeLossReportOnDuplicateUpdate(boResult, mergeLossReportBto);
        if (drugLossReportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLossReportBto, "__$validPropertySet"),
                    "drugLossReportDetailBtoList")) {
                createDrugLossReportDetailBtoOnDuplicateUpdate(
                        boResult, mergeLossReportBto, drugLossReportBO);
            }
        }
        boResult.setRootBo(drugLossReportBO);
        return boResult;
    }

    public static class DeleteLossReportBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugLossReportBO getRootBo() {
            return (DrugLossReportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteLossReportBto, DrugLossReportBO> getCreatedBto(
                DeleteLossReportBto deleteLossReportBto) {
            return this.getAddedResult(deleteLossReportBto);
        }

        @AutoGenerated(locked = true)
        public DrugLossReport getDeleted_DrugLossReport() {
            return (DrugLossReport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugLossReport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteLossReportBto, DrugLossReport, DrugLossReportBO> getUpdatedBto(
                DeleteLossReportBto deleteLossReportBto) {
            return super.getUpdatedResult(deleteLossReportBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteLossReportBto, DrugLossReportBO> getUnmodifiedBto(
                DeleteLossReportBto deleteLossReportBto) {
            return super.getUnmodifiedResult(deleteLossReportBto);
        }
    }

    public static class MergeLossReportBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugLossReportBO getRootBo() {
            return (DrugLossReportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeLossReportBto.DrugLossReportDetailBto, DrugLossReportDetailBO>
                getCreatedBto(MergeLossReportBto.DrugLossReportDetailBto drugLossReportDetailBto) {
            return this.getAddedResult(drugLossReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeLossReportBto, DrugLossReportBO> getCreatedBto(
                MergeLossReportBto mergeLossReportBto) {
            return this.getAddedResult(mergeLossReportBto);
        }

        @AutoGenerated(locked = true)
        public DrugLossReportDetail getDeleted_DrugLossReportDetail() {
            return (DrugLossReportDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugLossReportDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugLossReport getDeleted_DrugLossReport() {
            return (DrugLossReport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugLossReport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeLossReportBto.DrugLossReportDetailBto,
                        DrugLossReportDetail,
                        DrugLossReportDetailBO>
                getUpdatedBto(MergeLossReportBto.DrugLossReportDetailBto drugLossReportDetailBto) {
            return super.getUpdatedResult(drugLossReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeLossReportBto, DrugLossReport, DrugLossReportBO> getUpdatedBto(
                MergeLossReportBto mergeLossReportBto) {
            return super.getUpdatedResult(mergeLossReportBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeLossReportBto.DrugLossReportDetailBto, DrugLossReportDetailBO>
                getUnmodifiedBto(
                        MergeLossReportBto.DrugLossReportDetailBto drugLossReportDetailBto) {
            return super.getUnmodifiedResult(drugLossReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeLossReportBto, DrugLossReportBO> getUnmodifiedBto(
                MergeLossReportBto mergeLossReportBto) {
            return super.getUnmodifiedResult(mergeLossReportBto);
        }
    }
}
