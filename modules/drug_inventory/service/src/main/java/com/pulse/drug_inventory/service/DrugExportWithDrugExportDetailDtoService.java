package com.pulse.drug_inventory.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.DrugExportWithDrugExportDetailDtoManager;
import com.pulse.drug_inventory.manager.dto.DrugExportWithDrugExportDetailDto;
import com.pulse.drug_inventory.service.converter.DrugExportWithDrugExportDetailDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "b0911914-7859-4df5-ac8a-459d8c966199|DTO|SERVICE")
public class DrugExportWithDrugExportDetailDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportWithDrugExportDetailDtoManager drugExportWithDrugExportDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportWithDrugExportDetailDtoServiceConverter
            drugExportWithDrugExportDetailDtoServiceConverter;

    @PublicInterface(id = "caf0018b-a3c3-4a01-9c77-0de22829082a", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "0a808095-f86a-3c35-bf8a-e629f8d09522")
    public List<DrugExportWithDrugExportDetailDto> getByDocumentNumbers(
            @Valid @NotNull(message = "出库单号不能为空") List<String> documentNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        documentNumber = new ArrayList<>(new HashSet<>(documentNumber));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByDocumentNumbers(documentNumber);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "10354a9b-878c-4674-94f8-6d1872b8a4a0", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "2ab35915-2970-334f-a82c-36d7571dd13e")
    public DrugExportWithDrugExportDetailDto getByDocumentNumber(
            @NotNull(message = "出库单号不能为空") String documentNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportWithDrugExportDetailDto> ret =
                getByDocumentNumbers(Arrays.asList(documentNumber));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c46417a6-dbf1-4304-bb85-750f808787f3", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "3cad64e7-2db0-33a1-9d8d-1cdd03ece508")
    public List<DrugExportWithDrugExportDetailDto> getByDrugApplyIds(
            @Valid @NotNull(message = "库存申请单id不能为空") List<String> drugApplyId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugApplyId = new ArrayList<>(new HashSet<>(drugApplyId));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByDrugApplyIds(drugApplyId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "26e902f4-6445-46a9-ac74-04d1374f138a", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "3f6c9164-636f-32c3-a5a0-7741cbb41b3b")
    public List<DrugExportWithDrugExportDetailDto> getByDrugApplyId(
            @NotNull(message = "库存申请单id不能为空") String drugApplyId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugApplyIds(Arrays.asList(drugApplyId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "ce6ea74a-b1bc-4183-9804-8f99a26cec78", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "3f75e6ec-d421-3fa6-91cf-55f1d7addd38")
    public List<DrugExportWithDrugExportDetailDto> getByDrugImportId(
            @NotNull(message = "入库单id不能为空") String drugImportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugImportIds(Arrays.asList(drugImportId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "f8b9e1e3-a290-4d8b-afa6-a30067c568f5", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "496d1432-2dbf-39f8-a917-1d6ee9804930")
    public DrugExportWithDrugExportDetailDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportWithDrugExportDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "f618b0d8-2c54-4f98-9cf2-29995d61e75e", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "6425fc3a-1809-35e9-aa72-10ecf098bff1")
    public List<DrugExportWithDrugExportDetailDto> getByStorageCode(
            @NotNull(message = "出库库房编码不能为空") String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStorageCodes(Arrays.asList(storageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "84ccbb87-eafd-4355-abb9-555b4be9105b", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "6889c4b5-2307-3819-8dd5-c23f4ecb172c")
    public List<DrugExportWithDrugExportDetailDto> getByAccountantStaffIds(
            @Valid @NotNull(message = "记帐人不能为空") List<String> accountantStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        accountantStaffId = new ArrayList<>(new HashSet<>(accountantStaffId));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByAccountantStaffIds(accountantStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "75ccbdef-b3a3-44db-93a9-452c6b1ef622", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "81352a67-d5b1-39c8-b1db-3522160fedec")
    public List<DrugExportWithDrugExportDetailDto> getByAcceptanceStaffIds(
            @Valid @NotNull(message = "领用人不能为空") List<String> acceptanceStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        acceptanceStaffId = new ArrayList<>(new HashSet<>(acceptanceStaffId));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByAcceptanceStaffIds(acceptanceStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0b1b2a48-514a-49ea-b74e-6bf508c9ce3d", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "89a58082-9d16-39a7-a2ba-30f73b55b28c")
    public List<DrugExportWithDrugExportDetailDto> getByApplyStaffIds(
            @Valid @NotNull(message = "制单人不能为空") List<String> applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        applyStaffId = new ArrayList<>(new HashSet<>(applyStaffId));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByApplyStaffIds(applyStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "8475f2b0-8f77-4179-8807-6701ce75a2f7", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "a0713619-1084-388e-b43d-611e566a8497")
    public List<DrugExportWithDrugExportDetailDto> getByAcceptanceStaffId(
            @NotNull(message = "领用人不能为空") String acceptanceStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAcceptanceStaffIds(Arrays.asList(acceptanceStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "7e28a08a-39ed-4e72-81a8-be2ddb0f1d14", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "bbdcdd6d-fd07-3293-8b18-255b67b3d4ef")
    public List<DrugExportWithDrugExportDetailDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0adf5480-1b05-4f31-a0e2-c6f4934db93c", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "c2174b49-e04f-3745-92f5-0bb2bf644cf1")
    public List<DrugExportWithDrugExportDetailDto> getByRefundExportId(
            @NotNull(message = "冲红原出库单id不能为空") String refundExportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRefundExportIds(Arrays.asList(refundExportId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c1be31a8-8130-4a87-8385-fad32e50503f", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "cc1b9416-1bd2-3ad1-8494-0da6bd6d8301")
    public List<DrugExportWithDrugExportDetailDto> getByStocktakingIds(
            @Valid @NotNull(message = "对应盘点id不能为空") List<String> stocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        stocktakingId = new ArrayList<>(new HashSet<>(stocktakingId));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByStocktakingIds(stocktakingId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "98e6e8b3-cc45-4e9b-97d5-7eabf39d6cc7", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "dd96ab02-18ec-359d-a226-89cfb6b7bc64")
    public List<DrugExportWithDrugExportDetailDto> getByAccountantStaffId(
            @NotNull(message = "记帐人不能为空") String accountantStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAccountantStaffIds(Arrays.asList(accountantStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "********-b092-4e88-88c3-1954f47cf13f", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "dfa68a76-b60d-324f-83d3-48ead58a0031")
    public List<DrugExportWithDrugExportDetailDto> getByDrugImportIds(
            @Valid @NotNull(message = "入库单id不能为空") List<String> drugImportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugImportId = new ArrayList<>(new HashSet<>(drugImportId));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByDrugImportIds(drugImportId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "9d662f94-e44d-42e9-a1dc-77821a1e3b2c", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "e06187e2-2e75-3c14-813f-f39560fd831f")
    public List<DrugExportWithDrugExportDetailDto> getByRefundExportIds(
            @Valid @NotNull(message = "冲红原出库单id不能为空") List<String> refundExportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        refundExportId = new ArrayList<>(new HashSet<>(refundExportId));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByRefundExportIds(refundExportId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f56d4975-8e7d-400e-8b0e-41cb7f156f40", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "e32e5d06-2f96-3be5-bf7f-8d76d74ffefa")
    public List<DrugExportWithDrugExportDetailDto> getByApplyStaffId(
            @NotNull(message = "制单人不能为空") String applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByApplyStaffIds(Arrays.asList(applyStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "668d9318-75a9-4564-9398-2389fd522fab", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "f5218d36-2606-3480-ab46-d6588835e662")
    public List<DrugExportWithDrugExportDetailDto> getByStorageCodes(
            @Valid @NotNull(message = "出库库房编码不能为空") List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        storageCode = new ArrayList<>(new HashSet<>(storageCode));
        List<DrugExportWithDrugExportDetailDto> drugExportWithDrugExportDetailDtoList =
                drugExportWithDrugExportDetailDtoManager.getByStorageCodes(storageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportWithDrugExportDetailDtoServiceConverter
                .DrugExportWithDrugExportDetailDtoConverter(drugExportWithDrugExportDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "edca33be-28af-4714-a56e-d51a9e7ebf1d", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "fd6cba9f-7178-3cad-989e-71e2bca9697e")
    public List<DrugExportWithDrugExportDetailDto> getByStocktakingId(
            @NotNull(message = "对应盘点id不能为空") String stocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStocktakingIds(Arrays.asList(stocktakingId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
