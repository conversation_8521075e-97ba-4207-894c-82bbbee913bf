package com.pulse.drug_inventory.service.query;

import com.pulse.drug_inventory.manager.converter.DrugLocationDtoConverter;
import com.pulse.drug_inventory.manager.dto.DrugLocationBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugLocationDto;
import com.pulse.drug_inventory.persist.qto.SearchDrugLocationQto;
import com.pulse.drug_inventory.service.DrugLocationBaseDtoService;
import com.pulse.drug_inventory.service.index.entity.SearchDrugLocationQtoService;
import com.pulse.drug_inventory.service.query.assembler.DrugLocationDtoDataAssembler;
import com.pulse.drug_inventory.service.query.assembler.DrugLocationDtoDataAssembler.DrugLocationDtoDataHolder;
import com.pulse.drug_inventory.service.query.collector.DrugLocationDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** DrugLocationDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "c2c5477f-9c78-3f89-86f9-54175e81f920")
public class DrugLocationDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private DrugLocationBaseDtoService drugLocationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLocationDtoConverter drugLocationDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLocationDtoDataAssembler drugLocationDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugLocationDtoDataCollector drugLocationDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchDrugLocationQtoService searchDrugLocationQtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "3fe9a227-2a47-334d-9c0e-968f29f7d32a")
    private List<DrugLocationDto> toDtoList(
            List<String> ids, DrugLocationDtoDataHolder dataHolder) {
        List<DrugLocationBaseDto> baseDtoList = drugLocationBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, DrugLocationDto> dtoMap =
                drugLocationDtoConverter
                        .convertFromDrugLocationBaseDtoToDrugLocationDto(baseDtoList)
                        .stream()
                        .collect(Collectors.toMap(DrugLocationDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchDrugLocationQto查询DrugLocationDto列表,分页 */
    @PublicInterface(id = "88c68cd4-7710-4bee-81c9-880158858bab", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "b92d2313-d256-319f-bcff-604007d00ab3")
    public VSQueryResult<DrugLocationDto> searchDrugLocationPaged(
            @Valid @NotNull SearchDrugLocationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchDrugLocationQtoService.queryPaged(qto);
        DrugLocationDtoDataHolder dataHolder = new DrugLocationDtoDataHolder();
        List<DrugLocationDto> dtoList = toDtoList(ids, dataHolder);
        drugLocationDtoDataCollector.collectDataDefault(dataHolder);
        drugLocationDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchDrugLocationQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
