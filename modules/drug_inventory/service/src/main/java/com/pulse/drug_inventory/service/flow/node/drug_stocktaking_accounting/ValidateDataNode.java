package com.pulse.drug_inventory.service.flow.node.drug_stocktaking_accounting;

import com.pulse.drug_inventory.common.enums.StocktakingStatusEnum;
import com.pulse.drug_inventory.common.enums.StocktakingTypeEnum;
import com.pulse.drug_inventory.manager.facade.organization.OrganizationDepartmentDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.pharmacy_warehouse_setting.DrugStorageAccountingPeriodBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.pharmacy_warehouse_setting.ExportImportWayBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.service.flow.context.DrugStocktakingAccountingContext;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.pulse.pharmacy_warehouse_setting.common.enums.AccountingPeriodStatusEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.StorageTypeEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.SystemExportImportWayEnum;
import com.pulse.pharmacy_warehouse_setting.manager.dto.DrugStorageAccountingPeriodBaseDto;
import com.pulse.pharmacy_warehouse_setting.manager.dto.ExportImportWayBaseDto;
import com.pulse.pharmacy_warehouse_setting.persist.eo.UkStorageCodeWayCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.flow.node.NodeIfComponent;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Component("drugInventory-drugStocktakingAccounting-validateData")
@AutoGenerated(locked = false, uuid = "57ea4f1c-1ee7-47fa-8285-0fb9b53830c5|FLOW_NODE|DEFINITION")
public class ValidateDataNode extends NodeIfComponent {

    @Resource
    private DrugStorageAccountingPeriodBaseDtoServiceInDrugInventoryRpcAdapter
            drugStorageAccountingPeriodBaseDtoServiceInDrugInventoryRpcAdapter;

    @Resource
    private OrganizationDepartmentDtoServiceInDrugInventoryRpcAdapter
            organizationDepartmentDtoServiceInDrugInventoryRpcAdapter;

    @Resource
    private ExportImportWayBaseDtoServiceInDrugInventoryRpcAdapter
            exportImportWayBaseDtoServiceInDrugInventoryRpcAdapter;

    /**
     * 实现流程判断逻辑 节点之间传参都必须通过Context传递 如果要去取Context，调用参数 getFirstContextBean() 如果要终止流程，调用
     * super.setEnd();
     */
    @AutoGenerated(locked = false, uuid = "57ea4f1c-1ee7-47fa-8285-0fb9b53830c5")
    public boolean processIf() {
        /** This block is generated by vs, do not modify, start anchor 1 */
        /** 获取宿主流程的context */
        DrugStocktakingAccountingContext context = getFirstContextBean();
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 以下开始处理业务逻辑

        var stocktakingDto = context.getDrugStocktakingBaseDto();
        if (stocktakingDto == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未找到盘存单！");
        } else if (StocktakingTypeEnum.SUMMARY_STOCKTAKING.equals(
                stocktakingDto.getStocktakingType())) {
            if (StocktakingStatusEnum.ACCOUNT.equals(stocktakingDto.getStocktakingStatus())) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "盘存单已结账，不能重复记账！");
            }
        } else if (StocktakingTypeEnum.SUB_STOCKTAKING.equals(
                stocktakingDto.getStocktakingType())) {
            if (StocktakingStatusEnum.ACCOUNT.equals(stocktakingDto.getStocktakingStatus())) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "盘存单已结账，不能重复记账！");
            } else if (StocktakingStatusEnum.SUBMIT.equals(stocktakingDto.getStocktakingStatus())
                    || StocktakingStatusEnum.SUMMARY.equals(
                            stocktakingDto.getStocktakingStatus())) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "窗口盘存单已提交或汇总，不能记账！");
            }
        }

        // TODO 校验盘存锁

        // 校验会计期间
        var storageCode = stocktakingDto.getStorageCode();
        OrganizationDepartmentDto storage =
                organizationDepartmentDtoServiceInDrugInventoryRpcAdapter.getById(storageCode);
        if (storage == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未找到该库房！");
        } else if (storage.getDepartment() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该组织无对应科室信息！");
        } else if (storage.getDepartment().getStorageType() == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该库房库房类型为空！");
        }
        context.setOrganizationDepartmentDto(storage);

        if (StorageTypeEnum.DRUG_STORAGE.equals(storage.getDepartment().getStorageType())) {
            List<DrugStorageAccountingPeriodBaseDto> accountingPeriodList =
                    drugStorageAccountingPeriodBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByStorageCode(storageCode);
            if (accountingPeriodList == null || accountingPeriodList.isEmpty()) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该库房未设置会计期间！");
            } else {
                var currentAccountingPeriod =
                        accountingPeriodList.stream()
                                .filter(
                                        a ->
                                                AccountingPeriodStatusEnum.CURRENT.equals(
                                                        a.getStatus()))
                                .findFirst()
                                .orElse(null);
                if (currentAccountingPeriod == null) {
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "该库房当前没有启用的会计期间！");
                } else if (currentAccountingPeriod.getStartDate().after(new Date())
                        || currentAccountingPeriod.getEndDate().before(new Date())) {
                    throw new IgnoredException(
                            ErrorCode.WRONG_PARAMETER,
                            "该库房当前时间不在会计期间"
                                    + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                            .format(currentAccountingPeriod.getStartDate())
                                    + "~"
                                    + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                            .format(currentAccountingPeriod.getEndDate())
                                    + "范围内！");
                }

                context.setDrugStorageAccountingPeriodBaseDto(currentAccountingPeriod);
            }
        }

        UkStorageCodeWayCodeEo ukStorageCodeWayCodeEo =
                new UkStorageCodeWayCodeEo(
                        storageCode, SystemExportImportWayEnum.STOCKTAKING.getWayCode());
        ExportImportWayBaseDto stocktakingWay =
                exportImportWayBaseDtoServiceInDrugInventoryRpcAdapter.getByStorageCodeAndWayCode(
                        ukStorageCodeWayCodeEo);
        if (stocktakingWay == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "未找到对应库房盘存出入库方式，请检查！");
        }
        context.setExportImportWayBaseDto(stocktakingWay);

        return true;
    }
}
