package com.pulse.drug_inventory.service;

import com.pulse.drug_inventory.manager.bo.*;
import com.pulse.drug_inventory.manager.dto.DrugLossReportBaseDto;
import com.pulse.drug_inventory.persist.dos.DrugLossReport;
import com.pulse.drug_inventory.persist.dos.DrugLossReportDetail;
import com.pulse.drug_inventory.service.base.BaseDrugLossReportBOService;
import com.pulse.drug_inventory.service.bto.DeleteLossReportBto;
import com.pulse.drug_inventory.service.bto.MergeLossReportBto;
import com.pulse.pulse.common.utils.IdGeneratorUtils;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "34589d1a-4c64-4f1b-9d12-a51434ca2c28|BO|SERVICE")
public class DrugLossReportBOService extends BaseDrugLossReportBOService {
    @AutoGenerated(locked = true)
    @Resource
    private DrugLossReportBaseDtoService drugLossReportBaseDtoService;

    /** merge报损单（按标识是否记账） */
    @PublicInterface(id = "5205699f-951d-45c2-8394-b577692bf7e2", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "0b57ed31-0184-4375-aa98-b1348d65ec0e")
    public String mergeLossReport(@Valid @NotNull MergeLossReportBto mergeLossReportBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugLossReportBaseDto drugLossReportBaseDto = null;
        if (mergeLossReportBto.getId() != null) {
            drugLossReportBaseDto =
                    drugLossReportBaseDtoService.getById(mergeLossReportBto.getId());
        }
        // 生成报损单号
        if (mergeLossReportBto.getReportLossNumber() == ""
                || mergeLossReportBto.getReportLossNumber() == null) {
            mergeLossReportBto.setReportLossNumber(
                    IdGeneratorUtils.generateNextSequenceId("DrugLossReport", "id", 20));
        }
        MergeLossReportBoResult boResult = super.mergeLossReportBase(mergeLossReportBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeLossReportBto.DrugLossReportDetailBto */
        {
            for (MergeLossReportBto.DrugLossReportDetailBto bto :
                    boResult.<MergeLossReportBto.DrugLossReportDetailBto>getBtoOfType(
                            MergeLossReportBto.DrugLossReportDetailBto.class)) {
                UpdatedBto<
                                MergeLossReportBto.DrugLossReportDetailBto,
                                DrugLossReportDetail,
                                DrugLossReportDetailBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<MergeLossReportBto.DrugLossReportDetailBto, DrugLossReportDetailBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    DrugLossReportDetailBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    DrugLossReportDetail entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    DrugLossReportDetailBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<DrugLossReportDetail> deletedEntityList =
                    boResult.getDeletedEntityList(DrugLossReportDetail.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 MergeLossReportBto */
        {
            MergeLossReportBto bto =
                    boResult.<MergeLossReportBto>getBtoOfType(MergeLossReportBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeLossReportBto, DrugLossReport, DrugLossReportBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeLossReportBto, DrugLossReportBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                DrugLossReportBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                DrugLossReport entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                DrugLossReportBO bo = addedBto.getBo();
                // 其他自定义操作...
                // TODO:获取出库方式为报损类型的code get_by_storage_code_and_way_code
                bo.setExportImportCode("1");
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "d1bbb4bc-0f75-4635-b3ff-d77328386310", module = "drug_inventory")
    @Transactional
    @AutoGenerated(locked = false, uuid = "d5516595-05de-4fbf-8c87-5b292c28f409")
    public String deleteLossReport(@Valid @NotNull DeleteLossReportBto deleteLossReportBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        DrugLossReportBaseDto drugLossReportBaseDto =
                drugLossReportBaseDtoService.getById(deleteLossReportBto.getId());
        DeleteLossReportBoResult boResult = super.deleteLossReportBase(deleteLossReportBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteLossReportBto */
        {
            DeleteLossReportBto bto =
                    boResult.<DeleteLossReportBto>getBtoOfType(DeleteLossReportBto.class).stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteLossReportBto, DrugLossReport> deletedBto =
                    boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
