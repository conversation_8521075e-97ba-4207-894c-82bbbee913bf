package com.pulse.drug_inventory.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.SubDrugStocktakingBaseDtoManager;
import com.pulse.drug_inventory.manager.dto.SubDrugStocktakingBaseDto;
import com.pulse.drug_inventory.service.converter.SubDrugStocktakingBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "e54e9e24-9b65-4235-8b23-49df68ba18e6|DTO|SERVICE")
public class SubDrugStocktakingBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private SubDrugStocktakingBaseDtoManager subDrugStocktakingBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private SubDrugStocktakingBaseDtoServiceConverter subDrugStocktakingBaseDtoServiceConverter;

    @PublicInterface(id = "7058501e-d849-4c10-a1a9-ef3a85b0b5f7", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "07271ddb-8b85-362c-ad90-d081d62bf435")
    public List<SubDrugStocktakingBaseDto> getByCreateStaffId(
            @NotNull(message = "制单人id不能为空") String createStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByCreateStaffIds(Arrays.asList(createStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "4c48bd8b-5d40-40cc-adc7-3adfdb19ed70", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "1b6d651c-bea2-3c06-9869-d7eb12b30a6c")
    public List<SubDrugStocktakingBaseDto> getBySubmitStaffId(
            @NotNull(message = "提交人id不能为空") String submitStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySubmitStaffIds(Arrays.asList(submitStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c78d788d-7028-4059-b52e-13026f796899", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "1c442553-9b12-35fc-a69e-a325a8bac97b")
    public SubDrugStocktakingBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SubDrugStocktakingBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "284e5a9b-d97d-4be2-8633-972f3938f8cd", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "3e72b6f1-668a-33e9-b704-26bba1109438")
    public List<SubDrugStocktakingBaseDto> getBySubmitStaffIds(
            @Valid @NotNull(message = "提交人id不能为空") List<String> submitStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        submitStaffId = new ArrayList<>(new HashSet<>(submitStaffId));
        List<SubDrugStocktakingBaseDto> subDrugStocktakingBaseDtoList =
                subDrugStocktakingBaseDtoManager.getBySubmitStaffIds(submitStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return subDrugStocktakingBaseDtoServiceConverter.SubDrugStocktakingBaseDtoConverter(
                subDrugStocktakingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "65209090-03d6-4d31-b897-f7f4340ef48b", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "45c122d3-2e30-3203-958c-1a41d0597769")
    public List<SubDrugStocktakingBaseDto> getByStorageCodes(
            @Valid @NotNull(message = "库房编码不能为空") List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        storageCode = new ArrayList<>(new HashSet<>(storageCode));
        List<SubDrugStocktakingBaseDto> subDrugStocktakingBaseDtoList =
                subDrugStocktakingBaseDtoManager.getByStorageCodes(storageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return subDrugStocktakingBaseDtoServiceConverter.SubDrugStocktakingBaseDtoConverter(
                subDrugStocktakingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "1ef0cd07-25fe-40df-bd57-9802b29c5e8f", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "80082549-d293-3f09-a7d9-07dd9f77197f")
    public List<SubDrugStocktakingBaseDto> getByCreateStaffIds(
            @Valid @NotNull(message = "制单人id不能为空") List<String> createStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        createStaffId = new ArrayList<>(new HashSet<>(createStaffId));
        List<SubDrugStocktakingBaseDto> subDrugStocktakingBaseDtoList =
                subDrugStocktakingBaseDtoManager.getByCreateStaffIds(createStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return subDrugStocktakingBaseDtoServiceConverter.SubDrugStocktakingBaseDtoConverter(
                subDrugStocktakingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "c0f86730-3b54-4b2d-a3c9-d59fa2edd83e", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "ca6df5cb-43ac-3baa-9102-4f3e2ec12d7e")
    public List<SubDrugStocktakingBaseDto> getByDrugStocktakingIds(
            @Valid @NotNull(message = "汇总盘点单id不能为空") List<String> drugStocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugStocktakingId = new ArrayList<>(new HashSet<>(drugStocktakingId));
        List<SubDrugStocktakingBaseDto> subDrugStocktakingBaseDtoList =
                subDrugStocktakingBaseDtoManager.getByDrugStocktakingIds(drugStocktakingId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return subDrugStocktakingBaseDtoServiceConverter.SubDrugStocktakingBaseDtoConverter(
                subDrugStocktakingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "7d7ebbc3-fcd8-4cfe-a888-4f79a914dcaf", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "edcc2749-f724-3d10-a79f-4cfba09d2b29")
    public List<SubDrugStocktakingBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<SubDrugStocktakingBaseDto> subDrugStocktakingBaseDtoList =
                subDrugStocktakingBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return subDrugStocktakingBaseDtoServiceConverter.SubDrugStocktakingBaseDtoConverter(
                subDrugStocktakingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "c608e300-d7bf-45db-bb8b-1e2bc536f30e", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "f20289a0-b2e6-341a-b13b-660dd1762c06")
    public List<SubDrugStocktakingBaseDto> getByStorageCode(
            @NotNull(message = "库房编码不能为空") String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStorageCodes(Arrays.asList(storageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "22ba19a7-7b51-4fa8-a6a1-a0a4a556ed0a", module = "drug_inventory")
    @AutoGenerated(locked = false, uuid = "f402c3dc-eb15-3a69-8a71-0d5e84426538")
    public List<SubDrugStocktakingBaseDto> getByDrugStocktakingId(
            @NotNull(message = "汇总盘点单id不能为空") String drugStocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugStocktakingIds(Arrays.asList(drugStocktakingId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
