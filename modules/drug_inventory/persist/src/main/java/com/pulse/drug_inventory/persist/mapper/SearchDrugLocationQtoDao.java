package com.pulse.drug_inventory.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.persist.qto.SearchDrugLocationQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "6b8bace2-1106-4543-8631-4f6a33f55e3c|QTO|DAO")
public class SearchDrugLocationQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询摆放位置 */
    @AutoGenerated(locked = false, uuid = "6b8bace2-1106-4543-8631-4f6a33f55e3c-count")
    public Integer count(SearchDrugLocationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(drug_location.id) FROM drug_location LEFT JOIN organization"
                    + " \"storageCode\" on drug_location.storage_code = \"storageCode\".id LEFT"
                    + " JOIN department \"storageCode_department\" on \"storageCode\".id ="
                    + " \"storageCode_department\".organization_id LEFT JOIN drug_origin"
                    + " \"drugOriginCode\" on drug_location.drug_origin_code ="
                    + " \"drugOriginCode\".drug_origin_code WHERE \"storageCode_department\".id ="
                    + " #storageCodeDepartmentIdIs AND \"drugOriginCode\".drug_origin_name like"
                    + " #drugOriginCodeDrugOriginNameLike AND drug_location.location like"
                    + " #locationLike AND \"drugOriginCode\".account_type in"
                    + " #drugOriginCodeAccountTypeIn AND ( drug_location.location like #searchLike"
                    + " OR \"drugOriginCode\".drug_origin_name like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.pinyin') like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.wubi') like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.custom') like #searchLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getDrugOriginCodeDrugOriginNameLike() == null) {
            conditionToRemove.add("#drugOriginCodeDrugOriginNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getDrugOriginCodeAccountTypeIn())) {
            conditionToRemove.add("#drugOriginCodeAccountTypeIn");
        }
        if (qto.getStorageCodeDepartmentIdIs() == null) {
            conditionToRemove.add("#storageCodeDepartmentIdIs");
        }
        if (qto.getLocationLike() == null) {
            conditionToRemove.add("#locationLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugOriginExtension\"");
        softDeleteTableAlias.add("\"drugOriginCode\"");
        softDeleteTableAlias.add("\"storageCode_department\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug\"");
        softDeleteTableAlias.add("\"drugSpecification\"");
        softDeleteTableAlias.add("\"storageCode\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugNameDictionaryList\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug_drugCatalog\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#drugOriginCodeDrugOriginNameLike", "?")
                        .replace(
                                "#drugOriginCodeAccountTypeIn",
                                CollectionUtil.isEmpty(qto.getDrugOriginCodeAccountTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getDrugOriginCodeAccountTypeIn().size()))
                        .replace("#storageCodeDepartmentIdIs", "?")
                        .replace("#locationLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#drugOriginCodeDrugOriginNameLike")) {
                sqlParams.add("%" + qto.getDrugOriginCodeDrugOriginNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#drugOriginCodeAccountTypeIn")) {
                sqlParams.addAll(qto.getDrugOriginCodeAccountTypeIn());
            } else if (paramName.equalsIgnoreCase("#storageCodeDepartmentIdIs")) {
                sqlParams.add(qto.getStorageCodeDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#locationLike")) {
                sqlParams.add("%" + qto.getLocationLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询摆放位置 */
    @AutoGenerated(locked = false, uuid = "6b8bace2-1106-4543-8631-4f6a33f55e3c-query-all")
    public List<String> query(SearchDrugLocationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_location.id FROM drug_location LEFT JOIN organization \"storageCode\""
                    + " on drug_location.storage_code = \"storageCode\".id LEFT JOIN department"
                    + " \"storageCode_department\" on \"storageCode\".id ="
                    + " \"storageCode_department\".organization_id LEFT JOIN drug_origin"
                    + " \"drugOriginCode\" on drug_location.drug_origin_code ="
                    + " \"drugOriginCode\".drug_origin_code WHERE \"storageCode_department\".id ="
                    + " #storageCodeDepartmentIdIs AND \"drugOriginCode\".drug_origin_name like"
                    + " #drugOriginCodeDrugOriginNameLike AND drug_location.location like"
                    + " #locationLike AND \"drugOriginCode\".account_type in"
                    + " #drugOriginCodeAccountTypeIn AND ( drug_location.location like #searchLike"
                    + " OR \"drugOriginCode\".drug_origin_name like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.pinyin') like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.wubi') like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.custom') like #searchLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getDrugOriginCodeDrugOriginNameLike() == null) {
            conditionToRemove.add("#drugOriginCodeDrugOriginNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getDrugOriginCodeAccountTypeIn())) {
            conditionToRemove.add("#drugOriginCodeAccountTypeIn");
        }
        if (qto.getStorageCodeDepartmentIdIs() == null) {
            conditionToRemove.add("#storageCodeDepartmentIdIs");
        }
        if (qto.getLocationLike() == null) {
            conditionToRemove.add("#locationLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugOriginExtension\"");
        softDeleteTableAlias.add("\"drugOriginCode\"");
        softDeleteTableAlias.add("\"storageCode_department\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug\"");
        softDeleteTableAlias.add("\"drugSpecification\"");
        softDeleteTableAlias.add("\"storageCode\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugNameDictionaryList\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug_drugCatalog\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#drugOriginCodeDrugOriginNameLike", "?")
                        .replace(
                                "#drugOriginCodeAccountTypeIn",
                                CollectionUtil.isEmpty(qto.getDrugOriginCodeAccountTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getDrugOriginCodeAccountTypeIn().size()))
                        .replace("#storageCodeDepartmentIdIs", "?")
                        .replace("#locationLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#drugOriginCodeDrugOriginNameLike")) {
                sqlParams.add("%" + qto.getDrugOriginCodeDrugOriginNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#drugOriginCodeAccountTypeIn")) {
                sqlParams.addAll(qto.getDrugOriginCodeAccountTypeIn());
            } else if (paramName.equalsIgnoreCase("#storageCodeDepartmentIdIs")) {
                sqlParams.add(qto.getStorageCodeDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#locationLike")) {
                sqlParams.add("%" + qto.getLocationLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  \"drugOriginCode\".drug_origin_name asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询摆放位置 */
    @AutoGenerated(locked = false, uuid = "6b8bace2-1106-4543-8631-4f6a33f55e3c-query-paginate")
    public List<String> queryPaged(SearchDrugLocationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_location.id FROM drug_location LEFT JOIN organization \"storageCode\""
                    + " on drug_location.storage_code = \"storageCode\".id LEFT JOIN department"
                    + " \"storageCode_department\" on \"storageCode\".id ="
                    + " \"storageCode_department\".organization_id LEFT JOIN drug_origin"
                    + " \"drugOriginCode\" on drug_location.drug_origin_code ="
                    + " \"drugOriginCode\".drug_origin_code WHERE \"storageCode_department\".id ="
                    + " #storageCodeDepartmentIdIs AND \"drugOriginCode\".drug_origin_name like"
                    + " #drugOriginCodeDrugOriginNameLike AND drug_location.location like"
                    + " #locationLike AND \"drugOriginCode\".account_type in"
                    + " #drugOriginCodeAccountTypeIn AND ( drug_location.location like #searchLike"
                    + " OR \"drugOriginCode\".drug_origin_name like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.pinyin') like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.wubi') like #searchLike OR"
                    + " JSON_VALUE(\"drugOriginCode\".input_code, '$.custom') like #searchLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getDrugOriginCodeDrugOriginNameLike() == null) {
            conditionToRemove.add("#drugOriginCodeDrugOriginNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getDrugOriginCodeAccountTypeIn())) {
            conditionToRemove.add("#drugOriginCodeAccountTypeIn");
        }
        if (qto.getStorageCodeDepartmentIdIs() == null) {
            conditionToRemove.add("#storageCodeDepartmentIdIs");
        }
        if (qto.getLocationLike() == null) {
            conditionToRemove.add("#locationLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        if (qto.getSearchLike() == null) {
            conditionToRemove.add("#searchLike");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugOriginExtension\"");
        softDeleteTableAlias.add("\"drugOriginCode\"");
        softDeleteTableAlias.add("\"storageCode_department\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug\"");
        softDeleteTableAlias.add("\"drugSpecification\"");
        softDeleteTableAlias.add("\"storageCode\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugNameDictionaryList\"");
        softDeleteTableAlias.add("\"drugOriginCode_drugSpecification_drug_drugCatalog\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#drugOriginCodeDrugOriginNameLike", "?")
                        .replace(
                                "#drugOriginCodeAccountTypeIn",
                                CollectionUtil.isEmpty(qto.getDrugOriginCodeAccountTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getDrugOriginCodeAccountTypeIn().size()))
                        .replace("#storageCodeDepartmentIdIs", "?")
                        .replace("#locationLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?")
                        .replace("#searchLike", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#drugOriginCodeDrugOriginNameLike")) {
                sqlParams.add("%" + qto.getDrugOriginCodeDrugOriginNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#drugOriginCodeAccountTypeIn")) {
                sqlParams.addAll(qto.getDrugOriginCodeAccountTypeIn());
            } else if (paramName.equalsIgnoreCase("#storageCodeDepartmentIdIs")) {
                sqlParams.add(qto.getStorageCodeDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#locationLike")) {
                sqlParams.add("%" + qto.getLocationLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            } else if (paramName.equalsIgnoreCase("#searchLike")) {
                sqlParams.add("%" + qto.getSearchLike() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  \"drugOriginCode\".drug_origin_name asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
