package com.pulse.drug_inventory.manager.bo;

import com.pulse.drug_inventory.persist.dos.DrugImportDetail;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.DecimalMin;

@DynamicInsert
@Getter
@Setter
@Table(name = "drug_import_detail")
@Entity
@AutoGenerated(locked = true, uuid = "43171823-3a3b-462a-bca6-14c8227d67e2|BO|DEFINITION")
public class DrugImportDetailBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 验收结果 */
    @Column(name = "accept_result")
    @AutoGenerated(locked = true, uuid = "03b58f32-331b-46c0-9dd9-8dfbb2a95260")
    private String acceptResult;

    /** 验收员工ID */
    @Column(name = "accept_staff_id")
    @AutoGenerated(locked = true, uuid = "0fee84cd-d844-4e84-ba7c-74a7d349a326")
    private String acceptStaffId;

    /** 帐簿类别 */
    @Column(name = "account_type_code")
    @AutoGenerated(locked = true, uuid = "********-9ed3-3043-8858-431ea902231d")
    private String accountTypeCode;

    /** 财务是否对账 */
    @Column(name = "accountant_flag")
    @AutoGenerated(locked = true, uuid = "ba09e15b-8706-36e8-aaad-0822f9151009")
    private Boolean accountantFlag;

    /** 数量 */
    @Column(name = "amount")
    @AutoGenerated(locked = true, uuid = "0809fb2e-0000-33d9-acd9-532292270b3a")
    private BigDecimal amount;

    /** 拆分系数 */
    @Column(name = "amount_per_package")
    @AutoGenerated(locked = true, uuid = "24b2ccd1-423a-357f-8d40-4ce172faab1c")
    private Long amountPerPackage;

    /** 批次库存ID */
    @Column(name = "batch_inventory_id")
    @AutoGenerated(locked = true, uuid = "f427bb21-0b13-39a1-9c88-d25e19dc756b")
    private String batchInventoryId;

    /** 批号 */
    @Column(name = "batch_number")
    @AutoGenerated(locked = true, uuid = "7268ffdd-5c57-3b05-a68b-94e9d01b1596")
    private String batchNumber;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "554520db-2677-3751-b714-86e5d701694c")
    private Date createdAt;

    /** 折扣/扣率 */
    @Column(name = "discount_rate")
    @AutoGenerated(locked = true, uuid = "5464845e-a6c1-38f4-b6fc-7e04ff7b7dfb")
    private BigDecimal discountRate;

    /** 出库单明细id */
    @Column(name = "drug_export_detail_id")
    @AutoGenerated(locked = true, uuid = "2259530d-ecda-3395-8580-3184a58f023d")
    private String drugExportDetailId;

    @ManyToOne
    @JoinColumn(name = "drug_import_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private DrugImportBO drugImportBO;

    /** 商品编码 冗余存 */
    @Column(name = "drug_origin_code")
    @AutoGenerated(locked = true, uuid = "96605381-9ab0-39ce-a4be-3be9520cb352")
    private String drugOriginCode;

    /** 药品产地名称 */
    @Column(name = "drug_origin_name")
    @AutoGenerated(locked = true, uuid = "ad6c46b8-38f0-34b2-a3bd-e691da505b89")
    private String drugOriginName;

    /** 药品产地规格id 描述入库数量的规格单位 */
    @Column(name = "drug_origin_specification_id")
    @AutoGenerated(locked = true, uuid = "baf15aea-c63b-32e3-b101-ee0c289e852b")
    private String drugOriginSpecificationId;

    /** 采购单明细id */
    @Column(name = "drug_purchase_detail_id")
    @AutoGenerated(locked = true, uuid = "9857b7cd-74a4-3192-bf97-c9945d9c704e")
    private String drugPurchaseDetailId;

    /** 药品规格 */
    @Column(name = "drug_specification")
    @AutoGenerated(locked = true, uuid = "a1925738-098e-3416-b748-6582752f4b69")
    private String drugSpecification;

    /** 对应电子发票号 */
    @Column(name = "electronic_invoice_code")
    @AutoGenerated(locked = true, uuid = "4015a86e-d0cd-32d1-8f4e-fd9c9c0105c6")
    private String electronicInvoiceCode;

    /** 有效期 */
    @Column(name = "expiration_date")
    @AutoGenerated(locked = true, uuid = "c30b5139-d9c1-31ca-8af1-bda1ecd2a364")
    private Date expirationDate;

    /** 厂家 */
    @Column(name = "firm_id")
    @AutoGenerated(locked = true, uuid = "5c89795c-f6d1-3c5f-9fcd-7d3563dd8add")
    private String firmId;

    /** GCP代码 */
    @Column(name = "gcp_code")
    @AutoGenerated(locked = true, uuid = "292cb87d-4bfa-4a12-acaa-3098334d5355")
    private String gcpCode;

    /** GCP标志 */
    @Column(name = "gcp_flag")
    @AutoGenerated(locked = true, uuid = "02a8d530-b0a3-3bd6-aed1-685a97af2834")
    private Boolean gcpFlag;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "6536eae8-8d2f-399b-93ad-c16e293d8573")
    @Id
    private String id;

    /** 入库采购日期 */
    @Column(name = "import_purchase_date")
    @AutoGenerated(locked = true, uuid = "e85b737b-e8b2-4e1a-ba0a-cf95ea05e84f")
    private Date importPurchaseDate;

    /** 发票号 */
    @Column(name = "invoice_code")
    @AutoGenerated(locked = true, uuid = "334f0ac0-6173-3d63-96a9-7c4638326205")
    private String invoiceCode;

    /** 发票日期 */
    @Column(name = "invoice_date_time")
    @AutoGenerated(locked = true, uuid = "184a79d9-2c5d-3bb2-bead-e42f3b511373")
    private Date invoiceDateTime;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 配送明细id */
    @Column(name = "platform_order_distribute_id")
    @AutoGenerated(locked = true, uuid = "7860d2a5-d955-3c58-bcea-b6c62f4a6847")
    private String platformOrderDistributeId;

    /** 生产日期 */
    @Column(name = "production_date")
    @AutoGenerated(locked = true, uuid = "8a3ff6c1-397c-39d6-9208-d7616527b882")
    private Date productionDate;

    /** 进货金额 */
    @Column(name = "purchase_cost")
    @AutoGenerated(locked = true, uuid = "c423a3b8-5814-3070-a200-a04d9c0eb1c6")
    private BigDecimal purchaseCost;

    /** 进货价 */
    @Column(name = "purchase_price")
    @AutoGenerated(locked = true, uuid = "be460530-c5ef-3e0e-8c04-795cc086efc4")
    @DecimalMin(value = "0", inclusive = true, message = "进货价 值不合法")
    private BigDecimal purchasePrice;

    /** 退药对应入库明细id */
    @Column(name = "refund_import_detail_id")
    @AutoGenerated(locked = true, uuid = "9e27e34d-1d3f-3f45-afd4-c0fe769159a1")
    private String refundImportDetailId;

    /** 摘要 */
    @Column(name = "remark")
    @AutoGenerated(locked = true, uuid = "eb9ff815-deab-3932-80e9-ba1c660184a2")
    private String remark;

    /** 零售金额 */
    @Column(name = "retail_cost")
    @AutoGenerated(locked = true, uuid = "fcf01740-7054-37f1-9648-779be0abf47a")
    private BigDecimal retailCost;

    /** 零售价 */
    @Column(name = "retail_price")
    @AutoGenerated(locked = true, uuid = "84a2950e-196e-3cf3-aa35-5207f85e01e5")
    @DecimalMin(value = "0", inclusive = true, message = "零售价 值不合法")
    private BigDecimal retailPrice;

    /** 退单标志 */
    @Column(name = "return_order_flag")
    @AutoGenerated(locked = true, uuid = "4df1f873-5ffa-409a-8617-c03d7e812bee")
    private Boolean returnOrderFlag;

    /** 结算单id */
    @Column(name = "settle_id")
    @AutoGenerated(locked = true, uuid = "cda1b932-c26d-3f66-b97c-f9d923796f48")
    private String settleId;

    /** 排序号 */
    @Column(name = "sort_number")
    @AutoGenerated(locked = true, uuid = "0ed17e1f-5f4e-3981-989e-f64c40ffa806")
    private Long sortNumber;

    /** 规格单位 */
    @Column(name = "specification_unit")
    @AutoGenerated(locked = true, uuid = "d69497d4-5cb0-366a-942e-fe95fd544721")
    private String specificationUnit;

    /** 盘点单明细id */
    @Column(name = "stocktaking_detail_id")
    @AutoGenerated(locked = true, uuid = "49bf5656-17af-3052-aacc-39de0daad624")
    private String stocktakingDetailId;

    /** 供货商id */
    @Column(name = "supplier_id")
    @AutoGenerated(locked = true, uuid = "3681f1dd-bea4-37cf-9f9d-************")
    private String supplierId;

    /** 招标标志 */
    @Column(name = "tender_flag")
    @AutoGenerated(locked = true, uuid = "61dd50d6-e77a-4bb2-8149-2e7c06de1cd0")
    private Boolean tenderFlag;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "961e0556-0ebe-3cee-90ff-71cf8a523c36")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "6e27ebe9-5acb-4a8a-8a2f-067582616770|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public DrugImportDetail convertToDrugImportDetail() {
        DrugImportDetail entity = new DrugImportDetail();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "sortNumber",
                "drugOriginCode",
                "drugOriginSpecificationId",
                "drugOriginName",
                "drugSpecification",
                "specificationUnit",
                "amountPerPackage",
                "firmId",
                "amount",
                "batchNumber",
                "expirationDate",
                "accountantFlag",
                "productionDate",
                "purchasePrice",
                "purchaseCost",
                "discountRate",
                "retailPrice",
                "retailCost",
                "remark",
                "refundImportDetailId",
                "batchInventoryId",
                "electronicInvoiceCode",
                "invoiceCode",
                "invoiceDateTime",
                "importPurchaseDate",
                "drugPurchaseDetailId",
                "drugExportDetailId",
                "stocktakingDetailId",
                "platformOrderDistributeId",
                "settleId",
                "accountTypeCode",
                "supplierId",
                "gcpFlag",
                "returnOrderFlag",
                "gcpCode",
                "tenderFlag",
                "acceptResult",
                "acceptStaffId",
                "createdAt",
                "updatedAt");
        DrugImportBO drugImportBO = this.getDrugImportBO();
        entity.setDrugImportId(drugImportBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getAcceptResult() {
        return this.acceptResult;
    }

    @AutoGenerated(locked = true)
    public String getAcceptStaffId() {
        return this.acceptStaffId;
    }

    @AutoGenerated(locked = true)
    public String getAccountTypeCode() {
        return this.accountTypeCode;
    }

    @AutoGenerated(locked = true)
    public Boolean getAccountantFlag() {
        return this.accountantFlag;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getAmount() {
        return this.amount;
    }

    @AutoGenerated(locked = true)
    public Long getAmountPerPackage() {
        return this.amountPerPackage;
    }

    @AutoGenerated(locked = true)
    public String getBatchInventoryId() {
        return this.batchInventoryId;
    }

    @AutoGenerated(locked = true)
    public String getBatchNumber() {
        return this.batchNumber;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getDiscountRate() {
        return this.discountRate;
    }

    @AutoGenerated(locked = true)
    public String getDrugExportDetailId() {
        return this.drugExportDetailId;
    }

    @AutoGenerated(locked = true)
    public DrugImportBO getDrugImportBO() {
        return this.drugImportBO;
    }

    @AutoGenerated(locked = true)
    public String getDrugImportId() {
        return this.getDrugImportBO().getId();
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginCode() {
        return this.drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginName() {
        return this.drugOriginName;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginSpecificationId() {
        return this.drugOriginSpecificationId;
    }

    @AutoGenerated(locked = true)
    public String getDrugPurchaseDetailId() {
        return this.drugPurchaseDetailId;
    }

    @AutoGenerated(locked = true)
    public String getDrugSpecification() {
        return this.drugSpecification;
    }

    @AutoGenerated(locked = true)
    public String getElectronicInvoiceCode() {
        return this.electronicInvoiceCode;
    }

    @AutoGenerated(locked = true)
    public Date getExpirationDate() {
        return this.expirationDate;
    }

    @AutoGenerated(locked = true)
    public String getFirmId() {
        return this.firmId;
    }

    @AutoGenerated(locked = true)
    public String getGcpCode() {
        return this.gcpCode;
    }

    @AutoGenerated(locked = true)
    public Boolean getGcpFlag() {
        return this.gcpFlag;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public Date getImportPurchaseDate() {
        return this.importPurchaseDate;
    }

    @AutoGenerated(locked = true)
    public String getInvoiceCode() {
        return this.invoiceCode;
    }

    @AutoGenerated(locked = true)
    public Date getInvoiceDateTime() {
        return this.invoiceDateTime;
    }

    @AutoGenerated(locked = true)
    public String getPlatformOrderDistributeId() {
        return this.platformOrderDistributeId;
    }

    @AutoGenerated(locked = true)
    public Date getProductionDate() {
        return this.productionDate;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getPurchaseCost() {
        return this.purchaseCost;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getPurchasePrice() {
        return this.purchasePrice;
    }

    @AutoGenerated(locked = true)
    public String getRefundImportDetailId() {
        return this.refundImportDetailId;
    }

    @AutoGenerated(locked = true)
    public String getRemark() {
        return this.remark;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getRetailCost() {
        return this.retailCost;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getRetailPrice() {
        return this.retailPrice;
    }

    @AutoGenerated(locked = true)
    public Boolean getReturnOrderFlag() {
        return this.returnOrderFlag;
    }

    @AutoGenerated(locked = true)
    public String getSettleId() {
        return this.settleId;
    }

    @AutoGenerated(locked = true)
    public Long getSortNumber() {
        return this.sortNumber;
    }

    @AutoGenerated(locked = true)
    public String getSpecificationUnit() {
        return this.specificationUnit;
    }

    @AutoGenerated(locked = true)
    public String getStocktakingDetailId() {
        return this.stocktakingDetailId;
    }

    @AutoGenerated(locked = true)
    public String getSupplierId() {
        return this.supplierId;
    }

    @AutoGenerated(locked = true)
    public Boolean getTenderFlag() {
        return this.tenderFlag;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setAcceptResult(String acceptResult) {
        this.acceptResult = acceptResult;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setAcceptStaffId(String acceptStaffId) {
        this.acceptStaffId = acceptStaffId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setAccountTypeCode(String accountTypeCode) {
        this.accountTypeCode = accountTypeCode;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setAccountantFlag(Boolean accountantFlag) {
        this.accountantFlag = accountantFlag;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setAmount(BigDecimal amount) {
        this.amount = amount;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setAmountPerPackage(Long amountPerPackage) {
        this.amountPerPackage = amountPerPackage;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setBatchInventoryId(String batchInventoryId) {
        this.batchInventoryId = batchInventoryId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDrugExportDetailId(String drugExportDetailId) {
        this.drugExportDetailId = drugExportDetailId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDrugImportBO(DrugImportBO drugImportBO) {
        this.drugImportBO = drugImportBO;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDrugOriginCode(String drugOriginCode) {
        this.drugOriginCode = drugOriginCode;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDrugOriginName(String drugOriginName) {
        this.drugOriginName = drugOriginName;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDrugOriginSpecificationId(String drugOriginSpecificationId) {
        this.drugOriginSpecificationId = drugOriginSpecificationId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDrugPurchaseDetailId(String drugPurchaseDetailId) {
        this.drugPurchaseDetailId = drugPurchaseDetailId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setDrugSpecification(String drugSpecification) {
        this.drugSpecification = drugSpecification;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setElectronicInvoiceCode(String electronicInvoiceCode) {
        this.electronicInvoiceCode = electronicInvoiceCode;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setFirmId(String firmId) {
        this.firmId = firmId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setGcpCode(String gcpCode) {
        this.gcpCode = gcpCode;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setGcpFlag(Boolean gcpFlag) {
        this.gcpFlag = gcpFlag;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setId(String id) {
        this.id = id;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setImportPurchaseDate(Date importPurchaseDate) {
        this.importPurchaseDate = importPurchaseDate;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setInvoiceDateTime(Date invoiceDateTime) {
        this.invoiceDateTime = invoiceDateTime;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setPlatformOrderDistributeId(String platformOrderDistributeId) {
        this.platformOrderDistributeId = platformOrderDistributeId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setPurchaseCost(BigDecimal purchaseCost) {
        this.purchaseCost = purchaseCost;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setRefundImportDetailId(String refundImportDetailId) {
        this.refundImportDetailId = refundImportDetailId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setRemark(String remark) {
        this.remark = remark;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setRetailCost(BigDecimal retailCost) {
        this.retailCost = retailCost;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setReturnOrderFlag(Boolean returnOrderFlag) {
        this.returnOrderFlag = returnOrderFlag;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setSettleId(String settleId) {
        this.settleId = settleId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setSpecificationUnit(String specificationUnit) {
        this.specificationUnit = specificationUnit;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setStocktakingDetailId(String stocktakingDetailId) {
        this.stocktakingDetailId = stocktakingDetailId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setSupplierId(String supplierId) {
        this.supplierId = supplierId;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setTenderFlag(Boolean tenderFlag) {
        this.tenderFlag = tenderFlag;
        return (DrugImportDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugImportDetailBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugImportDetailBO) this;
    }
}
