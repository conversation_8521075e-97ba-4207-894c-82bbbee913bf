package com.pulse.drug_inventory.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.DrugExportBaseDtoManager;
import com.pulse.drug_inventory.manager.converter.DrugExportBaseDtoConverter;
import com.pulse.drug_inventory.manager.dto.DrugExportBaseDto;
import com.pulse.drug_inventory.persist.dos.DrugExport;
import com.pulse.drug_inventory.persist.mapper.DrugExportDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "ee39b657-c064-4cf4-9231-3d2708d9e45d|DTO|BASE_MANAGER_IMPL")
public abstract class DrugExportBaseDtoManagerBaseImpl implements DrugExportBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportBaseDtoConverter drugExportBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportDao drugExportDao;

    @AutoGenerated(locked = true, uuid = "08cbfb19-b5bb-3714-b2c6-7f39ca9c0257")
    @Override
    public List<DrugExportBaseDto> getByApplyStaffIds(List<String> applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applyStaffId)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByApplyStaffIds(applyStaffId);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2874f5f3-fa64-3545-b940-3e1d2daebcf9")
    @Override
    public List<DrugExportBaseDto> getByStorageCodes(List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(storageCode)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByStorageCodes(storageCode);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "30c22450-b91d-3f0e-8606-f06292529839")
    @Override
    public List<DrugExportBaseDto> getByAcceptanceStaffId(String acceptanceStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByAcceptanceStaffIds(Arrays.asList(acceptanceStaffId));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3f4241fe-b1cf-3923-87d9-52a2c69c836e")
    @Override
    public List<DrugExportBaseDto> getByDrugImportId(String drugImportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByDrugImportIds(Arrays.asList(drugImportId));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "42d5faf4-a85e-3a13-b670-f7a920fdeaf2")
    @Override
    public List<DrugExportBaseDto> getByDrugImportIds(List<String> drugImportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugImportId)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByDrugImportIds(drugImportId);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "43f0ebd5-1142-346d-a5f5-4e3b58f2c3b1")
    @Override
    public List<DrugExportBaseDto> getByDrugApplyIds(List<String> drugApplyId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugApplyId)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByDrugApplyIds(drugApplyId);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "47ef0ad3-5d75-34eb-acec-06081019dfd8")
    @Override
    public List<DrugExportBaseDto> getByAcceptanceStaffIds(List<String> acceptanceStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(acceptanceStaffId)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByAcceptanceStaffIds(acceptanceStaffId);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4b352757-1230-3df4-8be9-7ccd7b7a4035")
    @Override
    public List<DrugExportBaseDto> getByStocktakingId(String stocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByStocktakingIds(Arrays.asList(stocktakingId));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "562eb505-afe4-3cea-b991-4b67213989eb")
    @Override
    public List<DrugExportBaseDto> getByDrugApplyId(String drugApplyId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByDrugApplyIds(Arrays.asList(drugApplyId));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "59fbec8a-e195-3dfa-bb0c-2439ceaed8e2")
    @Override
    public List<DrugExportBaseDto> getByRefundExportId(String refundExportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByRefundExportIds(Arrays.asList(refundExportId));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5e241afe-bd01-32f8-b576-bdde278e21cb")
    public List<DrugExportBaseDto> doConvertFromDrugExportToDrugExportBaseDto(
            List<DrugExport> drugExportList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        Map<String, DrugExportBaseDto> dtoMap =
                drugExportBaseDtoConverter
                        .convertFromDrugExportToDrugExportBaseDto(drugExportList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugExportBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugExportBaseDto> drugExportBaseDtoList = new ArrayList<>();
        for (DrugExport i : drugExportList) {
            DrugExportBaseDto drugExportBaseDto = dtoMap.get(i.getId());
            if (drugExportBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugExportBaseDtoList.add(drugExportBaseDto);
        }
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "6d91b732-c9b5-32d0-8726-5565dac60308")
    @Override
    public List<DrugExportBaseDto> getByAccountantStaffIds(List<String> accountantStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(accountantStaffId)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByAccountantStaffIds(accountantStaffId);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "6da02e17-79b8-3e23-8f6c-ffa3d0de7b58")
    @Override
    public List<DrugExportBaseDto> getByDocumentNumbers(List<String> documentNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(documentNumber)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByDocumentNumbers(documentNumber);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "6ffe3d94-0ffb-367f-ae0e-ead2cca55a28")
    @Override
    public List<DrugExportBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugExport> drugExportMap =
                drugExportList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugExportList =
                id.stream()
                        .map(i -> drugExportMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "76d05432-a260-33a1-830d-63d9c903f4cf")
    @Override
    public DrugExportBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugExportBaseDto drugExportBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugExportBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8e8b6154-f7da-346e-b15c-e9e9c0ba1050")
    @Override
    public List<DrugExportBaseDto> getByStocktakingIds(List<String> stocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(stocktakingId)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByStocktakingIds(stocktakingId);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9d60ab3b-503b-36a1-81cc-cc9d91cacfff")
    @Override
    public List<DrugExportBaseDto> getByRefundExportIds(List<String> refundExportId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(refundExportId)) {
            return Collections.emptyList();
        }

        List<DrugExport> drugExportList = drugExportDao.getByRefundExportIds(refundExportId);
        if (CollectionUtil.isEmpty(drugExportList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugExportToDrugExportBaseDto(drugExportList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a7ff281d-81d6-30e9-ab21-58ccadcd14fd")
    @Override
    public List<DrugExportBaseDto> getByApplyStaffId(String applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByApplyStaffIds(Arrays.asList(applyStaffId));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a973abd3-9d1d-39df-aa40-d492d39ac475")
    @Override
    public DrugExportBaseDto getByDocumentNumber(String documentNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> ret = getByDocumentNumbers(Arrays.asList(documentNumber));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugExportBaseDto drugExportBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugExportBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b1ae7af3-34cc-3051-af3f-6d66f2127255")
    @Override
    public List<DrugExportBaseDto> getByStorageCode(String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByStorageCodes(Arrays.asList(storageCode));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e5186c67-63f8-364c-99bf-e29b7daa9178")
    @Override
    public List<DrugExportBaseDto> getByAccountantStaffId(String accountantStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportBaseDto> drugExportBaseDtoList =
                getByAccountantStaffIds(Arrays.asList(accountantStaffId));
        return drugExportBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
