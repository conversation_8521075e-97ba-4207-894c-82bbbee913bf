package com.pulse.drug_inventory.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.dto.DrugOriginInventoryBaseDto;
import com.pulse.drug_inventory.persist.dos.DrugOriginInventory;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "6f6a7c8f-77ff-4013-98b2-edfb24cf5936|DTO|BASE_CONVERTER")
public class DrugOriginInventoryBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugOriginInventoryBaseDto convertFromDrugOriginInventoryToDrugOriginInventoryBaseDto(
            DrugOriginInventory drugOriginInventory) {
        return convertFromDrugOriginInventoryToDrugOriginInventoryBaseDto(
                        List.of(drugOriginInventory))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugOriginInventoryBaseDto>
            convertFromDrugOriginInventoryToDrugOriginInventoryBaseDto(
                    List<DrugOriginInventory> drugOriginInventoryList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginInventoryList)) {
            return new ArrayList<>();
        }
        List<DrugOriginInventoryBaseDto> drugOriginInventoryBaseDtoList = new ArrayList<>();
        for (DrugOriginInventory drugOriginInventory : drugOriginInventoryList) {
            if (drugOriginInventory == null) {
                continue;
            }
            DrugOriginInventoryBaseDto drugOriginInventoryBaseDto =
                    new DrugOriginInventoryBaseDto();
            drugOriginInventoryBaseDto.setId(drugOriginInventory.getId());
            drugOriginInventoryBaseDto.setStorageCode(drugOriginInventory.getStorageCode());
            drugOriginInventoryBaseDto.setDrugOriginCode(drugOriginInventory.getDrugOriginCode());
            drugOriginInventoryBaseDto.setAmount(drugOriginInventory.getAmount());
            drugOriginInventoryBaseDto.setPreOccupiedAmount(
                    drugOriginInventory.getPreOccupiedAmount());
            drugOriginInventoryBaseDto.setInTransitAmount(drugOriginInventory.getInTransitAmount());
            drugOriginInventoryBaseDto.setVirtualAmount(drugOriginInventory.getVirtualAmount());
            drugOriginInventoryBaseDto.setUseFrequency(drugOriginInventory.getUseFrequency());
            drugOriginInventoryBaseDto.setDrugOriginSpecificationId(
                    drugOriginInventory.getDrugOriginSpecificationId());
            drugOriginInventoryBaseDto.setLockVersion(drugOriginInventory.getLockVersion());
            drugOriginInventoryBaseDto.setCreatedAt(drugOriginInventory.getCreatedAt());
            drugOriginInventoryBaseDto.setUpdatedAt(drugOriginInventory.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugOriginInventoryBaseDtoList.add(drugOriginInventoryBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugOriginInventoryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
