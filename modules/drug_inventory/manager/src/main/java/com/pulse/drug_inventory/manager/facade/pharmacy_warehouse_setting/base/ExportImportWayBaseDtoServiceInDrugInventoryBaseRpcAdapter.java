package com.pulse.drug_inventory.manager.facade.pharmacy_warehouse_setting.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.pharmacy_warehouse_setting.manager.dto.ExportImportWayBaseDto;
import com.pulse.pharmacy_warehouse_setting.persist.eo.UkStorageCodeWayCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "1f74dba1-18f8-3000-99db-8c1a7b639e79")
public class ExportImportWayBaseDtoServiceInDrugInventoryBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "c42ce0c9-f807-4e1e-93a7-9285122c8597|RPC|BASE_ADAPTER")
    public ExportImportWayBaseDto getById(String id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/pharmacy_warehouse_setting/c42ce0c9-f807-4e1e-93a7-9285122c8597/ExportImportWayBaseDtoService-getById",
                        "com.pulse.pharmacy_warehouse_setting.service.ExportImportWayBaseDtoService",
                        "getById",
                        paramMap,
                        paramTypeMap,
                        "7ead5bf0-4f48-414d-9026-d83055a95c8b",
                        "c47ef390-e56c-4ad4-bf5b-04a1d408c462"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "ef31cafc-471d-4291-8c4b-d4a2308a4f9e|RPC|BASE_ADAPTER")
    public ExportImportWayBaseDto getByStorageCodeAndWayCode(UkStorageCodeWayCodeEo var) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("var", var);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("var", UkStorageCodeWayCodeEo.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/pharmacy_warehouse_setting/ef31cafc-471d-4291-8c4b-d4a2308a4f9e/ExportImportWayBaseDtoService-getByStorageCodeAndWayCode",
                        "com.pulse.pharmacy_warehouse_setting.service.ExportImportWayBaseDtoService",
                        "getByStorageCodeAndWayCode",
                        paramMap,
                        paramTypeMap,
                        "7ead5bf0-4f48-414d-9026-d83055a95c8b",
                        "c47ef390-e56c-4ad4-bf5b-04a1d408c462"),
                new TypeReference<>() {});
    }
}
