package com.pulse.drug_report.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;

/**
 * <b>[源自]</b> DrugDetailLedger
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "44152fe5-f81d-43c3-888d-b923d457b2c7|BTO|DEFINITION")
public class CreateDrugDetailLedgerBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 记账人 */
    @AutoGenerated(locked = true, uuid = "44f26c85-f015-47c5-bda8-773c1b86b5a9")
    private String accountStaffId;

    /** 账簿类别 */
    @AutoGenerated(locked = true, uuid = "2c61e7c2-ac82-472a-9d42-18f9ba943a12")
    private String accountType;

    /** 记账日期 */
    @AutoGenerated(locked = true, uuid = "3119209e-656f-4d68-b56c-ddbe356410de")
    private Date accountantDateTime;

    /** 会计期间ID */
    @AutoGenerated(locked = true, uuid = "e5468d92-3303-41c6-81ae-83d852449865")
    private String accountingPeriodId;

    /** 数量 */
    @AutoGenerated(locked = true, uuid = "6c110da9-f0cc-4719-a27a-282d8b4ac4bc")
    private BigDecimal amount;

    /** 业务单据id */
    @AutoGenerated(locked = true, uuid = "4d1fd158-5a90-4a20-b7b1-5260fca0b9df")
    private String businessDocumentId;

    /** 借还药明细id */
    @AutoGenerated(locked = true, uuid = "1fa1f6c6-3afa-4527-9fed-083d7aba5d26")
    private String drugBorrowDetailId;

    /** 批次库存id */
    @AutoGenerated(locked = true, uuid = "0824ba00-a9ce-4d9e-a2f6-4c2d5f242bb7")
    private String drugInventoryBatchId;

    /** 报损单明细id */
    @AutoGenerated(locked = true, uuid = "3ba9f527-0956-42ea-9b25-d382a6105e70")
    private String drugLossReportDetailId;

    /** 摆药批次明细id */
    @AutoGenerated(locked = true, uuid = "f3898054-bc1a-460d-8ab4-c476c07be776")
    private String drugOrderDispenseDetailBatchId;

    /** 药品产地编码 */
    @AutoGenerated(locked = true, uuid = "c052f38f-4d40-4443-b45a-0893c7b381ae")
    private String drugOriginCode;

    /** 药品名称 */
    @AutoGenerated(locked = true, uuid = "bfde9073-9f60-4223-9004-490900a083b5")
    private String drugOriginName;

    /** 药品产地规格id */
    @AutoGenerated(locked = true, uuid = "f9b7a465-2f61-47b4-a807-0a3d38df3f30")
    private String drugOriginSpecificationId;

    /** 处方发药批次明细id */
    @AutoGenerated(locked = true, uuid = "6255134c-e924-48bb-b75b-e4de9a8519e5")
    private String drugPrescriptionDispenseDetailBatchId;

    /** 调价单明细id */
    @AutoGenerated(locked = true, uuid = "84de3536-cace-4539-beb1-9079b73ab26e")
    private String drugPriceAdjustDetailId;

    /** 盘存单明细id */
    @AutoGenerated(locked = true, uuid = "3ea665ae-2287-4864-b40d-cbb5e6ba0f9c")
    private String drugStocktakingDetailId;

    /** 出库单明细id */
    @AutoGenerated(locked = true, uuid = "11ea6809-c978-426e-9a66-1288cb396ce5")
    private String exportDetailId;

    /** 出入库方式id */
    @AutoGenerated(locked = true, uuid = "fb0c904a-8d67-4615-a2a5-5487869227c5")
    private String exportImportId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "6959d5a5-ff00-4a28-8402-305dbcbf7c01")
    private String id;

    /** 入库单明细id */
    @AutoGenerated(locked = true, uuid = "4c9a2c5b-a175-489a-85ae-763da18e3804")
    private String importDetailId;

    /** 结转id */
    @AutoGenerated(locked = true, uuid = "da833207-0ae7-4947-a638-2daf1df046d0")
    private String monthlyFinancialReportId;

    /** 进价金额 */
    @AutoGenerated(locked = true, uuid = "5f11199c-93e8-4440-845a-62a5367a9451")
    private BigDecimal purchaseCost;

    /** 进价 */
    @AutoGenerated(locked = true, uuid = "f1a5ae4a-**************-b9b151ee7526")
    private BigDecimal purchasePrice;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "c9e3a4b7-9fbc-4fe9-8cbc-deacf595e456")
    private String remark;

    /** 零售金额 */
    @AutoGenerated(locked = true, uuid = "4794727b-c539-47c4-af68-bc4b57e5e205")
    private BigDecimal retailCost;

    /** 零售价 */
    @AutoGenerated(locked = true, uuid = "27bc217b-4fba-4d7f-bf8b-e121fafc6a22")
    private BigDecimal retailPrice;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "d85e6bfa-4b5b-44fd-b7d1-0b59be722fe8")
    private String storageCode;

    @AutoGenerated(locked = true)
    public void setAccountStaffId(String accountStaffId) {
        this.__$validPropertySet.add("accountStaffId");
        this.accountStaffId = accountStaffId;
    }

    @AutoGenerated(locked = true)
    public void setAccountType(String accountType) {
        this.__$validPropertySet.add("accountType");
        this.accountType = accountType;
    }

    @AutoGenerated(locked = true)
    public void setAccountantDateTime(Date accountantDateTime) {
        this.__$validPropertySet.add("accountantDateTime");
        this.accountantDateTime = accountantDateTime;
    }

    @AutoGenerated(locked = true)
    public void setAccountingPeriodId(String accountingPeriodId) {
        this.__$validPropertySet.add("accountingPeriodId");
        this.accountingPeriodId = accountingPeriodId;
    }

    @AutoGenerated(locked = true)
    public void setAmount(BigDecimal amount) {
        this.__$validPropertySet.add("amount");
        this.amount = amount;
    }

    @AutoGenerated(locked = true)
    public void setBusinessDocumentId(String businessDocumentId) {
        this.__$validPropertySet.add("businessDocumentId");
        this.businessDocumentId = businessDocumentId;
    }

    @AutoGenerated(locked = true)
    public void setDrugBorrowDetailId(String drugBorrowDetailId) {
        this.__$validPropertySet.add("drugBorrowDetailId");
        this.drugBorrowDetailId = drugBorrowDetailId;
    }

    @AutoGenerated(locked = true)
    public void setDrugInventoryBatchId(String drugInventoryBatchId) {
        this.__$validPropertySet.add("drugInventoryBatchId");
        this.drugInventoryBatchId = drugInventoryBatchId;
    }

    @AutoGenerated(locked = true)
    public void setDrugLossReportDetailId(String drugLossReportDetailId) {
        this.__$validPropertySet.add("drugLossReportDetailId");
        this.drugLossReportDetailId = drugLossReportDetailId;
    }

    @AutoGenerated(locked = true)
    public void setDrugOrderDispenseDetailBatchId(String drugOrderDispenseDetailBatchId) {
        this.__$validPropertySet.add("drugOrderDispenseDetailBatchId");
        this.drugOrderDispenseDetailBatchId = drugOrderDispenseDetailBatchId;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginCode(String drugOriginCode) {
        this.__$validPropertySet.add("drugOriginCode");
        this.drugOriginCode = drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginName(String drugOriginName) {
        this.__$validPropertySet.add("drugOriginName");
        this.drugOriginName = drugOriginName;
    }

    @AutoGenerated(locked = true)
    public void setDrugOriginSpecificationId(String drugOriginSpecificationId) {
        this.__$validPropertySet.add("drugOriginSpecificationId");
        this.drugOriginSpecificationId = drugOriginSpecificationId;
    }

    @AutoGenerated(locked = true)
    public void setDrugPrescriptionDispenseDetailBatchId(
            String drugPrescriptionDispenseDetailBatchId) {
        this.__$validPropertySet.add("drugPrescriptionDispenseDetailBatchId");
        this.drugPrescriptionDispenseDetailBatchId = drugPrescriptionDispenseDetailBatchId;
    }

    @AutoGenerated(locked = true)
    public void setDrugPriceAdjustDetailId(String drugPriceAdjustDetailId) {
        this.__$validPropertySet.add("drugPriceAdjustDetailId");
        this.drugPriceAdjustDetailId = drugPriceAdjustDetailId;
    }

    @AutoGenerated(locked = true)
    public void setDrugStocktakingDetailId(String drugStocktakingDetailId) {
        this.__$validPropertySet.add("drugStocktakingDetailId");
        this.drugStocktakingDetailId = drugStocktakingDetailId;
    }

    @AutoGenerated(locked = true)
    public void setExportDetailId(String exportDetailId) {
        this.__$validPropertySet.add("exportDetailId");
        this.exportDetailId = exportDetailId;
    }

    @AutoGenerated(locked = true)
    public void setExportImportId(String exportImportId) {
        this.__$validPropertySet.add("exportImportId");
        this.exportImportId = exportImportId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setImportDetailId(String importDetailId) {
        this.__$validPropertySet.add("importDetailId");
        this.importDetailId = importDetailId;
    }

    @AutoGenerated(locked = true)
    public void setMonthlyFinancialReportId(String monthlyFinancialReportId) {
        this.__$validPropertySet.add("monthlyFinancialReportId");
        this.monthlyFinancialReportId = monthlyFinancialReportId;
    }

    @AutoGenerated(locked = true)
    public void setPurchaseCost(BigDecimal purchaseCost) {
        this.__$validPropertySet.add("purchaseCost");
        this.purchaseCost = purchaseCost;
    }

    @AutoGenerated(locked = true)
    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.__$validPropertySet.add("purchasePrice");
        this.purchasePrice = purchasePrice;
    }

    @AutoGenerated(locked = true)
    public void setRemark(String remark) {
        this.__$validPropertySet.add("remark");
        this.remark = remark;
    }

    @AutoGenerated(locked = true)
    public void setRetailCost(BigDecimal retailCost) {
        this.__$validPropertySet.add("retailCost");
        this.retailCost = retailCost;
    }

    @AutoGenerated(locked = true)
    public void setRetailPrice(BigDecimal retailPrice) {
        this.__$validPropertySet.add("retailPrice");
        this.retailPrice = retailPrice;
    }

    @AutoGenerated(locked = true)
    public void setStorageCode(String storageCode) {
        this.__$validPropertySet.add("storageCode");
        this.storageCode = storageCode;
    }
}
