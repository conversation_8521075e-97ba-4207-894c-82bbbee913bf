package com.pulse.drug_inventory.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_inventory.manager.DrugStocktakingDetailAggDtoManager;
import com.pulse.drug_inventory.manager.DrugStocktakingDetailBaseDtoManager;
import com.pulse.drug_inventory.manager.converter.DrugStocktakingDetailAggDtoConverter;
import com.pulse.drug_inventory.manager.converter.DrugStocktakingDetailBaseDtoConverter;
import com.pulse.drug_inventory.manager.dto.DrugStocktakingDetailAggDto;
import com.pulse.drug_inventory.manager.dto.DrugStocktakingDetailBaseDto;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugOriginBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.persist.dos.DrugStocktakingDetail;
import com.pulse.drug_inventory.persist.mapper.DrugStocktakingDetailDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "ce5c7ac4-1be1-4b46-b31b-e1fb73c58d6d|DTO|BASE_MANAGER_IMPL")
public abstract class DrugStocktakingDetailAggDtoManagerBaseImpl
        implements DrugStocktakingDetailAggDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginBaseDtoServiceInDrugInventoryRpcAdapter
            drugOriginBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter
            drugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugStocktakingDetailAggDtoConverter drugStocktakingDetailAggDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugStocktakingDetailBaseDtoConverter drugStocktakingDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugStocktakingDetailBaseDtoManager drugStocktakingDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugStocktakingDetailDao drugStocktakingDetailDao;

    @AutoGenerated(locked = true, uuid = "039cf283-d717-3eeb-be91-7e4c91c0552b")
    @Override
    public DrugStocktakingDetailAggDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugStocktakingDetailAggDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugStocktakingDetailAggDto drugStocktakingDetailAggDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugStocktakingDetailAggDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "0a612692-88e9-3b6c-ac55-2966fe27c9c9")
    @Override
    public List<DrugStocktakingDetailAggDto> getByBatchInventoryId(String batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList =
                getByBatchInventoryIds(Arrays.asList(batchInventoryId));
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "47e1e317-145c-3642-8dc8-d2dd3cce4faf")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugOriginCode(String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList =
                getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "56d7b8e1-0736-39f3-a76e-f7242f9ac2ad")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugStocktakingId(String drugStocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList =
                getByDrugStocktakingIds(Arrays.asList(drugStocktakingId));
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "66e3560a-2a66-389f-958b-cb29be0cc896")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginSpecificationId)) {
            return Collections.emptyList();
        }

        List<DrugStocktakingDetail> drugStocktakingDetailList =
                drugStocktakingDetailDao.getByDrugOriginSpecificationIds(drugOriginSpecificationId);
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                drugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "69e4f29f-69cf-3aa9-830a-26fa97d7f29c")
    @Override
    public List<DrugStocktakingDetailAggDto> getBySummaryStocktakingDetailId(
            String summaryStocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList =
                getBySummaryStocktakingDetailIds(Arrays.asList(summaryStocktakingDetailId));
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8dabbe46-9660-3061-88d1-e639c4abd60a")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugStocktakingIds(
            List<String> drugStocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugStocktakingId)) {
            return Collections.emptyList();
        }

        List<DrugStocktakingDetail> drugStocktakingDetailList =
                drugStocktakingDetailDao.getByDrugStocktakingIds(drugStocktakingId);
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                drugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "904db665-0fef-387e-b6bb-6ead8d3b6db1")
    public List<DrugStocktakingDetailAggDto>
            doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                    List<DrugStocktakingDetail> drugStocktakingDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        Map<String, String> drugOriginSpecificationIdMap =
                drugStocktakingDetailList.stream()
                        .filter(i -> i.getDrugOriginSpecificationId() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugStocktakingDetail::getId,
                                        DrugStocktakingDetail::getDrugOriginSpecificationId));
        List<DrugOriginSpecificationBaseDto>
                drugOriginSpecificationIdDrugOriginSpecificationBaseDtoList =
                        drugOriginSpecificationBaseDtoServiceInDrugInventoryRpcAdapter.getByIds(
                                new ArrayList<>(
                                        new HashSet<>(drugOriginSpecificationIdMap.values())));
        Map<String, DrugOriginSpecificationBaseDto>
                drugOriginSpecificationIdDrugOriginSpecificationBaseDtoMapRaw =
                        drugOriginSpecificationIdDrugOriginSpecificationBaseDtoList.stream()
                                .collect(
                                        Collectors.toMap(
                                                DrugOriginSpecificationBaseDto::getId, i -> i));
        Map<String, DrugOriginSpecificationBaseDto>
                drugOriginSpecificationIdDrugOriginSpecificationBaseDtoMap =
                        drugOriginSpecificationIdMap.entrySet().stream()
                                .filter(
                                        i ->
                                                drugOriginSpecificationIdDrugOriginSpecificationBaseDtoMapRaw
                                                                .get(i.getValue())
                                                        != null)
                                .collect(
                                        Collectors.toMap(
                                                i -> i.getKey(),
                                                i ->
                                                        drugOriginSpecificationIdDrugOriginSpecificationBaseDtoMapRaw
                                                                .get(i.getValue())));
        Map<String, String> drugProducerIdMap =
                drugStocktakingDetailList.stream()
                        .filter(i -> i.getDrugProducerId() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugStocktakingDetail::getId,
                                        DrugStocktakingDetail::getDrugProducerId));
        List<DrugProducerDictionaryBaseDto> drugProducerIdDrugProducerDictionaryBaseDtoList =
                drugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(drugProducerIdMap.values())));
        Map<String, DrugProducerDictionaryBaseDto>
                drugProducerIdDrugProducerDictionaryBaseDtoMapRaw =
                        drugProducerIdDrugProducerDictionaryBaseDtoList.stream()
                                .collect(
                                        Collectors.toMap(
                                                DrugProducerDictionaryBaseDto::getId, i -> i));
        Map<String, DrugProducerDictionaryBaseDto> drugProducerIdDrugProducerDictionaryBaseDtoMap =
                drugProducerIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        drugProducerIdDrugProducerDictionaryBaseDtoMapRaw.get(
                                                        i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                drugProducerIdDrugProducerDictionaryBaseDtoMapRaw
                                                        .get(i.getValue())));
        Map<String, String> drugOriginCodeMap =
                drugStocktakingDetailList.stream()
                        .filter(i -> i.getDrugOriginCode() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugStocktakingDetail::getId,
                                        DrugStocktakingDetail::getDrugOriginCode));
        List<DrugOriginBaseDto> drugOriginCodeDrugOriginBaseDtoList =
                drugOriginBaseDtoServiceInDrugInventoryRpcAdapter.getByDrugOriginCodes(
                        new ArrayList<>(new HashSet<>(drugOriginCodeMap.values())));
        Map<String, DrugOriginBaseDto> drugOriginCodeDrugOriginBaseDtoMapRaw =
                drugOriginCodeDrugOriginBaseDtoList.stream()
                        .collect(Collectors.toMap(DrugOriginBaseDto::getDrugOriginCode, i -> i));
        Map<String, DrugOriginBaseDto> drugOriginCodeDrugOriginBaseDtoMap =
                drugOriginCodeMap.entrySet().stream()
                        .filter(
                                i ->
                                        drugOriginCodeDrugOriginBaseDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                drugOriginCodeDrugOriginBaseDtoMapRaw.get(
                                                        i.getValue())));

        List<DrugStocktakingDetailBaseDto> baseDtoList =
                drugStocktakingDetailBaseDtoConverter
                        .convertFromDrugStocktakingDetailToDrugStocktakingDetailBaseDto(
                                drugStocktakingDetailList);
        Map<String, DrugStocktakingDetailAggDto> dtoMap =
                drugStocktakingDetailAggDtoConverter
                        .convertFromDrugStocktakingDetailBaseDtoToDrugStocktakingDetailAggDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugStocktakingDetailAggDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList = new ArrayList<>();
        for (DrugStocktakingDetail i : drugStocktakingDetailList) {
            DrugStocktakingDetailAggDto drugStocktakingDetailAggDto = dtoMap.get(i.getId());
            if (drugStocktakingDetailAggDto == null) {
                continue;
            }

            if (null != i.getDrugOriginSpecificationId()) {
                drugStocktakingDetailAggDto.setDrugOriginSpecification(
                        drugOriginSpecificationIdDrugOriginSpecificationBaseDtoMap.getOrDefault(
                                i.getId(), null));
            }
            if (null != i.getDrugProducerId()) {
                drugStocktakingDetailAggDto.setDrugProducer(
                        drugProducerIdDrugProducerDictionaryBaseDtoMap.getOrDefault(
                                i.getId(), null));
            }
            if (null != i.getDrugOriginCode()) {
                drugStocktakingDetailAggDto.setDrugOriginCode(
                        drugOriginCodeDrugOriginBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugStocktakingDetailAggDtoList.add(drugStocktakingDetailAggDto);
        }
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "b0e6c353-5203-3d20-85e4-24c86fae2583")
    @Override
    public List<DrugStocktakingDetailAggDto> getBySummaryStocktakingDetailIds(
            List<String> summaryStocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(summaryStocktakingDetailId)) {
            return Collections.emptyList();
        }

        List<DrugStocktakingDetail> drugStocktakingDetailList =
                drugStocktakingDetailDao.getBySummaryStocktakingDetailIds(
                        summaryStocktakingDetailId);
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                drugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b82a2abb-c8b1-3af1-b598-b03ca9940940")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugOriginCodes(List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginCode)) {
            return Collections.emptyList();
        }

        List<DrugStocktakingDetail> drugStocktakingDetailList =
                drugStocktakingDetailDao.getByDrugOriginCodes(drugOriginCode);
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                drugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c22ae34f-00b1-366b-a995-a42de096d755")
    @Override
    public List<DrugStocktakingDetailAggDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugStocktakingDetail> drugStocktakingDetailList =
                drugStocktakingDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugStocktakingDetail> drugStocktakingDetailMap =
                drugStocktakingDetailList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugStocktakingDetailList =
                id.stream()
                        .map(i -> drugStocktakingDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                drugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cbc035f2-769e-3889-a7ed-e445b470ae38")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugProducerIds(List<String> drugProducerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugProducerId)) {
            return Collections.emptyList();
        }

        List<DrugStocktakingDetail> drugStocktakingDetailList =
                drugStocktakingDetailDao.getByDrugProducerIds(drugProducerId);
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                drugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "edb53c0b-33b0-3179-b676-3bdb79f4e9ac")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugOriginSpecificationId(
            String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList =
                getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f6307e3c-83e1-3a43-a57b-de838c092502")
    @Override
    public List<DrugStocktakingDetailAggDto> getByBatchInventoryIds(List<String> batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(batchInventoryId)) {
            return Collections.emptyList();
        }

        List<DrugStocktakingDetail> drugStocktakingDetailList =
                drugStocktakingDetailDao.getByBatchInventoryIds(batchInventoryId);
        if (CollectionUtil.isEmpty(drugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugStocktakingDetailToDrugStocktakingDetailAggDto(
                drugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f6be9a35-e19f-350b-8b06-e15dbedbef0e")
    @Override
    public List<DrugStocktakingDetailAggDto> getByDrugProducerId(String drugProducerId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList =
                getByDrugProducerIds(Arrays.asList(drugProducerId));
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
