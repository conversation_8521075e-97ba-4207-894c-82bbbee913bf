package com.pulse.drug_inventory.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.DrugStocktakingDetailBaseDtoManager;
import com.pulse.drug_inventory.manager.dto.DrugStocktakingDetailAggDto;
import com.pulse.drug_inventory.manager.dto.DrugStocktakingDetailBaseDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "ce5c7ac4-1be1-4b46-b31b-e1fb73c58d6d|DTO|BASE_CONVERTER")
public class DrugStocktakingDetailAggDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugStocktakingDetailBaseDtoManager drugStocktakingDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    public DrugStocktakingDetailBaseDto
            convertFromDrugStocktakingDetailAggDtoToDrugStocktakingDetailBaseDto(
                    DrugStocktakingDetailAggDto drugStocktakingDetailAggDto) {
        return convertFromDrugStocktakingDetailAggDtoToDrugStocktakingDetailBaseDto(
                        List.of(drugStocktakingDetailAggDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugStocktakingDetailBaseDto>
            convertFromDrugStocktakingDetailAggDtoToDrugStocktakingDetailBaseDto(
                    List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList) {
        if (CollectionUtil.isEmpty(drugStocktakingDetailAggDtoList)) {
            return new ArrayList<>();
        }
        return drugStocktakingDetailBaseDtoManager.getByIds(
                drugStocktakingDetailAggDtoList.stream()
                        .map(DrugStocktakingDetailAggDto::getId)
                        .collect(Collectors.toList()));
    }

    @AutoGenerated(locked = true)
    public DrugStocktakingDetailAggDto
            convertFromDrugStocktakingDetailBaseDtoToDrugStocktakingDetailAggDto(
                    DrugStocktakingDetailBaseDto drugStocktakingDetailBaseDto) {
        return convertFromDrugStocktakingDetailBaseDtoToDrugStocktakingDetailAggDto(
                        List.of(drugStocktakingDetailBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<DrugStocktakingDetailAggDto>
            convertFromDrugStocktakingDetailBaseDtoToDrugStocktakingDetailAggDto(
                    List<DrugStocktakingDetailBaseDto> drugStocktakingDetailBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugStocktakingDetailBaseDtoList)) {
            return new ArrayList<>();
        }
        List<DrugStocktakingDetailAggDto> drugStocktakingDetailAggDtoList = new ArrayList<>();
        for (DrugStocktakingDetailBaseDto drugStocktakingDetailBaseDto :
                drugStocktakingDetailBaseDtoList) {
            if (drugStocktakingDetailBaseDto == null) {
                continue;
            }
            DrugStocktakingDetailAggDto drugStocktakingDetailAggDto =
                    new DrugStocktakingDetailAggDto();
            drugStocktakingDetailAggDto.setId(drugStocktakingDetailBaseDto.getId());
            drugStocktakingDetailAggDto.setBatchInventoryId(
                    drugStocktakingDetailBaseDto.getBatchInventoryId());
            drugStocktakingDetailAggDto.setDrugStocktakingId(
                    drugStocktakingDetailBaseDto.getDrugStocktakingId());
            drugStocktakingDetailAggDto.setAccountTypeCode(
                    drugStocktakingDetailBaseDto.getAccountTypeCode());
            drugStocktakingDetailAggDto.setAmountPerPackage(
                    drugStocktakingDetailBaseDto.getAmountPerPackage());
            drugStocktakingDetailAggDto.setBatchNumber(
                    drugStocktakingDetailBaseDto.getBatchNumber());
            drugStocktakingDetailAggDto.setCreatedAt(drugStocktakingDetailBaseDto.getCreatedAt());
            drugStocktakingDetailAggDto.setDifferenceAmount(
                    drugStocktakingDetailBaseDto.getDifferenceAmount());
            drugStocktakingDetailAggDto.setDifferenceStockCost(
                    drugStocktakingDetailBaseDto.getDifferenceStockCost());
            drugStocktakingDetailAggDto.setDrugOriginName(
                    drugStocktakingDetailBaseDto.getDrugOriginName());
            drugStocktakingDetailAggDto.setExpirationDate(
                    drugStocktakingDetailBaseDto.getExpirationDate());
            drugStocktakingDetailAggDto.setLocationCode(
                    drugStocktakingDetailBaseDto.getLocationCode());
            drugStocktakingDetailAggDto.setMinDifferenceAmount(
                    drugStocktakingDetailBaseDto.getMinDifferenceAmount());
            drugStocktakingDetailAggDto.setMinStockAmount(
                    drugStocktakingDetailBaseDto.getMinStockAmount());
            drugStocktakingDetailAggDto.setMinStocktakingAmount(
                    drugStocktakingDetailBaseDto.getMinStocktakingAmount());
            drugStocktakingDetailAggDto.setPackageDifferenceAmount(
                    drugStocktakingDetailBaseDto.getPackageDifferenceAmount());
            drugStocktakingDetailAggDto.setSpecification(
                    drugStocktakingDetailBaseDto.getSpecification());
            drugStocktakingDetailAggDto.setPackageStockAmount(
                    drugStocktakingDetailBaseDto.getPackageStockAmount());
            drugStocktakingDetailAggDto.setPackageStocktakingAmount(
                    drugStocktakingDetailBaseDto.getPackageStocktakingAmount());
            drugStocktakingDetailAggDto.setUnit(drugStocktakingDetailBaseDto.getUnit());
            drugStocktakingDetailAggDto.setProfitLossType(
                    drugStocktakingDetailBaseDto.getProfitLossType());
            drugStocktakingDetailAggDto.setRemark(drugStocktakingDetailBaseDto.getRemark());
            drugStocktakingDetailAggDto.setRetailPrice(
                    drugStocktakingDetailBaseDto.getRetailPrice());
            drugStocktakingDetailAggDto.setSortNumber(drugStocktakingDetailBaseDto.getSortNumber());
            drugStocktakingDetailAggDto.setStockAmount(
                    drugStocktakingDetailBaseDto.getStockAmount());
            drugStocktakingDetailAggDto.setStockPrice(drugStocktakingDetailBaseDto.getStockPrice());
            drugStocktakingDetailAggDto.setStocktakingAmount(
                    drugStocktakingDetailBaseDto.getStocktakingAmount());
            drugStocktakingDetailAggDto.setUpdatedAt(drugStocktakingDetailBaseDto.getUpdatedAt());
            drugStocktakingDetailAggDto.setSummaryStocktakingDetailId(
                    drugStocktakingDetailBaseDto.getSummaryStocktakingDetailId());
            drugStocktakingDetailAggDto.setManageMode(drugStocktakingDetailBaseDto.getManageMode());
            drugStocktakingDetailAggDto.setDifferenceRetailCost(
                    drugStocktakingDetailBaseDto.getDifferenceRetailCost());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugStocktakingDetailAggDtoList.add(drugStocktakingDetailAggDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugStocktakingDetailAggDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
