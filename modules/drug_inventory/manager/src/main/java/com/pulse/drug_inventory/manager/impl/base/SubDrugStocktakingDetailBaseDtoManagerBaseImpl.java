package com.pulse.drug_inventory.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_inventory.manager.SubDrugStocktakingDetailBaseDtoManager;
import com.pulse.drug_inventory.manager.converter.SubDrugStocktakingDetailBaseDtoConverter;
import com.pulse.drug_inventory.manager.dto.SubDrugStocktakingDetailBaseDto;
import com.pulse.drug_inventory.persist.dos.SubDrugStocktakingDetail;
import com.pulse.drug_inventory.persist.mapper.SubDrugStocktakingDetailDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "9de52395-40ee-4c5f-95a9-94bf99abd99e|DTO|BASE_MANAGER_IMPL")
public abstract class SubDrugStocktakingDetailBaseDtoManagerBaseImpl
        implements SubDrugStocktakingDetailBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private SubDrugStocktakingDetailBaseDtoConverter subDrugStocktakingDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private SubDrugStocktakingDetailDao subDrugStocktakingDetailDao;

    @AutoGenerated(locked = true, uuid = "0442f89e-b9e8-3954-bf0b-4f1f53a9997f")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getByDrugStocktakingDetailIds(
            List<String> drugStocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugStocktakingDetailId)) {
            return Collections.emptyList();
        }

        List<SubDrugStocktakingDetail> subDrugStocktakingDetailList =
                subDrugStocktakingDetailDao.getByDrugStocktakingDetailIds(drugStocktakingDetailId);
        if (CollectionUtil.isEmpty(subDrugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromSubDrugStocktakingDetailToSubDrugStocktakingDetailBaseDto(
                subDrugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "153e0cf1-f22d-3c11-8a9a-4ac768d67f35")
    public List<SubDrugStocktakingDetailBaseDto>
            doConvertFromSubDrugStocktakingDetailToSubDrugStocktakingDetailBaseDto(
                    List<SubDrugStocktakingDetail> subDrugStocktakingDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(subDrugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        Map<String, SubDrugStocktakingDetailBaseDto> dtoMap =
                subDrugStocktakingDetailBaseDtoConverter
                        .convertFromSubDrugStocktakingDetailToSubDrugStocktakingDetailBaseDto(
                                subDrugStocktakingDetailList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        SubDrugStocktakingDetailBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<SubDrugStocktakingDetailBaseDto> subDrugStocktakingDetailBaseDtoList =
                new ArrayList<>();
        for (SubDrugStocktakingDetail i : subDrugStocktakingDetailList) {
            SubDrugStocktakingDetailBaseDto subDrugStocktakingDetailBaseDto = dtoMap.get(i.getId());
            if (subDrugStocktakingDetailBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            subDrugStocktakingDetailBaseDtoList.add(subDrugStocktakingDetailBaseDto);
        }
        return subDrugStocktakingDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "2dc7b0b3-9867-33a0-a9a1-f289eb8a8c0f")
    @Override
    public SubDrugStocktakingDetailBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SubDrugStocktakingDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        SubDrugStocktakingDetailBaseDto subDrugStocktakingDetailBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return subDrugStocktakingDetailBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4ef69a7b-cf98-34f6-bb8e-e95c9d51d5ed")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<SubDrugStocktakingDetail> subDrugStocktakingDetailList =
                subDrugStocktakingDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(subDrugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, SubDrugStocktakingDetail> subDrugStocktakingDetailMap =
                subDrugStocktakingDetailList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        subDrugStocktakingDetailList =
                id.stream()
                        .map(i -> subDrugStocktakingDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromSubDrugStocktakingDetailToSubDrugStocktakingDetailBaseDto(
                subDrugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "55afa868-c2a3-3599-b54e-f0945d995233")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginSpecificationId)) {
            return Collections.emptyList();
        }

        List<SubDrugStocktakingDetail> subDrugStocktakingDetailList =
                subDrugStocktakingDetailDao.getByDrugOriginSpecificationIds(
                        drugOriginSpecificationId);
        if (CollectionUtil.isEmpty(subDrugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromSubDrugStocktakingDetailToSubDrugStocktakingDetailBaseDto(
                subDrugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5a2b31aa-92ca-3e90-a69f-1141ebd1b9e9")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getByDrugOriginSpecificationId(
            String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SubDrugStocktakingDetailBaseDto> subDrugStocktakingDetailBaseDtoList =
                getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        return subDrugStocktakingDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "6ac33825-89a7-3633-b676-a18fa0c9ddc1")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getByDrugStocktakingDetailId(
            String drugStocktakingDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SubDrugStocktakingDetailBaseDto> subDrugStocktakingDetailBaseDtoList =
                getByDrugStocktakingDetailIds(Arrays.asList(drugStocktakingDetailId));
        return subDrugStocktakingDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "94c20c56-e1c2-3c36-a863-53811681dcd7")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getBySubStocktakingId(String subStocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SubDrugStocktakingDetailBaseDto> subDrugStocktakingDetailBaseDtoList =
                getBySubStocktakingIds(Arrays.asList(subStocktakingId));
        return subDrugStocktakingDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e89cd4b9-0591-3d06-a040-127bb405487c")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getByBatchInventoryId(String batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<SubDrugStocktakingDetailBaseDto> subDrugStocktakingDetailBaseDtoList =
                getByBatchInventoryIds(Arrays.asList(batchInventoryId));
        return subDrugStocktakingDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "eac70716-2050-347b-835a-d51feadab69f")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getBySubStocktakingIds(
            List<String> subStocktakingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(subStocktakingId)) {
            return Collections.emptyList();
        }

        List<SubDrugStocktakingDetail> subDrugStocktakingDetailList =
                subDrugStocktakingDetailDao.getBySubStocktakingIds(subStocktakingId);
        if (CollectionUtil.isEmpty(subDrugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromSubDrugStocktakingDetailToSubDrugStocktakingDetailBaseDto(
                subDrugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f5bd2a2c-734f-3101-b46a-54625e23adc9")
    @Override
    public List<SubDrugStocktakingDetailBaseDto> getByBatchInventoryIds(
            List<String> batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(batchInventoryId)) {
            return Collections.emptyList();
        }

        List<SubDrugStocktakingDetail> subDrugStocktakingDetailList =
                subDrugStocktakingDetailDao.getByBatchInventoryIds(batchInventoryId);
        if (CollectionUtil.isEmpty(subDrugStocktakingDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromSubDrugStocktakingDetailToSubDrugStocktakingDetailBaseDto(
                subDrugStocktakingDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
