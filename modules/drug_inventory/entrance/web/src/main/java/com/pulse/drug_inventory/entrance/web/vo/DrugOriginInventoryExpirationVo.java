package com.pulse.drug_inventory.entrance.web.vo;

import com.pulse.drug_inventory.entrance.web.vo.DrugOriginInventoryExpirationVo.OrganizationBaseVo;
import com.pulse.organization.common.enums.OrganizationStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "435ed4e4-ad4d-494f-9815-b7d2c23ba857|VO|DEFINITION")
public class DrugOriginInventoryExpirationVo {
    /** 数量 库存数量，按最小规格单位存储 */
    @AutoGenerated(locked = true, uuid = "497713d2-d047-4233-b285-7e61116494f8")
    private BigDecimal amount;

    /** 批次id */
    @Valid
    @AutoGenerated(locked = true, uuid = "03e42691-86de-4cb2-90b0-b46353d0f6b3")
    private DrugStockBatchBaseVo batch;

    /** 批号 */
    @AutoGenerated(locked = true, uuid = "b5b0030f-117b-4d5a-881c-e2aaa5242339")
    private String batchNumber;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "7dca7c8e-8d1c-4c62-8f72-0634b4808838")
    private Date createdAt;

    /** 药品产地编码 冗余存 */
    @Valid
    @AutoGenerated(locked = true, uuid = "cd6de70c-79dd-468c-9389-77555aa990fa")
    private DrugInventoryRefDrugOriginSimpleVo drugOriginCode;

    /** 药品产地规格id */
    @Valid
    @AutoGenerated(locked = true, uuid = "f7b373af-532f-4f25-8395-b8f7bc5b8f71")
    private DrugOriginSpecificationBaseVo drugOriginSpecification;

    /** 有效期 批次效期 */
    @AutoGenerated(locked = true, uuid = "3645894d-fa68-4d9f-901b-ed4ea9222528")
    private Date expirationDate;

    /** gcp编码 gcp药品一物一码，开单发药时也按编码开单发药 */
    @AutoGenerated(locked = true, uuid = "99b18847-8e0d-4ac0-b33d-e2ebc0fe0b3c")
    private String gcpCode;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "1a4e18a4-d2b1-4366-9e5d-9377f2dc307b")
    private String id;

    /** 进货日期 */
    @AutoGenerated(locked = true, uuid = "66f4e88c-12be-427e-a011-ff01399c37a6")
    private Date importDateTime;

    /** 库存ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "40f11347-983e-4f40-b306-dbaaaf823534")
    private DrugOriginInventoryBaseVo inventory;

    /** 进价 */
    @AutoGenerated(locked = true, uuid = "e7082c0a-5825-4163-a16b-b22e1e9b63d8")
    private BigDecimal purchasePrice;

    /** 零售价 */
    @AutoGenerated(locked = true, uuid = "ff218059-fe37-432b-995a-768b74cc64f9")
    private BigDecimal retailPrice;

    /** 库房编码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "7cbb951a-4c22-41a7-a579-274d446ef514")
    private OrganizationBaseVo storageCode;

    /** 可供标识id */
    @AutoGenerated(locked = true, uuid = "8b0e5a18-8c6d-41a5-a671-d790ac93e962")
    private String supplyId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "db7fe32e-462a-4bbd-9635-d384f050dfd5")
    private Date updatedAt;

    /**
     * 虚库存 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
     * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
     * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
     */
    @AutoGenerated(locked = true, uuid = "9984ec8d-d260-4621-8f42-80f99e7af8a3")
    private BigDecimal virtualAmount;

    @Setter
    @Getter
    public static class OrganizationBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "22c997e1-ef75-4582-9454-774ff5c0be22")
        private String id;

        /** 组织名称 */
        @AutoGenerated(locked = true, uuid = "789f34ee-698d-413f-aac9-18423a872a51")
        private String name;

        /** 排序号 */
        @AutoGenerated(locked = true, uuid = "1b0d03fe-7b00-4020-a413-7d6c067f8bdc")
        private Long sortNumber;

        /** 组织状态 */
        @AutoGenerated(locked = true, uuid = "2392a661-3eda-4c0e-b732-95a3bd823301")
        private OrganizationStatusEnum status;

        /** 别名 */
        @AutoGenerated(locked = true, uuid = "0fdaceb6-801c-4df0-acad-a4cce66a9e3d")
        private String alias;
    }
}
