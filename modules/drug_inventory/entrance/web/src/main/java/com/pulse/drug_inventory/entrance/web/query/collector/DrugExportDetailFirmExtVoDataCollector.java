package com.pulse.drug_inventory.entrance.web.query.collector;

import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryWithCatalogDto;
import com.pulse.drug_dictionary.manager.dto.DrugNameDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginExtensionBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginForDetailDto;
import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryWithDrugDto;
import com.pulse.drug_inventory.entrance.web.converter.DrugExportDetailFirmExtVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugCategoryVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugDictionaryWithCatalogVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugNameDictionaryBaseVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugOriginExtensionBaseVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugOriginForDetailVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugProducerDictionarySimpleVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugSpecificationDictionaryWithDrugVoConverter;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugExportDetailFirmExtVoDataAssembler.DrugExportDetailFirmExtVoDataHolder;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugCategoryVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugDictionaryWithCatalogVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugNameDictionaryBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginExtensionBaseVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugOriginForDetailVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugProducerDictionarySimpleVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugSpecificationDictionaryWithDrugVo;
import com.pulse.drug_inventory.manager.dto.DrugExportDetailBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugExportDetailFirmExtDto;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugCategoryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugNameDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugOriginBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugOriginExtensionBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugOriginForDetailDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugSpecificationDictionaryWithDrugDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.service.DrugExportDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装DrugExportDetailFirmExtVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "47401094-e537-3af2-b2bd-39107750a2de")
public class DrugExportDetailFirmExtVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DrugCategoryBaseDtoServiceInDrugInventoryRpcAdapter
            drugCategoryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter
            drugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportDetailBaseDtoService drugExportDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportDetailFirmExtVoConverter drugExportDetailFirmExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugExportDetailFirmExtVoDataCollector drugExportDetailFirmExtVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugCategoryVoConverter drugInventoryRefDrugCategoryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugDictionaryWithCatalogVoConverter
            drugInventoryRefDrugDictionaryWithCatalogVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugNameDictionaryBaseVoConverter
            drugInventoryRefDrugNameDictionaryBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginExtensionBaseVoConverter
            drugInventoryRefDrugOriginExtensionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugOriginForDetailVoConverter
            drugInventoryRefDrugOriginForDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugProducerDictionarySimpleVoConverter
            drugInventoryRefDrugProducerDictionarySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugNameDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugNameDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseDtoServiceInDrugInventoryRpcAdapter
            drugOriginBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginExtensionBaseDtoServiceInDrugInventoryRpcAdapter
            drugOriginExtensionBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginForDetailDtoServiceInDrugInventoryRpcAdapter
            drugOriginForDetailDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryWithDrugDtoServiceInDrugInventoryRpcAdapter
            drugSpecificationDictionaryWithDrugDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryWithDrugVoConverter
            drugSpecificationDictionaryWithDrugVoConverter;

    /** 获取DrugExportDetailFirmExtDto数据填充DrugExportDetailFirmExtVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "696850f5-2e85-368c-a29f-1a78009246e4")
    public void collectDataWithDtoData(
            List<DrugExportDetailFirmExtDto> dtoList,
            DrugExportDetailFirmExtVoDataHolder dataHolder) {
        List<DrugProducerDictionaryBaseDto> firmList = new ArrayList<>();

        for (DrugExportDetailFirmExtDto rootDto : dtoList) {
            DrugProducerDictionaryBaseDto firmDto = rootDto.getFirm();
            if (firmDto != null) {
                firmList.add(firmDto);
            }
        }

        // access firm
        Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo>
                firmVoMap =
                        drugInventoryRefDrugProducerDictionarySimpleVoConverter
                                .convertToDrugInventoryRefDrugProducerDictionarySimpleVoMap(
                                        firmList);
        dataHolder.firm =
                firmList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> firmVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "75c3dc61-06ff-353a-8fe1-febf1543ef06")
    private void fillDataWhenNecessary(DrugExportDetailFirmExtVoDataHolder dataHolder) {
        List<DrugExportDetailBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.firm == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugExportDetailBaseDto::getFirmId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugProducerDictionaryBaseDto> baseDtoList =
                    drugProducerDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugProducerDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugProducerDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugProducerDictionaryBaseDto::getId,
                                            Function.identity()));
            Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo>
                    dtoVoMap =
                            drugInventoryRefDrugProducerDictionarySimpleVoConverter
                                    .convertToDrugInventoryRefDrugProducerDictionarySimpleVoMap(
                                            baseDtoList);
            Map<DrugProducerDictionaryBaseDto, DrugInventoryRefDrugProducerDictionarySimpleVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.firm =
                    rootDtoList.stream()
                            .map(DrugExportDetailBaseDto::getFirmId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOrigin == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugExportDetailBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginBaseDto> baseDtoList =
                    drugOriginBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginBaseDto::getDrugOriginCode))
                            .collect(Collectors.toList());
            Map<String, DrugOriginBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginBaseDto::getDrugOriginCode,
                                            Function.identity()));
            Map<String, DrugOriginForDetailDto> drugOriginForDetailDtoMap =
                    drugOriginForDetailDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugOriginCodes(
                                    baseDtoList.stream()
                                            .map(DrugOriginBaseDto::getDrugOriginCode)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginForDetailDto::getDrugOriginCode,
                                            Function.identity()));
            Map<DrugOriginForDetailDto, DrugInventoryRefDrugOriginForDetailVo> dtoVoMap =
                    drugInventoryRefDrugOriginForDetailVoConverter
                            .convertToDrugInventoryRefDrugOriginForDetailVoMap(
                                    new ArrayList<>(drugOriginForDetailDtoMap.values()));
            Map<DrugOriginBaseDto, DrugInventoryRefDrugOriginForDetailVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            drugOriginForDetailDtoMap.containsKey(
                                                    baseDto.getDrugOriginCode()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            drugOriginForDetailDtoMap.get(
                                                                    baseDto.getDrugOriginCode())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOrigin =
                    rootDtoList.stream()
                            .map(DrugExportDetailBaseDto::getDrugOriginCode)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOrigin2DrugSpecification == null) {
            Set<String> ids =
                    dataHolder.drugOrigin.keySet().stream()
                            .map(DrugOriginBaseDto::getDrugSpecificationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugSpecificationDictionaryBaseDto> baseDtoList =
                    drugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugSpecificationDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugSpecificationDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugSpecificationDictionaryBaseDto::getId,
                                            Function.identity()));
            Map<String, DrugSpecificationDictionaryWithDrugDto>
                    drugSpecificationDictionaryWithDrugDtoMap =
                            drugSpecificationDictionaryWithDrugDtoServiceInDrugInventoryRpcAdapter
                                    .getByIds(
                                            baseDtoList.stream()
                                                    .map(DrugSpecificationDictionaryBaseDto::getId)
                                                    .collect(Collectors.toList()))
                                    .stream()
                                    .collect(
                                            Collectors.toMap(
                                                    DrugSpecificationDictionaryWithDrugDto::getId,
                                                    Function.identity()));
            Map<DrugSpecificationDictionaryWithDrugDto, DrugSpecificationDictionaryWithDrugVo>
                    dtoVoMap =
                            drugSpecificationDictionaryWithDrugVoConverter
                                    .convertToDrugSpecificationDictionaryWithDrugVoMap(
                                            new ArrayList<>(
                                                    drugSpecificationDictionaryWithDrugDtoMap
                                                            .values()));
            Map<DrugSpecificationDictionaryBaseDto, DrugSpecificationDictionaryWithDrugVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .filter(
                                            baseDto ->
                                                    drugSpecificationDictionaryWithDrugDtoMap
                                                            .containsKey(baseDto.getId()))
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto ->
                                                            dtoVoMap.get(
                                                                    drugSpecificationDictionaryWithDrugDtoMap
                                                                            .get(baseDto.getId())),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.drugOrigin2DrugSpecification =
                    dataHolder.drugOrigin.keySet().stream()
                            .map(DrugOriginBaseDto::getDrugSpecificationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOrigin2DrugOriginExtension == null) {
            Set<String> ids =
                    dataHolder.drugOrigin.keySet().stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginExtensionBaseDto> baseDtoList =
                    drugOriginExtensionBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginExtensionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugOriginExtensionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugOriginExtensionBaseDto::getDrugOriginCode));
            Map<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo> dtoVoMap =
                    drugInventoryRefDrugOriginExtensionBaseVoConverter
                            .convertToDrugInventoryRefDrugOriginExtensionBaseVoMap(baseDtoList);
            Map<DrugOriginExtensionBaseDto, DrugInventoryRefDrugOriginExtensionBaseVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.drugOrigin2DrugOriginExtension =
                    dataHolder.drugOrigin.keySet().stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOrigin2DrugNameDictionaryList == null) {
            Set<String> ids =
                    dataHolder.drugOrigin.keySet().stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugNameDictionaryBaseDto> baseDtoList =
                    drugNameDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugNameDictionaryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugNameDictionaryBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            DrugNameDictionaryBaseDto::getDrugOriginCode));
            Map<DrugNameDictionaryBaseDto, DrugInventoryRefDrugNameDictionaryBaseVo> dtoVoMap =
                    drugInventoryRefDrugNameDictionaryBaseVoConverter
                            .convertToDrugInventoryRefDrugNameDictionaryBaseVoMap(baseDtoList);
            Map<DrugNameDictionaryBaseDto, DrugInventoryRefDrugNameDictionaryBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOrigin2DrugNameDictionaryList =
                    dataHolder.drugOrigin.keySet().stream()
                            .map(DrugOriginBaseDto::getDrugOriginCode)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOrigin2DrugSpecification2Drug == null) {
            Set<String> ids =
                    dataHolder.drugOrigin2DrugSpecification.keySet().stream()
                            .map(DrugSpecificationDictionaryBaseDto::getDrugCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugDictionaryBaseDto> baseDtoList =
                    drugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugDictionaryBaseDto::getDrugCode))
                            .collect(Collectors.toList());
            Map<String, DrugDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugDictionaryBaseDto::getDrugCode,
                                            Function.identity()));
            Map<String, DrugDictionaryWithCatalogDto> drugDictionaryWithCatalogDtoMap =
                    drugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugCodes(
                                    baseDtoList.stream()
                                            .map(DrugDictionaryBaseDto::getDrugCode)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugDictionaryWithCatalogDto::getDrugCode,
                                            Function.identity()));
            Map<DrugDictionaryWithCatalogDto, DrugInventoryRefDrugDictionaryWithCatalogVo>
                    dtoVoMap =
                            drugInventoryRefDrugDictionaryWithCatalogVoConverter
                                    .convertToDrugInventoryRefDrugDictionaryWithCatalogVoMap(
                                            new ArrayList<>(
                                                    drugDictionaryWithCatalogDtoMap.values()));
            Map<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            drugDictionaryWithCatalogDtoMap.containsKey(
                                                    baseDto.getDrugCode()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            drugDictionaryWithCatalogDtoMap.get(
                                                                    baseDto.getDrugCode())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOrigin2DrugSpecification2Drug =
                    dataHolder.drugOrigin2DrugSpecification.keySet().stream()
                            .map(DrugSpecificationDictionaryBaseDto::getDrugCode)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOrigin2DrugSpecification2Drug2DrugCatalog == null) {
            Set<String> ids =
                    dataHolder.drugOrigin2DrugSpecification2Drug.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCatalogId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugCategoryBaseDto> baseDtoList =
                    drugCategoryBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugCategoryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugCategoryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugCategoryBaseDto::getId, Function.identity()));
            Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo> dtoVoMap =
                    drugInventoryRefDrugCategoryVoConverter
                            .convertToDrugInventoryRefDrugCategoryVoMap(baseDtoList);
            Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOrigin2DrugSpecification2Drug2DrugCatalog =
                    dataHolder.drugOrigin2DrugSpecification2Drug.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCatalogId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "e1a8a05d-d8cc-3b44-a26b-956c81e77679")
    public void collectDataDefault(DrugExportDetailFirmExtVoDataHolder dataHolder) {
        drugExportDetailFirmExtVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
