package com.pulse.drug_inventory.entrance.web.query.collector;

import com.pulse.drug_dictionary.manager.dto.DrugCategoryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugDictionaryWithCatalogDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugSpecificationDictionaryWithDrugDto;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugCategoryVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugInventoryRefDrugDictionaryWithCatalogVoConverter;
import com.pulse.drug_inventory.entrance.web.converter.DrugSpecificationDictionaryWithDrugVoConverter;
import com.pulse.drug_inventory.entrance.web.query.assembler.DrugSpecificationDictionaryWithDrugVoDataAssembler.DrugSpecificationDictionaryWithDrugVoDataHolder;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugCategoryVo;
import com.pulse.drug_inventory.entrance.web.vo.DrugInventoryRefDrugDictionaryWithCatalogVo;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugCategoryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter;
import com.pulse.drug_inventory.manager.facade.drug_dictionary.DrugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装DrugSpecificationDictionaryWithDrugVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "ab4061b6-9417-3e82-8bfc-450acfcf67f5")
public class DrugSpecificationDictionaryWithDrugVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DrugCategoryBaseDtoServiceInDrugInventoryRpcAdapter
            drugCategoryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter
            drugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugCategoryVoConverter drugInventoryRefDrugCategoryVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugInventoryRefDrugDictionaryWithCatalogVoConverter
            drugInventoryRefDrugDictionaryWithCatalogVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
            drugSpecificationDictionaryBaseDtoServiceInDrugInventoryRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryWithDrugVoConverter
            drugSpecificationDictionaryWithDrugVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugSpecificationDictionaryWithDrugVoDataCollector
            drugSpecificationDictionaryWithDrugVoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "57d051c1-40b6-3417-aa17-788ef9e61ee9")
    private void fillDataWhenNecessary(DrugSpecificationDictionaryWithDrugVoDataHolder dataHolder) {
        List<DrugSpecificationDictionaryBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.drug == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugSpecificationDictionaryBaseDto::getDrugCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugDictionaryBaseDto> baseDtoList =
                    drugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugDictionaryBaseDto::getDrugCode))
                            .collect(Collectors.toList());
            Map<String, DrugDictionaryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugDictionaryBaseDto::getDrugCode,
                                            Function.identity()));
            Map<String, DrugDictionaryWithCatalogDto> drugDictionaryWithCatalogDtoMap =
                    drugDictionaryWithCatalogDtoServiceInDrugInventoryRpcAdapter
                            .getByDrugCodes(
                                    baseDtoList.stream()
                                            .map(DrugDictionaryBaseDto::getDrugCode)
                                            .collect(Collectors.toList()))
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugDictionaryWithCatalogDto::getDrugCode,
                                            Function.identity()));
            Map<DrugDictionaryWithCatalogDto, DrugInventoryRefDrugDictionaryWithCatalogVo>
                    dtoVoMap =
                            drugInventoryRefDrugDictionaryWithCatalogVoConverter
                                    .convertToDrugInventoryRefDrugDictionaryWithCatalogVoMap(
                                            new ArrayList<>(
                                                    drugDictionaryWithCatalogDtoMap.values()));
            Map<DrugDictionaryBaseDto, DrugInventoryRefDrugDictionaryWithCatalogVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            drugDictionaryWithCatalogDtoMap.containsKey(
                                                    baseDto.getDrugCode()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            drugDictionaryWithCatalogDtoMap.get(
                                                                    baseDto.getDrugCode())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drug =
                    rootDtoList.stream()
                            .map(DrugSpecificationDictionaryBaseDto::getDrugCode)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drug2DrugCatalog == null) {
            Set<String> ids =
                    dataHolder.drug.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCatalogId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugCategoryBaseDto> baseDtoList =
                    drugCategoryBaseDtoServiceInDrugInventoryRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugCategoryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugCategoryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugCategoryBaseDto::getId, Function.identity()));
            Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo> dtoVoMap =
                    drugInventoryRefDrugCategoryVoConverter
                            .convertToDrugInventoryRefDrugCategoryVoMap(baseDtoList);
            Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drug2DrugCatalog =
                    dataHolder.drug.keySet().stream()
                            .map(DrugDictionaryBaseDto::getDrugCatalogId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "e2908458-382c-36f0-9393-7f20832dd3a2")
    public void collectDataDefault(DrugSpecificationDictionaryWithDrugVoDataHolder dataHolder) {
        drugSpecificationDictionaryWithDrugVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /**
     * 获取DrugSpecificationDictionaryWithDrugDto数据填充DrugSpecificationDictionaryWithDrugVo，并根据扩展关系填充剩余数据
     */
    @AutoGenerated(locked = true, uuid = "f883a042-6453-301a-8628-f7abef988669")
    public void collectDataWithDtoData(
            List<DrugSpecificationDictionaryWithDrugDto> dtoList,
            DrugSpecificationDictionaryWithDrugVoDataHolder dataHolder) {
        Map<DrugDictionaryBaseDto, DrugDictionaryWithCatalogDto> drugBaseDtoDtoMap =
                new LinkedHashMap<>();
        List<DrugCategoryBaseDto> drug2DrugCatalogList = new ArrayList<>();

        for (DrugSpecificationDictionaryWithDrugDto rootDto : dtoList) {
            DrugDictionaryWithCatalogDto drugDto = rootDto.getDrug();
            if (drugDto != null) {
                DrugDictionaryBaseDto drugBaseDto =
                        drugDictionaryBaseDtoServiceInDrugInventoryRpcAdapter
                                .getByDrugCodes(List.of(drugDto.getDrugCode()))
                                .stream()
                                .findAny()
                                .get();
                drugBaseDtoDtoMap.put(drugBaseDto, drugDto);
                DrugCategoryBaseDto drug2DrugCatalogDto = drugDto.getDrugCatalog();
                if (drug2DrugCatalogDto != null) {
                    drug2DrugCatalogList.add(drug2DrugCatalogDto);
                }
            }
        }

        // access drug
        Map<DrugDictionaryWithCatalogDto, DrugInventoryRefDrugDictionaryWithCatalogVo> drugVoMap =
                drugInventoryRefDrugDictionaryWithCatalogVoConverter
                        .convertToDrugInventoryRefDrugDictionaryWithCatalogVoMap(
                                new ArrayList<>(drugBaseDtoDtoMap.values()));
        dataHolder.drug =
                drugBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> drugVoMap.get(drugBaseDtoDtoMap.get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access drug2DrugCatalog
        Map<DrugCategoryBaseDto, DrugInventoryRefDrugCategoryVo> drug2DrugCatalogVoMap =
                drugInventoryRefDrugCategoryVoConverter.convertToDrugInventoryRefDrugCategoryVoMap(
                        drug2DrugCatalogList);
        dataHolder.drug2DrugCatalog =
                drug2DrugCatalogList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> drug2DrugCatalogVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
