package com.pulse.billing_public_config.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.ChargeItemAdjustPriceReceiptWithDetailDtoManager;
import com.pulse.billing_public_config.manager.dto.ChargeItemAdjustPriceReceiptWithDetailDto;
import com.pulse.billing_public_config.service.converter.ChargeItemAdjustPriceReceiptWithDetailDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "7e42d23d-e346-4361-a180-854a46f6bcd0|DTO|SERVICE")
public class ChargeItemAdjustPriceReceiptWithDetailDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemAdjustPriceReceiptWithDetailDtoManager
            chargeItemAdjustPriceReceiptWithDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemAdjustPriceReceiptWithDetailDtoServiceConverter
            chargeItemAdjustPriceReceiptWithDetailDtoServiceConverter;

    @PublicInterface(id = "56233b57-a265-4267-8504-84df7a51041f", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "2c625620-347a-3ada-bd94-e88b6479463b")
    public ChargeItemAdjustPriceReceiptWithDetailDto getByReceiptNumber(
            @NotNull(message = "调价单号不能为空") String receiptNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ChargeItemAdjustPriceReceiptWithDetailDto> ret =
                getByReceiptNumbers(Arrays.asList(receiptNumber));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c7a2e52c-6df8-48c7-8ce5-cb05249a1d14", module = "billing_public_config")
    @AutoGenerated(locked = false, uuid = "57cb20ec-86b7-39f8-8e8c-5d2f2547e304")
    public List<ChargeItemAdjustPriceReceiptWithDetailDto> getByReceiptNumbers(
            @Valid @NotNull(message = "调价单号不能为空") List<String> receiptNumber) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        receiptNumber = new ArrayList<>(new HashSet<>(receiptNumber));
        List<ChargeItemAdjustPriceReceiptWithDetailDto>
                chargeItemAdjustPriceReceiptWithDetailDtoList =
                        chargeItemAdjustPriceReceiptWithDetailDtoManager.getByReceiptNumbers(
                                receiptNumber);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return chargeItemAdjustPriceReceiptWithDetailDtoServiceConverter
                .ChargeItemAdjustPriceReceiptWithDetailDtoConverter(
                        chargeItemAdjustPriceReceiptWithDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
