package com.pulse.billing_public_config.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.persist.qto.SearchChargeItemQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "cbf619dc-c6bf-4a70-8428-661a8b5efbc5|QTO|DAO")
public class SearchChargeItemQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询收费项目 */
    @AutoGenerated(locked = false, uuid = "cbf619dc-c6bf-4a70-8428-661a8b5efbc5-count")
    public Integer count(SearchChargeItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(charge_item.item_code) FROM charge_item WHERE"
                    + " charge_item.organization_id = #organizationIdIs AND charge_item.item_code ="
                    + " #itemCodeIs AND charge_item.enable_flag = #enableFlagIs AND"
                    + " charge_item.package_type = #packageTypeIs AND ( charge_item.item_name like"
                    + " #inputCode OR JSON_VALUE(charge_item.input_code, '$.pinyin') like"
                    + " #inputCode OR JSON_VALUE(charge_item.input_code, '$.wubi') like #inputCode"
                    + " OR JSON_VALUE(charge_item.input_code, '$.custom') like #inputCode ) AND"
                    + " charge_item.charge_item_category in #chargeItemCategoryIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getChargeItemCategoryIn())) {
            conditionToRemove.add("#chargeItemCategoryIn");
        }
        if (qto.getOrganizationIdIs() == null) {
            conditionToRemove.add("#organizationIdIs");
        }
        if (qto.getPackageTypeIs() == null) {
            conditionToRemove.add("#packageTypeIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getItemCodeIs() == null) {
            conditionToRemove.add("#itemCodeIs");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace(
                                "#chargeItemCategoryIn",
                                CollectionUtil.isEmpty(qto.getChargeItemCategoryIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getChargeItemCategoryIn().size()))
                        .replace("#organizationIdIs", "?")
                        .replace("#packageTypeIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#itemCodeIs", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#chargeItemCategoryIn")) {
                sqlParams.addAll(qto.getChargeItemCategoryIn());
            } else if (paramName.equalsIgnoreCase("#organizationIdIs")) {
                sqlParams.add(qto.getOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#packageTypeIs")) {
                sqlParams.add(qto.getPackageTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#itemCodeIs")) {
                sqlParams.add(qto.getItemCodeIs());
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询收费项目 */
    @AutoGenerated(locked = false, uuid = "cbf619dc-c6bf-4a70-8428-661a8b5efbc5-query-all")
    public List<String> query(SearchChargeItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT charge_item.item_code FROM charge_item WHERE charge_item.organization_id ="
                    + " #organizationIdIs AND charge_item.item_code = #itemCodeIs AND"
                    + " charge_item.enable_flag = #enableFlagIs AND charge_item.package_type ="
                    + " #packageTypeIs AND ( charge_item.item_name like #inputCode OR"
                    + " JSON_VALUE(charge_item.input_code, '$.pinyin') like #inputCode OR"
                    + " JSON_VALUE(charge_item.input_code, '$.wubi') like #inputCode OR"
                    + " JSON_VALUE(charge_item.input_code, '$.custom') like #inputCode ) AND"
                    + " charge_item.charge_item_category in #chargeItemCategoryIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getChargeItemCategoryIn())) {
            conditionToRemove.add("#chargeItemCategoryIn");
        }
        if (qto.getOrganizationIdIs() == null) {
            conditionToRemove.add("#organizationIdIs");
        }
        if (qto.getPackageTypeIs() == null) {
            conditionToRemove.add("#packageTypeIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getItemCodeIs() == null) {
            conditionToRemove.add("#itemCodeIs");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace(
                                "#chargeItemCategoryIn",
                                CollectionUtil.isEmpty(qto.getChargeItemCategoryIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getChargeItemCategoryIn().size()))
                        .replace("#organizationIdIs", "?")
                        .replace("#packageTypeIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#itemCodeIs", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#chargeItemCategoryIn")) {
                sqlParams.addAll(qto.getChargeItemCategoryIn());
            } else if (paramName.equalsIgnoreCase("#organizationIdIs")) {
                sqlParams.add(qto.getOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#packageTypeIs")) {
                sqlParams.add(qto.getPackageTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#itemCodeIs")) {
                sqlParams.add(qto.getItemCodeIs());
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  charge_item.sort_number asc , charge_item.item_code asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("item_code");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询收费项目 */
    @AutoGenerated(locked = false, uuid = "cbf619dc-c6bf-4a70-8428-661a8b5efbc5-query-paginate")
    public List<String> queryPaged(SearchChargeItemQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT charge_item.item_code FROM charge_item WHERE charge_item.organization_id ="
                    + " #organizationIdIs AND charge_item.item_code = #itemCodeIs AND"
                    + " charge_item.enable_flag = #enableFlagIs AND charge_item.package_type ="
                    + " #packageTypeIs AND ( charge_item.item_name like #inputCode OR"
                    + " JSON_VALUE(charge_item.input_code, '$.pinyin') like #inputCode OR"
                    + " JSON_VALUE(charge_item.input_code, '$.wubi') like #inputCode OR"
                    + " JSON_VALUE(charge_item.input_code, '$.custom') like #inputCode ) AND"
                    + " charge_item.charge_item_category in #chargeItemCategoryIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getChargeItemCategoryIn())) {
            conditionToRemove.add("#chargeItemCategoryIn");
        }
        if (qto.getOrganizationIdIs() == null) {
            conditionToRemove.add("#organizationIdIs");
        }
        if (qto.getPackageTypeIs() == null) {
            conditionToRemove.add("#packageTypeIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getItemCodeIs() == null) {
            conditionToRemove.add("#itemCodeIs");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        if (qto.getInputCode() == null) {
            conditionToRemove.add("#inputCode");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace(
                                "#chargeItemCategoryIn",
                                CollectionUtil.isEmpty(qto.getChargeItemCategoryIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getChargeItemCategoryIn().size()))
                        .replace("#organizationIdIs", "?")
                        .replace("#packageTypeIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#itemCodeIs", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?")
                        .replace("#inputCode", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#chargeItemCategoryIn")) {
                sqlParams.addAll(qto.getChargeItemCategoryIn());
            } else if (paramName.equalsIgnoreCase("#organizationIdIs")) {
                sqlParams.add(qto.getOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#packageTypeIs")) {
                sqlParams.add(qto.getPackageTypeIs().toString());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#itemCodeIs")) {
                sqlParams.add(qto.getItemCodeIs());
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCode")) {
                sqlParams.add("%" + qto.getInputCode() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  charge_item.sort_number asc , charge_item.item_code asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("item_code");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
