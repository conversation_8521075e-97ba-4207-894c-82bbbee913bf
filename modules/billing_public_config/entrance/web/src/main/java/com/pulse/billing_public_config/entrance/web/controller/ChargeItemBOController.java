package com.pulse.billing_public_config.entrance.web.controller;

import cn.hutool.core.collection.CollUtil;

import com.pulse.billing_public_config.common.enums.AuditStatusEnum;
import com.pulse.billing_public_config.common.enums.ChargePackageTypeEnum;
import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailBaseDto;
import com.pulse.billing_public_config.manager.facade.dictionary_basic.AttributeValueBOServiceInBillingPublicConfigRpcAdapter;
import com.pulse.billing_public_config.service.ChargeItemBOService;
import com.pulse.billing_public_config.service.ChargeItemBaseDtoService;
import com.pulse.billing_public_config.service.ChargeItemPriceDetailBaseDtoService;
import com.pulse.billing_public_config.service.bto.ChangeChargeItemAuditStatusBto;
import com.pulse.billing_public_config.service.bto.ChangeChargeItemEnableFlagBto;
import com.pulse.billing_public_config.service.bto.CreateChargeItemBto;
import com.pulse.billing_public_config.service.bto.SaveChargeItemPackageBto;
import com.pulse.billing_public_config.service.bto.SaveChargeItemPerformDepartmentListBto;
import com.pulse.billing_public_config.service.bto.SaveChargeItemPriceDetailListBto;
import com.pulse.billing_public_config.service.bto.UpdateChargeItemBto;
import com.pulse.dictionary_basic.service.bto.MergeAttributeValueBto;
import com.pulse.pulse.common.utils.DateUtils;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "b29bcff4-6a9a-3a2a-a29d-104269323eb5")
public class ChargeItemBOController {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemBOService chargeItemBOService;

    @Resource private ChargeItemBaseDtoService chargeItemBaseDtoService;
    @Resource private ChargeItemPriceDetailBaseDtoService chargeItemPriceDetailBaseDtoService;

    @Resource
    private AttributeValueBOServiceInBillingPublicConfigRpcAdapter
            attributeValueBOServiceInBillingPublicConfigRpcAdapter;

    /** 创建收费项目 */
    @PublicInterface(id = "2ecb9768-29fa-4c15-bbb9-075067af931a", version = "1744017840924")
    @AutoGenerated(locked = false, uuid = "2ecb9768-29fa-4c15-bbb9-075067af931a")
    @RequestMapping(
            value = {"/api/billing-public-config/create-charge-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String createChargeItem(
            @Valid @NotNull CreateChargeItemBto createChargeItemBto,
            @Valid List<MergeAttributeValueBto> attributeValueList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = chargeItemBOService.createChargeItem(createChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        attributeValueBOServiceInBillingPublicConfigRpcAdapter.mergeAttributeValueList(
                attributeValueList, "CHARGE_ITEM", createChargeItemBto.getItemCode());

        return result;
    }

    /** 更新收费项目 */
    @PublicInterface(id = "5d0842fc-7d05-4b85-b521-228258c234cf", version = "1744018110107")
    @AutoGenerated(locked = false, uuid = "5d0842fc-7d05-4b85-b521-228258c234cf")
    @RequestMapping(
            value = {"/api/billing-public-config/update-charge-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateChargeItem(
            @Valid UpdateChargeItemBto updateChargeItemBto,
            @Valid List<MergeAttributeValueBto> attributeValueList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = chargeItemBOService.updateChargeItem(updateChargeItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        attributeValueBOServiceInBillingPublicConfigRpcAdapter.mergeAttributeValueList(
                attributeValueList, "CHARGE_ITEM", updateChargeItemBto.getItemCode());
        return result;
    }

    /** 保存收费药品套餐 */
    @PublicInterface(id = "60044c0d-9dc6-4851-a148-27f9b24c3a8f", version = "1743576047162")
    @AutoGenerated(locked = false, uuid = "60044c0d-9dc6-4851-a148-27f9b24c3a8f")
    @RequestMapping(
            value = {"/api/billing-public-config/save-charge-drug-package"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String saveChargeDrugPackage(
            @Valid @NotNull SaveChargeItemPackageBto saveChargeItemPackageBto,
            @Valid List<MergeAttributeValueBto> attributeValueList) {
        ChargeItemBaseDto chargeItemBaseDto =
                chargeItemBaseDtoService.getByItemCode(saveChargeItemPackageBto.getItemCode());
        if (chargeItemBaseDto != null) {
            if (ChargePackageTypeEnum.NOT.equals(chargeItemBaseDto.getPackageType())) {
                throw new IgnoredException(ErrorCode.SYS_ERROR, "套餐ID与收费项目ID重复!");
            }
        }

        attributeValueBOServiceInBillingPublicConfigRpcAdapter.mergeAttributeValueList(
                attributeValueList, "CHARGE_ITEM", saveChargeItemPackageBto.getItemCode());

        saveChargeItemPackageBto.setPackageType(ChargePackageTypeEnum.DRUG_PACKAGE);
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = chargeItemBOService.saveChargeItemPackage(saveChargeItemPackageBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存收费项目套餐 */
    @PublicInterface(id = "62b8d7cf-0366-46d7-9be4-1d869b5a17dd", version = "1743576092705")
    @AutoGenerated(locked = false, uuid = "62b8d7cf-0366-46d7-9be4-1d869b5a17dd")
    @RequestMapping(
            value = {"/api/billing-public-config/save-charge-item-package"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String saveChargeItemPackage(
            @Valid @NotNull SaveChargeItemPackageBto saveChargeItemPackageBto,
            @Valid List<MergeAttributeValueBto> attributeValueList) {

        ChargeItemBaseDto chargeItemBaseDto =
                chargeItemBaseDtoService.getByItemCode(saveChargeItemPackageBto.getItemCode());
        if (chargeItemBaseDto != null) {
            if (ChargePackageTypeEnum.NOT.equals(chargeItemBaseDto.getPackageType())) {
                throw new IgnoredException(ErrorCode.SYS_ERROR, "套餐ID与收费项目ID重复!");
            }
        }

        saveChargeItemPackageBto.setPackageType(ChargePackageTypeEnum.CHARGE_PACKAGE);
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = chargeItemBOService.saveChargeItemPackage(saveChargeItemPackageBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改收费项目启用标识 */
    @PublicInterface(id = "6b0aa718-8490-47fa-a151-29f06cba60b8", version = "1743066768403")
    @AutoGenerated(locked = false, uuid = "6b0aa718-8490-47fa-a151-29f06cba60b8")
    @RequestMapping(
            value = {"/api/billing-public-config/change-charge-item-enable-flag"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String changeChargeItemEnableFlag(
            @Valid ChangeChargeItemEnableFlagBto changeChargeItemEnableFlagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                chargeItemBOService.changeChargeItemEnableFlag(changeChargeItemEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存收费项目执行科室列表 */
    @PublicInterface(id = "ab46c89b-6f65-4931-b2bc-d737399bda87", version = "1743058485374")
    @AutoGenerated(locked = false, uuid = "ab46c89b-6f65-4931-b2bc-d737399bda87")
    @RequestMapping(
            value = {"/api/billing-public-config/save-charge-item-perform-department-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String saveChargeItemPerformDepartmentList(
            @Valid SaveChargeItemPerformDepartmentListBto saveChargeItemPerformDepartmentListBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                chargeItemBOService.saveChargeItemPerformDepartmentList(
                        saveChargeItemPerformDepartmentListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改复核状态 */
    @PublicInterface(id = "d581cc0a-e4cd-4638-bc70-55452ff5020f", version = "1743067126478")
    @AutoGenerated(locked = false, uuid = "d581cc0a-e4cd-4638-bc70-55452ff5020f")
    @RequestMapping(
            value = {"/api/billing-public-config/change-charge-item-audit-status"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String changeChargeItemAuditStatus(
            @Valid
                    ChangeChargeItemAuditStatusBto.ChargeItemPriceDetailBto
                            chargeItemPriceDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = chargeItemBOService.changeChargeItemAuditStatus(chargeItemPriceDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存收费项目价格明细 */
    @PublicInterface(id = "ec51a87e-0991-4df5-89c1-b7aa22f88dc5", version = "1743058356226")
    @AutoGenerated(locked = false, uuid = "ec51a87e-0991-4df5-89c1-b7aa22f88dc5")
    @RequestMapping(
            value = {"/api/billing-public-config/save-charge-item-price-detail-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String saveChargeItemPriceDetailList(
            @Valid @NotNull SaveChargeItemPriceDetailListBto saveChargeItemPriceDetailListBto) {
        if (CollUtil.isEmpty(saveChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList())) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "收费项目价格明细不能为空!");
        }

        List<ChargeItemPriceDetailBaseDto> chargeItemPriceDetailInDB =
                chargeItemPriceDetailBaseDtoService.getByChargeItemCode(
                        saveChargeItemPriceDetailListBto.getItemCode());
        Map<String, ChargeItemPriceDetailBaseDto> chargeItemPriceDetailInDBMap =
                chargeItemPriceDetailInDB.stream()
                        .collect(
                                Collectors.toMap(
                                        ChargeItemPriceDetailBaseDto::getId, Function.identity()));

        for (SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto chargeItemPriceDetailBto :
                saveChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList()) {
            if (chargeItemPriceDetailBto.getId() == null) {
                chargeItemPriceDetailBto.setAuditStatus(AuditStatusEnum.PASS);
            }
        }

        Map<String, List<SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto>> campusMap =
                saveChargeItemPriceDetailListBto.getChargeItemPriceDetailBtoList().stream()
                        .collect(
                                Collectors.groupingBy(
                                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                                ::getCampusId));

        for (List<SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto>
                chargeItemPriceDetailBtos : campusMap.values()) {
            Map<String, List<SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto>>
                    priceTypeMap =
                            chargeItemPriceDetailBtos.stream()
                                    .collect(
                                            Collectors.groupingBy(
                                                    SaveChargeItemPriceDetailListBto
                                                                    .ChargeItemPriceDetailBto
                                                            ::getPriceType));
            // 校验数据
            priceTypeMap.forEach(
                    (priceType, priceDetails) -> {
                        validatePriceDetails(priceDetails, chargeItemPriceDetailInDBMap);
                        if (priceDetails.size() > 1) {
                            validateTimeOverlap(priceDetails);
                        }
                    });
        }

        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                chargeItemBOService.saveChargeItemPriceDetailList(saveChargeItemPriceDetailListBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    private void validatePriceDetails(
            List<SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto> priceDetails,
            Map<String, ChargeItemPriceDetailBaseDto> chargeItemPriceDetailInDBMap) {
        if (priceDetails.size() > 1
                && priceDetails.size()
                        != priceDetails.stream()
                                .filter(valueItem -> valueItem.getId() != null)
                                .count()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "已存在同种价格类型不允许新增");
        }

        priceDetails.forEach(
                valueItem -> {
                    if (valueItem.getId() != null) {
                        ChargeItemPriceDetailBaseDto chargeItemPriceDetailBaseDto =
                                chargeItemPriceDetailInDBMap.get(valueItem.getId());
                        if (chargeItemPriceDetailBaseDto.getPrice().compareTo(valueItem.getPrice())
                                != 0) {
                            throw new IgnoredException(ErrorCode.SYS_ERROR, "收费项目价格明细价格不允许修改");
                        }
                        if (!valueItem
                                .getStartTime()
                                .equals(chargeItemPriceDetailBaseDto.getStartTime())) {
                            throw new IgnoredException(ErrorCode.SYS_ERROR, "收费项目价格明细开始时间不允许修改");
                        }
                    }
                });
    }

    private void validateTimeOverlap(
            List<SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto> priceDetails) {
        List<SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto> sortedList =
                priceDetails.stream()
                        .sorted(
                                Comparator.comparing(
                                        SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto
                                                ::getStartTime))
                        .collect(Collectors.toList());

        for (int i = 1; i < sortedList.size(); i++) {
            SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto previous =
                    sortedList.get(i - 1);
            SaveChargeItemPriceDetailListBto.ChargeItemPriceDetailBto current = sortedList.get(i);

            if (DateUtils.isOverlapping(
                    previous.getStartTime(),
                    previous.getEndTime(),
                    current.getStartTime(),
                    current.getEndTime())) {
                throw new IgnoredException(ErrorCode.SYS_ERROR, "收费项目价格明细时间段不允许重叠");
            }
        }
    }
}
