package com.pulse.billing_public_config.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.entrance.web.query.assembler.ChargeItemPerformDepartmentVoDataAssembler;
import com.pulse.billing_public_config.entrance.web.query.assembler.ChargeItemPerformDepartmentVoDataAssembler.ChargeItemPerformDepartmentVoDataHolder;
import com.pulse.billing_public_config.entrance.web.query.collector.ChargeItemPerformDepartmentVoDataCollector;
import com.pulse.billing_public_config.entrance.web.vo.BillingPublicConfigRefOrganizationVo;
import com.pulse.billing_public_config.entrance.web.vo.ChargeItemPerformDepartmentVo;
import com.pulse.billing_public_config.manager.dto.ChargeItemPerformDepartmentDto;
import com.pulse.billing_public_config.service.ChargeItemPerformDepartmentBaseDtoService;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ChargeItemPerformDepartmentVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "4aa123d1-9749-4e50-ab75-5d895ea75a7f|VO|CONVERTER")
public class ChargeItemPerformDepartmentVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private BillingPublicConfigRefOrganizationVoConverter
            billingPublicConfigRefOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPerformDepartmentBaseDtoService chargeItemPerformDepartmentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPerformDepartmentVoDataAssembler chargeItemPerformDepartmentVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPerformDepartmentVoDataCollector chargeItemPerformDepartmentVoDataCollector;

    /** 使用默认方式组装ChargeItemPerformDepartmentVo列表数据 */
    @AutoGenerated(locked = true, uuid = "07786f0f-a623-3b3e-b2ed-0ba8c3e88eab")
    public List<ChargeItemPerformDepartmentVo> convertAndAssembleDataList(
            List<ChargeItemPerformDepartmentDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        ChargeItemPerformDepartmentVoDataHolder dataHolder =
                new ChargeItemPerformDepartmentVoDataHolder();
        dataHolder.setRootBaseDtoList(
                chargeItemPerformDepartmentBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(ChargeItemPerformDepartmentDto::getId)
                                .collect(Collectors.toList())));
        Map<String, ChargeItemPerformDepartmentVo> voMap =
                convertToChargeItemPerformDepartmentVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        chargeItemPerformDepartmentVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        chargeItemPerformDepartmentVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把ChargeItemPerformDepartmentDto转换成ChargeItemPerformDepartmentVo */
    @AutoGenerated(locked = false, uuid = "4aa123d1-9749-4e50-ab75-5d895ea75a7f-converter-Map")
    public Map<ChargeItemPerformDepartmentDto, ChargeItemPerformDepartmentVo>
            convertToChargeItemPerformDepartmentVoMap(
                    List<ChargeItemPerformDepartmentDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<OrganizationBaseDto, BillingPublicConfigRefOrganizationVo> departmentMap =
                billingPublicConfigRefOrganizationVoConverter
                        .convertToBillingPublicConfigRefOrganizationVoMap(
                                dtoList.stream()
                                        .filter(Objects::nonNull)
                                        .map(ChargeItemPerformDepartmentDto::getDepartment)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()));
        Map<ChargeItemPerformDepartmentDto, ChargeItemPerformDepartmentVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ChargeItemPerformDepartmentVo vo =
                                                    new ChargeItemPerformDepartmentVo();
                                            vo.setId(dto.getId());
                                            vo.setItemCode(dto.getItemCode());
                                            vo.setDefaultFlag(dto.getDefaultFlag());
                                            vo.setDepartment(
                                                    dto.getDepartment() == null
                                                            ? null
                                                            : departmentMap.get(
                                                                    dto.getDepartment()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ChargeItemPerformDepartmentDto转换成ChargeItemPerformDepartmentVo */
    @AutoGenerated(locked = true, uuid = "4aa123d1-9749-4e50-ab75-5d895ea75a7f-converter-list")
    public List<ChargeItemPerformDepartmentVo> convertToChargeItemPerformDepartmentVoList(
            List<ChargeItemPerformDepartmentDto> dtoList) {
        return new ArrayList<>(convertToChargeItemPerformDepartmentVoMap(dtoList).values());
    }

    /** 使用默认方式组装ChargeItemPerformDepartmentVo数据 */
    @AutoGenerated(locked = true, uuid = "94d6806b-ab7f-318f-b0fa-86f20c61ca66")
    public ChargeItemPerformDepartmentVo convertAndAssembleData(
            ChargeItemPerformDepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ChargeItemPerformDepartmentDto转换成ChargeItemPerformDepartmentVo */
    @AutoGenerated(locked = true, uuid = "e6bb6e14-e78a-3887-ad95-5995968e10c9")
    public ChargeItemPerformDepartmentVo convertToChargeItemPerformDepartmentVo(
            ChargeItemPerformDepartmentDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToChargeItemPerformDepartmentVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
