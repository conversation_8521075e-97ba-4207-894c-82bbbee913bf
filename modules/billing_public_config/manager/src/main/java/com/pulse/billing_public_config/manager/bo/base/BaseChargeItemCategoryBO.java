package com.pulse.billing_public_config.manager.bo.base;

import com.pulse.billing_public_config.manager.bo.ChargeItemCategoryBO;
import com.pulse.billing_public_config.persist.dos.ChargeItemCategory;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.converter.InputCodeEoConverter;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.eo.StringListConverter;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.Valid;

@DoNotModify
@Table(name = "charge_item_category")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "25f36484-586f-3d29-ab69-3afdcf368815")
public abstract class BaseChargeItemCategoryBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 校园ID列表 */
    @Column(name = "campus_id_list")
    @Valid
    @AutoGenerated(locked = true, uuid = "ef919992-93a6-46b6-bb9a-c00ff8734f61")
    @Convert(converter = StringListConverter.class)
    private List<String> campusIdList;

    /** 分类代码 */
    @Column(name = "category_code")
    @AutoGenerated(locked = true, uuid = "e40f5cb1-0f84-4b42-b596-c4f1a6d7fbe4")
    private String categoryCode;

    /** 收费目录ID */
    @Column(name = "category_id")
    @AutoGenerated(locked = true, uuid = "0f686c2d-e3a8-47c2-990c-619a50d11392")
    @Id
    private String categoryId;

    /** 目录名称 */
    @Column(name = "category_name")
    @AutoGenerated(locked = true, uuid = "19f50f8c-28dc-47bf-9e1a-46d3dba91614")
    private String categoryName;

    /** 收费项目类型 */
    @Column(name = "charge_item_type")
    @AutoGenerated(locked = true, uuid = "99bdeed4-f08c-4390-a40d-069a5c4dd43a")
    private String chargeItemType;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "6320f10b-7db3-5052-be76-c2650c7f77c1")
    private Date createdAt;

    /** 创建者 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "259ad4a1-d080-4b34-8f32-ff0f99d6b527")
    private String createdBy;

    /** 启用标志 */
    @Column(name = "enable_flag")
    @AutoGenerated(locked = true, uuid = "c4f923b4-869d-4845-9d99-e508032e358b")
    private Boolean enableFlag;

    /** 输入代码 */
    @Column(name = "input_code")
    @Valid
    @AutoGenerated(locked = true, uuid = "5e89f55c-4f61-4e7c-81eb-738baffce5c2")
    @Convert(converter = InputCodeEoConverter.class)
    private InputCodeEo inputCode;

    /** 项目内涵 */
    @Column(name = "item_connotation")
    @AutoGenerated(locked = true, uuid = "865aa8f7-2e82-423b-94b7-02bcf34408f9")
    private String itemConnotation;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "94e7e04f-d5a5-4e4f-919f-dabec8202523")
    @Version
    private Long lockVersion;

    /** 父目录ID */
    @Column(name = "parent_category_id")
    @AutoGenerated(locked = true, uuid = "41b338fa-ca91-46fc-a910-73c0947f3098")
    private String parentCategoryId;

    /** 核算类型 */
    @Column(name = "reckoning_type")
    @AutoGenerated(locked = true, uuid = "ef652e2e-8d54-4fef-8609-3b66f5556030")
    private String reckoningType;

    /** 排序编号 */
    @Column(name = "sort_number")
    @AutoGenerated(locked = true, uuid = "516e03fb-7560-4601-b654-8421d058cebe")
    private Long sortNumber;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "76218f41-7055-5f9a-919b-489f828d5e1a")
    private Date updatedAt;

    /** 更新者 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "a15dba98-68f9-45ed-bf11-b1cdcb138ca5")
    private String updatedBy;

    /** 使用范围列表 */
    @Column(name = "use_scope_list")
    @Valid
    @AutoGenerated(locked = true, uuid = "64a4d902-ab60-43ce-9106-854ae3377acc")
    @Convert(converter = StringListConverter.class)
    private List<String> useScopeList;

    @AutoGenerated(locked = true)
    public ChargeItemCategory convertToChargeItemCategory() {
        ChargeItemCategory entity = new ChargeItemCategory();
        BoUtil.copyProperties(
                this,
                entity,
                "categoryId",
                "categoryCode",
                "categoryName",
                "inputCode",
                "sortNumber",
                "chargeItemType",
                "reckoningType",
                "parentCategoryId",
                "itemConnotation",
                "enableFlag",
                "campusIdList",
                "useScopeList",
                "createdBy",
                "updatedBy",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static ChargeItemCategoryBO getByCategoryId(String categoryId) {
        Session session = TransactionalSessionFactory.getSession();
        ChargeItemCategoryBO chargeItemCategory =
                (ChargeItemCategoryBO)
                        session.createQuery(
                                        "from ChargeItemCategoryBO where "
                                                + "categoryId =: categoryId ")
                                .setParameter("categoryId", categoryId)
                                .uniqueResult();
        return chargeItemCategory;
    }

    @AutoGenerated(locked = true)
    public List<String> getCampusIdList() {
        return this.campusIdList;
    }

    @AutoGenerated(locked = true)
    public String getCategoryCode() {
        return this.categoryCode;
    }

    @AutoGenerated(locked = true)
    public String getCategoryId() {
        return this.categoryId;
    }

    @AutoGenerated(locked = true)
    public String getCategoryName() {
        return this.categoryName;
    }

    @AutoGenerated(locked = true)
    public String getChargeItemType() {
        return this.chargeItemType;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Boolean getEnableFlag() {
        return this.enableFlag;
    }

    @AutoGenerated(locked = true)
    public InputCodeEo getInputCode() {
        return this.inputCode;
    }

    @AutoGenerated(locked = true)
    public String getItemConnotation() {
        return this.itemConnotation;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getParentCategoryId() {
        return this.parentCategoryId;
    }

    @AutoGenerated(locked = true)
    public String getReckoningType() {
        return this.reckoningType;
    }

    @AutoGenerated(locked = true)
    public Long getSortNumber() {
        return this.sortNumber;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public List<String> getUseScopeList() {
        return this.useScopeList;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setCampusIdList(List<String> campusIdList) {
        this.campusIdList = campusIdList;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setCategoryId(String categoryId) {
        this.categoryId = categoryId;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setCategoryName(String categoryName) {
        this.categoryName = categoryName;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setChargeItemType(String chargeItemType) {
        this.chargeItemType = chargeItemType;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setEnableFlag(Boolean enableFlag) {
        this.enableFlag = enableFlag;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setInputCode(InputCodeEo inputCode) {
        this.inputCode = inputCode;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setItemConnotation(String itemConnotation) {
        this.itemConnotation = itemConnotation;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setParentCategoryId(String parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setReckoningType(String reckoningType) {
        this.reckoningType = reckoningType;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (ChargeItemCategoryBO) this;
    }

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBO setUseScopeList(List<String> useScopeList) {
        this.useScopeList = useScopeList;
        return (ChargeItemCategoryBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
