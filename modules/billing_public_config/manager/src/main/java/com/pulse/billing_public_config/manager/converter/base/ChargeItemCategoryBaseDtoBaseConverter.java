package com.pulse.billing_public_config.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.dto.ChargeItemCategoryBaseDto;
import com.pulse.billing_public_config.persist.dos.ChargeItemCategory;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "75889e31-bac4-4260-82ab-ebb559394f90|DTO|BASE_CONVERTER")
public class ChargeItemCategoryBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ChargeItemCategoryBaseDto convertFromChargeItemCategoryToChargeItemCategoryBaseDto(
            ChargeItemCategory chargeItemCategory) {
        return convertFromChargeItemCategoryToChargeItemCategoryBaseDto(List.of(chargeItemCategory))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ChargeItemCategoryBaseDto> convertFromChargeItemCategoryToChargeItemCategoryBaseDto(
            List<ChargeItemCategory> chargeItemCategoryList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargeItemCategoryList)) {
            return new ArrayList<>();
        }
        List<ChargeItemCategoryBaseDto> chargeItemCategoryBaseDtoList = new ArrayList<>();
        for (ChargeItemCategory chargeItemCategory : chargeItemCategoryList) {
            if (chargeItemCategory == null) {
                continue;
            }
            ChargeItemCategoryBaseDto chargeItemCategoryBaseDto = new ChargeItemCategoryBaseDto();
            chargeItemCategoryBaseDto.setCategoryId(chargeItemCategory.getCategoryId());
            chargeItemCategoryBaseDto.setCategoryCode(chargeItemCategory.getCategoryCode());
            chargeItemCategoryBaseDto.setCategoryName(chargeItemCategory.getCategoryName());
            chargeItemCategoryBaseDto.setInputCode(chargeItemCategory.getInputCode());
            chargeItemCategoryBaseDto.setSortNumber(chargeItemCategory.getSortNumber());
            chargeItemCategoryBaseDto.setChargeItemType(chargeItemCategory.getChargeItemType());
            chargeItemCategoryBaseDto.setReckoningType(chargeItemCategory.getReckoningType());
            chargeItemCategoryBaseDto.setParentCategoryId(chargeItemCategory.getParentCategoryId());
            chargeItemCategoryBaseDto.setItemConnotation(chargeItemCategory.getItemConnotation());
            chargeItemCategoryBaseDto.setEnableFlag(chargeItemCategory.getEnableFlag());
            chargeItemCategoryBaseDto.setCampusIdList(chargeItemCategory.getCampusIdList());
            chargeItemCategoryBaseDto.setUseScopeList(chargeItemCategory.getUseScopeList());
            chargeItemCategoryBaseDto.setCreatedBy(chargeItemCategory.getCreatedBy());
            chargeItemCategoryBaseDto.setUpdatedBy(chargeItemCategory.getUpdatedBy());
            chargeItemCategoryBaseDto.setLockVersion(chargeItemCategory.getLockVersion());
            chargeItemCategoryBaseDto.setCreatedAt(chargeItemCategory.getCreatedAt());
            chargeItemCategoryBaseDto.setUpdatedAt(chargeItemCategory.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            chargeItemCategoryBaseDtoList.add(chargeItemCategoryBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return chargeItemCategoryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
