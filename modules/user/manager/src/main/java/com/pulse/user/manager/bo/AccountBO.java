package com.pulse.user.manager.bo;

import com.pulse.user.common.enums.AccountStatusEnum;
import com.pulse.user.persist.dos.Account;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE user_account  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ?")
@Getter
@Setter
@Table(name = "user_account")
@Entity
@AutoGenerated(locked = true, uuid = "a9f3e0ae-54b9-465f-8942-86a0f6293562|BO|DEFINITION")
public class AccountBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 访问令牌 */
    @Column(name = "access_token")
    @AutoGenerated(locked = true, uuid = "b8120e60-c8f2-4f89-9368-f8704a93991e")
    private String accessToken;

    /** 访问令牌有效期 */
    @Column(name = "access_token_valid_period")
    @AutoGenerated(locked = true, uuid = "552e6153-91ea-4c92-9871-60084f4e1e88")
    private Long accessTokenValidPeriod;

    /** 账户名称 */
    @Column(name = "account_name")
    @AutoGenerated(locked = true, uuid = "f1ca2f29-ef85-4373-8035-59ce095055be")
    private String accountName;

    /** 认证功能数据 */
    @Column(name = "authentication_feature_data")
    @AutoGenerated(locked = true, uuid = "a20b25f5-7055-4294-a76a-be361d8f28ce")
    private String authenticationFeatureData;

    /** 认证特征数据盐值 */
    @Column(name = "authentication_feature_data_salt")
    @AutoGenerated(locked = true, uuid = "********-7cab-40a6-8d85-8fe741d55371")
    private String authenticationFeatureDataSalt;

    /** 手机号 */
    @Column(name = "cellphone")
    @AutoGenerated(locked = true, uuid = "4962e218-2c65-44a4-bb8f-9cc2e17965ed")
    private String cellphone;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "2d7e5921-7cd9-4e32-847a-346e26718b80")
    private Date createdAt;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "80a07c3d-e127-453f-a670-cd0f93fb6a90")
    private Long deletedAt = 0L;

    /** 加密密码 */
    @Column(name = "encryption_password")
    @AutoGenerated(locked = true, uuid = "d7b10664-37a0-4de3-b8cf-5a74369f141c")
    private String encryptionPassword;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "476cd7ac-d37f-4451-b1bf-ec644b02353f")
    @Id
    private String id;

    /** 最后登录失败时间 */
    @Column(name = "last_login_failure_time")
    @AutoGenerated(locked = true, uuid = "62bb900a-2daf-4380-b1ee-a5d02f153f7b")
    private Date lastLoginFailureTime;

    /** 上次登录时间 */
    @Column(name = "last_login_time")
    @AutoGenerated(locked = true, uuid = "abe4debb-af43-4804-ad1a-57b02c16c952")
    private Date lastLoginTime;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 登录失败次数 */
    @Column(name = "login_failure_count")
    @AutoGenerated(locked = true, uuid = "63263348-711b-4e06-b680-7656661b5dd4")
    private Integer loginFailureCount;

    /** 登录类型 */
    @Column(name = "login_type")
    @AutoGenerated(locked = true, uuid = "1c454d2f-60e2-4937-a995-8e4e4b9a2105")
    private String loginType;

    /** 密码盐值 */
    @Column(name = "password_salt")
    @AutoGenerated(locked = true, uuid = "179c40e1-6493-4bd1-8f8b-ad7b9add10ca")
    private String passwordSalt;

    /** 刷新令牌 */
    @Column(name = "refresh_token")
    @AutoGenerated(locked = true, uuid = "84ccf95d-26b9-46c2-95c6-24742f2b4fd5")
    private String refreshToken;

    /** 状态 */
    @Column(name = "status")
    @AutoGenerated(locked = true, uuid = "2bc95048-b3a5-4538-a810-40d264fb030c")
    @Enumerated(EnumType.STRING)
    private AccountStatusEnum status;

    /** 第三方账户ID */
    @Column(name = "third_party_account_id")
    @AutoGenerated(locked = true, uuid = "74f91b00-56da-4b8c-a9c6-0c8522a6fe9d")
    private String thirdPartyAccountId;

    /** 第三方平台类型 */
    @Column(name = "third_party_platform_type")
    @AutoGenerated(locked = true, uuid = "341448eb-3848-45d2-97ec-71de956d3bbb")
    private String thirdPartyPlatformType;

    /** 令牌类型 */
    @Column(name = "token_type")
    @AutoGenerated(locked = true, uuid = "e711e84d-e788-4a9b-b55b-6aea395d05b6")
    private String tokenType;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "84e21910-30f1-44e2-bf13-76f942e66717")
    private Date updatedAt;

    @ManyToOne
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private UserInfoBO userInfoBO;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "3f2a84df-34f5-4043-ac30-041433cd6b60|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public Account convertToAccount() {
        Account entity = new Account();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "loginType",
                "accountName",
                "encryptionPassword",
                "passwordSalt",
                "cellphone",
                "thirdPartyAccountId",
                "thirdPartyPlatformType",
                "accessToken",
                "refreshToken",
                "accessTokenValidPeriod",
                "tokenType",
                "authenticationFeatureData",
                "authenticationFeatureDataSalt",
                "status",
                "lastLoginTime",
                "loginFailureCount",
                "lastLoginFailureTime",
                "createdAt",
                "updatedAt",
                "deletedAt");
        UserInfoBO userInfoBO = this.getUserInfoBO();
        entity.setUserId(userInfoBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getAccessToken() {
        return this.accessToken;
    }

    @AutoGenerated(locked = true)
    public Long getAccessTokenValidPeriod() {
        return this.accessTokenValidPeriod;
    }

    @AutoGenerated(locked = true)
    public String getAccountName() {
        return this.accountName;
    }

    @AutoGenerated(locked = true)
    public String getAuthenticationFeatureData() {
        return this.authenticationFeatureData;
    }

    @AutoGenerated(locked = true)
    public String getAuthenticationFeatureDataSalt() {
        return this.authenticationFeatureDataSalt;
    }

    @AutoGenerated(locked = true)
    public String getCellphone() {
        return this.cellphone;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getEncryptionPassword() {
        return this.encryptionPassword;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public Date getLastLoginFailureTime() {
        return this.lastLoginFailureTime;
    }

    @AutoGenerated(locked = true)
    public Date getLastLoginTime() {
        return this.lastLoginTime;
    }

    @AutoGenerated(locked = true)
    public Integer getLoginFailureCount() {
        return this.loginFailureCount;
    }

    @AutoGenerated(locked = true)
    public String getLoginType() {
        return this.loginType;
    }

    @AutoGenerated(locked = true)
    public String getPasswordSalt() {
        return this.passwordSalt;
    }

    @AutoGenerated(locked = true)
    public String getRefreshToken() {
        return this.refreshToken;
    }

    @AutoGenerated(locked = true)
    public AccountStatusEnum getStatus() {
        return this.status;
    }

    @AutoGenerated(locked = true)
    public String getThirdPartyAccountId() {
        return this.thirdPartyAccountId;
    }

    @AutoGenerated(locked = true)
    public String getThirdPartyPlatformType() {
        return this.thirdPartyPlatformType;
    }

    @AutoGenerated(locked = true)
    public String getTokenType() {
        return this.tokenType;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUserId() {
        return this.getUserInfoBO().getId();
    }

    @AutoGenerated(locked = true)
    public UserInfoBO getUserInfoBO() {
        return this.userInfoBO;
    }

    @AutoGenerated(locked = true)
    public AccountBO setAccessToken(String accessToken) {
        this.accessToken = accessToken;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setAccessTokenValidPeriod(Long accessTokenValidPeriod) {
        this.accessTokenValidPeriod = accessTokenValidPeriod;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setAccountName(String accountName) {
        this.accountName = accountName;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setAuthenticationFeatureData(String authenticationFeatureData) {
        this.authenticationFeatureData = authenticationFeatureData;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setAuthenticationFeatureDataSalt(String authenticationFeatureDataSalt) {
        this.authenticationFeatureDataSalt = authenticationFeatureDataSalt;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setCellphone(String cellphone) {
        this.cellphone = cellphone;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setEncryptionPassword(String encryptionPassword) {
        this.encryptionPassword = encryptionPassword;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setId(String id) {
        this.id = id;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setLastLoginFailureTime(Date lastLoginFailureTime) {
        this.lastLoginFailureTime = lastLoginFailureTime;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setLoginFailureCount(Integer loginFailureCount) {
        this.loginFailureCount = loginFailureCount;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setLoginType(String loginType) {
        this.loginType = loginType;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setPasswordSalt(String passwordSalt) {
        this.passwordSalt = passwordSalt;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setStatus(AccountStatusEnum status) {
        this.status = status;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setThirdPartyAccountId(String thirdPartyAccountId) {
        this.thirdPartyAccountId = thirdPartyAccountId;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setThirdPartyPlatformType(String thirdPartyPlatformType) {
        this.thirdPartyPlatformType = thirdPartyPlatformType;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setTokenType(String tokenType) {
        this.tokenType = tokenType;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (AccountBO) this;
    }

    @AutoGenerated(locked = true)
    public AccountBO setUserInfoBO(UserInfoBO userInfoBO) {
        this.userInfoBO = userInfoBO;
        return (AccountBO) this;
    }
}
