package com.pulse.user.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.StaffUserWithStaffDto;
import com.pulse.user.manager.UserBaseDtoManager;
import com.pulse.user.manager.UserSimpleDtoManager;
import com.pulse.user.manager.converter.UserBaseDtoConverter;
import com.pulse.user.manager.converter.UserSimpleDtoConverter;
import com.pulse.user.manager.dto.UserBaseDto;
import com.pulse.user.manager.dto.UserSimpleDto;
import com.pulse.user.manager.facade.organization.StaffUserWithStaffDtoServiceInUserRpcAdapter;
import com.pulse.user.persist.dos.UserInfo;
import com.pulse.user.persist.mapper.UserInfoDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "d9e39485-cfcb-4a95-9dd8-d04ec1cca349|DTO|BASE_MANAGER_IMPL")
public abstract class UserSimpleDtoManagerBaseImpl implements UserSimpleDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private StaffUserWithStaffDtoServiceInUserRpcAdapter
            staffUserWithStaffDtoServiceInUserRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private UserBaseDtoConverter userBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private UserBaseDtoManager userBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private UserInfoDao userInfoDao;

    @AutoGenerated(locked = true)
    @Autowired
    private UserSimpleDtoConverter userSimpleDtoConverter;

    @AutoGenerated(locked = true, uuid = "215016b8-1795-3192-8786-b586900ad49c")
    public List<UserSimpleDto> doConvertFromUserInfoToUserSimpleDto(List<UserInfo> userInfoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(userInfoList)) {
            return Collections.emptyList();
        }

        List<StaffUserWithStaffDto> staffUserWithStaffDtoList =
                staffUserWithStaffDtoServiceInUserRpcAdapter.getByUserIds(
                        userInfoList.stream().map(i -> i.getId()).collect(Collectors.toList()));
        Map<String, StaffUserWithStaffDto> idStaffUserWithStaffDtoMap =
                staffUserWithStaffDtoList.stream()
                        .collect(Collectors.toMap(i -> i.getUserId(), i -> i));

        List<UserBaseDto> baseDtoList =
                userBaseDtoConverter.convertFromUserInfoToUserBaseDto(userInfoList);
        Map<String, UserSimpleDto> dtoMap =
                userSimpleDtoConverter.convertFromUserBaseDtoToUserSimpleDto(baseDtoList).stream()
                        .collect(
                                Collectors.toMap(
                                        UserSimpleDto::getId, Function.identity(), (o1, o2) -> o1));

        List<UserSimpleDto> userSimpleDtoList = new ArrayList<>();
        for (UserInfo i : userInfoList) {
            UserSimpleDto userSimpleDto = dtoMap.get(i.getId());
            if (userSimpleDto == null) {
                continue;
            }

            if (null != i.getId()) {
                userSimpleDto.setStaffUserWithStaff(
                        idStaffUserWithStaffDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            userSimpleDtoList.add(userSimpleDto);
        }
        return userSimpleDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "7654acab-9516-3bca-90f1-c58057974c05")
    @Override
    public UserSimpleDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserSimpleDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        UserSimpleDto userSimpleDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return userSimpleDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9700f7f9-7e4b-3a5e-a63e-25d523b65c0b")
    @Override
    public List<UserSimpleDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<UserInfo> userInfoList = userInfoDao.getByIds(id);
        if (CollectionUtil.isEmpty(userInfoList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, UserInfo> userInfoMap =
                userInfoList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        userInfoList =
                id.stream()
                        .map(i -> userInfoMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromUserInfoToUserSimpleDto(userInfoList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
