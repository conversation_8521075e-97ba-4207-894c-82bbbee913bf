package com.pulse.organization.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.pulse.organization.common.enums.StaffStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "8f684ec7-bf7c-4a73-a022-17af27ae14dc|DTO|DEFINITION")
public class StaffWithStaffOrganizationListDto {
    /** 核算组织id */
    @AutoGenerated(locked = true, uuid = "9314bf2a-ab87-4c16-8e23-b84041e7964d")
    private String accountingOrganizationId;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "006f5326-a3c5-4208-8cbf-f5b8d909ad54")
    private Date birthDate;

    /** 证件编号 */
    @AutoGenerated(locked = true, uuid = "ce7ef6a9-9c17-4b58-b84e-69efc669e81f")
    private String certificateNumber;

    /** 证件类型id */
    @AutoGenerated(locked = true, uuid = "be96f06e-90ea-4ae3-b17e-0649adfd7291")
    private String certificateTypeId;

    /** 下乡结束日期 */
    @AutoGenerated(locked = true, uuid = "f7a3c5ce-5b66-4545-87e3-074c5406c30d")
    private Date countrysideEndDate;

    /** 下乡开始日期 */
    @AutoGenerated(locked = true, uuid = "a97bef70-cb2f-4a90-a9ac-2d47a84ba9a8")
    private Date countrysideStartDate;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "b79b1b5b-8b6d-41f4-bcbe-1ccf2509c59e")
    private Date createdAt;

    /** 创建者ID */
    @AutoGenerated(locked = true, uuid = "694f0f65-b2ee-424e-befe-cd54a83c7062")
    private String createdBy;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "fee0b636-de3f-4456-b9c0-147dadc6d099")
    private Long deletedAt;

    /** 删除者ID */
    @AutoGenerated(locked = true, uuid = "e4e4570e-71ea-4adb-97ec-1c4fd83aa188")
    private String deletedBy;

    /** 介绍描述 */
    @AutoGenerated(locked = true, uuid = "7e414365-81b7-4af0-bb0b-b6061701fbcd")
    private String description;

    /** 医生备注 */
    @AutoGenerated(locked = true, uuid = "49729c52-9cad-43c4-843b-ef847726b58f")
    private String doctorRemark;

    /** 电子邮件地址 */
    @AutoGenerated(locked = true, uuid = "9da01c39-09b0-4707-a8b3-b504e0cc7992")
    private String emailAddress;

    /** 专家标志 */
    @AutoGenerated(locked = true, uuid = "9a3ef359-ab61-4b46-8aaa-963277736fda")
    private Boolean expertFlag;

    /** 性别 */
    @AutoGenerated(locked = true, uuid = "5b20da12-bc54-4336-a0c4-04518044aee2")
    private GenderEnum gender;

    /** 人事组织id */
    @AutoGenerated(locked = true, uuid = "6a1f0fd3-fc15-41af-ac47-6a5226cd141e")
    private String hrOrganizationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f079790e-fe00-4e07-be5e-92e9a3c48627")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "85e9ee18-787a-4635-b739-78084e8c45f7")
    private InputCodeEo inputCode;

    /** 姓名 */
    @AutoGenerated(locked = true, uuid = "d1ea701c-4ad3-467d-9cce-f79ad97ef1ca")
    private String name;

    /** 所属组织id */
    @AutoGenerated(locked = true, uuid = "0fefdb0a-ee23-4399-be25-58ebfba0c6c2")
    private String organizationId;

    /** 电话号码 */
    @AutoGenerated(locked = true, uuid = "4f4f83d7-6010-4404-bf3d-432c0a613b8e")
    private String phoneNumber;

    /** 职务id */
    @AutoGenerated(locked = true, uuid = "4dd1d680-9a31-4bc0-9ab7-190b81f16006")
    private String positionId;

    /** 职称id */
    @AutoGenerated(locked = true, uuid = "05a00a5c-1383-41fa-b20e-65a9da03b9be")
    private String professionalTitleId;

    /** 晋升日期 */
    @AutoGenerated(locked = true, uuid = "ae7ba6c6-3871-427e-905c-7ba13104fa12")
    private Date promotionDate;

    /** 省平台作废标识 */
    @AutoGenerated(locked = true, uuid = "3d79b7f8-47ae-44f4-92de-d412aa36d7dc")
    private Boolean provincePlatformCancelFlag;

    /** 挂号医生启用标志 */
    @AutoGenerated(locked = true, uuid = "732c5fd2-d8de-41d3-aa85-6b78045d0cce")
    private Boolean registerDoctorEnableFlag;

    /** 挂号医生标志 */
    @AutoGenerated(locked = true, uuid = "bf21aa9c-fd6b-4c37-ba05-6515478538ee")
    private Boolean registerDoctorFlag;

    /** 挂号类别ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c49941ff-ef0e-4a8f-9fd5-41013f7f0324")
    private List<String> registerTypeList;

    /** 手机短号 */
    @AutoGenerated(locked = true, uuid = "10c8a4f1-7dc1-44d6-b77b-fc315aa7d1f6")
    private String shortPhoneNumber;

    /** 序号 */
    @AutoGenerated(locked = true, uuid = "57fbcf6e-6fbe-477e-838c-eae9f6374e20")
    private Long sortNumber;

    /** 员工编号 */
    @AutoGenerated(locked = true, uuid = "6a4ec0a4-745c-42a0-be7d-90f55c926aaa")
    private String staffNumber;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "35e7af94-adbc-43c7-b060-c07713d55020")
    private List<StaffOrganizationDto> staffOrganizationList;

    /** 职工类别id */
    @AutoGenerated(locked = true, uuid = "c28c2625-9a45-41a8-8862-90e3bcf8c6da")
    private String staffTypeId;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "47213d70-1149-48cb-9ccc-876f2f07d658")
    private StaffStatusEnum status;

    /** 更新者ID */
    @AutoGenerated(locked = true, uuid = "8e8804f2-2816-4b4e-aa11-d0100e58e812")
    private String updateBy;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "44cbbd20-9e35-4272-a99d-a9e20a5e0752")
    private Date updatedAt;
}
