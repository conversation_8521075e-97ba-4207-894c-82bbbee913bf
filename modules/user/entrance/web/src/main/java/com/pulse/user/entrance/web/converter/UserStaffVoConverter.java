package com.pulse.user.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffUserWithStaffDto;
import com.pulse.user.entrance.web.query.assembler.UserStaffVoDataAssembler;
import com.pulse.user.entrance.web.query.assembler.UserStaffVoDataAssembler.UserStaffVoDataHolder;
import com.pulse.user.entrance.web.query.collector.UserStaffVoDataCollector;
import com.pulse.user.entrance.web.vo.UserStaffVo;
import com.pulse.user.entrance.web.vo.UserStaffVo.StaffBaseVo;
import com.pulse.user.entrance.web.vo.UserStaffVo.StaffUserWithStaffVo;
import com.pulse.user.manager.dto.UserStaffDto;
import com.pulse.user.service.UserBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到UserStaffVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "4c05662a-3fae-46a9-a42f-02304f6d5153|VO|CONVERTER")
public class UserStaffVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private UserBaseDtoService userBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private UserStaffVoDataAssembler userStaffVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private UserStaffVoDataCollector userStaffVoDataCollector;

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "02bca617-70f0-3040-8684-251ced89a9be")
    public UserStaffVo.StaffBaseVo convertToStaffBaseVo(StaffBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装UserStaffVo数据 */
    @AutoGenerated(locked = true, uuid = "0506cc93-a80e-30d0-95ce-e956635f6135")
    public UserStaffVo convertAndAssembleData(UserStaffDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffUserWithStaffDto转换成StaffUserWithStaffVo */
    @AutoGenerated(locked = true, uuid = "353bf4e8-79ef-3192-8721-05eb6a12bdbe")
    public UserStaffVo.StaffUserWithStaffVo convertToStaffUserWithStaffVo(
            StaffUserWithStaffDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffUserWithStaffVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装UserStaffVo列表数据 */
    @AutoGenerated(locked = true, uuid = "399dfba1-ab4c-3742-ad60-2b8fbcf1ff51")
    public List<UserStaffVo> convertAndAssembleDataList(List<UserStaffDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        UserStaffVoDataHolder dataHolder = new UserStaffVoDataHolder();
        dataHolder.setRootBaseDtoList(
                userBaseDtoService.getByIds(
                        dtoList.stream().map(UserStaffDto::getId).collect(Collectors.toList())));
        Map<String, UserStaffVo> voMap =
                convertToUserStaffVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        userStaffVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        userStaffVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把UserStaffDto转换成UserStaffVo */
    @AutoGenerated(locked = false, uuid = "4c05662a-3fae-46a9-a42f-02304f6d5153-converter-Map")
    public Map<UserStaffDto, UserStaffVo> convertToUserStaffVoMap(List<UserStaffDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffUserWithStaffDto, StaffUserWithStaffVo> staffUserMap =
                convertToStaffUserWithStaffVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(UserStaffDto::getStaffUser)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<UserStaffDto, UserStaffVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            UserStaffVo vo = new UserStaffVo();
                                            vo.setUserExpirationEnd(dto.getUserExpirationEnd());
                                            vo.setId(dto.getId());
                                            vo.setAddress(dto.getAddress());
                                            vo.setBirthDay(dto.getBirthDay());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setGender(dto.getGender());
                                            vo.setNames(dto.getNames());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setAge(dto.getAge());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setUserExpirationStart(dto.getUserExpirationStart());
                                            vo.setStaffUser(
                                                    dto.getStaffUser() == null
                                                            ? null
                                                            : staffUserMap.get(dto.getStaffUser()));
                                            vo.setUserName(dto.getUserName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把UserStaffDto转换成UserStaffVo */
    @AutoGenerated(locked = true, uuid = "4c05662a-3fae-46a9-a42f-02304f6d5153-converter-list")
    public List<UserStaffVo> convertToUserStaffVoList(List<UserStaffDto> dtoList) {
        return new ArrayList<>(convertToUserStaffVoMap(dtoList).values());
    }

    /** 把UserStaffDto转换成UserStaffVo */
    @AutoGenerated(locked = true, uuid = "5c142f2a-f59c-3344-ba25-3f5f8ba7dda7")
    public UserStaffVo convertToUserStaffVo(UserStaffDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToUserStaffVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = false, uuid = "5f934b89-f68a-462d-8df0-355a23635b37-converter-Map")
    public Map<StaffBaseDto, UserStaffVo.StaffBaseVo> convertToStaffBaseVoMap(
            List<StaffBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffBaseDto, StaffBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffBaseVo vo = new StaffBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setBirthDate(dto.getBirthDate());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setDescription(dto.getDescription());
                                            vo.setEmailAddress(dto.getEmailAddress());
                                            vo.setGender(dto.getGender());
                                            vo.setCertificateNumber(dto.getCertificateNumber());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setName(dto.getName());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setStaffNumber(dto.getStaffNumber());
                                            vo.setStatus(dto.getStatus());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setShortPhoneNumber(dto.getShortPhoneNumber());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdateBy(dto.getUpdateBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setCertificateTypeId(dto.getCertificateTypeId());
                                            vo.setStaffTypeId(dto.getStaffTypeId());
                                            vo.setExpertFlag(dto.getExpertFlag());
                                            vo.setProfessionalTitleId(dto.getProfessionalTitleId());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setAccountingOrganizationId(
                                                    dto.getAccountingOrganizationId());
                                            vo.setHrOrganizationId(dto.getHrOrganizationId());
                                            vo.setPositionId(dto.getPositionId());
                                            vo.setPromotionDate(dto.getPromotionDate());
                                            vo.setRegisterTypeList(dto.getRegisterTypeList());
                                            vo.setProvincePlatformCancelFlag(
                                                    dto.getProvincePlatformCancelFlag());
                                            vo.setCountrysideStartDate(
                                                    dto.getCountrysideStartDate());
                                            vo.setDoctorRemark(dto.getDoctorRemark());
                                            vo.setRegisterDoctorFlag(dto.getRegisterDoctorFlag());
                                            vo.setCountrysideEndDate(dto.getCountrysideEndDate());
                                            vo.setRegisterDoctorEnableFlag(
                                                    dto.getRegisterDoctorEnableFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "5f934b89-f68a-462d-8df0-355a23635b37-converter-list")
    public List<UserStaffVo.StaffBaseVo> convertToStaffBaseVoList(List<StaffBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffBaseVoMap(dtoList).values());
    }

    /** 把StaffUserWithStaffDto转换成StaffUserWithStaffVo */
    @AutoGenerated(locked = false, uuid = "b3cfedac-d9fe-428b-9efa-138c7cade769-converter-Map")
    public Map<StaffUserWithStaffDto, UserStaffVo.StaffUserWithStaffVo>
            convertToStaffUserWithStaffVoMap(List<StaffUserWithStaffDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffBaseDto, StaffBaseVo> staffMap =
                convertToStaffBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(StaffUserWithStaffDto::getStaff)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffUserWithStaffDto, StaffUserWithStaffVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffUserWithStaffVo vo = new StaffUserWithStaffVo();
                                            vo.setId(dto.getId());
                                            vo.setStaff(
                                                    dto.getStaff() == null
                                                            ? null
                                                            : staffMap.get(dto.getStaff()));
                                            vo.setUserId(dto.getUserId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffUserWithStaffDto转换成StaffUserWithStaffVo */
    @AutoGenerated(locked = true, uuid = "b3cfedac-d9fe-428b-9efa-138c7cade769-converter-list")
    public List<UserStaffVo.StaffUserWithStaffVo> convertToStaffUserWithStaffVoList(
            List<StaffUserWithStaffDto> dtoList) {
        return new ArrayList<>(convertToStaffUserWithStaffVoMap(dtoList).values());
    }
}
