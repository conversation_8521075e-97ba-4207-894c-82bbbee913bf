package com.pulse.user.service.converter;

import com.pulse.user.manager.dto.UserListAccountDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "e928bc6c-9309-3edf-8886-07deb5cc15af")
public class UserListAccountDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<UserListAccountDto> UserListAccountDtoConverter(
            List<UserListAccountDto> userListAccountDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return userListAccountDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
