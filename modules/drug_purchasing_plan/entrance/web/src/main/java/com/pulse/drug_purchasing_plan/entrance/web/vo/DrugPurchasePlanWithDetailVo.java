package com.pulse.drug_purchasing_plan.entrance.web.vo;

import com.pulse.drug_purchasing_plan.common.enums.PurchaseAuditTypeEnum;
import com.pulse.drug_purchasing_plan.common.enums.PurchasePlatformTypeEnum;
import com.pulse.drug_purchasing_plan.common.enums.PurchaseTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "d15c95d9-e89d-42e9-ab15-9f4f8d85ea66|VO|DEFINITION")
public class DrugPurchasePlanWithDetailVo {
    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "ef2c3be6-0281-4d06-992a-b5d2dab9a521")
    private Date auditDateTime;

    /** 审核者 */
    @AutoGenerated(locked = true, uuid = "d7e763aa-7d3d-4a2e-9811-a28ea296cbae")
    private String auditStaffId;

    /** 审核状态 已保存、已提交、已审核 、已作废 */
    @AutoGenerated(locked = true, uuid = "fd071f08-f735-4154-87af-39ea62667bc0")
    private PurchaseAuditTypeEnum auditStatus;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "f013daac-1400-4300-8f0e-ef0391456be2")
    private Date createdAt;

    /** 制单人 */
    @AutoGenerated(locked = true, uuid = "3841b27e-c3ae-4b03-8dba-d81f47cca374")
    private String createdBy;

    /** 配送地址 */
    @AutoGenerated(locked = true, uuid = "f36b8402-8f2a-4c51-88d7-094c79869b56")
    private String deliveryAddress;

    /** 配送科室 */
    @AutoGenerated(locked = true, uuid = "c3c4b975-3f34-4a84-8cbc-1703c8d149c6")
    private String deliveryDrugStorageId;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "911d728c-f3f9-44fb-a55d-f5d4edd69c49")
    private List<DrugPurchasePlanDetailVo> drugPurchasePlanDetailList;

    /** 中药类型 */
    @AutoGenerated(locked = true, uuid = "e3995efd-c67e-48b8-9d70-ca18da36c409")
    private String herbType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "e3d138bb-e39c-4b62-9872-fb366532988c")
    private String id;

    /** 执行状态 */
    @AutoGenerated(locked = true, uuid = "1fd3ae79-b324-4cad-a961-0bc103b2eb8e")
    private Boolean performStatus;

    /** 采购计划类型 药械平台采购、普通采购 、中草药采购 */
    @AutoGenerated(locked = true, uuid = "a8e81c7e-e065-4cdc-977e-1b0eb0b8018c")
    private PurchaseTypeEnum planType;

    /** 整个计划单的进货价 */
    @AutoGenerated(locked = true, uuid = "dbb9be73-0315-47cd-8b09-1419e84bd12c")
    private BigDecimal purchaseAccount;

    /** 采购计划单号 */
    @AutoGenerated(locked = true, uuid = "248f889d-ba18-4930-8d46-b0fc4b3d82ab")
    private String purchasePlanNumber;

    /** 药械平台采购类型 正常订单、急救药品临时订单 */
    @AutoGenerated(locked = true, uuid = "e14cf45c-152e-412f-a080-e7fa2b5631d0")
    private PurchasePlatformTypeEnum purchasePlatformType;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "6325fba6-4542-4c2c-a851-305803b1e377")
    private String remark;

    /** 零售金额 */
    @AutoGenerated(locked = true, uuid = "4f799d9e-f127-432d-b886-a02ba31e210d")
    private BigDecimal retailCost;

    /** 开始消耗日期 计算消耗量的时间范围 */
    @AutoGenerated(locked = true, uuid = "d9c1a981-77da-4c17-b375-173b0b0925ef")
    private Date startDateTime;

    /** 计划生成时间（提交时间） */
    @AutoGenerated(locked = true, uuid = "9db65d94-5453-411c-981a-b6deb64af9ca")
    private Date submitDateTime;
}
