package com.pulse.drug_purchasing_plan.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_purchasing_plan.manager.DrugPurchaseBaseDtoManager;
import com.pulse.drug_purchasing_plan.manager.dto.DrugPurchaseBaseDto;
import com.pulse.drug_purchasing_plan.service.converter.DrugPurchaseBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "26ec164c-28e6-411d-8bc4-e78d3de2ef3e|DTO|SERVICE")
public class DrugPurchaseBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchaseBaseDtoManager drugPurchaseBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPurchaseBaseDtoServiceConverter drugPurchaseBaseDtoServiceConverter;

    @PublicInterface(
            id = "718e1ec0-ae53-4062-a836-ef6b81aa6d7d",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656359")
    @AutoGenerated(locked = false, uuid = "23615c86-64a5-3416-a47d-d335ebab8014")
    public List<DrugPurchaseBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugPurchaseBaseDto> drugPurchaseBaseDtoList = drugPurchaseBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchaseBaseDtoServiceConverter.DrugPurchaseBaseDtoConverter(
                drugPurchaseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "c20bc681-9bcf-4064-b568-dffdca89d0c6",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741589469593")
    @AutoGenerated(locked = false, uuid = "26138138-6dbf-3c0b-9a87-1f7e62a69031")
    public List<DrugPurchaseBaseDto> getByCreateStaffIds(
            @Valid @NotNull(message = "制单人不能为空") List<String> createStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        createStaffId = new ArrayList<>(new HashSet<>(createStaffId));
        List<DrugPurchaseBaseDto> drugPurchaseBaseDtoList =
                drugPurchaseBaseDtoManager.getByCreateStaffIds(createStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchaseBaseDtoServiceConverter.DrugPurchaseBaseDtoConverter(
                drugPurchaseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "dadc6248-353f-40ce-82d2-38a7186e39d3",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656351")
    @AutoGenerated(locked = false, uuid = "4a8dc3a6-2be2-3795-acba-17c087fdcb81")
    public List<DrugPurchaseBaseDto> getByDeliveryDrugStorageIds(
            @Valid @NotNull(message = "配送科室不能为空") List<String> deliveryDrugStorageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        deliveryDrugStorageId = new ArrayList<>(new HashSet<>(deliveryDrugStorageId));
        List<DrugPurchaseBaseDto> drugPurchaseBaseDtoList =
                drugPurchaseBaseDtoManager.getByDeliveryDrugStorageIds(deliveryDrugStorageId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchaseBaseDtoServiceConverter.DrugPurchaseBaseDtoConverter(
                drugPurchaseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "2b349e68-69bb-47ca-9d8c-fd34a0d1a29e",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656353")
    @AutoGenerated(locked = false, uuid = "76ff97f1-a00a-3766-b1b6-c2e1ca388b3a")
    public DrugPurchaseBaseDto getByDrugPurchasePlanId(
            @NotNull(message = "采购计划id不能为空") String drugPurchasePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchaseBaseDto> ret = getByDrugPurchasePlanIds(Arrays.asList(drugPurchasePlanId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "b43690a0-c9cd-41cf-a466-19cb06e3c945",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741589469591")
    @AutoGenerated(locked = false, uuid = "7b0bf304-e9c6-30cb-902a-61c113677e38")
    public List<DrugPurchaseBaseDto> getByCreateStaffId(
            @NotNull(message = "制单人不能为空") String createStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByCreateStaffIds(Arrays.asList(createStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "93d1efe1-4bc3-4070-b82f-c18fdb4c2cf7",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656355")
    @AutoGenerated(locked = false, uuid = "89893a6c-89b4-3228-8e68-55a2e2850b77")
    public List<DrugPurchaseBaseDto> getByDrugPurchasePlanIds(
            @Valid @NotNull(message = "采购计划id不能为空") List<String> drugPurchasePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugPurchasePlanId = new ArrayList<>(new HashSet<>(drugPurchasePlanId));
        List<DrugPurchaseBaseDto> drugPurchaseBaseDtoList =
                drugPurchaseBaseDtoManager.getByDrugPurchasePlanIds(drugPurchasePlanId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchaseBaseDtoServiceConverter.DrugPurchaseBaseDtoConverter(
                drugPurchaseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "13a39474-af16-47c6-8c6d-875c101aedea",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656347")
    @AutoGenerated(locked = false, uuid = "c895dc76-f92a-3dac-8bc4-18623848f964")
    public List<DrugPurchaseBaseDto> getByStorageCodes(
            @Valid @NotNull(message = "库房编码不能为空") List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        storageCode = new ArrayList<>(new HashSet<>(storageCode));
        List<DrugPurchaseBaseDto> drugPurchaseBaseDtoList =
                drugPurchaseBaseDtoManager.getByStorageCodes(storageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPurchaseBaseDtoServiceConverter.DrugPurchaseBaseDtoConverter(
                drugPurchaseBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "6f29f488-c95e-454c-ad82-cd686333e96b",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656349")
    @AutoGenerated(locked = false, uuid = "ce99bd05-98c3-3014-afd0-acff255bc98d")
    public List<DrugPurchaseBaseDto> getByDeliveryDrugStorageId(
            @NotNull(message = "配送科室不能为空") String deliveryDrugStorageId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDeliveryDrugStorageIds(Arrays.asList(deliveryDrugStorageId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "*************-4ee9-a912-154ec9846b24",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656357")
    @AutoGenerated(locked = false, uuid = "d2e5a7e8-ef6d-38e8-a145-a0c103bbcf50")
    public DrugPurchaseBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPurchaseBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "81d4f5c1-f22e-4bb7-9ab6-9e9a2478bdf1",
            module = "drug_purchasing_plan",
            moduleId = "18cd3a9c-0889-41c0-9a41-3ab4466f0f1f",
            pubRpc = true,
            version = "1741585656345")
    @AutoGenerated(locked = false, uuid = "db129da6-d80b-362a-bf7d-bee41768a4f5")
    public List<DrugPurchaseBaseDto> getByStorageCode(
            @NotNull(message = "库房编码不能为空") String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStorageCodes(Arrays.asList(storageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
