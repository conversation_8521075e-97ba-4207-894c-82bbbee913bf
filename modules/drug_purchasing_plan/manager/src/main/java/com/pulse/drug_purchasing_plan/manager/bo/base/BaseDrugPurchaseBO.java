package com.pulse.drug_purchasing_plan.manager.bo.base;

import com.pulse.drug_purchasing_plan.common.enums.PurchasePlatformTypeEnum;
import com.pulse.drug_purchasing_plan.common.enums.PurchaseTypeEnum;
import com.pulse.drug_purchasing_plan.manager.bo.DrugPurchaseBO;
import com.pulse.drug_purchasing_plan.manager.bo.DrugPurchaseDetailBO;
import com.pulse.drug_purchasing_plan.persist.dos.DrugPurchase;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_purchase")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "45bad2cd-8174-3bc7-8561-b2fa45649944")
public abstract class BaseDrugPurchaseBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 记账标志 记账时机 */
    @Column(name = "accountant_flag")
    @AutoGenerated(locked = true, uuid = "44270fef-9809-3e22-b8c2-ccd247853dc5")
    private Boolean accountantFlag;

    /** 已付总金额 */
    @Column(name = "charge")
    @AutoGenerated(locked = true, uuid = "bbc62f18-deec-35af-bae3-0ff38b0b2cf1")
    private BigDecimal charge;

    /** 应付总金额 */
    @Column(name = "cost")
    @AutoGenerated(locked = true, uuid = "3662e17c-dac8-3659-a306-11de0451a1a8")
    private BigDecimal cost;

    /** 制单人 */
    @Column(name = "create_staff_id")
    @AutoGenerated(locked = true, uuid = "5198b702-dc47-35be-b459-************")
    private String createStaffId;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "c43647bf-4036-39f4-9171-3ec75f52025c")
    private Date createdAt;

    /** 配送地址 */
    @Column(name = "delivery_address")
    @AutoGenerated(locked = true, uuid = "594d83fb-e08e-38a6-b970-da5cabcd87db")
    private String deliveryAddress;

    /** 配送科室 */
    @Column(name = "delivery_drug_storage_id")
    @AutoGenerated(locked = true, uuid = "dd42feaa-da78-3ad5-a905-6313618b5539")
    private String deliveryDrugStorageId;

    @JoinColumn(name = "drug_purchase_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugPurchaseDetailBO> drugPurchaseDetailBOSet = new HashSet<>();

    /** 采购计划id */
    @Column(name = "drug_purchase_plan_id")
    @AutoGenerated(locked = true, uuid = "067a7b27-9dd4-3f33-9262-1605f5b37968")
    private String drugPurchasePlanId;

    /** 购货药品种数 采购单包含的药品种数 */
    @Column(name = "drug_type_count")
    @AutoGenerated(locked = true, uuid = "8b7a9582-2ab9-378a-9588-ae50eaf7623a")
    private Long drugTypeCount;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "1cc8aa79-c69f-3592-af11-0e04df03b50e")
    @Id
    private String id;

    /** 入库标志 */
    @Column(name = "import_flag")
    @AutoGenerated(locked = true, uuid = "d2734bf9-926f-3d81-8970-9542fce7ebf6")
    private Boolean importFlag;

    /** 作废标志 */
    @Column(name = "invalid_flag")
    @AutoGenerated(locked = true, uuid = "133c7dbd-6e6b-40da-bfe0-8225e90f8913")
    private Boolean invalidFlag;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "78cde863-3548-4321-b259-9d3cb427eaab")
    @Version
    private Long lockVersion;

    /** 执行时间 */
    @Column(name = "perform_date_time")
    @AutoGenerated(locked = true, uuid = "f0e8390f-b490-397d-9289-f5cc80d62a94")
    private Date performDateTime;

    /** 生成采购单日期 */
    @Column(name = "purchase_date_time")
    @AutoGenerated(locked = true, uuid = "98c68ea7-7896-37a7-94db-97129b0eaf11")
    private Date purchaseDateTime;

    /** 采购单描述 */
    @Column(name = "purchase_description")
    @AutoGenerated(locked = true, uuid = "42674d7d-0c20-3f22-bcec-83f5eeed7847")
    private String purchaseDescription;

    /** 采购单号 */
    @Column(name = "purchase_number")
    @AutoGenerated(locked = true, uuid = "1e99ea7a-ab82-3e5a-be37-62f2156ae76b")
    private String purchaseNumber;

    /** 药械平台订单ID */
    @Column(name = "purchase_platform_order_id")
    @AutoGenerated(locked = true, uuid = "7f51db98-fea1-327f-961b-77252e931ded")
    private String purchasePlatformOrderId;

    /** 供货平台类型 正常订单、急救药品临时订单 */
    @Column(name = "purchase_platform_type")
    @AutoGenerated(locked = true, uuid = "6164dcae-df8d-3ba9-a9a9-1701cc14a490")
    @Enumerated(EnumType.STRING)
    private PurchasePlatformTypeEnum purchasePlatformType;

    /** 采购类型 药械平台采购、普通采购 、中草药采购 */
    @Column(name = "purchase_type")
    @AutoGenerated(locked = true, uuid = "dbc74b63-d65f-355c-a03e-29a68ef67736")
    @Enumerated(EnumType.STRING)
    private PurchaseTypeEnum purchaseType;

    /** 备注 */
    @Column(name = "remark")
    @AutoGenerated(locked = true, uuid = "ffe31f1c-ea2f-33ca-a669-798f917a3e3f")
    private String remark;

    /** 库房编码 */
    @Column(name = "storage_code")
    @AutoGenerated(locked = true, uuid = "e40f1778-8e92-3080-a85d-bf2ac9f839d0")
    private String storageCode;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "77b40083-9ecb-362b-9468-ff49a8e18da5")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public DrugPurchase convertToDrugPurchase() {
        DrugPurchase entity = new DrugPurchase();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "storageCode",
                "drugPurchasePlanId",
                "purchaseNumber",
                "purchasePlatformOrderId",
                "purchaseDescription",
                "purchasePlatformType",
                "purchaseDateTime",
                "performDateTime",
                "createStaffId",
                "cost",
                "charge",
                "remark",
                "accountantFlag",
                "deliveryDrugStorageId",
                "deliveryAddress",
                "purchaseType",
                "importFlag",
                "drugTypeCount",
                "invalidFlag",
                "createdAt",
                "updatedAt",
                "lockVersion");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public Boolean getAccountantFlag() {
        return this.accountantFlag;
    }

    @AutoGenerated(locked = true)
    public static DrugPurchaseBO getByDrugPurchasePlanId(String drugPurchasePlanId) {
        Session session = TransactionalSessionFactory.getSession();
        DrugPurchaseBO drugPurchase =
                (DrugPurchaseBO)
                        session.createQuery(
                                        "from DrugPurchaseBO where "
                                                + "drugPurchasePlanId =: drugPurchasePlanId ")
                                .setParameter("drugPurchasePlanId", drugPurchasePlanId)
                                .uniqueResult();
        return drugPurchase;
    }

    @AutoGenerated(locked = true)
    public static DrugPurchaseBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugPurchaseBO drugPurchase =
                (DrugPurchaseBO)
                        session.createQuery("from DrugPurchaseBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugPurchase;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getCharge() {
        return this.charge;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getCost() {
        return this.cost;
    }

    @AutoGenerated(locked = true)
    public String getCreateStaffId() {
        return this.createStaffId;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getDeliveryAddress() {
        return this.deliveryAddress;
    }

    @AutoGenerated(locked = true)
    public String getDeliveryDrugStorageId() {
        return this.deliveryDrugStorageId;
    }

    @AutoGenerated(locked = true)
    public Set<DrugPurchaseDetailBO> getDrugPurchaseDetailBOSet() {
        return this.drugPurchaseDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public String getDrugPurchasePlanId() {
        return this.drugPurchasePlanId;
    }

    @AutoGenerated(locked = true)
    public Long getDrugTypeCount() {
        return this.drugTypeCount;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public Boolean getImportFlag() {
        return this.importFlag;
    }

    @AutoGenerated(locked = true)
    public Boolean getInvalidFlag() {
        return this.invalidFlag;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public Date getPerformDateTime() {
        return this.performDateTime;
    }

    @AutoGenerated(locked = true)
    public Date getPurchaseDateTime() {
        return this.purchaseDateTime;
    }

    @AutoGenerated(locked = true)
    public String getPurchaseDescription() {
        return this.purchaseDescription;
    }

    @AutoGenerated(locked = true)
    public String getPurchaseNumber() {
        return this.purchaseNumber;
    }

    @AutoGenerated(locked = true)
    public String getPurchasePlatformOrderId() {
        return this.purchasePlatformOrderId;
    }

    @AutoGenerated(locked = true)
    public PurchasePlatformTypeEnum getPurchasePlatformType() {
        return this.purchasePlatformType;
    }

    @AutoGenerated(locked = true)
    public PurchaseTypeEnum getPurchaseType() {
        return this.purchaseType;
    }

    @AutoGenerated(locked = true)
    public String getRemark() {
        return this.remark;
    }

    @AutoGenerated(locked = true)
    public String getStorageCode() {
        return this.storageCode;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setAccountantFlag(Boolean accountantFlag) {
        this.accountantFlag = accountantFlag;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setCharge(BigDecimal charge) {
        this.charge = charge;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setCost(BigDecimal cost) {
        this.cost = cost;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setCreateStaffId(String createStaffId) {
        this.createStaffId = createStaffId;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setDeliveryDrugStorageId(String deliveryDrugStorageId) {
        this.deliveryDrugStorageId = deliveryDrugStorageId;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugPurchaseDetailBOSet(Set<DrugPurchaseDetailBO> drugPurchaseDetailBOSet) {
        this.drugPurchaseDetailBOSet = drugPurchaseDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setDrugPurchasePlanId(String drugPurchasePlanId) {
        this.drugPurchasePlanId = drugPurchasePlanId;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setDrugTypeCount(Long drugTypeCount) {
        this.drugTypeCount = drugTypeCount;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setId(String id) {
        this.id = id;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setImportFlag(Boolean importFlag) {
        this.importFlag = importFlag;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setInvalidFlag(Boolean invalidFlag) {
        this.invalidFlag = invalidFlag;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setPerformDateTime(Date performDateTime) {
        this.performDateTime = performDateTime;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setPurchaseDateTime(Date purchaseDateTime) {
        this.purchaseDateTime = purchaseDateTime;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setPurchaseDescription(String purchaseDescription) {
        this.purchaseDescription = purchaseDescription;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setPurchaseNumber(String purchaseNumber) {
        this.purchaseNumber = purchaseNumber;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setPurchasePlatformOrderId(String purchasePlatformOrderId) {
        this.purchasePlatformOrderId = purchasePlatformOrderId;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setPurchasePlatformType(PurchasePlatformTypeEnum purchasePlatformType) {
        this.purchasePlatformType = purchasePlatformType;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setPurchaseType(PurchaseTypeEnum purchaseType) {
        this.purchaseType = purchaseType;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setRemark(String remark) {
        this.remark = remark;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setStorageCode(String storageCode) {
        this.storageCode = storageCode;
        return (DrugPurchaseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPurchaseBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugPurchaseBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
