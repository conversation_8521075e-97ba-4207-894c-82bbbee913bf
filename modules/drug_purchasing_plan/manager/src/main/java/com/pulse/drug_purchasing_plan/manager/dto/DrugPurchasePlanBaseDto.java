package com.pulse.drug_purchasing_plan.manager.dto;

import com.pulse.drug_purchasing_plan.common.enums.PurchaseAuditTypeEnum;
import com.pulse.drug_purchasing_plan.common.enums.PurchasePlatformTypeEnum;
import com.pulse.drug_purchasing_plan.common.enums.PurchaseTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "613f14e8-5161-4cd6-930c-44b53407cea3|DTO|DEFINITION")
public class DrugPurchasePlanBaseDto {
    /** 审核时间 */
    @AutoGenerated(locked = true, uuid = "04e85db2-5490-4b31-8f98-1a741e31ea79")
    private Date auditDateTime;

    /** 审核者 */
    @AutoGenerated(locked = true, uuid = "2cf461c6-dfba-4489-8f3f-afb8cbc2a628")
    private String auditStaffId;

    /** 审核状态 已保存、已提交、已审核 、已作废 */
    @AutoGenerated(locked = true, uuid = "f3e47298-1fcf-4786-bb57-56bf0c2ccb9f")
    private PurchaseAuditTypeEnum auditStatus;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "31b587ff-26cc-4606-a48a-4f529f52707a")
    private Date createdAt;

    /** 制单人 */
    @AutoGenerated(locked = true, uuid = "efff69cf-85ef-4888-86d5-3047e43a2067")
    private String createdBy;

    /** 配送地址 */
    @AutoGenerated(locked = true, uuid = "7ddce51a-a419-4611-8f05-957bb456c710")
    private String deliveryAddress;

    /** 配送科室 */
    @AutoGenerated(locked = true, uuid = "c0fd3b3f-54e4-47c6-a9c1-2a6d915cd79b")
    private String deliveryDrugStorageId;

    /** 结束消耗日期 */
    @AutoGenerated(locked = true, uuid = "04aee51d-229a-435d-90d7-67b865cd36f7")
    private Date endDateTime;

    /** 中药类型 */
    @AutoGenerated(locked = true, uuid = "57152e46-f764-4b59-87e3-58aae7e0f173")
    private String herbType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "b790eb1d-b65c-403c-af0c-4f27736d97d6")
    private String id;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "a9ce235a-5dc7-46c4-9122-327ae8b04ee6")
    private Long lockVersion;

    /** 执行状态 */
    @AutoGenerated(locked = true, uuid = "1abce868-9628-4089-b1da-4027810aa976")
    private Boolean performStatus;

    /** 采购计划类型 药械平台采购、普通采购 、中草药采购 */
    @AutoGenerated(locked = true, uuid = "e61ca92b-2d89-47f1-bc21-1b97c6c9f9eb")
    private PurchaseTypeEnum planType;

    /** 整个计划单的进货价 */
    @AutoGenerated(locked = true, uuid = "80f7f428-caa0-46be-aa0c-a0e6ac9dbbbb")
    private BigDecimal purchaseAccount;

    /** 采购计划单号 */
    @AutoGenerated(locked = true, uuid = "5173a1a5-cf8b-4797-a70c-658c258d969c")
    private String purchasePlanNumber;

    /** 药械平台采购类型 正常订单、急救药品临时订单 */
    @AutoGenerated(locked = true, uuid = "2c312f3e-7b56-4bfb-a9c3-9ea728385d05")
    private PurchasePlatformTypeEnum purchasePlatformType;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "d6cb64b9-4e24-42a9-a04f-712add6b75b8")
    private String remark;

    /** 零售金额 */
    @AutoGenerated(locked = true, uuid = "b307a15a-9fa4-4595-82bc-77fb5140cc55")
    private BigDecimal retailCost;

    /** 开始消耗日期 计算消耗量的时间范围 */
    @AutoGenerated(locked = true, uuid = "a47e0248-ae19-479f-a330-4b06da9779a4")
    private Date startDateTime;

    /** 库房编码 */
    @AutoGenerated(locked = true, uuid = "3c675879-5e1f-4ac1-adc3-604f34f2ebfd")
    private String storageCode;

    /** 计划生成时间（提交时间） */
    @AutoGenerated(locked = true, uuid = "fb836e3b-a1b4-440c-b053-e25e82634073")
    private Date submitDateTime;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "5784573b-6069-42e2-86a6-9c02f6617e35")
    private Date updatedAt;

    /** 更新者ID */
    @AutoGenerated(locked = true, uuid = "cf63aaf8-792c-4138-baaa-e69da992bcf8")
    private String updatedBy;
}
