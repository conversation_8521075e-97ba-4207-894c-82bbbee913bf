package com.pulse.drug_circulation.manager.bo.base;

import com.pulse.drug_circulation.manager.bo.DrugDispenseDeliveryBO;
import com.pulse.drug_circulation.persist.dos.DrugDispenseDelivery;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_dispense_delivery")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "08a8c8cf-cb66-3c8e-bb6c-a089f94baf68")
public abstract class BaseDrugDispenseDeliveryBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "aadecc13-cb76-308a-9263-17ee36f9640e")
    private Date createdAt;

    /** 送达人 */
    @Column(name = "delivered_staff_id")
    @AutoGenerated(locked = true, uuid = "034a3c7f-2029-3efc-8081-db219deb701e")
    private String deliveredStaffId;

    /** 送达时间 */
    @Column(name = "delivered_time")
    @AutoGenerated(locked = true, uuid = "23694b9c-6c20-36f6-a16e-d07f1ed43e0d")
    private Date deliveredTime;

    /** 出门核对人 */
    @Column(name = "delivery_audit_staff_id")
    @AutoGenerated(locked = true, uuid = "a4711109-e2ad-3b42-86ae-78163b109adf")
    private String deliveryAuditStaffId;

    /** 出门核对时间 */
    @Column(name = "delivery_audit_time")
    @AutoGenerated(locked = true, uuid = "36d89e97-4d82-3caf-aa40-0c9c3523942b")
    private Date deliveryAuditTime;

    /** 配送数量 */
    @Column(name = "delivery_count")
    @AutoGenerated(locked = true, uuid = "3e4ae9bf-3492-3cd4-ba95-33edb055bbba")
    private Long deliveryCount;

    /** 发往病区/科室 */
    @Column(name = "delivery_department_code")
    @AutoGenerated(locked = true, uuid = "aacf4ea2-d02f-309d-b901-87fc057880f0")
    private String deliveryDepartmentCode;

    /** 发往地址 */
    @Column(name = "delivery_location")
    @AutoGenerated(locked = true, uuid = "1dc5b1ef-4fa2-327c-b833-7e6ce0bb2d0c")
    private String deliveryLocation;

    /** 配送单号 */
    @Column(name = "delivery_number")
    @AutoGenerated(locked = true, uuid = "3ed5b72b-6659-3dfb-98da-9d1984a714c1")
    private String deliveryNumber;

    /** 配送人 */
    @Column(name = "delivery_staff_id")
    @AutoGenerated(locked = true, uuid = "0291f2f3-33cb-3da3-8f1f-8def8e3df5e6")
    private String deliveryStaffId;

    /** 配送时间 */
    @Column(name = "delivery_time")
    @AutoGenerated(locked = true, uuid = "ae04779f-094e-32a5-a1ca-a0dd9d79ac71")
    private Date deliveryTime;

    /** 发药药房编码 */
    @Column(name = "dispense_storage_code")
    @AutoGenerated(locked = true, uuid = "15266ee7-3e8c-350c-9c6b-22a15bc55956")
    private String dispenseStorageCode;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "2ac82bfa-790f-3fc6-b497-cc461896d51a")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "594daf49-eaaf-43b7-9de5-53226c83f494")
    @Version
    private Long lockVersion;

    /** 打印人 */
    @Column(name = "print_staff_id")
    @AutoGenerated(locked = true, uuid = "8665e11a-3b67-39a0-93fb-948dcf845966")
    private String printStaffId;

    /** 打印时间 */
    @Column(name = "print_time")
    @AutoGenerated(locked = true, uuid = "ed7a3311-e5fa-3e2b-a097-64a3f4e2ecab")
    private Date printTime;

    /** 确认数量 */
    @Column(name = "receive_count")
    @AutoGenerated(locked = true, uuid = "b0858906-e6b3-38e7-8767-0b8a1a26e206")
    private Long receiveCount;

    /** 接收人 */
    @Column(name = "receive_staff_id")
    @AutoGenerated(locked = true, uuid = "66f3f846-115f-3290-ac14-ec671ff3ff2b")
    private String receiveStaffId;

    /** 接收时间 */
    @Column(name = "receive_time")
    @AutoGenerated(locked = true, uuid = "97c9e745-3f2b-34d2-a34c-f86f0223e53c")
    private Date receiveTime;

    /** 拒收标识 */
    @Column(name = "reject_flag")
    @AutoGenerated(locked = true, uuid = "5630b002-55ba-3aab-ac13-5a1ddd13b2cd")
    private Boolean rejectFlag;

    /** 拒收原因 */
    @Column(name = "reject_reason")
    @AutoGenerated(locked = true, uuid = "6e2b26b1-77b6-32fe-a324-24af04a0ef19")
    private String rejectReason;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "0c1fe788-0b81-34bb-887c-260662e72706")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public DrugDispenseDelivery convertToDrugDispenseDelivery() {
        DrugDispenseDelivery entity = new DrugDispenseDelivery();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "dispenseStorageCode",
                "deliveryNumber",
                "deliveryDepartmentCode",
                "deliveryLocation",
                "deliveryStaffId",
                "deliveryTime",
                "deliveryCount",
                "deliveryAuditStaffId",
                "deliveryAuditTime",
                "printStaffId",
                "printTime",
                "deliveredStaffId",
                "deliveredTime",
                "receiveStaffId",
                "receiveTime",
                "receiveCount",
                "rejectFlag",
                "rejectReason",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static DrugDispenseDeliveryBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugDispenseDeliveryBO drugDispenseDelivery =
                (DrugDispenseDeliveryBO)
                        session.createQuery("from DrugDispenseDeliveryBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugDispenseDelivery;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getDeliveredStaffId() {
        return this.deliveredStaffId;
    }

    @AutoGenerated(locked = true)
    public Date getDeliveredTime() {
        return this.deliveredTime;
    }

    @AutoGenerated(locked = true)
    public String getDeliveryAuditStaffId() {
        return this.deliveryAuditStaffId;
    }

    @AutoGenerated(locked = true)
    public Date getDeliveryAuditTime() {
        return this.deliveryAuditTime;
    }

    @AutoGenerated(locked = true)
    public Long getDeliveryCount() {
        return this.deliveryCount;
    }

    @AutoGenerated(locked = true)
    public String getDeliveryDepartmentCode() {
        return this.deliveryDepartmentCode;
    }

    @AutoGenerated(locked = true)
    public String getDeliveryLocation() {
        return this.deliveryLocation;
    }

    @AutoGenerated(locked = true)
    public String getDeliveryNumber() {
        return this.deliveryNumber;
    }

    @AutoGenerated(locked = true)
    public String getDeliveryStaffId() {
        return this.deliveryStaffId;
    }

    @AutoGenerated(locked = true)
    public Date getDeliveryTime() {
        return this.deliveryTime;
    }

    @AutoGenerated(locked = true)
    public String getDispenseStorageCode() {
        return this.dispenseStorageCode;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getPrintStaffId() {
        return this.printStaffId;
    }

    @AutoGenerated(locked = true)
    public Date getPrintTime() {
        return this.printTime;
    }

    @AutoGenerated(locked = true)
    public Long getReceiveCount() {
        return this.receiveCount;
    }

    @AutoGenerated(locked = true)
    public String getReceiveStaffId() {
        return this.receiveStaffId;
    }

    @AutoGenerated(locked = true)
    public Date getReceiveTime() {
        return this.receiveTime;
    }

    @AutoGenerated(locked = true)
    public Boolean getRejectFlag() {
        return this.rejectFlag;
    }

    @AutoGenerated(locked = true)
    public String getRejectReason() {
        return this.rejectReason;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveredStaffId(String deliveredStaffId) {
        this.deliveredStaffId = deliveredStaffId;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveredTime(Date deliveredTime) {
        this.deliveredTime = deliveredTime;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryAuditStaffId(String deliveryAuditStaffId) {
        this.deliveryAuditStaffId = deliveryAuditStaffId;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryAuditTime(Date deliveryAuditTime) {
        this.deliveryAuditTime = deliveryAuditTime;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryCount(Long deliveryCount) {
        this.deliveryCount = deliveryCount;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryDepartmentCode(String deliveryDepartmentCode) {
        this.deliveryDepartmentCode = deliveryDepartmentCode;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryLocation(String deliveryLocation) {
        this.deliveryLocation = deliveryLocation;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryNumber(String deliveryNumber) {
        this.deliveryNumber = deliveryNumber;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryStaffId(String deliveryStaffId) {
        this.deliveryStaffId = deliveryStaffId;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setDispenseStorageCode(String dispenseStorageCode) {
        this.dispenseStorageCode = dispenseStorageCode;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setId(String id) {
        this.id = id;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setPrintStaffId(String printStaffId) {
        this.printStaffId = printStaffId;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setPrintTime(Date printTime) {
        this.printTime = printTime;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setReceiveCount(Long receiveCount) {
        this.receiveCount = receiveCount;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setReceiveStaffId(String receiveStaffId) {
        this.receiveStaffId = receiveStaffId;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setRejectFlag(Boolean rejectFlag) {
        this.rejectFlag = rejectFlag;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
        return (DrugDispenseDeliveryBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugDispenseDeliveryBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugDispenseDeliveryBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
