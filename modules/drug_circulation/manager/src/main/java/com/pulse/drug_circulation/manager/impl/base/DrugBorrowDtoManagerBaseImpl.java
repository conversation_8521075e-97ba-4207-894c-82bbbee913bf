package com.pulse.drug_circulation.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.drug_circulation.manager.DrugBorrowBaseDtoManager;
import com.pulse.drug_circulation.manager.DrugBorrowDetailDtoManager;
import com.pulse.drug_circulation.manager.DrugBorrowDtoManager;
import com.pulse.drug_circulation.manager.converter.DrugBorrowBaseDtoConverter;
import com.pulse.drug_circulation.manager.converter.DrugBorrowDtoConverter;
import com.pulse.drug_circulation.manager.dto.DrugBorrowBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDto;
import com.pulse.drug_circulation.manager.facade.organization.StaffBaseDtoServiceInDrugCirculationRpcAdapter;
import com.pulse.drug_circulation.persist.dos.DrugBorrow;
import com.pulse.drug_circulation.persist.mapper.DrugBorrowDao;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "6ba5c6f3-ff2b-414a-a3b2-f7402bfc1208|DTO|BASE_MANAGER_IMPL")
public abstract class DrugBorrowDtoManagerBaseImpl implements DrugBorrowDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowBaseDtoConverter drugBorrowBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowBaseDtoManager drugBorrowBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowDao drugBorrowDao;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowDetailDtoManager drugBorrowDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowDtoConverter drugBorrowDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private StaffBaseDtoServiceInDrugCirculationRpcAdapter
            staffBaseDtoServiceInDrugCirculationRpcAdapter;

    @AutoGenerated(locked = true, uuid = "119fb434-1d7c-3721-b789-d1a3c24346a9")
    @Override
    public List<DrugBorrowDto> getByStorageCode(String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDto> drugBorrowDtoList = getByStorageCodes(Arrays.asList(storageCode));
        return drugBorrowDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "20e4004a-b687-3727-95cf-8b10b7041b9f")
    @Override
    public List<DrugBorrowDto> getByBorrowId(String borrowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDto> drugBorrowDtoList = getByBorrowIds(Arrays.asList(borrowId));
        return drugBorrowDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3de1d4e6-f5d4-3aa8-96f7-e06361152992")
    @Override
    public List<DrugBorrowDto> getByApplyStaffId(String applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDto> drugBorrowDtoList = getByApplyStaffIds(Arrays.asList(applyStaffId));
        return drugBorrowDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "49d9b660-de89-36cf-8372-af4b8928cac4")
    @Override
    public List<DrugBorrowDto> getByBorrowStaffId(String borrowStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDto> drugBorrowDtoList = getByBorrowStaffIds(Arrays.asList(borrowStaffId));
        return drugBorrowDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "673caae7-c494-3e25-9778-c365eb3172c5")
    @Override
    public List<DrugBorrowDto> getByStorageCodes(List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(storageCode)) {
            return Collections.emptyList();
        }

        List<DrugBorrow> drugBorrowList = drugBorrowDao.getByStorageCodes(storageCode);
        if (CollectionUtil.isEmpty(drugBorrowList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowToDrugBorrowDto(drugBorrowList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9bdc6904-d5e6-31ea-b249-36f6f099db93")
    @Override
    public List<DrugBorrowDto> getByBorrowStaffIds(List<String> borrowStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(borrowStaffId)) {
            return Collections.emptyList();
        }

        List<DrugBorrow> drugBorrowList = drugBorrowDao.getByBorrowStaffIds(borrowStaffId);
        if (CollectionUtil.isEmpty(drugBorrowList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowToDrugBorrowDto(drugBorrowList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9c1eb361-8f9e-39f2-b2e6-9bf1b8323dda")
    @Override
    public List<DrugBorrowDto> getByApplyStaffIds(List<String> applyStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applyStaffId)) {
            return Collections.emptyList();
        }

        List<DrugBorrow> drugBorrowList = drugBorrowDao.getByApplyStaffIds(applyStaffId);
        if (CollectionUtil.isEmpty(drugBorrowList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowToDrugBorrowDto(drugBorrowList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a7bbf926-5324-33f7-aaba-a627e510e8b1")
    @Override
    public DrugBorrowDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugBorrowDto drugBorrowDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugBorrowDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ac9497d6-5adb-3c1c-9d02-20a5b255bdc3")
    @Override
    public List<DrugBorrowDto> getByBorrowIds(List<String> borrowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(borrowId)) {
            return Collections.emptyList();
        }

        List<DrugBorrow> drugBorrowList = drugBorrowDao.getByBorrowIds(borrowId);
        if (CollectionUtil.isEmpty(drugBorrowList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowToDrugBorrowDto(drugBorrowList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c6767595-2eac-3ee4-9e7a-dbaa9d81ffa2")
    public List<DrugBorrowDto> doConvertFromDrugBorrowToDrugBorrowDto(
            List<DrugBorrow> drugBorrowList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugBorrowList)) {
            return Collections.emptyList();
        }

        Map<String, String> applyStaffIdMap =
                drugBorrowList.stream()
                        .filter(i -> i.getApplyStaffId() != null)
                        .collect(Collectors.toMap(DrugBorrow::getId, DrugBorrow::getApplyStaffId));
        List<StaffBaseDto> applyStaffIdStaffBaseDtoList =
                staffBaseDtoServiceInDrugCirculationRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(applyStaffIdMap.values())));
        Map<String, StaffBaseDto> applyStaffIdStaffBaseDtoMapRaw =
                applyStaffIdStaffBaseDtoList.stream()
                        .collect(Collectors.toMap(StaffBaseDto::getId, i -> i));
        Map<String, StaffBaseDto> applyStaffIdStaffBaseDtoMap =
                applyStaffIdMap.entrySet().stream()
                        .filter(i -> applyStaffIdStaffBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> applyStaffIdStaffBaseDtoMapRaw.get(i.getValue())));
        Map<String, String> borrowStaffIdMap =
                drugBorrowList.stream()
                        .filter(i -> i.getBorrowStaffId() != null)
                        .collect(Collectors.toMap(DrugBorrow::getId, DrugBorrow::getBorrowStaffId));
        List<StaffBaseDto> borrowStaffIdStaffBaseDtoList =
                staffBaseDtoServiceInDrugCirculationRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(borrowStaffIdMap.values())));
        Map<String, StaffBaseDto> borrowStaffIdStaffBaseDtoMapRaw =
                borrowStaffIdStaffBaseDtoList.stream()
                        .collect(Collectors.toMap(StaffBaseDto::getId, i -> i));
        Map<String, StaffBaseDto> borrowStaffIdStaffBaseDtoMap =
                borrowStaffIdMap.entrySet().stream()
                        .filter(i -> borrowStaffIdStaffBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> borrowStaffIdStaffBaseDtoMapRaw.get(i.getValue())));

        List<DrugBorrowDetailDto> drugBorrowDetailDtoList =
                drugBorrowDetailDtoManager.getByDrugBorrowIds(
                        drugBorrowList.stream().map(i -> i.getId()).collect(Collectors.toList()));
        Map<String, List<DrugBorrowDetailDto>> idDrugBorrowDetailDtoListMap =
                drugBorrowDetailDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getDrugBorrowId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<DrugBorrowBaseDto> baseDtoList =
                drugBorrowBaseDtoConverter.convertFromDrugBorrowToDrugBorrowBaseDto(drugBorrowList);
        Map<String, DrugBorrowDto> dtoMap =
                drugBorrowDtoConverter
                        .convertFromDrugBorrowBaseDtoToDrugBorrowDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugBorrowDto::getId, Function.identity(), (o1, o2) -> o1));

        List<DrugBorrowDto> drugBorrowDtoList = new ArrayList<>();
        for (DrugBorrow i : drugBorrowList) {
            DrugBorrowDto drugBorrowDto = dtoMap.get(i.getId());
            if (drugBorrowDto == null) {
                continue;
            }

            if (null != i.getApplyStaffId()) {
                drugBorrowDto.setApplyStaff(
                        applyStaffIdStaffBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getBorrowStaffId()) {
                drugBorrowDto.setBorrowStaff(
                        borrowStaffIdStaffBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getId()) {
                drugBorrowDto.setDrugBorrowDetailList(
                        idDrugBorrowDetailDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugBorrowDtoList.add(drugBorrowDto);
        }
        return drugBorrowDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "e9bce952-6b1d-373d-8679-290944003ade")
    @Override
    public List<DrugBorrowDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugBorrow> drugBorrowList = drugBorrowDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugBorrowList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugBorrow> drugBorrowMap =
                drugBorrowList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugBorrowList =
                id.stream()
                        .map(i -> drugBorrowMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugBorrowToDrugBorrowDto(drugBorrowList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
