package com.pulse.drug_circulation.service.bto;

import com.pulse.drug_circulation.common.enums.ApplyStatusEnum;
import com.pulse.drug_circulation.common.enums.ApplyTypeEnum;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> DrugApply
 *
 * <p><b>[操作]</b> CREATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "53cf1e83-2600-4e43-bd86-2cadb1202630|BTO|DEFINITION")
public class CreateDrugApplyBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 请领科室编码 */
    @AutoGenerated(locked = true, uuid = "d49d8751-fc94-4f31-97b0-3cea3ba8cb34")
    private String applyDepartmentCode;

    /** 申请单描述 */
    @AutoGenerated(locked = true, uuid = "8efcac95-b72e-436e-b96e-9421fd18188e")
    private String applyDescription;

    /** 申请单号 */
    @AutoGenerated(locked = true, uuid = "8a5cec54-8921-47c3-8d51-9b8347822601")
    private String applyNumber;

    /** 制单人id */
    @AutoGenerated(locked = true, uuid = "5bf19bcf-f4ca-4f48-8505-ec7804c23fd4")
    private String applyStaffId;

    /** 申请状态 已保存、已提交、已审核、已执行 (库房发放后为已执行)、已作废、已拒绝、部分发药 */
    @AutoGenerated(locked = true, uuid = "b2317735-27e8-4e51-955d-2242015a43fd")
    private ApplyStatusEnum applyStatus;

    /** 请领单类型 库存请领单、科室请领单 */
    @AutoGenerated(locked = true, uuid = "1641cbd5-64c1-451c-b082-b95a569de76a")
    private ApplyTypeEnum applyType;

    /** 审核人id */
    @AutoGenerated(locked = true, uuid = "c8cf7a95-4fd8-45dd-82fb-d82e39714af9")
    private String auditStaffId;

    /** 消耗结束日期 */
    @AutoGenerated(locked = true, uuid = "10a4759c-b214-420d-93e2-18f594d51ece")
    private Date consumeEndDate;

    /** 消耗开始日期 */
    @AutoGenerated(locked = true, uuid = "ca8669dd-f6bd-45db-8ab2-ebdca878536a")
    private Date consumeStartDate;

    @Valid
    @AutoGenerated(locked = true, uuid = "45c8ae5a-7971-475b-90c1-1056ec0179ab")
    private List<CreateDrugApplyBto.DrugApplyDetailBto> drugApplyDetailBtoList;

    /** 受理日期 */
    @AutoGenerated(locked = true, uuid = "033aa007-5389-4b66-9892-8c7d38ab10dc")
    private Date executeDateTime;

    /** 受理人 */
    @AutoGenerated(locked = true, uuid = "eb5a72a0-bbee-4822-a394-b50690f0f194")
    private String executeStaffId;

    /** 发放库房编码 */
    @AutoGenerated(locked = true, uuid = "8d43550a-0ff6-46e8-86fd-58c144423985")
    private String provideStorageCode;

    /** 退药标识 浙二退药给上级库房走的是退药请领模式 */
    @AutoGenerated(locked = true, uuid = "9d039051-fa49-4b9e-b9a2-b95109329b0d")
    private Boolean refundFlag;

    /** 拒绝原因 */
    @AutoGenerated(locked = true, uuid = "0baefbc9-a4b6-4378-b297-fc2b150c5b5c")
    private String rejectReason;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "a0b65cc8-0c8b-4fde-bd0d-fc0920a1828a")
    private String remark;

    /** 提交时间 */
    @AutoGenerated(locked = true, uuid = "15eb77f4-6c88-464a-8671-c39b7a94d7a9")
    private Date submitDateTime;

    @AutoGenerated(locked = true)
    public void setApplyDepartmentCode(String applyDepartmentCode) {
        this.__$validPropertySet.add("applyDepartmentCode");
        this.applyDepartmentCode = applyDepartmentCode;
    }

    @AutoGenerated(locked = true)
    public void setApplyDescription(String applyDescription) {
        this.__$validPropertySet.add("applyDescription");
        this.applyDescription = applyDescription;
    }

    @AutoGenerated(locked = true)
    public void setApplyNumber(String applyNumber) {
        this.__$validPropertySet.add("applyNumber");
        this.applyNumber = applyNumber;
    }

    @AutoGenerated(locked = true)
    public void setApplyStaffId(String applyStaffId) {
        this.__$validPropertySet.add("applyStaffId");
        this.applyStaffId = applyStaffId;
    }

    @AutoGenerated(locked = true)
    public void setApplyStatus(ApplyStatusEnum applyStatus) {
        this.__$validPropertySet.add("applyStatus");
        this.applyStatus = applyStatus;
    }

    @AutoGenerated(locked = true)
    public void setApplyType(ApplyTypeEnum applyType) {
        this.__$validPropertySet.add("applyType");
        this.applyType = applyType;
    }

    @AutoGenerated(locked = true)
    public void setAuditStaffId(String auditStaffId) {
        this.__$validPropertySet.add("auditStaffId");
        this.auditStaffId = auditStaffId;
    }

    @AutoGenerated(locked = true)
    public void setConsumeEndDate(Date consumeEndDate) {
        this.__$validPropertySet.add("consumeEndDate");
        this.consumeEndDate = consumeEndDate;
    }

    @AutoGenerated(locked = true)
    public void setConsumeStartDate(Date consumeStartDate) {
        this.__$validPropertySet.add("consumeStartDate");
        this.consumeStartDate = consumeStartDate;
    }

    @AutoGenerated(locked = true)
    public void setDrugApplyDetailBtoList(
            List<CreateDrugApplyBto.DrugApplyDetailBto> drugApplyDetailBtoList) {
        this.__$validPropertySet.add("drugApplyDetailBtoList");
        this.drugApplyDetailBtoList = drugApplyDetailBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExecuteDateTime(Date executeDateTime) {
        this.__$validPropertySet.add("executeDateTime");
        this.executeDateTime = executeDateTime;
    }

    @AutoGenerated(locked = true)
    public void setExecuteStaffId(String executeStaffId) {
        this.__$validPropertySet.add("executeStaffId");
        this.executeStaffId = executeStaffId;
    }

    @AutoGenerated(locked = true)
    public void setProvideStorageCode(String provideStorageCode) {
        this.__$validPropertySet.add("provideStorageCode");
        this.provideStorageCode = provideStorageCode;
    }

    @AutoGenerated(locked = true)
    public void setRefundFlag(Boolean refundFlag) {
        this.__$validPropertySet.add("refundFlag");
        this.refundFlag = refundFlag;
    }

    @AutoGenerated(locked = true)
    public void setRejectReason(String rejectReason) {
        this.__$validPropertySet.add("rejectReason");
        this.rejectReason = rejectReason;
    }

    @AutoGenerated(locked = true)
    public void setRemark(String remark) {
        this.__$validPropertySet.add("remark");
        this.remark = remark;
    }

    @AutoGenerated(locked = true)
    public void setSubmitDateTime(Date submitDateTime) {
        this.__$validPropertySet.add("submitDateTime");
        this.submitDateTime = submitDateTime;
    }

    /**
     * <b>[源自]</b> DrugApplyDetail
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class DrugApplyDetailBto {
        /** 排序号 */
        @AutoGenerated(locked = true, uuid = "76dc3e60-e43c-4934-a5bd-b78f955c7285")
        private Long sortNumber;

        /** 药品产地规格id */
        @AutoGenerated(locked = true, uuid = "e8145c3f-afc4-4a97-91b7-d4a9f0f0a137")
        private String drugOriginSpecificationId;

        /** 申请的批号 */
        @AutoGenerated(locked = true, uuid = "002ca77c-2094-417a-9498-bf0f2d64c1be")
        private String batchNumber;

        /** 有效期 */
        @AutoGenerated(locked = true, uuid = "8ccf6623-ef58-440c-bd68-3f299a57e0c4")
        private Date expirationDate;

        /** 请领数量 */
        @AutoGenerated(locked = true, uuid = "89881f80-6cf7-46fd-b05c-608342a8e6be")
        private Long applyAmount;

        /** 请领数量参考值 */
        @AutoGenerated(locked = true, uuid = "0ceb3ba4-9af4-4a8c-b359-7d6d1e078749")
        private Long referenceAmount;

        /** 急用标志 */
        @AutoGenerated(locked = true, uuid = "30dc8fec-f598-4513-8465-cda57853f44d")
        private Boolean emergencyFlag;

        /** 库存数量 提交时的库存数量 */
        @AutoGenerated(locked = true, uuid = "c5c7a6ad-0479-4c91-aaa7-1910128c54dc")
        private Long stockAmount;

        /** 药品产地编码 冗余存 */
        @AutoGenerated(locked = true, uuid = "3f7314d7-cb23-4c2c-b79b-e21ffa5e6be7")
        private String drugOriginCode;

        /** 状态 */
        @AutoGenerated(locked = true, uuid = "9ff32d68-1fca-47cc-99cf-d8f671d3a20d")
        private ApplyStatusEnum status;

        /** 药物产地名称 */
        @AutoGenerated(locked = true, uuid = "8de5d928-f3bd-49ac-aaf2-71c7e188ed7c")
        private String drugOriginName;

        /** 单位 */
        @AutoGenerated(locked = true, uuid = "11ae2306-a773-45b5-b2cf-f9372baebb31")
        private String unit;

        /** 药品名称 */
        @AutoGenerated(locked = true, uuid = "ff192610-4bf2-4385-a112-984838535f2a")
        private String drugName;

        /** 实发数量 */
        @AutoGenerated(locked = true, uuid = "66b2f087-2b84-4634-b3cf-46ab8c62598e")
        private Long actualAmount;

        /** 规格 */
        @AutoGenerated(locked = true, uuid = "04967f19-f6ce-42da-b4fb-837049f55632")
        private String specification;

        /** 规格名称 */
        @AutoGenerated(locked = true, uuid = "0ab1ae04-87ea-4038-965d-4c505e34c006")
        private String specificationName;

        /** 药品类型 */
        @AutoGenerated(locked = true, uuid = "b3966d7a-7f41-4734-a6da-21429c0fb807")
        private DrugTypeEnum drugType;

        /** 请领价格 */
        @AutoGenerated(locked = true, uuid = "565965c4-0880-4dee-81ea-73656f2116e5")
        private BigDecimal applyPrice;

        /** 零售价 */
        @AutoGenerated(locked = true, uuid = "b4a559a4-a8fe-45bb-9b06-b9e8990cc1fa")
        private BigDecimal retailPrice;

        /** 请领人 */
        @AutoGenerated(locked = true, uuid = "2670c026-d746-432a-8010-808340ae117b")
        private String applyStaff;

        /** 受理人 */
        @AutoGenerated(locked = true, uuid = "567356ff-f950-4a7b-b749-9b0d0243edde")
        private String receiverStaff;

        /** 接收日期 */
        @AutoGenerated(locked = true, uuid = "426a8762-5dbb-4ce5-a9b5-bef958a8eb41")
        private Date receiverDate;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setSortNumber(Long sortNumber) {
            this.__$validPropertySet.add("sortNumber");
            this.sortNumber = sortNumber;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginSpecificationId(String drugOriginSpecificationId) {
            this.__$validPropertySet.add("drugOriginSpecificationId");
            this.drugOriginSpecificationId = drugOriginSpecificationId;
        }

        @AutoGenerated(locked = true)
        public void setBatchNumber(String batchNumber) {
            this.__$validPropertySet.add("batchNumber");
            this.batchNumber = batchNumber;
        }

        @AutoGenerated(locked = true)
        public void setExpirationDate(Date expirationDate) {
            this.__$validPropertySet.add("expirationDate");
            this.expirationDate = expirationDate;
        }

        @AutoGenerated(locked = true)
        public void setApplyAmount(Long applyAmount) {
            this.__$validPropertySet.add("applyAmount");
            this.applyAmount = applyAmount;
        }

        @AutoGenerated(locked = true)
        public void setReferenceAmount(Long referenceAmount) {
            this.__$validPropertySet.add("referenceAmount");
            this.referenceAmount = referenceAmount;
        }

        @AutoGenerated(locked = true)
        public void setEmergencyFlag(Boolean emergencyFlag) {
            this.__$validPropertySet.add("emergencyFlag");
            this.emergencyFlag = emergencyFlag;
        }

        @AutoGenerated(locked = true)
        public void setStockAmount(Long stockAmount) {
            this.__$validPropertySet.add("stockAmount");
            this.stockAmount = stockAmount;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginCode(String drugOriginCode) {
            this.__$validPropertySet.add("drugOriginCode");
            this.drugOriginCode = drugOriginCode;
        }

        @AutoGenerated(locked = true)
        public void setStatus(ApplyStatusEnum status) {
            this.__$validPropertySet.add("status");
            this.status = status;
        }

        @AutoGenerated(locked = true)
        public void setDrugOriginName(String drugOriginName) {
            this.__$validPropertySet.add("drugOriginName");
            this.drugOriginName = drugOriginName;
        }

        @AutoGenerated(locked = true)
        public void setUnit(String unit) {
            this.__$validPropertySet.add("unit");
            this.unit = unit;
        }

        @AutoGenerated(locked = true)
        public void setDrugName(String drugName) {
            this.__$validPropertySet.add("drugName");
            this.drugName = drugName;
        }

        @AutoGenerated(locked = true)
        public void setActualAmount(Long actualAmount) {
            this.__$validPropertySet.add("actualAmount");
            this.actualAmount = actualAmount;
        }

        @AutoGenerated(locked = true)
        public void setSpecification(String specification) {
            this.__$validPropertySet.add("specification");
            this.specification = specification;
        }

        @AutoGenerated(locked = true)
        public void setSpecificationName(String specificationName) {
            this.__$validPropertySet.add("specificationName");
            this.specificationName = specificationName;
        }

        @AutoGenerated(locked = true)
        public void setDrugType(DrugTypeEnum drugType) {
            this.__$validPropertySet.add("drugType");
            this.drugType = drugType;
        }

        @AutoGenerated(locked = true)
        public void setApplyPrice(BigDecimal applyPrice) {
            this.__$validPropertySet.add("applyPrice");
            this.applyPrice = applyPrice;
        }

        @AutoGenerated(locked = true)
        public void setRetailPrice(BigDecimal retailPrice) {
            this.__$validPropertySet.add("retailPrice");
            this.retailPrice = retailPrice;
        }

        @AutoGenerated(locked = true)
        public void setApplyStaff(String applyStaff) {
            this.__$validPropertySet.add("applyStaff");
            this.applyStaff = applyStaff;
        }

        @AutoGenerated(locked = true)
        public void setReceiverStaff(String receiverStaff) {
            this.__$validPropertySet.add("receiverStaff");
            this.receiverStaff = receiverStaff;
        }

        @AutoGenerated(locked = true)
        public void setReceiverDate(Date receiverDate) {
            this.__$validPropertySet.add("receiverDate");
            this.receiverDate = receiverDate;
        }
    }
}
