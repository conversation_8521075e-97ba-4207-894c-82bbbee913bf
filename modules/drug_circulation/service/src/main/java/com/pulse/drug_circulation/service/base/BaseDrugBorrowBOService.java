package com.pulse.drug_circulation.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_circulation.manager.bo.*;
import com.pulse.drug_circulation.manager.bo.DrugBorrowBO;
import com.pulse.drug_circulation.persist.dos.DrugBorrow;
import com.pulse.drug_circulation.persist.dos.DrugBorrowDetail;
import com.pulse.drug_circulation.service.base.BaseDrugBorrowBOService.CreateBorrowAndDetailBoResult;
import com.pulse.drug_circulation.service.base.BaseDrugBorrowBOService.CreateBorrowDetailBoResult;
import com.pulse.drug_circulation.service.base.BaseDrugBorrowBOService.DeleteBorrowBoResult;
import com.pulse.drug_circulation.service.base.BaseDrugBorrowBOService.DeleteBorrowDetailBoResult;
import com.pulse.drug_circulation.service.base.BaseDrugBorrowBOService.MergeBorrowBoResult;
import com.pulse.drug_circulation.service.base.BaseDrugBorrowBOService.MergeBorrowDetailBoResult;
import com.pulse.drug_circulation.service.base.BaseDrugBorrowBOService.MergeDrugBorrowAndDetailBoResult;
import com.pulse.drug_circulation.service.bto.CreateBorrowAndDetailBto;
import com.pulse.drug_circulation.service.bto.CreateBorrowDetailBto;
import com.pulse.drug_circulation.service.bto.DeleteBorrowBto;
import com.pulse.drug_circulation.service.bto.DeleteBorrowDetailBto;
import com.pulse.drug_circulation.service.bto.MergeBorrowBto;
import com.pulse.drug_circulation.service.bto.MergeBorrowDetailBto;
import com.pulse.drug_circulation.service.bto.MergeDrugBorrowAndDetailBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "545e8a96-011d-3417-a8e5-ab13ee91357b")
public class BaseDrugBorrowBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 新建借还药带明细 */
    @AutoGenerated(locked = true)
    protected CreateBorrowAndDetailBoResult createBorrowAndDetailBase(
            CreateBorrowAndDetailBto createBorrowAndDetailBto) {
        if (createBorrowAndDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateBorrowAndDetailBoResult boResult = new CreateBorrowAndDetailBoResult();
        DrugBorrowBO drugBorrowBO =
                createCreateBorrowAndDetailOnDuplicateThrowEx(boResult, createBorrowAndDetailBto);
        if (drugBorrowBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "drugBorrowDetailBtoList")) {
                createDrugBorrowDetailBto(boResult, createBorrowAndDetailBto, drugBorrowBO);
            }
        }
        boResult.setRootBo(drugBorrowBO);
        return boResult;
    }

    /** 创建借还药明细 */
    @AutoGenerated(locked = true)
    protected CreateBorrowDetailBoResult createBorrowDetailBase(
            CreateBorrowDetailBto createBorrowDetailBto) {
        if (createBorrowDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateBorrowDetailBoResult boResult = new CreateBorrowDetailBoResult();
        DrugBorrowBO drugBorrowBO =
                updateCreateBorrowDetailOnMissThrowEx(boResult, createBorrowDetailBto);
        if (drugBorrowBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createBorrowDetailBto, "__$validPropertySet"),
                    "drugBorrowDetailBtoList")) {
                createDrugBorrowDetailBto(boResult, createBorrowDetailBto, drugBorrowBO);
            }
        }
        boResult.setRootBo(drugBorrowBO);
        return boResult;
    }

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private DrugBorrowBO createCreateBorrowAndDetailOnDuplicateThrowEx(
            CreateBorrowAndDetailBoResult boResult,
            CreateBorrowAndDetailBto createBorrowAndDetailBto) {
        DrugBorrowBO drugBorrowBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createBorrowAndDetailBto.getId() == null);
        if (!allNull && !found) {
            drugBorrowBO = DrugBorrowBO.getById(createBorrowAndDetailBto.getId());
            if (drugBorrowBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugBorrowBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", drugBorrowBO.getId(), "drug_borrow");
                throw new IgnoredException(400, "药物借还记录单已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "drug_borrow",
                        drugBorrowBO.getId(),
                        "drug_borrow");
                throw new IgnoredException(400, "药物借还记录单已存在");
            }
        } else {
            drugBorrowBO = new DrugBorrowBO();
            if (pkExist) {
                drugBorrowBO.setId(createBorrowAndDetailBto.getId());
            } else {
                drugBorrowBO.setId(String.valueOf(this.idGenerator.allocateId("drug_borrow")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowType")) {
                drugBorrowBO.setBorrowType(createBorrowAndDetailBto.getBorrowType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowStaffId")) {
                drugBorrowBO.setBorrowStaffId(createBorrowAndDetailBto.getBorrowStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "planReturnDateTime")) {
                drugBorrowBO.setPlanReturnDateTime(
                        createBorrowAndDetailBto.getPlanReturnDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "returnStatus")) {
                drugBorrowBO.setReturnStatus(createBorrowAndDetailBto.getReturnStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "submitDateTime")) {
                drugBorrowBO.setSubmitDateTime(createBorrowAndDetailBto.getSubmitDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowId")) {
                drugBorrowBO.setBorrowId(createBorrowAndDetailBto.getBorrowId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "cost")) {
                drugBorrowBO.setCost(createBorrowAndDetailBto.getCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "returnNumber")) {
                drugBorrowBO.setReturnNumber(createBorrowAndDetailBto.getReturnNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "storageCode")) {
                drugBorrowBO.setStorageCode(createBorrowAndDetailBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "inventoryNumber")) {
                drugBorrowBO.setInventoryNumber(createBorrowAndDetailBto.getInventoryNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowDepartment")) {
                drugBorrowBO.setBorrowDepartment(createBorrowAndDetailBto.getBorrowDepartment());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createBorrowAndDetailBto);
            addedBto.setBo(drugBorrowBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugBorrowBO;
    }

    /** 创建对象DrugBorrowDetailBto */
    @AutoGenerated(locked = true)
    private void createDrugBorrowDetailBto(
            BaseDrugBorrowBOService.CreateBorrowAndDetailBoResult boResult,
            CreateBorrowAndDetailBto createBorrowAndDetailBto,
            DrugBorrowBO drugBorrowBO) {
        if (CollectionUtil.isNotEmpty(createBorrowAndDetailBto.getDrugBorrowDetailBtoList())) {
            for (CreateBorrowAndDetailBto.DrugBorrowDetailBto item :
                    createBorrowAndDetailBto.getDrugBorrowDetailBtoList()) {
                DrugBorrowDetailBO subBo = new DrugBorrowDetailBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "sortNumber")) {
                    subBo.setSortNumber(item.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "drugOriginSpecificationId")) {
                    subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "drugName")) {
                    subBo.setDrugName(item.getDrugName());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "amount")) {
                    subBo.setAmount(item.getAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "batchInventoryId")) {
                    subBo.setBatchInventoryId(item.getBatchInventoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "drugBorrowDetailId")) {
                    subBo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "returnedAmount")) {
                    subBo.setReturnedAmount(item.getReturnedAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "returnStatus")) {
                    subBo.setReturnStatus(item.getReturnStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "price")) {
                    subBo.setPrice(item.getPrice());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "cost")) {
                    subBo.setCost(item.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "purchasePrice")) {
                    subBo.setPurchasePrice(item.getPurchasePrice());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "stock")) {
                    subBo.setStock(item.getStock());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "unit")) {
                    subBo.setUnit(item.getUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "batchNumber")) {
                    subBo.setBatchNumber(item.getBatchNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "expirationDate")) {
                    subBo.setExpirationDate(item.getExpirationDate());
                }
                subBo.setDrugBorrowBO(drugBorrowBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("drug_borrow_detail")));
                drugBorrowBO.getDrugBorrowDetailBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象DrugBorrowDetailBto */
    @AutoGenerated(locked = true)
    private void createDrugBorrowDetailBto(
            CreateBorrowDetailBoResult boResult,
            CreateBorrowDetailBto createBorrowDetailBto,
            DrugBorrowBO drugBorrowBO) {
        if (CollectionUtil.isNotEmpty(createBorrowDetailBto.getDrugBorrowDetailBtoList())) {
            for (CreateBorrowDetailBto.DrugBorrowDetailBto item :
                    createBorrowDetailBto.getDrugBorrowDetailBtoList()) {
                DrugBorrowDetailBO subBo = new DrugBorrowDetailBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "sortNumber")) {
                    subBo.setSortNumber(item.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "drugOriginSpecificationId")) {
                    subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "drugName")) {
                    subBo.setDrugName(item.getDrugName());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "amount")) {
                    subBo.setAmount(item.getAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "batchInventoryId")) {
                    subBo.setBatchInventoryId(item.getBatchInventoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "drugBorrowDetailId")) {
                    subBo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "returnedAmount")) {
                    subBo.setReturnedAmount(item.getReturnedAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "returnStatus")) {
                    subBo.setReturnStatus(item.getReturnStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "price")) {
                    subBo.setPrice(item.getPrice());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "cost")) {
                    subBo.setCost(item.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "purchasePrice")) {
                    subBo.setPurchasePrice(item.getPurchasePrice());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "stock")) {
                    subBo.setStock(item.getStock());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "unit")) {
                    subBo.setUnit(item.getUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "batchNumber")) {
                    subBo.setBatchNumber(item.getBatchNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "expirationDate")) {
                    subBo.setExpirationDate(item.getExpirationDate());
                }
                subBo.setDrugBorrowBO(drugBorrowBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("drug_borrow_detail")));
                drugBorrowBO.getDrugBorrowDetailBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:DrugBorrowDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugBorrowDetailBtoOnDuplicateUpdate(
            MergeBorrowDetailBoResult boResult,
            MergeBorrowDetailBto mergeBorrowDetailBto,
            DrugBorrowBO drugBorrowBO) {
        if (CollectionUtil.isNotEmpty(mergeBorrowDetailBto.getDrugBorrowDetailBtoList())) {
            for (MergeBorrowDetailBto.DrugBorrowDetailBto item :
                    mergeBorrowDetailBto.getDrugBorrowDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugBorrowDetailBO> any =
                        drugBorrowBO.getDrugBorrowDetailBOSet().stream()
                                .filter(
                                        drugBorrowDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugBorrowDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugBorrowDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugBorrowDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugName")) {
                            bo.setDrugName(item.getDrugName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugBorrowDetailId")) {
                            bo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnedAmount")) {
                            bo.setReturnedAmount(item.getReturnedAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnStatus")) {
                            bo.setReturnStatus(item.getReturnStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "cost")) {
                            bo.setCost(item.getCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stock")) {
                            bo.setStock(item.getStock());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                    } else {
                        DrugBorrowDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugBorrowDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugName")) {
                            bo.setDrugName(item.getDrugName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugBorrowDetailId")) {
                            bo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnedAmount")) {
                            bo.setReturnedAmount(item.getReturnedAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnStatus")) {
                            bo.setReturnStatus(item.getReturnStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "cost")) {
                            bo.setCost(item.getCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stock")) {
                            bo.setStock(item.getStock());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                    }
                } else {
                    DrugBorrowDetailBO subBo = new DrugBorrowDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugName")) {
                        subBo.setDrugName(item.getDrugName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amount")) {
                        subBo.setAmount(item.getAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchInventoryId")) {
                        subBo.setBatchInventoryId(item.getBatchInventoryId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugBorrowDetailId")) {
                        subBo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "returnedAmount")) {
                        subBo.setReturnedAmount(item.getReturnedAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "returnStatus")) {
                        subBo.setReturnStatus(item.getReturnStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "price")) {
                        subBo.setPrice(item.getPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "cost")) {
                        subBo.setCost(item.getCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stock")) {
                        subBo.setStock(item.getStock());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "unit")) {
                        subBo.setUnit(item.getUnit());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    subBo.setDrugBorrowBO(drugBorrowBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("drug_borrow_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugBorrowBO.getDrugBorrowDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:DrugBorrowDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createDrugBorrowDetailBtoOnDuplicateUpdate(
            BaseDrugBorrowBOService.MergeDrugBorrowAndDetailBoResult boResult,
            MergeDrugBorrowAndDetailBto mergeDrugBorrowAndDetailBto,
            DrugBorrowBO drugBorrowBO) {
        if (CollectionUtil.isNotEmpty(mergeDrugBorrowAndDetailBto.getDrugBorrowDetailBtoList())) {
            for (MergeDrugBorrowAndDetailBto.DrugBorrowDetailBto item :
                    mergeDrugBorrowAndDetailBto.getDrugBorrowDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<DrugBorrowDetailBO> any =
                        drugBorrowBO.getDrugBorrowDetailBOSet().stream()
                                .filter(
                                        drugBorrowDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugBorrowDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        DrugBorrowDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugBorrowDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugName")) {
                            bo.setDrugName(item.getDrugName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugBorrowDetailId")) {
                            bo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnedAmount")) {
                            bo.setReturnedAmount(item.getReturnedAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnStatus")) {
                            bo.setReturnStatus(item.getReturnStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "cost")) {
                            bo.setCost(item.getCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stock")) {
                            bo.setStock(item.getStock());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                    } else {
                        DrugBorrowDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToDrugBorrowDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugOriginSpecificationId")) {
                            bo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugName")) {
                            bo.setDrugName(item.getDrugName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "amount")) {
                            bo.setAmount(item.getAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchInventoryId")) {
                            bo.setBatchInventoryId(item.getBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugBorrowDetailId")) {
                            bo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnedAmount")) {
                            bo.setReturnedAmount(item.getReturnedAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "returnStatus")) {
                            bo.setReturnStatus(item.getReturnStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "price")) {
                            bo.setPrice(item.getPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "cost")) {
                            bo.setCost(item.getCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchasePrice")) {
                            bo.setPurchasePrice(item.getPurchasePrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stock")) {
                            bo.setStock(item.getStock());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "unit")) {
                            bo.setUnit(item.getUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                    }
                } else {
                    DrugBorrowDetailBO subBo = new DrugBorrowDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugOriginSpecificationId")) {
                        subBo.setDrugOriginSpecificationId(item.getDrugOriginSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugName")) {
                        subBo.setDrugName(item.getDrugName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "amount")) {
                        subBo.setAmount(item.getAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchInventoryId")) {
                        subBo.setBatchInventoryId(item.getBatchInventoryId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugBorrowDetailId")) {
                        subBo.setDrugBorrowDetailId(item.getDrugBorrowDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "returnedAmount")) {
                        subBo.setReturnedAmount(item.getReturnedAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "returnStatus")) {
                        subBo.setReturnStatus(item.getReturnStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "price")) {
                        subBo.setPrice(item.getPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "cost")) {
                        subBo.setCost(item.getCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchasePrice")) {
                        subBo.setPurchasePrice(item.getPurchasePrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stock")) {
                        subBo.setStock(item.getStock());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "unit")) {
                        subBo.setUnit(item.getUnit());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    subBo.setDrugBorrowBO(drugBorrowBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("drug_borrow_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    drugBorrowBO.getDrugBorrowDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugBorrowBO createMergeBorrowOnDuplicateUpdate(
            BaseDrugBorrowBOService.MergeBorrowBoResult boResult, MergeBorrowBto mergeBorrowBto) {
        DrugBorrowBO drugBorrowBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeBorrowBto.getId() == null);
        if (!allNull && !found) {
            drugBorrowBO = DrugBorrowBO.getById(mergeBorrowBto.getId());
            if (drugBorrowBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugBorrowBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
                updatedBto.setBto(mergeBorrowBto);
                updatedBto.setBo(drugBorrowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugBorrowBO.setApplyStaffId(mergeBorrowBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowType")) {
                    drugBorrowBO.setBorrowType(mergeBorrowBto.getBorrowType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowStaffId")) {
                    drugBorrowBO.setBorrowStaffId(mergeBorrowBto.getBorrowStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "planReturnDateTime")) {
                    drugBorrowBO.setPlanReturnDateTime(mergeBorrowBto.getPlanReturnDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "returnStatus")) {
                    drugBorrowBO.setReturnStatus(mergeBorrowBto.getReturnStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "submitDateTime")) {
                    drugBorrowBO.setSubmitDateTime(mergeBorrowBto.getSubmitDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowId")) {
                    drugBorrowBO.setBorrowId(mergeBorrowBto.getBorrowId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "cost")) {
                    drugBorrowBO.setCost(mergeBorrowBto.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "returnNumber")) {
                    drugBorrowBO.setReturnNumber(mergeBorrowBto.getReturnNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugBorrowBO.setStorageCode(mergeBorrowBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "inventoryNumber")) {
                    drugBorrowBO.setInventoryNumber(mergeBorrowBto.getInventoryNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowDepartment")) {
                    drugBorrowBO.setBorrowDepartment(mergeBorrowBto.getBorrowDepartment());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
                updatedBto.setBto(mergeBorrowBto);
                updatedBto.setBo(drugBorrowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugBorrowBO.setApplyStaffId(mergeBorrowBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowType")) {
                    drugBorrowBO.setBorrowType(mergeBorrowBto.getBorrowType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowStaffId")) {
                    drugBorrowBO.setBorrowStaffId(mergeBorrowBto.getBorrowStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "planReturnDateTime")) {
                    drugBorrowBO.setPlanReturnDateTime(mergeBorrowBto.getPlanReturnDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "returnStatus")) {
                    drugBorrowBO.setReturnStatus(mergeBorrowBto.getReturnStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "submitDateTime")) {
                    drugBorrowBO.setSubmitDateTime(mergeBorrowBto.getSubmitDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowId")) {
                    drugBorrowBO.setBorrowId(mergeBorrowBto.getBorrowId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "cost")) {
                    drugBorrowBO.setCost(mergeBorrowBto.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "returnNumber")) {
                    drugBorrowBO.setReturnNumber(mergeBorrowBto.getReturnNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugBorrowBO.setStorageCode(mergeBorrowBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "inventoryNumber")) {
                    drugBorrowBO.setInventoryNumber(mergeBorrowBto.getInventoryNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                        "borrowDepartment")) {
                    drugBorrowBO.setBorrowDepartment(mergeBorrowBto.getBorrowDepartment());
                }
            }
        } else {
            drugBorrowBO = new DrugBorrowBO();
            if (pkExist) {
                drugBorrowBO.setId(mergeBorrowBto.getId());
            } else {
                drugBorrowBO.setId(String.valueOf(this.idGenerator.allocateId("drug_borrow")));
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "applyStaffId")) {
                drugBorrowBO.setApplyStaffId(mergeBorrowBto.getApplyStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "borrowType")) {
                drugBorrowBO.setBorrowType(mergeBorrowBto.getBorrowType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "borrowStaffId")) {
                drugBorrowBO.setBorrowStaffId(mergeBorrowBto.getBorrowStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "planReturnDateTime")) {
                drugBorrowBO.setPlanReturnDateTime(mergeBorrowBto.getPlanReturnDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "returnStatus")) {
                drugBorrowBO.setReturnStatus(mergeBorrowBto.getReturnStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "submitDateTime")) {
                drugBorrowBO.setSubmitDateTime(mergeBorrowBto.getSubmitDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "borrowId")) {
                drugBorrowBO.setBorrowId(mergeBorrowBto.getBorrowId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "cost")) {
                drugBorrowBO.setCost(mergeBorrowBto.getCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "returnNumber")) {
                drugBorrowBO.setReturnNumber(mergeBorrowBto.getReturnNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "storageCode")) {
                drugBorrowBO.setStorageCode(mergeBorrowBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "inventoryNumber")) {
                drugBorrowBO.setInventoryNumber(mergeBorrowBto.getInventoryNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeBorrowBto, "__$validPropertySet"),
                    "borrowDepartment")) {
                drugBorrowBO.setBorrowDepartment(mergeBorrowBto.getBorrowDepartment());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeBorrowBto);
            addedBto.setBo(drugBorrowBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugBorrowBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugBorrowBO createMergeDrugBorrowAndDetailOnDuplicateUpdate(
            MergeDrugBorrowAndDetailBoResult boResult,
            MergeDrugBorrowAndDetailBto mergeDrugBorrowAndDetailBto) {
        DrugBorrowBO drugBorrowBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeDrugBorrowAndDetailBto.getId() == null);
        if (!allNull && !found) {
            drugBorrowBO = DrugBorrowBO.getById(mergeDrugBorrowAndDetailBto.getId());
            if (drugBorrowBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugBorrowBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
                updatedBto.setBto(mergeDrugBorrowAndDetailBto);
                updatedBto.setBo(drugBorrowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugBorrowBO.setApplyStaffId(mergeDrugBorrowAndDetailBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowType")) {
                    drugBorrowBO.setBorrowType(mergeDrugBorrowAndDetailBto.getBorrowType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowStaffId")) {
                    drugBorrowBO.setBorrowStaffId(mergeDrugBorrowAndDetailBto.getBorrowStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "planReturnDateTime")) {
                    drugBorrowBO.setPlanReturnDateTime(
                            mergeDrugBorrowAndDetailBto.getPlanReturnDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "returnStatus")) {
                    drugBorrowBO.setReturnStatus(mergeDrugBorrowAndDetailBto.getReturnStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "submitDateTime")) {
                    drugBorrowBO.setSubmitDateTime(mergeDrugBorrowAndDetailBto.getSubmitDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowId")) {
                    drugBorrowBO.setBorrowId(mergeDrugBorrowAndDetailBto.getBorrowId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "cost")) {
                    drugBorrowBO.setCost(mergeDrugBorrowAndDetailBto.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "returnNumber")) {
                    drugBorrowBO.setReturnNumber(mergeDrugBorrowAndDetailBto.getReturnNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugBorrowBO.setStorageCode(mergeDrugBorrowAndDetailBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "inventoryNumber")) {
                    drugBorrowBO.setInventoryNumber(
                            mergeDrugBorrowAndDetailBto.getInventoryNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowDepartment")) {
                    drugBorrowBO.setBorrowDepartment(
                            mergeDrugBorrowAndDetailBto.getBorrowDepartment());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
                updatedBto.setBto(mergeDrugBorrowAndDetailBto);
                updatedBto.setBo(drugBorrowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "applyStaffId")) {
                    drugBorrowBO.setApplyStaffId(mergeDrugBorrowAndDetailBto.getApplyStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowType")) {
                    drugBorrowBO.setBorrowType(mergeDrugBorrowAndDetailBto.getBorrowType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowStaffId")) {
                    drugBorrowBO.setBorrowStaffId(mergeDrugBorrowAndDetailBto.getBorrowStaffId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "planReturnDateTime")) {
                    drugBorrowBO.setPlanReturnDateTime(
                            mergeDrugBorrowAndDetailBto.getPlanReturnDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "returnStatus")) {
                    drugBorrowBO.setReturnStatus(mergeDrugBorrowAndDetailBto.getReturnStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "submitDateTime")) {
                    drugBorrowBO.setSubmitDateTime(mergeDrugBorrowAndDetailBto.getSubmitDateTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowId")) {
                    drugBorrowBO.setBorrowId(mergeDrugBorrowAndDetailBto.getBorrowId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "cost")) {
                    drugBorrowBO.setCost(mergeDrugBorrowAndDetailBto.getCost());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "returnNumber")) {
                    drugBorrowBO.setReturnNumber(mergeDrugBorrowAndDetailBto.getReturnNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugBorrowBO.setStorageCode(mergeDrugBorrowAndDetailBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "inventoryNumber")) {
                    drugBorrowBO.setInventoryNumber(
                            mergeDrugBorrowAndDetailBto.getInventoryNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                        "borrowDepartment")) {
                    drugBorrowBO.setBorrowDepartment(
                            mergeDrugBorrowAndDetailBto.getBorrowDepartment());
                }
            }
        } else {
            drugBorrowBO = new DrugBorrowBO();
            if (pkExist) {
                drugBorrowBO.setId(mergeDrugBorrowAndDetailBto.getId());
            } else {
                drugBorrowBO.setId(String.valueOf(this.idGenerator.allocateId("drug_borrow")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "applyStaffId")) {
                drugBorrowBO.setApplyStaffId(mergeDrugBorrowAndDetailBto.getApplyStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowType")) {
                drugBorrowBO.setBorrowType(mergeDrugBorrowAndDetailBto.getBorrowType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowStaffId")) {
                drugBorrowBO.setBorrowStaffId(mergeDrugBorrowAndDetailBto.getBorrowStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "planReturnDateTime")) {
                drugBorrowBO.setPlanReturnDateTime(
                        mergeDrugBorrowAndDetailBto.getPlanReturnDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "returnStatus")) {
                drugBorrowBO.setReturnStatus(mergeDrugBorrowAndDetailBto.getReturnStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "submitDateTime")) {
                drugBorrowBO.setSubmitDateTime(mergeDrugBorrowAndDetailBto.getSubmitDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowId")) {
                drugBorrowBO.setBorrowId(mergeDrugBorrowAndDetailBto.getBorrowId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "cost")) {
                drugBorrowBO.setCost(mergeDrugBorrowAndDetailBto.getCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "returnNumber")) {
                drugBorrowBO.setReturnNumber(mergeDrugBorrowAndDetailBto.getReturnNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "storageCode")) {
                drugBorrowBO.setStorageCode(mergeDrugBorrowAndDetailBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "inventoryNumber")) {
                drugBorrowBO.setInventoryNumber(mergeDrugBorrowAndDetailBto.getInventoryNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "borrowDepartment")) {
                drugBorrowBO.setBorrowDepartment(mergeDrugBorrowAndDetailBto.getBorrowDepartment());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeDrugBorrowAndDetailBto);
            addedBto.setBo(drugBorrowBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugBorrowBO;
    }

    /** 删除借还药 */
    @AutoGenerated(locked = true)
    protected DeleteBorrowBoResult deleteBorrowBase(DeleteBorrowBto deleteBorrowBto) {
        if (deleteBorrowBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteBorrowBoResult boResult = new DeleteBorrowBoResult();
        DrugBorrowBO drugBorrowBO = deleteDeleteBorrowOnMissThrowEx(boResult, deleteBorrowBto);
        boResult.setRootBo(drugBorrowBO);
        return boResult;
    }

    /** 删除借还药明细 */
    @AutoGenerated(locked = true)
    protected DeleteBorrowDetailBoResult deleteBorrowDetailBase(
            DeleteBorrowDetailBto deleteBorrowDetailBto) {
        if (deleteBorrowDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteBorrowDetailBoResult boResult = new DeleteBorrowDetailBoResult();
        DrugBorrowBO drugBorrowBO =
                updateDeleteBorrowDetailOnMissThrowEx(boResult, deleteBorrowDetailBto);
        if (drugBorrowBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteBorrowDetailBto, "__$validPropertySet"),
                    "drugBorrowDetailBtoList")) {
                deleteDrugBorrowDetailBtoOnMissThrowEx(
                        boResult, deleteBorrowDetailBto, drugBorrowBO);
            }
        }
        boResult.setRootBo(drugBorrowBO);
        return boResult;
    }

    /** 删除对象:deleteBorrow,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugBorrowBO deleteDeleteBorrowOnMissThrowEx(
            BaseDrugBorrowBOService.DeleteBorrowBoResult boResult,
            DeleteBorrowBto deleteBorrowBto) {
        DrugBorrowBO drugBorrowBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteBorrowBto.getId() == null);
        if (!allNull && !found) {
            drugBorrowBO = DrugBorrowBO.getById(deleteBorrowBto.getId());
            found = true;
        }
        if (drugBorrowBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugBorrowBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteBorrowBto);
            deletedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugBorrowBO;
        }
    }

    /** 删除对象:drugBorrowDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteDrugBorrowDetailBtoOnMissThrowEx(
            DeleteBorrowDetailBoResult boResult,
            DeleteBorrowDetailBto deleteBorrowDetailBto,
            DrugBorrowBO drugBorrowBO) {
        if (CollectionUtil.isNotEmpty(deleteBorrowDetailBto.getDrugBorrowDetailBtoList())) {
            for (DeleteBorrowDetailBto.DrugBorrowDetailBto item :
                    deleteBorrowDetailBto.getDrugBorrowDetailBtoList()) {
                Optional<DrugBorrowDetailBO> any =
                        drugBorrowBO.getDrugBorrowDetailBOSet().stream()
                                .filter(
                                        drugBorrowDetailBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                drugBorrowDetailBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    drugBorrowBO.getDrugBorrowDetailBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToDrugBorrowDetail());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** merge借还药 */
    @AutoGenerated(locked = true)
    protected MergeBorrowBoResult mergeBorrowBase(MergeBorrowBto mergeBorrowBto) {
        if (mergeBorrowBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeBorrowBoResult boResult = new MergeBorrowBoResult();
        DrugBorrowBO drugBorrowBO = createMergeBorrowOnDuplicateUpdate(boResult, mergeBorrowBto);
        boResult.setRootBo(drugBorrowBO);
        return boResult;
    }

    /** 更新借还药 */
    @AutoGenerated(locked = true)
    protected MergeBorrowDetailBoResult mergeBorrowDetailBase(
            MergeBorrowDetailBto mergeBorrowDetailBto) {
        if (mergeBorrowDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeBorrowDetailBoResult boResult = new MergeBorrowDetailBoResult();
        DrugBorrowBO drugBorrowBO =
                updateMergeBorrowDetailOnMissThrowEx(boResult, mergeBorrowDetailBto);
        if (drugBorrowBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeBorrowDetailBto, "__$validPropertySet"),
                    "drugBorrowDetailBtoList")) {
                createDrugBorrowDetailBtoOnDuplicateUpdate(
                        boResult, mergeBorrowDetailBto, drugBorrowBO);
            }
        }
        boResult.setRootBo(drugBorrowBO);
        return boResult;
    }

    /** merge借还药 */
    @AutoGenerated(locked = true)
    protected MergeDrugBorrowAndDetailBoResult mergeDrugBorrowAndDetailBase(
            MergeDrugBorrowAndDetailBto mergeDrugBorrowAndDetailBto) {
        if (mergeDrugBorrowAndDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeDrugBorrowAndDetailBoResult boResult = new MergeDrugBorrowAndDetailBoResult();
        DrugBorrowBO drugBorrowBO =
                createMergeDrugBorrowAndDetailOnDuplicateUpdate(
                        boResult, mergeDrugBorrowAndDetailBto);
        if (drugBorrowBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeDrugBorrowAndDetailBto, "__$validPropertySet"),
                    "drugBorrowDetailBtoList")) {
                createDrugBorrowDetailBtoOnDuplicateUpdate(
                        boResult, mergeDrugBorrowAndDetailBto, drugBorrowBO);
            }
        }
        boResult.setRootBo(drugBorrowBO);
        return boResult;
    }

    /** 更新对象:createBorrowDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugBorrowBO updateCreateBorrowDetailOnMissThrowEx(
            BaseDrugBorrowBOService.CreateBorrowDetailBoResult boResult,
            CreateBorrowDetailBto createBorrowDetailBto) {
        DrugBorrowBO drugBorrowBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createBorrowDetailBto.getId() == null);
        if (!allNull && !found) {
            drugBorrowBO = DrugBorrowBO.getById(createBorrowDetailBto.getId());
            found = true;
        }
        if (drugBorrowBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
            updatedBto.setBto(createBorrowDetailBto);
            updatedBto.setBo(drugBorrowBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugBorrowBO;
        }
    }

    /** 更新对象:deleteBorrowDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugBorrowBO updateDeleteBorrowDetailOnMissThrowEx(
            BaseDrugBorrowBOService.DeleteBorrowDetailBoResult boResult,
            DeleteBorrowDetailBto deleteBorrowDetailBto) {
        DrugBorrowBO drugBorrowBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteBorrowDetailBto.getId() == null);
        if (!allNull && !found) {
            drugBorrowBO = DrugBorrowBO.getById(deleteBorrowDetailBto.getId());
            found = true;
        }
        if (drugBorrowBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
            updatedBto.setBto(deleteBorrowDetailBto);
            updatedBto.setBo(drugBorrowBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugBorrowBO;
        }
    }

    /** 更新对象:mergeBorrowDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugBorrowBO updateMergeBorrowDetailOnMissThrowEx(
            BaseDrugBorrowBOService.MergeBorrowDetailBoResult boResult,
            MergeBorrowDetailBto mergeBorrowDetailBto) {
        DrugBorrowBO drugBorrowBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeBorrowDetailBto.getId() == null);
        if (!allNull && !found) {
            drugBorrowBO = DrugBorrowBO.getById(mergeBorrowDetailBto.getId());
            found = true;
        }
        if (drugBorrowBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(drugBorrowBO.convertToDrugBorrow());
            updatedBto.setBto(mergeBorrowDetailBto);
            updatedBto.setBo(drugBorrowBO);
            boResult.getUpdatedList().add(updatedBto);
            return drugBorrowBO;
        }
    }

    public static class CreateBorrowAndDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugBorrowBO getRootBo() {
            return (DrugBorrowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateBorrowAndDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getCreatedBto(CreateBorrowAndDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return this.getAddedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateBorrowAndDetailBto, DrugBorrowBO> getCreatedBto(
                CreateBorrowAndDetailBto createBorrowAndDetailBto) {
            return this.getAddedResult(createBorrowAndDetailBto);
        }

        @AutoGenerated(locked = true)
        public DrugBorrowDetail getDeleted_DrugBorrowDetail() {
            return (DrugBorrowDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrowDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugBorrow getDeleted_DrugBorrow() {
            return (DrugBorrow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateBorrowAndDetailBto.DrugBorrowDetailBto,
                        DrugBorrowDetail,
                        DrugBorrowDetailBO>
                getUpdatedBto(CreateBorrowAndDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUpdatedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateBorrowAndDetailBto, DrugBorrow, DrugBorrowBO> getUpdatedBto(
                CreateBorrowAndDetailBto createBorrowAndDetailBto) {
            return super.getUpdatedResult(createBorrowAndDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateBorrowAndDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getUnmodifiedBto(CreateBorrowAndDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUnmodifiedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateBorrowAndDetailBto, DrugBorrowBO> getUnmodifiedBto(
                CreateBorrowAndDetailBto createBorrowAndDetailBto) {
            return super.getUnmodifiedResult(createBorrowAndDetailBto);
        }
    }

    public static class MergeBorrowBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugBorrowBO getRootBo() {
            return (DrugBorrowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeBorrowBto, DrugBorrowBO> getCreatedBto(MergeBorrowBto mergeBorrowBto) {
            return this.getAddedResult(mergeBorrowBto);
        }

        @AutoGenerated(locked = true)
        public DrugBorrow getDeleted_DrugBorrow() {
            return (DrugBorrow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeBorrowBto, DrugBorrow, DrugBorrowBO> getUpdatedBto(
                MergeBorrowBto mergeBorrowBto) {
            return super.getUpdatedResult(mergeBorrowBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeBorrowBto, DrugBorrowBO> getUnmodifiedBto(
                MergeBorrowBto mergeBorrowBto) {
            return super.getUnmodifiedResult(mergeBorrowBto);
        }
    }

    public static class CreateBorrowDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugBorrowBO getRootBo() {
            return (DrugBorrowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateBorrowDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getCreatedBto(CreateBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return this.getAddedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateBorrowDetailBto, DrugBorrowBO> getCreatedBto(
                CreateBorrowDetailBto createBorrowDetailBto) {
            return this.getAddedResult(createBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public DrugBorrowDetail getDeleted_DrugBorrowDetail() {
            return (DrugBorrowDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrowDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugBorrow getDeleted_DrugBorrow() {
            return (DrugBorrow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateBorrowDetailBto.DrugBorrowDetailBto,
                        DrugBorrowDetail,
                        DrugBorrowDetailBO>
                getUpdatedBto(CreateBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUpdatedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateBorrowDetailBto, DrugBorrow, DrugBorrowBO> getUpdatedBto(
                CreateBorrowDetailBto createBorrowDetailBto) {
            return super.getUpdatedResult(createBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateBorrowDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getUnmodifiedBto(CreateBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUnmodifiedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateBorrowDetailBto, DrugBorrowBO> getUnmodifiedBto(
                CreateBorrowDetailBto createBorrowDetailBto) {
            return super.getUnmodifiedResult(createBorrowDetailBto);
        }
    }

    public static class MergeBorrowDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugBorrowBO getRootBo() {
            return (DrugBorrowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeBorrowDetailBto, DrugBorrowBO> getCreatedBto(
                MergeBorrowDetailBto mergeBorrowDetailBto) {
            return this.getAddedResult(mergeBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeBorrowDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO> getCreatedBto(
                MergeBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return this.getAddedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public DrugBorrow getDeleted_DrugBorrow() {
            return (DrugBorrow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrow.class));
        }

        @AutoGenerated(locked = true)
        public DrugBorrowDetail getDeleted_DrugBorrowDetail() {
            return (DrugBorrowDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrowDetail.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeBorrowDetailBto, DrugBorrow, DrugBorrowBO> getUpdatedBto(
                MergeBorrowDetailBto mergeBorrowDetailBto) {
            return super.getUpdatedResult(mergeBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeBorrowDetailBto.DrugBorrowDetailBto,
                        DrugBorrowDetail,
                        DrugBorrowDetailBO>
                getUpdatedBto(MergeBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUpdatedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeBorrowDetailBto, DrugBorrowBO> getUnmodifiedBto(
                MergeBorrowDetailBto mergeBorrowDetailBto) {
            return super.getUnmodifiedResult(mergeBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeBorrowDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getUnmodifiedBto(MergeBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUnmodifiedResult(drugBorrowDetailBto);
        }
    }

    public static class DeleteBorrowBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugBorrowBO getRootBo() {
            return (DrugBorrowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteBorrowBto, DrugBorrowBO> getCreatedBto(
                DeleteBorrowBto deleteBorrowBto) {
            return this.getAddedResult(deleteBorrowBto);
        }

        @AutoGenerated(locked = true)
        public DrugBorrow getDeleted_DrugBorrow() {
            return (DrugBorrow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteBorrowBto, DrugBorrow, DrugBorrowBO> getUpdatedBto(
                DeleteBorrowBto deleteBorrowBto) {
            return super.getUpdatedResult(deleteBorrowBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteBorrowBto, DrugBorrowBO> getUnmodifiedBto(
                DeleteBorrowBto deleteBorrowBto) {
            return super.getUnmodifiedResult(deleteBorrowBto);
        }
    }

    public static class DeleteBorrowDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugBorrowBO getRootBo() {
            return (DrugBorrowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteBorrowDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getCreatedBto(DeleteBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return this.getAddedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteBorrowDetailBto, DrugBorrowBO> getCreatedBto(
                DeleteBorrowDetailBto deleteBorrowDetailBto) {
            return this.getAddedResult(deleteBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public DrugBorrowDetail getDeleted_DrugBorrowDetail() {
            return (DrugBorrowDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrowDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugBorrow getDeleted_DrugBorrow() {
            return (DrugBorrow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteBorrowDetailBto.DrugBorrowDetailBto,
                        DrugBorrowDetail,
                        DrugBorrowDetailBO>
                getUpdatedBto(DeleteBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUpdatedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteBorrowDetailBto, DrugBorrow, DrugBorrowBO> getUpdatedBto(
                DeleteBorrowDetailBto deleteBorrowDetailBto) {
            return super.getUpdatedResult(deleteBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteBorrowDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getUnmodifiedBto(DeleteBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUnmodifiedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteBorrowDetailBto, DrugBorrowBO> getUnmodifiedBto(
                DeleteBorrowDetailBto deleteBorrowDetailBto) {
            return super.getUnmodifiedResult(deleteBorrowDetailBto);
        }
    }

    public static class MergeDrugBorrowAndDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugBorrowBO getRootBo() {
            return (DrugBorrowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDrugBorrowAndDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getCreatedBto(MergeDrugBorrowAndDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return this.getAddedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDrugBorrowAndDetailBto, DrugBorrowBO> getCreatedBto(
                MergeDrugBorrowAndDetailBto mergeDrugBorrowAndDetailBto) {
            return this.getAddedResult(mergeDrugBorrowAndDetailBto);
        }

        @AutoGenerated(locked = true)
        public DrugBorrowDetail getDeleted_DrugBorrowDetail() {
            return (DrugBorrowDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrowDetail.class));
        }

        @AutoGenerated(locked = true)
        public DrugBorrow getDeleted_DrugBorrow() {
            return (DrugBorrow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugBorrow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeDrugBorrowAndDetailBto.DrugBorrowDetailBto,
                        DrugBorrowDetail,
                        DrugBorrowDetailBO>
                getUpdatedBto(MergeDrugBorrowAndDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUpdatedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeDrugBorrowAndDetailBto, DrugBorrow, DrugBorrowBO> getUpdatedBto(
                MergeDrugBorrowAndDetailBto mergeDrugBorrowAndDetailBto) {
            return super.getUpdatedResult(mergeDrugBorrowAndDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDrugBorrowAndDetailBto.DrugBorrowDetailBto, DrugBorrowDetailBO>
                getUnmodifiedBto(
                        MergeDrugBorrowAndDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
            return super.getUnmodifiedResult(drugBorrowDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDrugBorrowAndDetailBto, DrugBorrowBO> getUnmodifiedBto(
                MergeDrugBorrowAndDetailBto mergeDrugBorrowAndDetailBto) {
            return super.getUnmodifiedResult(mergeDrugBorrowAndDetailBto);
        }
    }
}
