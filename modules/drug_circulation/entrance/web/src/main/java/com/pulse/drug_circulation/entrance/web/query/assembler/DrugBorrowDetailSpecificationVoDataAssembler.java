package com.pulse.drug_circulation.entrance.web.query.assembler;

import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailSpecificationVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugOriginBaseVo;
import com.pulse.drug_circulation.manager.dto.DrugBorrowBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailBaseDto;
import com.pulse.drug_circulation.service.DrugBorrowDetailBaseDtoService;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** DrugBorrowDetailSpecificationVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "6d360375-8c2f-37f9-b0af-c9cf06ae038e")
public class DrugBorrowDetailSpecificationVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailBaseDtoService drugBorrowDetailBaseDtoService;

    /** 批量自定义组装DrugBorrowDetailSpecificationVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "3e6fed21-09fc-3388-937d-1aa73290b50b")
    public void assembleDataCustomized(List<DrugBorrowDetailSpecificationVo> dataList) {
        // 自定义数据组装

    }

    /** 组装drugOriginSpecification数据 */
    @AutoGenerated(locked = true, uuid = "c850a0e5-a9f8-32d6-817f-16d302560567")
    private void assembleDrugOriginSpecificationData(
            DrugBorrowDetailSpecificationVoDataAssembler.DrugBorrowDetailSpecificationVoDataHolder
                    dataHolder) {
        Map<String, Pair<DrugOriginBaseDto, DrugOriginBaseVo>> drugOriginSpecification2DrugOrigin =
                dataHolder.drugOriginSpecification2DrugOrigin.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getDrugOriginCode(),
                                        dto ->
                                                Pair.of(
                                                        dto,
                                                        dataHolder
                                                                .drugOriginSpecification2DrugOrigin
                                                                .get(dto)),
                                        (o1, o2) -> o1));
        for (Map.Entry<
                        DrugOriginSpecificationBaseDto,
                        DrugBorrowDetailSpecificationVo.DrugOriginSpecificationWithDrugOriginVo>
                drugOriginSpecification : dataHolder.drugOriginSpecification.entrySet()) {
            DrugOriginSpecificationBaseDto baseDto = drugOriginSpecification.getKey();
            DrugBorrowDetailSpecificationVo.DrugOriginSpecificationWithDrugOriginVo vo =
                    drugOriginSpecification.getValue();
            vo.setDrugOrigin(
                    Optional.ofNullable(
                                    drugOriginSpecification2DrugOrigin.get(
                                            baseDto.getDrugOriginCode()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }
    }

    /** 组装DrugBorrowDetailSpecificationVo数据 */
    @AutoGenerated(locked = true, uuid = "dec9471b-fbfa-3d84-a373-2e7801f34d6a")
    public void assembleData(
            Map<String, DrugBorrowDetailSpecificationVo> voMap,
            DrugBorrowDetailSpecificationVoDataAssembler.DrugBorrowDetailSpecificationVoDataHolder
                    dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<DrugBorrowDetailBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, List<Pair<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo>>>
                drugBorrowDetailList =
                        dataHolder.drugBorrowDetailList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getDrugBorrowDetailId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .drugBorrowDetailList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));
        Map<
                        String,
                        Pair<
                                DrugOriginSpecificationBaseDto,
                                DrugBorrowDetailSpecificationVo
                                        .DrugOriginSpecificationWithDrugOriginVo>>
                drugOriginSpecification =
                        dataHolder.drugOriginSpecification.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.drugOriginSpecification
                                                                        .get(dto)),
                                                (o1, o2) -> o1));
        Map<
                        String,
                        Pair<
                                DrugOriginBatchInventoryBaseDto,
                                DrugBorrowDetailSpecificationVo.DrugOriginBatchInventoryBaseVo>>
                batchInventory =
                        dataHolder.batchInventory.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto ->
                                                        Pair.of(
                                                                dto,
                                                                dataHolder.batchInventory.get(dto)),
                                                (o1, o2) -> o1));
        Map<String, Pair<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo>> drugBorrowDetail =
                dataHolder.drugBorrowDetail.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.drugBorrowDetail.get(dto)),
                                        (o1, o2) -> o1));
        Map<String, Pair<DrugBorrowBaseDto, DrugBorrowBaseVo>> drugBorrow =
                dataHolder.drugBorrow.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.drugBorrow.get(dto)),
                                        (o1, o2) -> o1));

        for (DrugBorrowDetailBaseDto baseDto : baseDtoList) {
            DrugBorrowDetailSpecificationVo vo = voMap.get(baseDto.getId());
            vo.setDrugBorrowDetailList(
                    Optional.ofNullable(drugBorrowDetailList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
            vo.setDrugOriginSpecification(
                    Optional.ofNullable(
                                    drugOriginSpecification.get(
                                            baseDto.getDrugOriginSpecificationId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setBatchInventory(
                    Optional.ofNullable(batchInventory.get(baseDto.getBatchInventoryId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugBorrowDetail(
                    Optional.ofNullable(drugBorrowDetail.get(baseDto.getDrugBorrowDetailId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
            vo.setDrugBorrow(
                    Optional.ofNullable(drugBorrow.get(baseDto.getDrugBorrowId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDrugOriginSpecificationData(dataHolder);

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class DrugBorrowDetailSpecificationVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugBorrowDetailBaseDto> rootBaseDtoList;

        /** 持有字段drugBorrowDetailList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> drugBorrowDetailList;

        /** 持有字段drugOriginSpecification的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<
                        DrugOriginSpecificationBaseDto,
                        DrugBorrowDetailSpecificationVo.DrugOriginSpecificationWithDrugOriginVo>
                drugOriginSpecification;

        /** 持有字段batchInventory的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<
                        DrugOriginBatchInventoryBaseDto,
                        DrugBorrowDetailSpecificationVo.DrugOriginBatchInventoryBaseVo>
                batchInventory;

        /** 持有字段drugBorrowDetail的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> drugBorrowDetail;

        /** 持有字段drugBorrow的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugBorrowBaseDto, DrugBorrowBaseVo> drugBorrow;

        /** 持有字段drugOriginSpecification.drugOrigin的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugOriginBaseDto, DrugOriginBaseVo> drugOriginSpecification2DrugOrigin;
    }
}
