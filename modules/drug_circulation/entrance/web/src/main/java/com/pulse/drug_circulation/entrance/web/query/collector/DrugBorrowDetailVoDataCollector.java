package com.pulse.drug_circulation.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_circulation.entrance.web.converter.DrugBorrowBaseVoConverter;
import com.pulse.drug_circulation.entrance.web.converter.DrugBorrowDetailBaseVoConverter;
import com.pulse.drug_circulation.entrance.web.converter.DrugBorrowDetailVoConverter;
import com.pulse.drug_circulation.entrance.web.converter.DrugOriginBaseVoConverter;
import com.pulse.drug_circulation.entrance.web.query.assembler.DrugBorrowDetailVoDataAssembler.DrugBorrowDetailVoDataHolder;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugOriginBaseVo;
import com.pulse.drug_circulation.manager.dto.DrugBorrowBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailDto;
import com.pulse.drug_circulation.manager.facade.drug_dictionary.DrugOriginBaseDtoServiceInDrugCirculationRpcAdapter;
import com.pulse.drug_circulation.manager.facade.drug_dictionary.DrugOriginSpecificationBaseDtoServiceInDrugCirculationRpcAdapter;
import com.pulse.drug_circulation.manager.facade.drug_dictionary.DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter;
import com.pulse.drug_circulation.manager.facade.drug_inventory.DrugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter;
import com.pulse.drug_circulation.service.DrugBorrowBaseDtoService;
import com.pulse.drug_circulation.service.DrugBorrowDetailBaseDtoService;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithDrugOriginDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装DrugBorrowDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "970fbcda-edad-3135-8d43-95bd9359d660")
public class DrugBorrowDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowBaseDtoService drugBorrowBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowBaseVoConverter drugBorrowBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailBaseDtoService drugBorrowDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailBaseVoConverter drugBorrowDetailBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailVoConverter drugBorrowDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailVoDataCollector drugBorrowDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseDtoServiceInDrugCirculationRpcAdapter
            drugOriginBaseDtoServiceInDrugCirculationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseVoConverter drugOriginBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter
            drugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationBaseDtoServiceInDrugCirculationRpcAdapter
            drugOriginSpecificationBaseDtoServiceInDrugCirculationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter
            drugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter;

    /** 获取DrugBorrowDetailDto数据填充DrugBorrowDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "3d355d59-5f48-3218-b763-be17c8e39c09")
    public void collectDataWithDtoData(
            List<DrugBorrowDetailDto> dtoList, DrugBorrowDetailVoDataHolder dataHolder) {
        List<DrugBorrowDetailBaseDto> drugBorrowDetailList = new ArrayList<>();
        List<DrugBorrowDetailBaseDto> drugBorrowDetailListList = new ArrayList<>();
        Map<DrugOriginSpecificationBaseDto, DrugOriginSpecificationWithDrugOriginDto>
                drugOriginSpecificationBaseDtoDtoMap = new LinkedHashMap<>();
        List<DrugOriginBaseDto> drugOriginSpecification2DrugOriginList = new ArrayList<>();
        List<DrugOriginBatchInventoryBaseDto> batchInventoryList = new ArrayList<>();

        for (DrugBorrowDetailDto rootDto : dtoList) {
            DrugBorrowDetailBaseDto drugBorrowDetailDto = rootDto.getDrugBorrowDetail();
            if (drugBorrowDetailDto != null) {
                drugBorrowDetailList.add(drugBorrowDetailDto);
            }
            if (CollectionUtil.isNotEmpty(rootDto.getDrugBorrowDetailList())) {
                for (DrugBorrowDetailBaseDto drugBorrowDetailListDto :
                        rootDto.getDrugBorrowDetailList()) {
                    drugBorrowDetailListList.add(drugBorrowDetailListDto);
                }
            }
            DrugOriginSpecificationWithDrugOriginDto drugOriginSpecificationDto =
                    rootDto.getDrugOriginSpecification();
            if (drugOriginSpecificationDto != null) {
                DrugOriginSpecificationBaseDto drugOriginSpecificationBaseDto =
                        drugOriginSpecificationBaseDtoServiceInDrugCirculationRpcAdapter
                                .getByIds(List.of(drugOriginSpecificationDto.getId()))
                                .stream()
                                .findAny()
                                .get();
                drugOriginSpecificationBaseDtoDtoMap.put(
                        drugOriginSpecificationBaseDto, drugOriginSpecificationDto);
                DrugOriginBaseDto drugOriginSpecification2DrugOriginDto =
                        drugOriginSpecificationDto.getDrugOrigin();
                if (drugOriginSpecification2DrugOriginDto != null) {
                    drugOriginSpecification2DrugOriginList.add(
                            drugOriginSpecification2DrugOriginDto);
                }
            }
            DrugOriginBatchInventoryBaseDto batchInventoryDto = rootDto.getBatchInventory();
            if (batchInventoryDto != null) {
                batchInventoryList.add(batchInventoryDto);
            }
        }

        // access drugBorrowDetail
        Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> drugBorrowDetailVoMap =
                drugBorrowDetailBaseVoConverter.convertToDrugBorrowDetailBaseVoMap(
                        drugBorrowDetailList);
        dataHolder.drugBorrowDetail =
                drugBorrowDetailList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> drugBorrowDetailVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access drugBorrowDetailList
        Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> drugBorrowDetailListVoMap =
                drugBorrowDetailBaseVoConverter.convertToDrugBorrowDetailBaseVoMap(
                        drugBorrowDetailListList);
        dataHolder.drugBorrowDetailList =
                drugBorrowDetailListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> drugBorrowDetailListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access drugOriginSpecification
        Map<
                        DrugOriginSpecificationWithDrugOriginDto,
                        DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo>
                drugOriginSpecificationVoMap =
                        drugBorrowDetailVoConverter
                                .convertToDrugOriginSpecificationWithDrugOriginVoMap(
                                        new ArrayList<>(
                                                drugOriginSpecificationBaseDtoDtoMap.values()));
        dataHolder.drugOriginSpecification =
                drugOriginSpecificationBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                drugOriginSpecificationVoMap.get(
                                                        drugOriginSpecificationBaseDtoDtoMap.get(
                                                                baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access drugOriginSpecification2DrugOrigin
        Map<DrugOriginBaseDto, DrugOriginBaseVo> drugOriginSpecification2DrugOriginVoMap =
                drugOriginBaseVoConverter.convertToDrugOriginBaseVoMap(
                        drugOriginSpecification2DrugOriginList);
        dataHolder.drugOriginSpecification2DrugOrigin =
                drugOriginSpecification2DrugOriginList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                drugOriginSpecification2DrugOriginVoMap.get(
                                                        baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access batchInventory
        Map<DrugOriginBatchInventoryBaseDto, DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo>
                batchInventoryVoMap =
                        drugBorrowDetailVoConverter.convertToDrugOriginBatchInventoryBaseVoMap(
                                batchInventoryList);
        dataHolder.batchInventory =
                batchInventoryList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> batchInventoryVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "6ca1b800-0a67-3c36-869d-5d7d2d8c662a")
    private void fillDataWhenNecessary(DrugBorrowDetailVoDataHolder dataHolder) {
        List<DrugBorrowDetailBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.drugBorrowDetail == null || dataHolder.drugBorrowDetailList == null) {
            Set<String> ids = new HashSet<>();
            if (dataHolder.drugBorrowDetail == null) {
                ids.addAll(
                        rootDtoList.stream()
                                .map(DrugBorrowDetailBaseDto::getDrugBorrowDetailId)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet()));
            }
            if (dataHolder.drugBorrowDetailList == null) {
                ids.addAll(
                        rootDtoList.stream()
                                .map(DrugBorrowDetailBaseDto::getDrugBorrowDetailId)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet()));
            }
            List<DrugBorrowDetailBaseDto> baseDtoList =
                    drugBorrowDetailBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugBorrowDetailBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<DrugBorrowDetailBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(DrugBorrowDetailBaseDto::getId));
            if (dataHolder.drugBorrowDetail == null) {
                Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> dtoVoMap =
                        drugBorrowDetailBaseVoConverter.convertToDrugBorrowDetailBaseVoMap(
                                baseDtoList);
                Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> baseDtoVoMap =
                        baseDtoList.stream()
                                .collect(
                                        Collectors.toMap(
                                                Function.identity(),
                                                baseDto -> dtoVoMap.get(baseDto),
                                                (o1, o2) -> o1,
                                                LinkedHashMap::new));
                dataHolder.drugBorrowDetail =
                        rootDtoList.stream()
                                .map(DrugBorrowDetailBaseDto::getDrugBorrowDetailId)
                                .flatMap(
                                        tmpId ->
                                                Optional.ofNullable(baseDtoMap.get(tmpId))
                                                        .orElse(new ArrayList<>())
                                                        .stream())
                                .filter(Objects::nonNull)
                                .collect(
                                        Collectors.toMap(
                                                Function.identity(),
                                                baseDto -> baseDtoVoMap.get(baseDto),
                                                (o1, o2) -> o1,
                                                LinkedHashMap::new));
            }
            if (dataHolder.drugBorrowDetailList == null) {
                Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> dtoVoMap =
                        drugBorrowDetailBaseVoConverter.convertToDrugBorrowDetailBaseVoMap(
                                baseDtoList);
                Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> baseDtoVoMap =
                        baseDtoList.stream()
                                .collect(
                                        Collectors.toMap(
                                                Function.identity(),
                                                baseDto -> dtoVoMap.get(baseDto),
                                                (o1, o2) -> o1,
                                                LinkedHashMap::new));
                dataHolder.drugBorrowDetailList =
                        rootDtoList.stream()
                                .map(DrugBorrowDetailBaseDto::getDrugBorrowDetailId)
                                .flatMap(
                                        tmpId ->
                                                Optional.ofNullable(baseDtoMap.get(tmpId))
                                                        .orElse(new ArrayList<>())
                                                        .stream())
                                .filter(Objects::nonNull)
                                .collect(
                                        Collectors.toMap(
                                                Function.identity(),
                                                baseDto -> baseDtoVoMap.get(baseDto),
                                                (o1, o2) -> o1,
                                                LinkedHashMap::new));
            }
        }
        if (dataHolder.drugOriginSpecification == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugBorrowDetailBaseDto::getDrugOriginSpecificationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginSpecificationBaseDto> baseDtoList =
                    drugOriginSpecificationBaseDtoServiceInDrugCirculationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginSpecificationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugOriginSpecificationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginSpecificationBaseDto::getId,
                                            Function.identity()));
            Map<String, DrugOriginSpecificationWithDrugOriginDto>
                    drugOriginSpecificationWithDrugOriginDtoMap =
                            drugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter
                                    .getByIds(
                                            baseDtoList.stream()
                                                    .map(DrugOriginSpecificationBaseDto::getId)
                                                    .collect(Collectors.toList()))
                                    .stream()
                                    .collect(
                                            Collectors.toMap(
                                                    DrugOriginSpecificationWithDrugOriginDto::getId,
                                                    Function.identity()));
            Map<
                            DrugOriginSpecificationWithDrugOriginDto,
                            DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo>
                    dtoVoMap =
                            drugBorrowDetailVoConverter
                                    .convertToDrugOriginSpecificationWithDrugOriginVoMap(
                                            new ArrayList<>(
                                                    drugOriginSpecificationWithDrugOriginDtoMap
                                                            .values()));
            Map<
                            DrugOriginSpecificationBaseDto,
                            DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .filter(
                                            baseDto ->
                                                    drugOriginSpecificationWithDrugOriginDtoMap
                                                            .containsKey(baseDto.getId()))
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto ->
                                                            dtoVoMap.get(
                                                                    drugOriginSpecificationWithDrugOriginDtoMap
                                                                            .get(baseDto.getId())),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.drugOriginSpecification =
                    rootDtoList.stream()
                            .map(DrugBorrowDetailBaseDto::getDrugOriginSpecificationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugBorrow == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugBorrowDetailBaseDto::getDrugBorrowId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugBorrowBaseDto> baseDtoList =
                    drugBorrowBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(DrugBorrowBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugBorrowBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugBorrowBaseDto::getId, Function.identity()));
            Map<DrugBorrowBaseDto, DrugBorrowBaseVo> dtoVoMap =
                    drugBorrowBaseVoConverter.convertToDrugBorrowBaseVoMap(baseDtoList);
            Map<DrugBorrowBaseDto, DrugBorrowBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugBorrow =
                    rootDtoList.stream()
                            .map(DrugBorrowDetailBaseDto::getDrugBorrowId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.batchInventory == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(DrugBorrowDetailBaseDto::getBatchInventoryId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginBatchInventoryBaseDto> baseDtoList =
                    drugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginBatchInventoryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, DrugOriginBatchInventoryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginBatchInventoryBaseDto::getId,
                                            Function.identity()));
            Map<DrugOriginBatchInventoryBaseDto, DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo>
                    dtoVoMap =
                            drugBorrowDetailVoConverter.convertToDrugOriginBatchInventoryBaseVoMap(
                                    baseDtoList);
            Map<DrugOriginBatchInventoryBaseDto, DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo>
                    baseDtoVoMap =
                            baseDtoList.stream()
                                    .collect(
                                            Collectors.toMap(
                                                    Function.identity(),
                                                    baseDto -> dtoVoMap.get(baseDto),
                                                    (o1, o2) -> o1,
                                                    LinkedHashMap::new));
            dataHolder.batchInventory =
                    rootDtoList.stream()
                            .map(DrugBorrowDetailBaseDto::getBatchInventoryId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.drugOriginSpecification2DrugOrigin == null) {
            Set<String> ids =
                    dataHolder.drugOriginSpecification.keySet().stream()
                            .map(DrugOriginSpecificationBaseDto::getDrugOriginCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<DrugOriginBaseDto> baseDtoList =
                    drugOriginBaseDtoServiceInDrugCirculationRpcAdapter
                            .getByDrugOriginCodes(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(DrugOriginBaseDto::getDrugOriginCode))
                            .collect(Collectors.toList());
            Map<String, DrugOriginBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            DrugOriginBaseDto::getDrugOriginCode,
                                            Function.identity()));
            Map<DrugOriginBaseDto, DrugOriginBaseVo> dtoVoMap =
                    drugOriginBaseVoConverter.convertToDrugOriginBaseVoMap(baseDtoList);
            Map<DrugOriginBaseDto, DrugOriginBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.drugOriginSpecification2DrugOrigin =
                    dataHolder.drugOriginSpecification.keySet().stream()
                            .map(DrugOriginSpecificationBaseDto::getDrugOriginCode)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "ab0fb27d-a0d3-3152-bd06-19f2c08882bb")
    public void collectDataDefault(DrugBorrowDetailVoDataHolder dataHolder) {
        drugBorrowDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
