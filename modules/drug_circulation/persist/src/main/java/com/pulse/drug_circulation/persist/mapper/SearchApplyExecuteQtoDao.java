package com.pulse.drug_circulation.persist.mapper;

import cn.hutool.core.util.StrUtil;

import com.pulse.drug_circulation.persist.qto.SearchApplyExecuteQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "b1ba0dc4-259c-4ef6-9fe2-9fb5e5d021eb|QTO|DAO")
public class SearchApplyExecuteQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询受理请领 */
    @AutoGenerated(locked = false, uuid = "b1ba0dc4-259c-4ef6-9fe2-9fb5e5d021eb-count")
    public Integer count(SearchApplyExecuteQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(drug_apply.id) FROM drug_apply LEFT JOIN department"
                    + " \"applyDepartment\" on drug_apply.apply_department_code ="
                    + " \"applyDepartment\".id WHERE drug_apply.apply_status = #applyStatusIs AND"
                    + " \"applyDepartment\".organization_id = #applyDepartmentOrganizationIdIs AND"
                    + " drug_apply.apply_type = #applyTypeIs AND drug_apply.apply_staff_id ="
                    + " #applyStaffIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getApplyDepartmentOrganizationIdIs() == null) {
            conditionToRemove.add("#applyDepartmentOrganizationIdIs");
        }
        if (qto.getApplyStaffIdIs() == null) {
            conditionToRemove.add("#applyStaffIdIs");
        }
        if (qto.getApplyStatusIs() == null) {
            conditionToRemove.add("#applyStatusIs");
        }
        if (qto.getApplyTypeIs() == null) {
            conditionToRemove.add("#applyTypeIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugApplyDetailList_drugOriginCode\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_receiverStaff\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_drugOriginSpecification\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_applyStaff\"");
        softDeleteTableAlias.add("\"auditStaff\"");
        softDeleteTableAlias.add("\"provideStorage\"");
        softDeleteTableAlias.add("\"executeStaff\"");
        softDeleteTableAlias.add("\"applyDepartment\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#applyDepartmentOrganizationIdIs", "?")
                        .replace("#applyStaffIdIs", "?")
                        .replace("#applyStatusIs", "?")
                        .replace("#applyTypeIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#applyDepartmentOrganizationIdIs")) {
                sqlParams.add(qto.getApplyDepartmentOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#applyStaffIdIs")) {
                sqlParams.add(qto.getApplyStaffIdIs());
            } else if (paramName.equalsIgnoreCase("#applyStatusIs")) {
                sqlParams.add(qto.getApplyStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#applyTypeIs")) {
                sqlParams.add(qto.getApplyTypeIs().toString());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = false, uuid = "b1ba0dc4-259c-4ef6-9fe2-9fb5e5d021eb-filter")
    public List<Map> filter(List<String> idList, SearchApplyExecuteQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_apply.id AS \"drug_apply.id\",\"drugApplyDetailList\".id AS"
                    + " \"drugApplyDetailList.id\" FROM drug_apply drug_apply LEFT JOIN (SELECT"
                    + " \"drugApplyDetailList\".* FROM drug_apply_detail \"drugApplyDetailList\""
                    + " LEFT JOIN drug_origin \"drugApplyDetailList_drugOriginCode\" on"
                    + " \"drugApplyDetailList\".drug_origin_code ="
                    + " \"drugApplyDetailList_drugOriginCode\".drug_origin_code WHERE"
                    + " \"drugApplyDetailList_drugOriginCode\".store_condition ="
                    + " #drugOriginCodeStoreConditionIs AND"
                    + " \"drugApplyDetailList_drugOriginCode\".toxic_type ="
                    + " #drugOriginCodeToxicTypeIs AND"
                    + " \"drugApplyDetailList_drugOriginCode\".account_type ="
                    + " #drugOriginCodeAccountTypeIs AND \"drugApplyDetailList\".emergency_flag ="
                    + " #emergencyFlagIs AND \"drugApplyDetailList\".status = #statusDetailIs AND"
                    + " \"drugApplyDetailList\".receiver_date <= #receiverDateLessThanEqual AND"
                    + " \"drugApplyDetailList\".receiver_date >= #receiverDateBiggerThanEqual )"
                    + " \"drugApplyDetailList\" ON drug_apply.id ="
                    + " \"drugApplyDetailList\".drug_apply_id WHERE drug_apply.id in ##mainId ";
        if (idList.isEmpty()) {
            return List.of();
        }
        sql = sql.replace("##mainId", SqlUtil.buildInSqlPram(idList.size()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getFilter().getDrugOriginCodeStoreConditionIs() == null) {
            conditionToRemove.add("#drugOriginCodeStoreConditionIs");
        }
        if (qto.getFilter().getDrugOriginCodeAccountTypeIs() == null) {
            conditionToRemove.add("#drugOriginCodeAccountTypeIs");
        }
        if (qto.getFilter().getReceiverDateBiggerThanEqual() == null) {
            conditionToRemove.add("#receiverDateBiggerThanEqual");
        }
        if (qto.getFilter().getDrugOriginCodeToxicTypeIs() == null) {
            conditionToRemove.add("#drugOriginCodeToxicTypeIs");
        }
        if (qto.getFilter().getReceiverDateLessThanEqual() == null) {
            conditionToRemove.add("#receiverDateLessThanEqual");
        }
        if (qto.getFilter().getEmergencyFlagIs() == null) {
            conditionToRemove.add("#emergencyFlagIs");
        }
        if (qto.getFilter().getStatusDetailIs() == null) {
            conditionToRemove.add("#statusDetailIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#drugOriginCodeStoreConditionIs", "?")
                        .replace("#drugOriginCodeAccountTypeIs", "?")
                        .replace("#receiverDateBiggerThanEqual", "?")
                        .replace("#drugOriginCodeToxicTypeIs", "?")
                        .replace("#receiverDateLessThanEqual", "?")
                        .replace("#emergencyFlagIs", "?")
                        .replace("#statusDetailIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#drugOriginCodeStoreConditionIs")) {
                sqlParams.add(qto.getFilter().getDrugOriginCodeStoreConditionIs());
            } else if (paramName.equalsIgnoreCase("#drugOriginCodeAccountTypeIs")) {
                sqlParams.add(qto.getFilter().getDrugOriginCodeAccountTypeIs());
            } else if (paramName.equalsIgnoreCase("#receiverDateBiggerThanEqual")) {
                sqlParams.add(qto.getFilter().getReceiverDateBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#drugOriginCodeToxicTypeIs")) {
                sqlParams.add(qto.getFilter().getDrugOriginCodeToxicTypeIs());
            } else if (paramName.equalsIgnoreCase("#receiverDateLessThanEqual")) {
                sqlParams.add(qto.getFilter().getReceiverDateLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#emergencyFlagIs")) {
                sqlParams.add(qto.getFilter().getEmergencyFlagIs());
            } else if (paramName.equalsIgnoreCase("#statusDetailIs")) {
                sqlParams.add(qto.getFilter().getStatusDetailIs().toString());
            }
        }
        sqlParams.addAll(idList);
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        return this.sqlManager.getList(
                parsedSql,
                resultSet -> {
                    Map<String, Object> rowData = new HashMap<>();
                    if (StrUtil.isNotEmpty(resultSet.getString("drug_apply.id"))) {
                        rowData.put("drug_apply", resultSet.getString("drug_apply.id"));
                    }
                    if (StrUtil.isNotEmpty(resultSet.getString("drugApplyDetailList.id"))) {
                        rowData.put(
                                "drugApplyDetailList",
                                resultSet.getString("drugApplyDetailList.id"));
                    }
                    return rowData;
                },
                sqlParams);
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询受理请领 */
    @AutoGenerated(locked = false, uuid = "b1ba0dc4-259c-4ef6-9fe2-9fb5e5d021eb-query-all")
    public List<String> query(SearchApplyExecuteQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_apply.id FROM drug_apply LEFT JOIN department \"applyDepartment\" on"
                    + " drug_apply.apply_department_code = \"applyDepartment\".id WHERE"
                    + " drug_apply.apply_status = #applyStatusIs AND"
                    + " \"applyDepartment\".organization_id = #applyDepartmentOrganizationIdIs AND"
                    + " drug_apply.apply_type = #applyTypeIs AND drug_apply.apply_staff_id ="
                    + " #applyStaffIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getApplyDepartmentOrganizationIdIs() == null) {
            conditionToRemove.add("#applyDepartmentOrganizationIdIs");
        }
        if (qto.getApplyStaffIdIs() == null) {
            conditionToRemove.add("#applyStaffIdIs");
        }
        if (qto.getApplyStatusIs() == null) {
            conditionToRemove.add("#applyStatusIs");
        }
        if (qto.getApplyTypeIs() == null) {
            conditionToRemove.add("#applyTypeIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugApplyDetailList_drugOriginCode\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_receiverStaff\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_drugOriginSpecification\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_applyStaff\"");
        softDeleteTableAlias.add("\"auditStaff\"");
        softDeleteTableAlias.add("\"provideStorage\"");
        softDeleteTableAlias.add("\"executeStaff\"");
        softDeleteTableAlias.add("\"applyDepartment\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#applyDepartmentOrganizationIdIs", "?")
                        .replace("#applyStaffIdIs", "?")
                        .replace("#applyStatusIs", "?")
                        .replace("#applyTypeIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#applyDepartmentOrganizationIdIs")) {
                sqlParams.add(qto.getApplyDepartmentOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#applyStaffIdIs")) {
                sqlParams.add(qto.getApplyStaffIdIs());
            } else if (paramName.equalsIgnoreCase("#applyStatusIs")) {
                sqlParams.add(qto.getApplyStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#applyTypeIs")) {
                sqlParams.add(qto.getApplyTypeIs().toString());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_apply.submit_date_time asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询受理请领 */
    @AutoGenerated(locked = false, uuid = "b1ba0dc4-259c-4ef6-9fe2-9fb5e5d021eb-query-paginate")
    public List<String> queryPaged(SearchApplyExecuteQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT drug_apply.id FROM drug_apply LEFT JOIN department \"applyDepartment\" on"
                    + " drug_apply.apply_department_code = \"applyDepartment\".id WHERE"
                    + " drug_apply.apply_status = #applyStatusIs AND"
                    + " \"applyDepartment\".organization_id = #applyDepartmentOrganizationIdIs AND"
                    + " drug_apply.apply_type = #applyTypeIs AND drug_apply.apply_staff_id ="
                    + " #applyStaffIdIs ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getApplyDepartmentOrganizationIdIs() == null) {
            conditionToRemove.add("#applyDepartmentOrganizationIdIs");
        }
        if (qto.getApplyStaffIdIs() == null) {
            conditionToRemove.add("#applyStaffIdIs");
        }
        if (qto.getApplyStatusIs() == null) {
            conditionToRemove.add("#applyStatusIs");
        }
        if (qto.getApplyTypeIs() == null) {
            conditionToRemove.add("#applyTypeIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"drugApplyDetailList_drugOriginCode\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_receiverStaff\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_drugOriginSpecification\"");
        softDeleteTableAlias.add("\"drugApplyDetailList_applyStaff\"");
        softDeleteTableAlias.add("\"auditStaff\"");
        softDeleteTableAlias.add("\"provideStorage\"");
        softDeleteTableAlias.add("\"executeStaff\"");
        softDeleteTableAlias.add("\"applyDepartment\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#applyDepartmentOrganizationIdIs", "?")
                        .replace("#applyStaffIdIs", "?")
                        .replace("#applyStatusIs", "?")
                        .replace("#applyTypeIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#applyDepartmentOrganizationIdIs")) {
                sqlParams.add(qto.getApplyDepartmentOrganizationIdIs());
            } else if (paramName.equalsIgnoreCase("#applyStaffIdIs")) {
                sqlParams.add(qto.getApplyStaffIdIs());
            } else if (paramName.equalsIgnoreCase("#applyStatusIs")) {
                sqlParams.add(qto.getApplyStatusIs().toString());
            } else if (paramName.equalsIgnoreCase("#applyTypeIs")) {
                sqlParams.add(qto.getApplyTypeIs().toString());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  drug_apply.submit_date_time asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
