package com.pulse.drug_circulation.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.drug_circulation.common.enums.ApplyStatusEnum;
import com.pulse.drug_circulation.common.enums.ApplyTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "drug_apply", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "51e7c5e6-3272-31c9-9ff5-a2786fb285cb|ENTITY|DEFINITION")
public class DrugApply {
    @AutoGenerated(locked = true, uuid = "52a5f7a6-35f9-3ef8-a9c9-101510df4c9c")
    @TableField(value = "apply_department_code")
    private String applyDepartmentCode;

    @AutoGenerated(locked = true, uuid = "46a46449-f531-32fd-a21b-e22bd954e6ba")
    @TableField(value = "apply_description")
    private String applyDescription;

    @AutoGenerated(locked = true, uuid = "2e452cbe-61d8-34dd-81db-e197f9e2ae38")
    @TableField(value = "apply_number")
    private String applyNumber;

    @AutoGenerated(locked = true, uuid = "d6596d02-d122-3fbb-967f-4fd6235d7695")
    @TableField(value = "apply_staff_id")
    private String applyStaffId;

    /** 已保存、已提交、已审核、已执行 (库房发放后为已执行)、已作废、已拒绝、部分发药 */
    @AutoGenerated(locked = true, uuid = "146101f1-ed73-3881-87b5-9c17922fd8d4")
    @TableField(value = "apply_status")
    private ApplyStatusEnum applyStatus;

    /** 库存请领单、科室请领单 */
    @AutoGenerated(locked = true, uuid = "239d05bc-481b-34b4-a5cb-6e517f938880")
    @TableField(value = "apply_type")
    private ApplyTypeEnum applyType;

    @AutoGenerated(locked = true, uuid = "e7d32889-57e3-3c91-91a7-69bc69df942a")
    @TableField(value = "audit_staff_id")
    private String auditStaffId;

    @AutoGenerated(locked = true, uuid = "84e1b6c4-5a53-304a-9c69-c8201ae62d9b")
    @TableField(value = "consume_end_date")
    private Date consumeEndDate;

    @AutoGenerated(locked = true, uuid = "9506ff5e-63c8-3114-bb0e-b3c8951bfa13")
    @TableField(value = "consume_start_date")
    private Date consumeStartDate;

    @AutoGenerated(locked = true, uuid = "0575c750-caa7-3285-8aa3-0b1e56ba84b8")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "5ff55e64-960a-34bf-8100-11241a38827e")
    @TableField(value = "execute_date_time")
    private Date executeDateTime;

    @AutoGenerated(locked = true, uuid = "69a311c9-ded6-3358-84f1-0214e1f43925")
    @TableField(value = "execute_staff_id")
    private String executeStaffId;

    @AutoGenerated(locked = true, uuid = "357747c8-1e9c-3a99-883a-840764ae1621")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "34c1820b-8133-4e7a-87d9-6133bcb62c49")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "b1a0780b-6c70-3a34-9a1c-6c97af54b2b6")
    @TableField(value = "provide_storage_code")
    private String provideStorageCode;

    /** 浙二退药给上级库房走的是退药请领模式 */
    @AutoGenerated(locked = true, uuid = "9c9790f1-c37f-3949-8961-b7844f323e9f")
    @TableField(value = "refund_flag")
    private Boolean refundFlag;

    @AutoGenerated(locked = true, uuid = "4c225b5e-3df4-3554-8853-69f99b8b3822")
    @TableField(value = "reject_reason")
    private String rejectReason;

    @AutoGenerated(locked = true, uuid = "d1f36eea-b0e5-3579-a01c-bcdc8a27b77b")
    @TableField(value = "remark")
    private String remark;

    @AutoGenerated(locked = true, uuid = "8e3b9997-3bb6-3116-9366-c4d1a0064a0c")
    @TableField(value = "submit_date_time")
    private Date submitDateTime;

    @AutoGenerated(locked = true, uuid = "1758e9b2-e30e-3f0a-a8f3-664213d7e2aa")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
