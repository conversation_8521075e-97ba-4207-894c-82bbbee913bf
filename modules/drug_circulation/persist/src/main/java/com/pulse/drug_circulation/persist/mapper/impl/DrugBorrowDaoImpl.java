package com.pulse.drug_circulation.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.drug_circulation.persist.dos.DrugBorrow;
import com.pulse.drug_circulation.persist.mapper.DrugBorrowDao;
import com.pulse.drug_circulation.persist.mapper.mybatis.DrugBorrowMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "cb39dbe7-ff76-38eb-965a-a1ce0f46ea2f|ENTITY|DAO")
public class DrugBorrowDaoImpl implements DrugBorrowDao {
    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowMapper drugBorrowMapper;

    @AutoGenerated(locked = true, uuid = "0ad8517d-87bc-3c6e-b86d-3519108402b2")
    @Override
    public List<DrugBorrow> getByStorageCode(String storageCode) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("storage_code", storageCode).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "28e7f591-f079-3765-a16c-75c01184ae8e")
    @Override
    public DrugBorrow getById(String id) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return drugBorrowMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "4765b634-3a52-3e51-bc59-fe79bb283b71")
    @Override
    public List<DrugBorrow> getByBorrowStaffIds(List<String> borrowStaffId) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("borrow_staff_id", borrowStaffId).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "4e28dd2b-0e46-3482-b4c0-9daad6ec0676")
    @Override
    public List<DrugBorrow> getByIds(List<String> id) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "661407f7-179c-3f5b-b44d-4ec1d0d7b6b9")
    @Override
    public List<DrugBorrow> getByApplyStaffIds(List<String> applyStaffId) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("apply_staff_id", applyStaffId).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a6a3092b-44dd-381b-87a2-d30590ef3324")
    @Override
    public List<DrugBorrow> getByBorrowId(String borrowId) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("borrow_id", borrowId).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "c1e3361d-0ca9-3650-8110-74225f968e86")
    @Override
    public List<DrugBorrow> getByBorrowStaffId(String borrowStaffId) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("borrow_staff_id", borrowStaffId).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "c1fade14-be31-3f0e-aef3-1ca626084162")
    @Override
    public List<DrugBorrow> getByBorrowIds(List<String> borrowId) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("borrow_id", borrowId).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "eace88e5-012a-3d56-94c5-369a07487b5b")
    @Override
    public List<DrugBorrow> getByApplyStaffId(String applyStaffId) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("apply_staff_id", applyStaffId).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f8997a18-b718-3e14-a3bb-bd1a06a64f2d")
    @Override
    public List<DrugBorrow> getByStorageCodes(List<String> storageCode) {
        QueryWrapper<DrugBorrow> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("storage_code", storageCode).orderByAsc("id");
        return drugBorrowMapper.selectList(queryWrapper);
    }
}
