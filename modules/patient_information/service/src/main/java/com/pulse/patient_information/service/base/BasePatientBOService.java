package com.pulse.patient_information.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.patient_information.manager.bo.*;
import com.pulse.patient_information.manager.bo.PatientBO;
import com.pulse.patient_information.persist.dos.Patient;
import com.pulse.patient_information.persist.dos.PatientExpressAddress;
import com.pulse.patient_information.persist.dos.PatientIdentification;
import com.pulse.patient_information.persist.dos.PatientMerge;
import com.pulse.patient_information.persist.dos.PatientProfile;
import com.pulse.patient_information.persist.dos.PatientRelationship;
import com.pulse.patient_information.service.base.BasePatientBOService.ChangePatientStatusBoResult;
import com.pulse.patient_information.service.base.BasePatientBOService.MergePatientBoResult;
import com.pulse.patient_information.service.base.BasePatientBOService.MergePatientMergeBoResult;
import com.pulse.patient_information.service.base.BasePatientBOService.MergePatientSpecialBoResult;
import com.pulse.patient_information.service.base.BasePatientBOService.UpdatePatientPhoneBoResult;
import com.pulse.patient_information.service.bto.ChangePatientStatusBto;
import com.pulse.patient_information.service.bto.MergePatientBto;
import com.pulse.patient_information.service.bto.MergePatientMergeBto;
import com.pulse.patient_information.service.bto.MergePatientSpecialBto;
import com.pulse.patient_information.service.bto.UpdatePatientPhoneBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "525a6a84-a0bf-3de7-923a-2b8e0343b703")
public class BasePatientBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 更新患者状态 */
    @AutoGenerated(locked = true)
    protected ChangePatientStatusBoResult changePatientStatusBase(
            ChangePatientStatusBto changePatientStatusBto) {
        if (changePatientStatusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangePatientStatusBoResult boResult = new ChangePatientStatusBoResult();
        PatientBO patientBO =
                updateChangePatientStatusOnMissThrowEx(boResult, changePatientStatusBto);
        boResult.setRootBo(patientBO);
        return boResult;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private PatientBO createMergePatientMergeOnDuplicateUpdate(
            MergePatientMergeBoResult boResult, MergePatientMergeBto mergePatientMergeBto) {
        PatientBO patientBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergePatientMergeBto.getId() == null);
        if (!allNull && !found) {
            patientBO = PatientBO.getById(mergePatientMergeBto.getId());
            if (patientBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (patientBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(patientBO.convertToPatient());
                updatedBto.setBto(mergePatientMergeBto);
                updatedBto.setBo(patientBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "idType")) {
                    patientBO.setIdType(mergePatientMergeBto.getIdType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "unknownType")) {
                    patientBO.setUnknownType(mergePatientMergeBto.getUnknownType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "mainAccountFlag")) {
                    patientBO.setMainAccountFlag(mergePatientMergeBto.getMainAccountFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "greenChannelFlag")) {
                    patientBO.setGreenChannelFlag(mergePatientMergeBto.getGreenChannelFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "publicFundedLevel")) {
                    patientBO.setPublicFundedLevel(mergePatientMergeBto.getPublicFundedLevel());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "childrenCoordinatedFlag")) {
                    patientBO.setChildrenCoordinatedFlag(
                            mergePatientMergeBto.getChildrenCoordinatedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "childrenCoordinatedValidDate")) {
                    patientBO.setChildrenCoordinatedValidDate(
                            mergePatientMergeBto.getChildrenCoordinatedValidDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "veteranFlag")) {
                    patientBO.setVeteranFlag(mergePatientMergeBto.getVeteranFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "modelWorkerFlag")) {
                    patientBO.setModelWorkerFlag(mergePatientMergeBto.getModelWorkerFlag());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(patientBO.convertToPatient());
                updatedBto.setBto(mergePatientMergeBto);
                updatedBto.setBo(patientBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "idType")) {
                    patientBO.setIdType(mergePatientMergeBto.getIdType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "unknownType")) {
                    patientBO.setUnknownType(mergePatientMergeBto.getUnknownType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "mainAccountFlag")) {
                    patientBO.setMainAccountFlag(mergePatientMergeBto.getMainAccountFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "greenChannelFlag")) {
                    patientBO.setGreenChannelFlag(mergePatientMergeBto.getGreenChannelFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "publicFundedLevel")) {
                    patientBO.setPublicFundedLevel(mergePatientMergeBto.getPublicFundedLevel());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "childrenCoordinatedFlag")) {
                    patientBO.setChildrenCoordinatedFlag(
                            mergePatientMergeBto.getChildrenCoordinatedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "childrenCoordinatedValidDate")) {
                    patientBO.setChildrenCoordinatedValidDate(
                            mergePatientMergeBto.getChildrenCoordinatedValidDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "veteranFlag")) {
                    patientBO.setVeteranFlag(mergePatientMergeBto.getVeteranFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientMergeBto, "__$validPropertySet"),
                        "modelWorkerFlag")) {
                    patientBO.setModelWorkerFlag(mergePatientMergeBto.getModelWorkerFlag());
                }
            }
        } else {
            patientBO = new PatientBO();
            if (pkExist) {
                patientBO.setId(mergePatientMergeBto.getId());
            } else {
                patientBO.setId(String.valueOf(this.idGenerator.allocateId("patient")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "idType")) {
                patientBO.setIdType(mergePatientMergeBto.getIdType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "unknownType")) {
                patientBO.setUnknownType(mergePatientMergeBto.getUnknownType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "mainAccountFlag")) {
                patientBO.setMainAccountFlag(mergePatientMergeBto.getMainAccountFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "greenChannelFlag")) {
                patientBO.setGreenChannelFlag(mergePatientMergeBto.getGreenChannelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "publicFundedLevel")) {
                patientBO.setPublicFundedLevel(mergePatientMergeBto.getPublicFundedLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "childrenCoordinatedFlag")) {
                patientBO.setChildrenCoordinatedFlag(
                        mergePatientMergeBto.getChildrenCoordinatedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "childrenCoordinatedValidDate")) {
                patientBO.setChildrenCoordinatedValidDate(
                        mergePatientMergeBto.getChildrenCoordinatedValidDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "veteranFlag")) {
                patientBO.setVeteranFlag(mergePatientMergeBto.getVeteranFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "modelWorkerFlag")) {
                patientBO.setModelWorkerFlag(mergePatientMergeBto.getModelWorkerFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergePatientMergeBto);
            addedBto.setBo(patientBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return patientBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private PatientBO createMergePatientOnDuplicateUpdate(
            MergePatientBoResult boResult, MergePatientBto mergePatientBto) {
        PatientBO patientBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergePatientBto.getId() == null);
        if (!allNull && !found) {
            patientBO = PatientBO.getById(mergePatientBto.getId());
            if (patientBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (patientBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(patientBO.convertToPatient());
                updatedBto.setBto(mergePatientBto);
                updatedBto.setBo(patientBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "displayId")) {
                    patientBO.setDisplayId(mergePatientBto.getDisplayId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "name")) {
                    patientBO.setName(mergePatientBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "nameInputCode")) {
                    patientBO.setNameInputCode(mergePatientBto.getNameInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "translateName")) {
                    patientBO.setTranslateName(mergePatientBto.getTranslateName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "publicFundedUnit")) {
                    patientBO.setPublicFundedUnit(mergePatientBto.getPublicFundedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "publicMedicalExpensesCertificateNumber")) {
                    patientBO.setPublicMedicalExpensesCertificateNumber(
                            mergePatientBto.getPublicMedicalExpensesCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "gender")) {
                    patientBO.setGender(mergePatientBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "idNumber")) {
                    patientBO.setIdNumber(mergePatientBto.getIdNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "insuranceTypeId")) {
                    patientBO.setInsuranceTypeId(mergePatientBto.getInsuranceTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "insuranceNumber")) {
                    patientBO.setInsuranceNumber(mergePatientBto.getInsuranceNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "insuranceCardNumber")) {
                    patientBO.setInsuranceCardNumber(mergePatientBto.getInsuranceCardNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "cellphone")) {
                    patientBO.setCellphone(mergePatientBto.getCellphone());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "phoneNumber")) {
                    patientBO.setPhoneNumber(mergePatientBto.getPhoneNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "defaultChargeType")) {
                    patientBO.setDefaultChargeType(mergePatientBto.getDefaultChargeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "identityCode")) {
                    patientBO.setIdentityCode(mergePatientBto.getIdentityCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "birthday")) {
                    patientBO.setBirthday(mergePatientBto.getBirthday());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "birthAddress")) {
                    patientBO.setBirthAddress(mergePatientBto.getBirthAddress());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "unknownFlag")) {
                    patientBO.setUnknownFlag(mergePatientBto.getUnknownFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "bloodCardFlag")) {
                    patientBO.setBloodCardFlag(mergePatientBto.getBloodCardFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "disabilityFlag")) {
                    patientBO.setDisabilityFlag(mergePatientBto.getDisabilityFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "vipFlag")) {
                    patientBO.setVipFlag(mergePatientBto.getVipFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "status")) {
                    patientBO.setStatus(mergePatientBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "avatar")) {
                    patientBO.setAvatar(mergePatientBto.getAvatar());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "commercialInsuranceFlag")) {
                    patientBO.setCommercialInsuranceFlag(
                            mergePatientBto.getCommercialInsuranceFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "createdBy")) {
                    patientBO.setCreatedBy(mergePatientBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "updatedBy")) {
                    patientBO.setUpdatedBy(mergePatientBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "idType")) {
                    patientBO.setIdType(mergePatientBto.getIdType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "unknownType")) {
                    patientBO.setUnknownType(mergePatientBto.getUnknownType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "mainAccountFlag")) {
                    patientBO.setMainAccountFlag(mergePatientBto.getMainAccountFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "greenChannelFlag")) {
                    patientBO.setGreenChannelFlag(mergePatientBto.getGreenChannelFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "publicFundedLevel")) {
                    patientBO.setPublicFundedLevel(mergePatientBto.getPublicFundedLevel());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "childrenCoordinatedFlag")) {
                    patientBO.setChildrenCoordinatedFlag(
                            mergePatientBto.getChildrenCoordinatedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "childrenCoordinatedValidDate")) {
                    patientBO.setChildrenCoordinatedValidDate(
                            mergePatientBto.getChildrenCoordinatedValidDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "veteranFlag")) {
                    patientBO.setVeteranFlag(mergePatientBto.getVeteranFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "modelWorkerFlag")) {
                    patientBO.setModelWorkerFlag(mergePatientBto.getModelWorkerFlag());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(patientBO.convertToPatient());
                updatedBto.setBto(mergePatientBto);
                updatedBto.setBo(patientBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "displayId")) {
                    patientBO.setDisplayId(mergePatientBto.getDisplayId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "name")) {
                    patientBO.setName(mergePatientBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "nameInputCode")) {
                    patientBO.setNameInputCode(mergePatientBto.getNameInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "translateName")) {
                    patientBO.setTranslateName(mergePatientBto.getTranslateName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "publicFundedUnit")) {
                    patientBO.setPublicFundedUnit(mergePatientBto.getPublicFundedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "publicMedicalExpensesCertificateNumber")) {
                    patientBO.setPublicMedicalExpensesCertificateNumber(
                            mergePatientBto.getPublicMedicalExpensesCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "gender")) {
                    patientBO.setGender(mergePatientBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "idNumber")) {
                    patientBO.setIdNumber(mergePatientBto.getIdNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "insuranceTypeId")) {
                    patientBO.setInsuranceTypeId(mergePatientBto.getInsuranceTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "insuranceNumber")) {
                    patientBO.setInsuranceNumber(mergePatientBto.getInsuranceNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "insuranceCardNumber")) {
                    patientBO.setInsuranceCardNumber(mergePatientBto.getInsuranceCardNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "cellphone")) {
                    patientBO.setCellphone(mergePatientBto.getCellphone());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "phoneNumber")) {
                    patientBO.setPhoneNumber(mergePatientBto.getPhoneNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "defaultChargeType")) {
                    patientBO.setDefaultChargeType(mergePatientBto.getDefaultChargeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "identityCode")) {
                    patientBO.setIdentityCode(mergePatientBto.getIdentityCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "birthday")) {
                    patientBO.setBirthday(mergePatientBto.getBirthday());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "birthAddress")) {
                    patientBO.setBirthAddress(mergePatientBto.getBirthAddress());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "unknownFlag")) {
                    patientBO.setUnknownFlag(mergePatientBto.getUnknownFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "bloodCardFlag")) {
                    patientBO.setBloodCardFlag(mergePatientBto.getBloodCardFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "disabilityFlag")) {
                    patientBO.setDisabilityFlag(mergePatientBto.getDisabilityFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "vipFlag")) {
                    patientBO.setVipFlag(mergePatientBto.getVipFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "status")) {
                    patientBO.setStatus(mergePatientBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "avatar")) {
                    patientBO.setAvatar(mergePatientBto.getAvatar());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "commercialInsuranceFlag")) {
                    patientBO.setCommercialInsuranceFlag(
                            mergePatientBto.getCommercialInsuranceFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "createdBy")) {
                    patientBO.setCreatedBy(mergePatientBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "updatedBy")) {
                    patientBO.setUpdatedBy(mergePatientBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "idType")) {
                    patientBO.setIdType(mergePatientBto.getIdType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "unknownType")) {
                    patientBO.setUnknownType(mergePatientBto.getUnknownType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "mainAccountFlag")) {
                    patientBO.setMainAccountFlag(mergePatientBto.getMainAccountFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "greenChannelFlag")) {
                    patientBO.setGreenChannelFlag(mergePatientBto.getGreenChannelFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "publicFundedLevel")) {
                    patientBO.setPublicFundedLevel(mergePatientBto.getPublicFundedLevel());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "childrenCoordinatedFlag")) {
                    patientBO.setChildrenCoordinatedFlag(
                            mergePatientBto.getChildrenCoordinatedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "childrenCoordinatedValidDate")) {
                    patientBO.setChildrenCoordinatedValidDate(
                            mergePatientBto.getChildrenCoordinatedValidDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "veteranFlag")) {
                    patientBO.setVeteranFlag(mergePatientBto.getVeteranFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                        "modelWorkerFlag")) {
                    patientBO.setModelWorkerFlag(mergePatientBto.getModelWorkerFlag());
                }
            }
        } else {
            patientBO = new PatientBO();
            if (pkExist) {
                patientBO.setId(mergePatientBto.getId());
            } else {
                patientBO.setId(String.valueOf(this.idGenerator.allocateId("patient")));
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "displayId")) {
                patientBO.setDisplayId(mergePatientBto.getDisplayId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "name")) {
                patientBO.setName(mergePatientBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "nameInputCode")) {
                patientBO.setNameInputCode(mergePatientBto.getNameInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "translateName")) {
                patientBO.setTranslateName(mergePatientBto.getTranslateName());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "publicFundedUnit")) {
                patientBO.setPublicFundedUnit(mergePatientBto.getPublicFundedUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "publicMedicalExpensesCertificateNumber")) {
                patientBO.setPublicMedicalExpensesCertificateNumber(
                        mergePatientBto.getPublicMedicalExpensesCertificateNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "gender")) {
                patientBO.setGender(mergePatientBto.getGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "idNumber")) {
                patientBO.setIdNumber(mergePatientBto.getIdNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "insuranceTypeId")) {
                patientBO.setInsuranceTypeId(mergePatientBto.getInsuranceTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "insuranceNumber")) {
                patientBO.setInsuranceNumber(mergePatientBto.getInsuranceNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "insuranceCardNumber")) {
                patientBO.setInsuranceCardNumber(mergePatientBto.getInsuranceCardNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "cellphone")) {
                patientBO.setCellphone(mergePatientBto.getCellphone());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "phoneNumber")) {
                patientBO.setPhoneNumber(mergePatientBto.getPhoneNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "defaultChargeType")) {
                patientBO.setDefaultChargeType(mergePatientBto.getDefaultChargeType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "identityCode")) {
                patientBO.setIdentityCode(mergePatientBto.getIdentityCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "birthday")) {
                patientBO.setBirthday(mergePatientBto.getBirthday());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "birthAddress")) {
                patientBO.setBirthAddress(mergePatientBto.getBirthAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "unknownFlag")) {
                patientBO.setUnknownFlag(mergePatientBto.getUnknownFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "bloodCardFlag")) {
                patientBO.setBloodCardFlag(mergePatientBto.getBloodCardFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "disabilityFlag")) {
                patientBO.setDisabilityFlag(mergePatientBto.getDisabilityFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "vipFlag")) {
                patientBO.setVipFlag(mergePatientBto.getVipFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "status")) {
                patientBO.setStatus(mergePatientBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "avatar")) {
                patientBO.setAvatar(mergePatientBto.getAvatar());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "commercialInsuranceFlag")) {
                patientBO.setCommercialInsuranceFlag(mergePatientBto.getCommercialInsuranceFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "createdBy")) {
                patientBO.setCreatedBy(mergePatientBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "updatedBy")) {
                patientBO.setUpdatedBy(mergePatientBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "idType")) {
                patientBO.setIdType(mergePatientBto.getIdType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "unknownType")) {
                patientBO.setUnknownType(mergePatientBto.getUnknownType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "mainAccountFlag")) {
                patientBO.setMainAccountFlag(mergePatientBto.getMainAccountFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "greenChannelFlag")) {
                patientBO.setGreenChannelFlag(mergePatientBto.getGreenChannelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "publicFundedLevel")) {
                patientBO.setPublicFundedLevel(mergePatientBto.getPublicFundedLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "childrenCoordinatedFlag")) {
                patientBO.setChildrenCoordinatedFlag(mergePatientBto.getChildrenCoordinatedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "childrenCoordinatedValidDate")) {
                patientBO.setChildrenCoordinatedValidDate(
                        mergePatientBto.getChildrenCoordinatedValidDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "veteranFlag")) {
                patientBO.setVeteranFlag(mergePatientBto.getVeteranFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "modelWorkerFlag")) {
                patientBO.setModelWorkerFlag(mergePatientBto.getModelWorkerFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergePatientBto);
            addedBto.setBo(patientBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return patientBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private PatientBO createMergePatientSpecialOnDuplicateUpdate(
            MergePatientSpecialBoResult boResult, MergePatientSpecialBto mergePatientSpecialBto) {
        PatientBO patientBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergePatientSpecialBto.getId() == null);
        if (!allNull && !found) {
            patientBO = PatientBO.getById(mergePatientSpecialBto.getId());
            if (patientBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (patientBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(patientBO.convertToPatient());
                updatedBto.setBto(mergePatientSpecialBto);
                updatedBto.setBo(patientBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "displayId")) {
                    patientBO.setDisplayId(mergePatientSpecialBto.getDisplayId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "name")) {
                    patientBO.setName(mergePatientSpecialBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "nameInputCode")) {
                    patientBO.setNameInputCode(mergePatientSpecialBto.getNameInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "publicFundedUnit")) {
                    patientBO.setPublicFundedUnit(mergePatientSpecialBto.getPublicFundedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "publicMedicalExpensesCertificateNumber")) {
                    patientBO.setPublicMedicalExpensesCertificateNumber(
                            mergePatientSpecialBto.getPublicMedicalExpensesCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "gender")) {
                    patientBO.setGender(mergePatientSpecialBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "idType")) {
                    patientBO.setIdType(mergePatientSpecialBto.getIdType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "idNumber")) {
                    patientBO.setIdNumber(mergePatientSpecialBto.getIdNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "defaultChargeType")) {
                    patientBO.setDefaultChargeType(mergePatientSpecialBto.getDefaultChargeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "birthday")) {
                    patientBO.setBirthday(mergePatientSpecialBto.getBirthday());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "createdBy")) {
                    patientBO.setCreatedBy(mergePatientSpecialBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "updatedBy")) {
                    patientBO.setUpdatedBy(mergePatientSpecialBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "publicFundedLevel")) {
                    patientBO.setPublicFundedLevel(mergePatientSpecialBto.getPublicFundedLevel());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "childrenCoordinatedFlag")) {
                    patientBO.setChildrenCoordinatedFlag(
                            mergePatientSpecialBto.getChildrenCoordinatedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "childrenCoordinatedValidDate")) {
                    patientBO.setChildrenCoordinatedValidDate(
                            mergePatientSpecialBto.getChildrenCoordinatedValidDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "veteranFlag")) {
                    patientBO.setVeteranFlag(mergePatientSpecialBto.getVeteranFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "modelWorkerFlag")) {
                    patientBO.setModelWorkerFlag(mergePatientSpecialBto.getModelWorkerFlag());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(patientBO.convertToPatient());
                updatedBto.setBto(mergePatientSpecialBto);
                updatedBto.setBo(patientBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "displayId")) {
                    patientBO.setDisplayId(mergePatientSpecialBto.getDisplayId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "name")) {
                    patientBO.setName(mergePatientSpecialBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "nameInputCode")) {
                    patientBO.setNameInputCode(mergePatientSpecialBto.getNameInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "publicFundedUnit")) {
                    patientBO.setPublicFundedUnit(mergePatientSpecialBto.getPublicFundedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "publicMedicalExpensesCertificateNumber")) {
                    patientBO.setPublicMedicalExpensesCertificateNumber(
                            mergePatientSpecialBto.getPublicMedicalExpensesCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "gender")) {
                    patientBO.setGender(mergePatientSpecialBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "idType")) {
                    patientBO.setIdType(mergePatientSpecialBto.getIdType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "idNumber")) {
                    patientBO.setIdNumber(mergePatientSpecialBto.getIdNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "defaultChargeType")) {
                    patientBO.setDefaultChargeType(mergePatientSpecialBto.getDefaultChargeType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "birthday")) {
                    patientBO.setBirthday(mergePatientSpecialBto.getBirthday());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "createdBy")) {
                    patientBO.setCreatedBy(mergePatientSpecialBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "updatedBy")) {
                    patientBO.setUpdatedBy(mergePatientSpecialBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "publicFundedLevel")) {
                    patientBO.setPublicFundedLevel(mergePatientSpecialBto.getPublicFundedLevel());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "childrenCoordinatedFlag")) {
                    patientBO.setChildrenCoordinatedFlag(
                            mergePatientSpecialBto.getChildrenCoordinatedFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "childrenCoordinatedValidDate")) {
                    patientBO.setChildrenCoordinatedValidDate(
                            mergePatientSpecialBto.getChildrenCoordinatedValidDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "veteranFlag")) {
                    patientBO.setVeteranFlag(mergePatientSpecialBto.getVeteranFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergePatientSpecialBto, "__$validPropertySet"),
                        "modelWorkerFlag")) {
                    patientBO.setModelWorkerFlag(mergePatientSpecialBto.getModelWorkerFlag());
                }
            }
        } else {
            patientBO = new PatientBO();
            if (pkExist) {
                patientBO.setId(mergePatientSpecialBto.getId());
            } else {
                patientBO.setId(String.valueOf(this.idGenerator.allocateId("patient")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "displayId")) {
                patientBO.setDisplayId(mergePatientSpecialBto.getDisplayId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "name")) {
                patientBO.setName(mergePatientSpecialBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "nameInputCode")) {
                patientBO.setNameInputCode(mergePatientSpecialBto.getNameInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "publicFundedUnit")) {
                patientBO.setPublicFundedUnit(mergePatientSpecialBto.getPublicFundedUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "publicMedicalExpensesCertificateNumber")) {
                patientBO.setPublicMedicalExpensesCertificateNumber(
                        mergePatientSpecialBto.getPublicMedicalExpensesCertificateNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "gender")) {
                patientBO.setGender(mergePatientSpecialBto.getGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "idType")) {
                patientBO.setIdType(mergePatientSpecialBto.getIdType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "idNumber")) {
                patientBO.setIdNumber(mergePatientSpecialBto.getIdNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "defaultChargeType")) {
                patientBO.setDefaultChargeType(mergePatientSpecialBto.getDefaultChargeType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "birthday")) {
                patientBO.setBirthday(mergePatientSpecialBto.getBirthday());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "createdBy")) {
                patientBO.setCreatedBy(mergePatientSpecialBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "updatedBy")) {
                patientBO.setUpdatedBy(mergePatientSpecialBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "publicFundedLevel")) {
                patientBO.setPublicFundedLevel(mergePatientSpecialBto.getPublicFundedLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "childrenCoordinatedFlag")) {
                patientBO.setChildrenCoordinatedFlag(
                        mergePatientSpecialBto.getChildrenCoordinatedFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "childrenCoordinatedValidDate")) {
                patientBO.setChildrenCoordinatedValidDate(
                        mergePatientSpecialBto.getChildrenCoordinatedValidDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "veteranFlag")) {
                patientBO.setVeteranFlag(mergePatientSpecialBto.getVeteranFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "modelWorkerFlag")) {
                patientBO.setModelWorkerFlag(mergePatientSpecialBto.getModelWorkerFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergePatientSpecialBto);
            addedBto.setBo(patientBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return patientBO;
    }

    /** 创建对象:PatientExpressAddressBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientExpressAddressBtoOnDuplicateUpdate(
            MergePatientBoResult boResult, MergePatientBto mergePatientBto, PatientBO patientBO) {
        if (CollectionUtil.isEmpty(mergePatientBto.getPatientExpressAddressBtoList())) {
            mergePatientBto.setPatientExpressAddressBtoList(List.of());
        }
        patientBO
                .getPatientExpressAddressBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergePatientBto.getPatientExpressAddressBtoList().stream()
                                            .filter(
                                                    patientExpressAddressBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (patientExpressAddressBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            patientExpressAddressBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToPatientExpressAddress());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergePatientBto.getPatientExpressAddressBtoList())) {
            for (MergePatientBto.PatientExpressAddressBto item :
                    mergePatientBto.getPatientExpressAddressBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<PatientExpressAddressBO> any =
                        patientBO.getPatientExpressAddressBOSet().stream()
                                .filter(
                                        patientExpressAddressBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                patientExpressAddressBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        PatientExpressAddressBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientExpressAddress());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "consignee")) {
                            bo.setConsignee(item.getConsignee());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expressAddress")) {
                            bo.setExpressAddress(item.getExpressAddress());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "phoneNumber")) {
                            bo.setPhoneNumber(item.getPhoneNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultIndicator")) {
                            bo.setDefaultIndicator(item.getDefaultIndicator());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "label")) {
                            bo.setLabel(item.getLabel());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "lastUsageTime")) {
                            bo.setLastUsageTime(item.getLastUsageTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        PatientExpressAddressBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientExpressAddress());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "consignee")) {
                            bo.setConsignee(item.getConsignee());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expressAddress")) {
                            bo.setExpressAddress(item.getExpressAddress());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "phoneNumber")) {
                            bo.setPhoneNumber(item.getPhoneNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultIndicator")) {
                            bo.setDefaultIndicator(item.getDefaultIndicator());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "label")) {
                            bo.setLabel(item.getLabel());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "lastUsageTime")) {
                            bo.setLastUsageTime(item.getLastUsageTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    PatientExpressAddressBO subBo = new PatientExpressAddressBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "consignee")) {
                        subBo.setConsignee(item.getConsignee());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expressAddress")) {
                        subBo.setExpressAddress(item.getExpressAddress());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "phoneNumber")) {
                        subBo.setPhoneNumber(item.getPhoneNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "defaultIndicator")) {
                        subBo.setDefaultIndicator(item.getDefaultIndicator());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "label")) {
                        subBo.setLabel(item.getLabel());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "lastUsageTime")) {
                        subBo.setLastUsageTime(item.getLastUsageTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setPatientBO(patientBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("patient_express_address")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    patientBO.getPatientExpressAddressBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:PatientIdentificationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientIdentificationBtoOnDuplicateUpdate(
            MergePatientBoResult boResult, MergePatientBto mergePatientBto, PatientBO patientBO) {
        if (CollectionUtil.isEmpty(mergePatientBto.getPatientIdentificationBtoList())) {
            mergePatientBto.setPatientIdentificationBtoList(List.of());
        }
        patientBO
                .getPatientIdentificationBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergePatientBto.getPatientIdentificationBtoList().stream()
                                            .filter(
                                                    patientIdentificationBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (patientIdentificationBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            patientIdentificationBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        allNull =
                                                                (patientIdentificationBtoList
                                                                                        .getIdentificationClass()
                                                                                == null)
                                                                        && (patientIdentificationBtoList
                                                                                        .getIdentificationNumber()
                                                                                == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                                    item
                                                                                            .getIdentificationClass(),
                                                                                    patientIdentificationBtoList
                                                                                            .getIdentificationClass())
                                                                            && BoUtil.equals(
                                                                                    item
                                                                                            .getIdentificationNumber(),
                                                                                    patientIdentificationBtoList
                                                                                            .getIdentificationNumber());
                                                            if (found) {
                                                                return found;
                                                            }
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToPatientIdentification());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergePatientBto.getPatientIdentificationBtoList())) {
            for (MergePatientBto.PatientIdentificationBto item :
                    mergePatientBto.getPatientIdentificationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<PatientIdentificationBO> any =
                        patientBO.getPatientIdentificationBOSet().stream()
                                .filter(
                                        patientIdentificationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                patientIdentificationBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull =
                                                    (item.getIdentificationClass() == null)
                                                            && (item.getIdentificationNumber()
                                                                    == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                        patientIdentificationBOSet
                                                                                .getIdentificationClass(),
                                                                        item
                                                                                .getIdentificationClass())
                                                                && BoUtil.equals(
                                                                        patientIdentificationBOSet
                                                                                .getIdentificationNumber(),
                                                                        item
                                                                                .getIdentificationNumber());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'identification_class'";
                                                    uk += ",";
                                                    uk += "'identification_number'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        PatientIdentificationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientIdentification());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationClass")) {
                            bo.setIdentificationClass(item.getIdentificationClass());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationNumber")) {
                            bo.setIdentificationNumber(item.getIdentificationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updateBy")) {
                            bo.setUpdateBy(item.getUpdateBy());
                        }
                    } else {
                        PatientIdentificationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientIdentification());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationClass")) {
                            bo.setIdentificationClass(item.getIdentificationClass());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationNumber")) {
                            bo.setIdentificationNumber(item.getIdentificationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updateBy")) {
                            bo.setUpdateBy(item.getUpdateBy());
                        }
                    }
                } else {
                    PatientIdentificationBO subBo = new PatientIdentificationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "identificationClass")) {
                        subBo.setIdentificationClass(item.getIdentificationClass());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "identificationNumber")) {
                        subBo.setIdentificationNumber(item.getIdentificationNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updateBy")) {
                        subBo.setUpdateBy(item.getUpdateBy());
                    }
                    subBo.setPatientBO(patientBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("patient_identification")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    patientBO.getPatientIdentificationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:PatientIdentificationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientIdentificationBtoOnDuplicateUpdate(
            MergePatientSpecialBoResult boResult,
            MergePatientSpecialBto mergePatientSpecialBto,
            PatientBO patientBO) {
        if (CollectionUtil.isNotEmpty(mergePatientSpecialBto.getPatientIdentificationBtoList())) {
            for (MergePatientSpecialBto.PatientIdentificationBto item :
                    mergePatientSpecialBto.getPatientIdentificationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<PatientIdentificationBO> any =
                        patientBO.getPatientIdentificationBOSet().stream()
                                .filter(
                                        patientIdentificationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                patientIdentificationBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull =
                                                    (item.getIdentificationClass() == null)
                                                            && (item.getIdentificationNumber()
                                                                    == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                        patientIdentificationBOSet
                                                                                .getIdentificationClass(),
                                                                        item
                                                                                .getIdentificationClass())
                                                                && BoUtil.equals(
                                                                        patientIdentificationBOSet
                                                                                .getIdentificationNumber(),
                                                                        item
                                                                                .getIdentificationNumber());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'identification_class'";
                                                    uk += ",";
                                                    uk += "'identification_number'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        PatientIdentificationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientIdentification());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationClass")) {
                            bo.setIdentificationClass(item.getIdentificationClass());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationNumber")) {
                            bo.setIdentificationNumber(item.getIdentificationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updateBy")) {
                            bo.setUpdateBy(item.getUpdateBy());
                        }
                    } else {
                        PatientIdentificationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientIdentification());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationClass")) {
                            bo.setIdentificationClass(item.getIdentificationClass());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationNumber")) {
                            bo.setIdentificationNumber(item.getIdentificationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updateBy")) {
                            bo.setUpdateBy(item.getUpdateBy());
                        }
                    }
                } else {
                    PatientIdentificationBO subBo = new PatientIdentificationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "identificationClass")) {
                        subBo.setIdentificationClass(item.getIdentificationClass());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "identificationNumber")) {
                        subBo.setIdentificationNumber(item.getIdentificationNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updateBy")) {
                        subBo.setUpdateBy(item.getUpdateBy());
                    }
                    subBo.setPatientBO(patientBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("patient_identification")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    patientBO.getPatientIdentificationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:PatientMergeBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientMergeBtoOnDuplicateUpdate(
            MergePatientBoResult boResult, MergePatientBto mergePatientBto, PatientBO patientBO) {
        if (CollectionUtil.isEmpty(mergePatientBto.getPatientMergeBtoList())) {
            mergePatientBto.setPatientMergeBtoList(List.of());
        }
        patientBO
                .getPatientMergeBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergePatientBto.getPatientMergeBtoList().stream()
                                            .filter(
                                                    patientMergeBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (patientMergeBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            patientMergeBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToPatientMerge());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergePatientBto.getPatientMergeBtoList())) {
            for (MergePatientBto.PatientMergeBto item : mergePatientBto.getPatientMergeBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<PatientMergeBO> any =
                        patientBO.getPatientMergeBOSet().stream()
                                .filter(
                                        patientMergeBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                patientMergeBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        PatientMergeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientMerge());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelated")) {
                            bo.setPatientRelated(item.getPatientRelated());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRetainDisplay")) {
                            bo.setPatientRetainDisplay(item.getPatientRetainDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelatedDisplay")) {
                            bo.setPatientRelatedDisplay(item.getPatientRelatedDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        PatientMergeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientMerge());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelated")) {
                            bo.setPatientRelated(item.getPatientRelated());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRetainDisplay")) {
                            bo.setPatientRetainDisplay(item.getPatientRetainDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelatedDisplay")) {
                            bo.setPatientRelatedDisplay(item.getPatientRelatedDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    PatientMergeBO subBo = new PatientMergeBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "patientRelated")) {
                        subBo.setPatientRelated(item.getPatientRelated());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "patientRetainDisplay")) {
                        subBo.setPatientRetainDisplay(item.getPatientRetainDisplay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "patientRelatedDisplay")) {
                        subBo.setPatientRelatedDisplay(item.getPatientRelatedDisplay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setPatientBO(patientBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("patient_merge")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    patientBO.getPatientMergeBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:PatientMergeBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientMergeBtoOnDuplicateUpdate(
            BasePatientBOService.MergePatientMergeBoResult boResult,
            MergePatientMergeBto mergePatientMergeBto,
            PatientBO patientBO) {
        if (CollectionUtil.isEmpty(mergePatientMergeBto.getPatientMergeBtoList())) {
            mergePatientMergeBto.setPatientMergeBtoList(List.of());
        }
        patientBO
                .getPatientMergeBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergePatientMergeBto.getPatientMergeBtoList().stream()
                                            .filter(
                                                    patientMergeBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (patientMergeBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            patientMergeBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToPatientMerge());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergePatientMergeBto.getPatientMergeBtoList())) {
            for (MergePatientMergeBto.PatientMergeBto item :
                    mergePatientMergeBto.getPatientMergeBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<PatientMergeBO> any =
                        patientBO.getPatientMergeBOSet().stream()
                                .filter(
                                        patientMergeBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                patientMergeBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        PatientMergeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientMerge());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelated")) {
                            bo.setPatientRelated(item.getPatientRelated());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRetainDisplay")) {
                            bo.setPatientRetainDisplay(item.getPatientRetainDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelatedDisplay")) {
                            bo.setPatientRelatedDisplay(item.getPatientRelatedDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        PatientMergeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientMerge());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelated")) {
                            bo.setPatientRelated(item.getPatientRelated());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRetainDisplay")) {
                            bo.setPatientRetainDisplay(item.getPatientRetainDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "patientRelatedDisplay")) {
                            bo.setPatientRelatedDisplay(item.getPatientRelatedDisplay());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    PatientMergeBO subBo = new PatientMergeBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "patientRelated")) {
                        subBo.setPatientRelated(item.getPatientRelated());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "patientRetainDisplay")) {
                        subBo.setPatientRetainDisplay(item.getPatientRetainDisplay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "patientRelatedDisplay")) {
                        subBo.setPatientRelatedDisplay(item.getPatientRelatedDisplay());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setPatientBO(patientBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("patient_merge")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    patientBO.getPatientMergeBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:PatientProfileBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientProfileBtoOnDuplicateUpdate(
            BasePatientBOService.MergePatientBoResult boResult,
            MergePatientBto mergePatientBto,
            PatientBO patientBO) {
        if (patientBO.getPatientProfileBO() != null) {
            PatientProfileBO bo = patientBO.getOrCreatePatientProfileBO();
            MergePatientBto.PatientProfileBto bto = mergePatientBto.getPatientProfileBto();
            if (bto == null) {
                PatientProfile deletedItem =
                        patientBO.getPatientProfileBO().convertToPatientProfile();
                boResult.getDeletedList().add(deletedItem);
                patientBO.setPatientProfileBO(null);
                return;
            }
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setBo(bo);
            updatedBto.setEntity(bo.convertToPatientProfile());
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "remark")) {
                bo.setRemark(bto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "citizenship")) {
                bo.setCitizenship(bto.getCitizenship());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "nation")) {
                bo.setNation(bto.getNation());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "marriageStatus")) {
                bo.setMarriageStatus(bto.getMarriageStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"), "job")) {
                bo.setJob(bto.getJob());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "nativeAddress")) {
                bo.setNativeAddress(bto.getNativeAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"), "email")) {
                bo.setEmail(bto.getEmail());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "currentAddress")) {
                bo.setCurrentAddress(bto.getCurrentAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "registeredResidenceAddress")) {
                bo.setRegisteredResidenceAddress(bto.getRegisteredResidenceAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "workUnit")) {
                bo.setWorkUnit(bto.getWorkUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "unitInContract")) {
                bo.setUnitInContract(bto.getUnitInContract());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "securityLevel")) {
                bo.setSecurityLevel(bto.getSecurityLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "wechatAccount")) {
                bo.setWechatAccount(bto.getWechatAccount());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "userAccount")) {
                bo.setUserAccount(bto.getUserAccount());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "policeStationName")) {
                bo.setPoliceStationName(bto.getPoliceStationName());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "externalPlatformId")) {
                bo.setExternalPlatformId(bto.getExternalPlatformId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "specialMedicalRecordNumber")) {
                bo.setSpecialMedicalRecordNumber(bto.getSpecialMedicalRecordNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "specialMedicalEndDate")) {
                bo.setSpecialMedicalEndDate(bto.getSpecialMedicalEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "educationLevel")) {
                bo.setEducationLevel(bto.getEducationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "patientCaseSource")) {
                bo.setPatientCaseSource(bto.getPatientCaseSource());
            }
            boResult.getUpdatedList().add(updatedBto);
        } else {
            if (mergePatientBto.getPatientProfileBto() == null) {
                return;
            }
            PatientProfileBO patientProfileBO = patientBO.getOrCreatePatientProfileBO();
            MergePatientBto.PatientProfileBto patientProfileBto =
                    mergePatientBto.getPatientProfileBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "remark")) {
                patientProfileBO.setRemark(mergePatientBto.getPatientProfileBto().getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "citizenship")) {
                patientProfileBO.setCitizenship(
                        mergePatientBto.getPatientProfileBto().getCitizenship());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "nation")) {
                patientProfileBO.setNation(mergePatientBto.getPatientProfileBto().getNation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "marriageStatus")) {
                patientProfileBO.setMarriageStatus(
                        mergePatientBto.getPatientProfileBto().getMarriageStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "job")) {
                patientProfileBO.setJob(mergePatientBto.getPatientProfileBto().getJob());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "nativeAddress")) {
                patientProfileBO.setNativeAddress(
                        mergePatientBto.getPatientProfileBto().getNativeAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "email")) {
                patientProfileBO.setEmail(mergePatientBto.getPatientProfileBto().getEmail());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "currentAddress")) {
                patientProfileBO.setCurrentAddress(
                        mergePatientBto.getPatientProfileBto().getCurrentAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "registeredResidenceAddress")) {
                patientProfileBO.setRegisteredResidenceAddress(
                        mergePatientBto.getPatientProfileBto().getRegisteredResidenceAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "workUnit")) {
                patientProfileBO.setWorkUnit(mergePatientBto.getPatientProfileBto().getWorkUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "unitInContract")) {
                patientProfileBO.setUnitInContract(
                        mergePatientBto.getPatientProfileBto().getUnitInContract());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "securityLevel")) {
                patientProfileBO.setSecurityLevel(
                        mergePatientBto.getPatientProfileBto().getSecurityLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "wechatAccount")) {
                patientProfileBO.setWechatAccount(
                        mergePatientBto.getPatientProfileBto().getWechatAccount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "userAccount")) {
                patientProfileBO.setUserAccount(
                        mergePatientBto.getPatientProfileBto().getUserAccount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "policeStationName")) {
                patientProfileBO.setPoliceStationName(
                        mergePatientBto.getPatientProfileBto().getPoliceStationName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "externalPlatformId")) {
                patientProfileBO.setExternalPlatformId(
                        mergePatientBto.getPatientProfileBto().getExternalPlatformId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "specialMedicalRecordNumber")) {
                patientProfileBO.setSpecialMedicalRecordNumber(
                        mergePatientBto.getPatientProfileBto().getSpecialMedicalRecordNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "specialMedicalEndDate")) {
                patientProfileBO.setSpecialMedicalEndDate(
                        mergePatientBto.getPatientProfileBto().getSpecialMedicalEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "educationLevel")) {
                patientProfileBO.setEducationLevel(
                        mergePatientBto.getPatientProfileBto().getEducationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "patientCaseSource")) {
                patientProfileBO.setPatientCaseSource(
                        mergePatientBto.getPatientProfileBto().getPatientCaseSource());
            }

            patientProfileBO.setId(String.valueOf(this.idGenerator.allocateId("patient_profile")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(patientProfileBto);
            addedBto.setBo(patientProfileBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:PatientProfileBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientProfileBtoOnDuplicateUpdate(
            BasePatientBOService.MergePatientSpecialBoResult boResult,
            MergePatientSpecialBto mergePatientSpecialBto,
            PatientBO patientBO) {
        if (patientBO.getPatientProfileBO() != null) {
            PatientProfileBO bo = patientBO.getOrCreatePatientProfileBO();
            MergePatientSpecialBto.PatientProfileBto bto =
                    mergePatientSpecialBto.getPatientProfileBto();
            if (bto == null) {
                PatientProfile deletedItem =
                        patientBO.getPatientProfileBO().convertToPatientProfile();
                boResult.getDeletedList().add(deletedItem);
                patientBO.setPatientProfileBO(null);
                return;
            }
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setBo(bo);
            updatedBto.setEntity(bo.convertToPatientProfile());
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "remark")) {
                bo.setRemark(bto.getRemark());
            }
            boResult.getUpdatedList().add(updatedBto);
        } else {
            if (mergePatientSpecialBto.getPatientProfileBto() == null) {
                return;
            }
            PatientProfileBO patientProfileBO = patientBO.getOrCreatePatientProfileBO();
            MergePatientSpecialBto.PatientProfileBto patientProfileBto =
                    mergePatientSpecialBto.getPatientProfileBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(patientProfileBto, "__$validPropertySet"),
                    "remark")) {
                patientProfileBO.setRemark(
                        mergePatientSpecialBto.getPatientProfileBto().getRemark());
            }

            patientProfileBO.setId(String.valueOf(this.idGenerator.allocateId("patient_profile")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(patientProfileBto);
            addedBto.setBo(patientProfileBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:PatientRelationshipBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createPatientRelationshipBtoOnDuplicateUpdate(
            MergePatientBoResult boResult, MergePatientBto mergePatientBto, PatientBO patientBO) {
        if (CollectionUtil.isEmpty(mergePatientBto.getPatientRelationshipBtoList())) {
            mergePatientBto.setPatientRelationshipBtoList(List.of());
        }
        patientBO
                .getPatientRelationshipBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergePatientBto.getPatientRelationshipBtoList().stream()
                                            .filter(
                                                    patientRelationshipBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (patientRelationshipBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            patientRelationshipBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToPatientRelationship());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergePatientBto.getPatientRelationshipBtoList())) {
            for (MergePatientBto.PatientRelationshipBto item :
                    mergePatientBto.getPatientRelationshipBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<PatientRelationshipBO> any =
                        patientBO.getPatientRelationshipBOSet().stream()
                                .filter(
                                        patientRelationshipBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                patientRelationshipBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        PatientRelationshipBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientRelationship());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "wechatAccount")) {
                            bo.setWechatAccount(item.getWechatAccount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "name")) {
                            bo.setName(item.getName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "relationshipType")) {
                            bo.setRelationshipType(item.getRelationshipType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationClass")) {
                            bo.setIdentificationClass(item.getIdentificationClass());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationNumber")) {
                            bo.setIdentificationNumber(item.getIdentificationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "phoneNumber")) {
                            bo.setPhoneNumber(item.getPhoneNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "guardianFlag")) {
                            bo.setGuardianFlag(item.getGuardianFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "address")) {
                            bo.setAddress(item.getAddress());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "familyDisplayId")) {
                            bo.setFamilyDisplayId(item.getFamilyDisplayId());
                        }
                    } else {
                        PatientRelationshipBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToPatientRelationship());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "wechatAccount")) {
                            bo.setWechatAccount(item.getWechatAccount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "name")) {
                            bo.setName(item.getName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "relationshipType")) {
                            bo.setRelationshipType(item.getRelationshipType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationClass")) {
                            bo.setIdentificationClass(item.getIdentificationClass());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "identificationNumber")) {
                            bo.setIdentificationNumber(item.getIdentificationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "phoneNumber")) {
                            bo.setPhoneNumber(item.getPhoneNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "guardianFlag")) {
                            bo.setGuardianFlag(item.getGuardianFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "address")) {
                            bo.setAddress(item.getAddress());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "familyDisplayId")) {
                            bo.setFamilyDisplayId(item.getFamilyDisplayId());
                        }
                    }
                } else {
                    PatientRelationshipBO subBo = new PatientRelationshipBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "wechatAccount")) {
                        subBo.setWechatAccount(item.getWechatAccount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "name")) {
                        subBo.setName(item.getName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "relationshipType")) {
                        subBo.setRelationshipType(item.getRelationshipType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "identificationClass")) {
                        subBo.setIdentificationClass(item.getIdentificationClass());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "identificationNumber")) {
                        subBo.setIdentificationNumber(item.getIdentificationNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "phoneNumber")) {
                        subBo.setPhoneNumber(item.getPhoneNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "guardianFlag")) {
                        subBo.setGuardianFlag(item.getGuardianFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "address")) {
                        subBo.setAddress(item.getAddress());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "familyDisplayId")) {
                        subBo.setFamilyDisplayId(item.getFamilyDisplayId());
                    }
                    subBo.setPatientBO(patientBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("patient_relationship")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    patientBO.getPatientRelationshipBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 保存病人信息 */
    @AutoGenerated(locked = true)
    protected MergePatientBoResult mergePatientBase(MergePatientBto mergePatientBto) {
        if (mergePatientBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergePatientBoResult boResult = new MergePatientBoResult();
        PatientBO patientBO = createMergePatientOnDuplicateUpdate(boResult, mergePatientBto);
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "patientProfileBto")) {
                createPatientProfileBtoOnDuplicateUpdate(boResult, mergePatientBto, patientBO);
            }
        }
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "patientIdentificationBtoList")) {
                createPatientIdentificationBtoOnDuplicateUpdate(
                        boResult, mergePatientBto, patientBO);
            }
        }
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "patientRelationshipBtoList")) {
                createPatientRelationshipBtoOnDuplicateUpdate(boResult, mergePatientBto, patientBO);
            }
        }
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "patientExpressAddressBtoList")) {
                createPatientExpressAddressBtoOnDuplicateUpdate(
                        boResult, mergePatientBto, patientBO);
            }
        }
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergePatientBto, "__$validPropertySet"),
                    "patientMergeBtoList")) {
                createPatientMergeBtoOnDuplicateUpdate(boResult, mergePatientBto, patientBO);
            }
        }
        boResult.setRootBo(patientBO);
        return boResult;
    }

    /** 保存患者合并信息 */
    @AutoGenerated(locked = true)
    protected MergePatientMergeBoResult mergePatientMergeBase(
            MergePatientMergeBto mergePatientMergeBto) {
        if (mergePatientMergeBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergePatientMergeBoResult boResult = new MergePatientMergeBoResult();
        PatientBO patientBO =
                createMergePatientMergeOnDuplicateUpdate(boResult, mergePatientMergeBto);
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergePatientMergeBto, "__$validPropertySet"),
                    "patientMergeBtoList")) {
                createPatientMergeBtoOnDuplicateUpdate(boResult, mergePatientMergeBto, patientBO);
            }
        }
        boResult.setRootBo(patientBO);
        return boResult;
    }

    /** 保存特殊患者 */
    @AutoGenerated(locked = true)
    protected MergePatientSpecialBoResult mergePatientSpecialBase(
            MergePatientSpecialBto mergePatientSpecialBto) {
        if (mergePatientSpecialBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergePatientSpecialBoResult boResult = new MergePatientSpecialBoResult();
        PatientBO patientBO =
                createMergePatientSpecialOnDuplicateUpdate(boResult, mergePatientSpecialBto);
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "patientProfileBto")) {
                createPatientProfileBtoOnDuplicateUpdate(
                        boResult, mergePatientSpecialBto, patientBO);
            }
        }
        if (patientBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergePatientSpecialBto, "__$validPropertySet"),
                    "patientIdentificationBtoList")) {
                createPatientIdentificationBtoOnDuplicateUpdate(
                        boResult, mergePatientSpecialBto, patientBO);
            }
        }
        boResult.setRootBo(patientBO);
        return boResult;
    }

    /** 更新对象:changePatientStatus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private PatientBO updateChangePatientStatusOnMissThrowEx(
            BasePatientBOService.ChangePatientStatusBoResult boResult,
            ChangePatientStatusBto changePatientStatusBto) {
        PatientBO patientBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changePatientStatusBto.getId() == null);
        if (!allNull && !found) {
            patientBO = PatientBO.getById(changePatientStatusBto.getId());
            found = true;
        }
        if (patientBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(patientBO.convertToPatient());
            updatedBto.setBto(changePatientStatusBto);
            updatedBto.setBo(patientBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changePatientStatusBto, "__$validPropertySet"),
                    "status")) {
                patientBO.setStatus(changePatientStatusBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changePatientStatusBto, "__$validPropertySet"),
                    "updatedBy")) {
                patientBO.setUpdatedBy(changePatientStatusBto.getUpdatedBy());
            }
            return patientBO;
        }
    }

    /** 更新病人电话信息 */
    @AutoGenerated(locked = true)
    protected UpdatePatientPhoneBoResult updatePatientPhoneBase(
            UpdatePatientPhoneBto updatePatientPhoneBto) {
        if (updatePatientPhoneBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdatePatientPhoneBoResult boResult = new UpdatePatientPhoneBoResult();
        PatientBO patientBO = updateUpdatePatientPhoneOnMissIgnore(boResult, updatePatientPhoneBto);
        boResult.setRootBo(patientBO);
        return boResult;
    }

    /** 更新对象:updatePatientPhone,如果不存在就跳过 */
    @AutoGenerated(locked = true)
    private PatientBO updateUpdatePatientPhoneOnMissIgnore(
            BasePatientBOService.UpdatePatientPhoneBoResult boResult,
            UpdatePatientPhoneBto updatePatientPhoneBto) {
        PatientBO patientBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updatePatientPhoneBto.getId() == null);
        if (!allNull && !found) {
            patientBO = PatientBO.getById(updatePatientPhoneBto.getId());
            found = true;
        }
        if (patientBO == null) {
            return null;
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(patientBO.convertToPatient());
            updatedBto.setBto(updatePatientPhoneBto);
            updatedBto.setBo(patientBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updatePatientPhoneBto, "__$validPropertySet"),
                    "cellphone")) {
                patientBO.setCellphone(updatePatientPhoneBto.getCellphone());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updatePatientPhoneBto, "__$validPropertySet"),
                    "phoneNumber")) {
                patientBO.setPhoneNumber(updatePatientPhoneBto.getPhoneNumber());
            }
            return patientBO;
        }
    }

    public static class MergePatientBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public PatientBO getRootBo() {
            return (PatientBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientBto.PatientProfileBto, PatientProfileBO> getCreatedBto(
                MergePatientBto.PatientProfileBto patientProfileBto) {
            return this.getAddedResult(patientProfileBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientBto.PatientIdentificationBto, PatientIdentificationBO>
                getCreatedBto(MergePatientBto.PatientIdentificationBto patientIdentificationBto) {
            return this.getAddedResult(patientIdentificationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientBto.PatientRelationshipBto, PatientRelationshipBO>
                getCreatedBto(MergePatientBto.PatientRelationshipBto patientRelationshipBto) {
            return this.getAddedResult(patientRelationshipBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientBto.PatientExpressAddressBto, PatientExpressAddressBO>
                getCreatedBto(MergePatientBto.PatientExpressAddressBto patientExpressAddressBto) {
            return this.getAddedResult(patientExpressAddressBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientBto.PatientMergeBto, PatientMergeBO> getCreatedBto(
                MergePatientBto.PatientMergeBto patientMergeBto) {
            return this.getAddedResult(patientMergeBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientBto, PatientBO> getCreatedBto(MergePatientBto mergePatientBto) {
            return this.getAddedResult(mergePatientBto);
        }

        @AutoGenerated(locked = true)
        public PatientProfile getDeleted_PatientProfile() {
            return (PatientProfile)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientProfile.class));
        }

        @AutoGenerated(locked = true)
        public PatientIdentification getDeleted_PatientIdentification() {
            return (PatientIdentification)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientIdentification.class));
        }

        @AutoGenerated(locked = true)
        public PatientRelationship getDeleted_PatientRelationship() {
            return (PatientRelationship)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientRelationship.class));
        }

        @AutoGenerated(locked = true)
        public PatientExpressAddress getDeleted_PatientExpressAddress() {
            return (PatientExpressAddress)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientExpressAddress.class));
        }

        @AutoGenerated(locked = true)
        public PatientMerge getDeleted_PatientMerge() {
            return (PatientMerge)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientMerge.class));
        }

        @AutoGenerated(locked = true)
        public Patient getDeleted_Patient() {
            return (Patient) CollectionUtil.getFirst(this.getDeletedEntityList(Patient.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergePatientBto.PatientProfileBto, PatientProfile, PatientProfileBO>
                getUpdatedBto(MergePatientBto.PatientProfileBto patientProfileBto) {
            return super.getUpdatedResult(patientProfileBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergePatientBto.PatientIdentificationBto,
                        PatientIdentification,
                        PatientIdentificationBO>
                getUpdatedBto(MergePatientBto.PatientIdentificationBto patientIdentificationBto) {
            return super.getUpdatedResult(patientIdentificationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergePatientBto.PatientRelationshipBto,
                        PatientRelationship,
                        PatientRelationshipBO>
                getUpdatedBto(MergePatientBto.PatientRelationshipBto patientRelationshipBto) {
            return super.getUpdatedResult(patientRelationshipBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergePatientBto.PatientExpressAddressBto,
                        PatientExpressAddress,
                        PatientExpressAddressBO>
                getUpdatedBto(MergePatientBto.PatientExpressAddressBto patientExpressAddressBto) {
            return super.getUpdatedResult(patientExpressAddressBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergePatientBto.PatientMergeBto, PatientMerge, PatientMergeBO>
                getUpdatedBto(MergePatientBto.PatientMergeBto patientMergeBto) {
            return super.getUpdatedResult(patientMergeBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergePatientBto, Patient, PatientBO> getUpdatedBto(
                MergePatientBto mergePatientBto) {
            return super.getUpdatedResult(mergePatientBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientBto.PatientProfileBto, PatientProfileBO> getUnmodifiedBto(
                MergePatientBto.PatientProfileBto patientProfileBto) {
            return super.getUnmodifiedResult(patientProfileBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientBto.PatientIdentificationBto, PatientIdentificationBO>
                getUnmodifiedBto(
                        MergePatientBto.PatientIdentificationBto patientIdentificationBto) {
            return super.getUnmodifiedResult(patientIdentificationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientBto.PatientRelationshipBto, PatientRelationshipBO>
                getUnmodifiedBto(MergePatientBto.PatientRelationshipBto patientRelationshipBto) {
            return super.getUnmodifiedResult(patientRelationshipBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientBto.PatientExpressAddressBto, PatientExpressAddressBO>
                getUnmodifiedBto(
                        MergePatientBto.PatientExpressAddressBto patientExpressAddressBto) {
            return super.getUnmodifiedResult(patientExpressAddressBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientBto.PatientMergeBto, PatientMergeBO> getUnmodifiedBto(
                MergePatientBto.PatientMergeBto patientMergeBto) {
            return super.getUnmodifiedResult(patientMergeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientBto, PatientBO> getUnmodifiedBto(
                MergePatientBto mergePatientBto) {
            return super.getUnmodifiedResult(mergePatientBto);
        }
    }

    public static class ChangePatientStatusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public PatientBO getRootBo() {
            return (PatientBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangePatientStatusBto, PatientBO> getCreatedBto(
                ChangePatientStatusBto changePatientStatusBto) {
            return this.getAddedResult(changePatientStatusBto);
        }

        @AutoGenerated(locked = true)
        public Patient getDeleted_Patient() {
            return (Patient) CollectionUtil.getFirst(this.getDeletedEntityList(Patient.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<ChangePatientStatusBto, Patient, PatientBO> getUpdatedBto(
                ChangePatientStatusBto changePatientStatusBto) {
            return super.getUpdatedResult(changePatientStatusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangePatientStatusBto, PatientBO> getUnmodifiedBto(
                ChangePatientStatusBto changePatientStatusBto) {
            return super.getUnmodifiedResult(changePatientStatusBto);
        }
    }

    public static class MergePatientMergeBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public PatientBO getRootBo() {
            return (PatientBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientMergeBto.PatientMergeBto, PatientMergeBO> getCreatedBto(
                MergePatientMergeBto.PatientMergeBto patientMergeBto) {
            return this.getAddedResult(patientMergeBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientMergeBto, PatientBO> getCreatedBto(
                MergePatientMergeBto mergePatientMergeBto) {
            return this.getAddedResult(mergePatientMergeBto);
        }

        @AutoGenerated(locked = true)
        public PatientMerge getDeleted_PatientMerge() {
            return (PatientMerge)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientMerge.class));
        }

        @AutoGenerated(locked = true)
        public Patient getDeleted_Patient() {
            return (Patient) CollectionUtil.getFirst(this.getDeletedEntityList(Patient.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergePatientMergeBto.PatientMergeBto, PatientMerge, PatientMergeBO>
                getUpdatedBto(MergePatientMergeBto.PatientMergeBto patientMergeBto) {
            return super.getUpdatedResult(patientMergeBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergePatientMergeBto, Patient, PatientBO> getUpdatedBto(
                MergePatientMergeBto mergePatientMergeBto) {
            return super.getUpdatedResult(mergePatientMergeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientMergeBto.PatientMergeBto, PatientMergeBO> getUnmodifiedBto(
                MergePatientMergeBto.PatientMergeBto patientMergeBto) {
            return super.getUnmodifiedResult(patientMergeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientMergeBto, PatientBO> getUnmodifiedBto(
                MergePatientMergeBto mergePatientMergeBto) {
            return super.getUnmodifiedResult(mergePatientMergeBto);
        }
    }

    public static class MergePatientSpecialBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public PatientBO getRootBo() {
            return (PatientBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientSpecialBto.PatientProfileBto, PatientProfileBO> getCreatedBto(
                MergePatientSpecialBto.PatientProfileBto patientProfileBto) {
            return this.getAddedResult(patientProfileBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientSpecialBto, PatientBO> getCreatedBto(
                MergePatientSpecialBto mergePatientSpecialBto) {
            return this.getAddedResult(mergePatientSpecialBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergePatientSpecialBto.PatientIdentificationBto, PatientIdentificationBO>
                getCreatedBto(
                        MergePatientSpecialBto.PatientIdentificationBto patientIdentificationBto) {
            return this.getAddedResult(patientIdentificationBto);
        }

        @AutoGenerated(locked = true)
        public PatientProfile getDeleted_PatientProfile() {
            return (PatientProfile)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientProfile.class));
        }

        @AutoGenerated(locked = true)
        public Patient getDeleted_Patient() {
            return (Patient) CollectionUtil.getFirst(this.getDeletedEntityList(Patient.class));
        }

        @AutoGenerated(locked = true)
        public PatientIdentification getDeleted_PatientIdentification() {
            return (PatientIdentification)
                    CollectionUtil.getFirst(this.getDeletedEntityList(PatientIdentification.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergePatientSpecialBto.PatientProfileBto, PatientProfile, PatientProfileBO>
                getUpdatedBto(MergePatientSpecialBto.PatientProfileBto patientProfileBto) {
            return super.getUpdatedResult(patientProfileBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergePatientSpecialBto, Patient, PatientBO> getUpdatedBto(
                MergePatientSpecialBto mergePatientSpecialBto) {
            return super.getUpdatedResult(mergePatientSpecialBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergePatientSpecialBto.PatientIdentificationBto,
                        PatientIdentification,
                        PatientIdentificationBO>
                getUpdatedBto(
                        MergePatientSpecialBto.PatientIdentificationBto patientIdentificationBto) {
            return super.getUpdatedResult(patientIdentificationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientSpecialBto.PatientProfileBto, PatientProfileBO>
                getUnmodifiedBto(MergePatientSpecialBto.PatientProfileBto patientProfileBto) {
            return super.getUnmodifiedResult(patientProfileBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergePatientSpecialBto, PatientBO> getUnmodifiedBto(
                MergePatientSpecialBto mergePatientSpecialBto) {
            return super.getUnmodifiedResult(mergePatientSpecialBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        MergePatientSpecialBto.PatientIdentificationBto, PatientIdentificationBO>
                getUnmodifiedBto(
                        MergePatientSpecialBto.PatientIdentificationBto patientIdentificationBto) {
            return super.getUnmodifiedResult(patientIdentificationBto);
        }
    }

    public static class UpdatePatientPhoneBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public PatientBO getRootBo() {
            return (PatientBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdatePatientPhoneBto, PatientBO> getCreatedBto(
                UpdatePatientPhoneBto updatePatientPhoneBto) {
            return this.getAddedResult(updatePatientPhoneBto);
        }

        @AutoGenerated(locked = true)
        public Patient getDeleted_Patient() {
            return (Patient) CollectionUtil.getFirst(this.getDeletedEntityList(Patient.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdatePatientPhoneBto, Patient, PatientBO> getUpdatedBto(
                UpdatePatientPhoneBto updatePatientPhoneBto) {
            return super.getUpdatedResult(updatePatientPhoneBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdatePatientPhoneBto, PatientBO> getUnmodifiedBto(
                UpdatePatientPhoneBto updatePatientPhoneBto) {
            return super.getUnmodifiedResult(updatePatientPhoneBto);
        }
    }
}
