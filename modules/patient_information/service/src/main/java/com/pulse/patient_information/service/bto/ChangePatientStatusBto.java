package com.pulse.patient_information.service.bto;

import com.pulse.patient_information.common.enums.PatientStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> Patient
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "bd426cf0-397c-43b3-b413-b87b628123d4|BTO|DEFINITION")
public class ChangePatientStatusBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "ff9dc989-d943-4f93-9e64-eadb4f8aefb2")
    private String id;

    /** 状态码 */
    @AutoGenerated(locked = true, uuid = "fd47b2d2-43b7-46bd-bda9-12dee5583bec")
    private PatientStatusEnum status;

    /** 操作员 */
    @AutoGenerated(locked = true, uuid = "ba912c16-25aa-4085-989c-948c287ca312")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setStatus(PatientStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
