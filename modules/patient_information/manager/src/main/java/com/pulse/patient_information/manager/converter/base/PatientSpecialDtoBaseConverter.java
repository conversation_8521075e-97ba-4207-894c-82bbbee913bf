package com.pulse.patient_information.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.patient_information.manager.PatientBaseDtoManager;
import com.pulse.patient_information.manager.dto.PatientBaseDto;
import com.pulse.patient_information.manager.dto.PatientSpecialDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "3121e659-4b8d-44f4-8f03-a9a8347ecea0|DTO|BASE_CONVERTER")
public class PatientSpecialDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private PatientBaseDtoManager patientBaseDtoManager;

    @AutoGenerated(locked = true)
    public PatientSpecialDto convertFromPatientBaseDtoToPatientSpecialDto(
            PatientBaseDto patientBaseDto) {
        return convertFromPatientBaseDtoToPatientSpecialDto(List.of(patientBaseDto)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<PatientSpecialDto> convertFromPatientBaseDtoToPatientSpecialDto(
            List<PatientBaseDto> patientBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(patientBaseDtoList)) {
            return new ArrayList<>();
        }
        List<PatientSpecialDto> patientSpecialDtoList = new ArrayList<>();
        for (PatientBaseDto patientBaseDto : patientBaseDtoList) {
            if (patientBaseDto == null) {
                continue;
            }
            PatientSpecialDto patientSpecialDto = new PatientSpecialDto();
            patientSpecialDto.setChildrenCoordinatedFlag(
                    patientBaseDto.getChildrenCoordinatedFlag());
            patientSpecialDto.setUpdatedAt(patientBaseDto.getUpdatedAt());
            patientSpecialDto.setPublicFundedUnit(patientBaseDto.getPublicFundedUnit());
            patientSpecialDto.setPublicMedicalExpensesCertificateNumber(
                    patientBaseDto.getPublicMedicalExpensesCertificateNumber());
            patientSpecialDto.setName(patientBaseDto.getName());
            patientSpecialDto.setCreatedBy(patientBaseDto.getCreatedBy());
            patientSpecialDto.setUpdatedBy(patientBaseDto.getUpdatedBy());
            patientSpecialDto.setIdType(patientBaseDto.getIdType());
            patientSpecialDto.setChildrenCoordinatedValidDate(
                    patientBaseDto.getChildrenCoordinatedValidDate());
            patientSpecialDto.setGender(patientBaseDto.getGender());
            patientSpecialDto.setDefaultChargeType(patientBaseDto.getDefaultChargeType());
            patientSpecialDto.setBirthday(patientBaseDto.getBirthday());
            patientSpecialDto.setPublicFundedLevel(patientBaseDto.getPublicFundedLevel());
            patientSpecialDto.setVeteranFlag(patientBaseDto.getVeteranFlag());
            patientSpecialDto.setIdNumber(patientBaseDto.getIdNumber());
            patientSpecialDto.setModelWorkerFlag(patientBaseDto.getModelWorkerFlag());
            patientSpecialDto.setId(patientBaseDto.getId());
            patientSpecialDto.setDisplayId(patientBaseDto.getDisplayId());
            patientSpecialDto.setIdentityCode(patientBaseDto.getIdentityCode());
            patientSpecialDto.setCreatedAt(patientBaseDto.getCreatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            patientSpecialDtoList.add(patientSpecialDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return patientSpecialDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public PatientBaseDto convertFromPatientSpecialDtoToPatientBaseDto(
            PatientSpecialDto patientSpecialDto) {
        return convertFromPatientSpecialDtoToPatientBaseDto(List.of(patientSpecialDto)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<PatientBaseDto> convertFromPatientSpecialDtoToPatientBaseDto(
            List<PatientSpecialDto> patientSpecialDtoList) {
        if (CollectionUtil.isEmpty(patientSpecialDtoList)) {
            return new ArrayList<>();
        }
        return patientBaseDtoManager.getByIds(
                patientSpecialDtoList.stream()
                        .map(PatientSpecialDto::getId)
                        .collect(Collectors.toList()));
    }
}
