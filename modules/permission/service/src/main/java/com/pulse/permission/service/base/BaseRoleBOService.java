package com.pulse.permission.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.permission.manager.bo.*;
import com.pulse.permission.manager.bo.RoleBO;
import com.pulse.permission.persist.dos.Role;
import com.pulse.permission.persist.dos.RolePermission;
import com.pulse.permission.persist.dos.RoleTeam;
import com.pulse.permission.persist.dos.UserRole;
import com.pulse.permission.service.base.BaseRoleBOService.BatchDeleteRolePermissionBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.BatchDeleteRoleTeamBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.BatchDeleteRoleUserBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.DeleteUserRoleBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.MergeRoleBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.MergeRolePermissionBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.MergeRoleTeamBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.MergeRoleUserBoResult;
import com.pulse.permission.service.base.BaseRoleBOService.MergeUserRoleBoResult;
import com.pulse.permission.service.bto.BatchDeleteRolePermissionBto;
import com.pulse.permission.service.bto.BatchDeleteRoleTeamBto;
import com.pulse.permission.service.bto.BatchDeleteRoleUserBto;
import com.pulse.permission.service.bto.DeleteUserRoleBto;
import com.pulse.permission.service.bto.MergeRoleBto;
import com.pulse.permission.service.bto.MergeRolePermissionBto;
import com.pulse.permission.service.bto.MergeRoleTeamBto;
import com.pulse.permission.service.bto.MergeRoleUserBto;
import com.pulse.permission.service.bto.MergeUserRoleBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "d7725fd2-f371-3ecc-8af7-9fc94c518c38")
public class BaseRoleBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 批量删除角色权限 */
    @AutoGenerated(locked = true)
    protected BatchDeleteRolePermissionBoResult batchDeleteRolePermissionBase(
            BatchDeleteRolePermissionBto batchDeleteRolePermissionBto) {
        if (batchDeleteRolePermissionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        BatchDeleteRolePermissionBoResult boResult = new BatchDeleteRolePermissionBoResult();
        RoleBO roleBO =
                updateBatchDeleteRolePermissionOnMissThrowEx(
                        boResult, batchDeleteRolePermissionBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    batchDeleteRolePermissionBto, "__$validPropertySet"),
                    "rolePermissionBtoList")) {
                deleteRolePermissionBtoOnMissThrowEx(
                        boResult, batchDeleteRolePermissionBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 批量删除角色团队 */
    @AutoGenerated(locked = true)
    protected BatchDeleteRoleTeamBoResult batchDeleteRoleTeamBase(
            BatchDeleteRoleTeamBto batchDeleteRoleTeamBto) {
        if (batchDeleteRoleTeamBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        BatchDeleteRoleTeamBoResult boResult = new BatchDeleteRoleTeamBoResult();
        RoleBO roleBO = updateBatchDeleteRoleTeamOnMissThrowEx(boResult, batchDeleteRoleTeamBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    batchDeleteRoleTeamBto, "__$validPropertySet"),
                    "roleTeamBtoList")) {
                deleteRoleTeamBtoOnMissThrowEx(boResult, batchDeleteRoleTeamBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 批量删除角色用户 */
    @AutoGenerated(locked = true)
    protected BatchDeleteRoleUserBoResult batchDeleteRoleUserBase(
            BatchDeleteRoleUserBto batchDeleteRoleUserBto) {
        if (batchDeleteRoleUserBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        BatchDeleteRoleUserBoResult boResult = new BatchDeleteRoleUserBoResult();
        RoleBO roleBO = updateBatchDeleteRoleUserOnMissThrowEx(boResult, batchDeleteRoleUserBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    batchDeleteRoleUserBto, "__$validPropertySet"),
                    "userRoleBtoList")) {
                deleteUserRoleBtoOnMissThrowEx(boResult, batchDeleteRoleUserBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private RoleBO createMergeRoleOnDuplicateUpdate(
            BaseRoleBOService.MergeRoleBoResult boResult, MergeRoleBto mergeRoleBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeRoleBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(mergeRoleBto.getId());
            if (roleBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull = (mergeRoleBto.getCode() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getByCode(mergeRoleBto.getCode());
            if (roleBO != null) {
                matchedUkName += "(";
                matchedUkName += "'code'";
                matchedUkName += ")";
                found = true;
            }
        }
        allNull = (mergeRoleBto.getName() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getByName(mergeRoleBto.getName());
            if (roleBO != null) {
                matchedUkName += "(";
                matchedUkName += "'name'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (roleBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(roleBO.convertToRole());
                updatedBto.setBto(mergeRoleBto);
                updatedBto.setBo(roleBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "code")) {
                    roleBO.setCode(mergeRoleBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "name")) {
                    roleBO.setName(mergeRoleBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "description")) {
                    roleBO.setDescription(mergeRoleBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "systemFlag")) {
                    roleBO.setSystemFlag(mergeRoleBto.getSystemFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "enableFlag")) {
                    roleBO.setEnableFlag(mergeRoleBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "disableReason")) {
                    roleBO.setDisableReason(mergeRoleBto.getDisableReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "createdBy")) {
                    roleBO.setCreatedBy(mergeRoleBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "updatedBy")) {
                    roleBO.setUpdatedBy(mergeRoleBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "deletedBy")) {
                    roleBO.setDeletedBy(mergeRoleBto.getDeletedBy());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(roleBO.convertToRole());
                updatedBto.setBto(mergeRoleBto);
                updatedBto.setBo(roleBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "code")) {
                    roleBO.setCode(mergeRoleBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "name")) {
                    roleBO.setName(mergeRoleBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "description")) {
                    roleBO.setDescription(mergeRoleBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "systemFlag")) {
                    roleBO.setSystemFlag(mergeRoleBto.getSystemFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "enableFlag")) {
                    roleBO.setEnableFlag(mergeRoleBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "disableReason")) {
                    roleBO.setDisableReason(mergeRoleBto.getDisableReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "createdBy")) {
                    roleBO.setCreatedBy(mergeRoleBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "updatedBy")) {
                    roleBO.setUpdatedBy(mergeRoleBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                        "deletedBy")) {
                    roleBO.setDeletedBy(mergeRoleBto.getDeletedBy());
                }
            }
        } else {
            roleBO = new RoleBO();
            if (pkExist) {
                roleBO.setId(mergeRoleBto.getId());
            } else {
                roleBO.setId(String.valueOf(this.idGenerator.allocateId("role")));
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "code")) {
                roleBO.setCode(mergeRoleBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "name")) {
                roleBO.setName(mergeRoleBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "description")) {
                roleBO.setDescription(mergeRoleBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "systemFlag")) {
                roleBO.setSystemFlag(mergeRoleBto.getSystemFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "enableFlag")) {
                roleBO.setEnableFlag(mergeRoleBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "disableReason")) {
                roleBO.setDisableReason(mergeRoleBto.getDisableReason());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "createdBy")) {
                roleBO.setCreatedBy(mergeRoleBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "updatedBy")) {
                roleBO.setUpdatedBy(mergeRoleBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRoleBto, "__$validPropertySet"),
                    "deletedBy")) {
                roleBO.setDeletedBy(mergeRoleBto.getDeletedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeRoleBto);
            addedBto.setBo(roleBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return roleBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private RoleBO createMergeRoleTeamOnDuplicateUpdate(
            MergeRoleTeamBoResult boResult, MergeRoleTeamBto mergeRoleTeamBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeRoleTeamBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(mergeRoleTeamBto.getId());
            if (roleBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (roleBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(roleBO.convertToRole());
                updatedBto.setBto(mergeRoleTeamBto);
                updatedBto.setBo(roleBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(roleBO.convertToRole());
                updatedBto.setBto(mergeRoleTeamBto);
                updatedBto.setBo(roleBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            roleBO = new RoleBO();
            if (pkExist) {
                roleBO.setId(mergeRoleTeamBto.getId());
            } else {
                roleBO.setId(String.valueOf(this.idGenerator.allocateId("role")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeRoleTeamBto);
            addedBto.setBo(roleBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return roleBO;
    }

    /** 创建对象:RolePermissionBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createRolePermissionBtoOnDuplicateUpdate(
            MergeRolePermissionBoResult boResult,
            MergeRolePermissionBto mergeRolePermissionBto,
            RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(mergeRolePermissionBto.getRolePermissionBtoList())) {
            for (MergeRolePermissionBto.RolePermissionBto item :
                    mergeRolePermissionBto.getRolePermissionBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<RolePermissionBO> any =
                        roleBO.getRolePermissionBOSet().stream()
                                .filter(
                                        rolePermissionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                rolePermissionBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull = (item.getPermissionId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                rolePermissionBOSet
                                                                        .getPermissionId(),
                                                                item.getPermissionId());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'permission_id'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        RolePermissionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRolePermission());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "permissionId")) {
                            bo.setPermissionId(item.getPermissionId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disableReason")) {
                            bo.setDisableReason(item.getDisableReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        RolePermissionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRolePermission());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "permissionId")) {
                            bo.setPermissionId(item.getPermissionId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disableReason")) {
                            bo.setDisableReason(item.getDisableReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    RolePermissionBO subBo = new RolePermissionBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "permissionId")) {
                        subBo.setPermissionId(item.getPermissionId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "disableReason")) {
                        subBo.setDisableReason(item.getDisableReason());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setRoleBO(roleBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("role_permission")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    roleBO.getRolePermissionBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:RoleTeamBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createRoleTeamBtoOnDuplicateUpdate(
            BaseRoleBOService.MergeRoleTeamBoResult boResult,
            MergeRoleTeamBto mergeRoleTeamBto,
            RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(mergeRoleTeamBto.getRoleTeamBtoList())) {
            for (MergeRoleTeamBto.RoleTeamBto item : mergeRoleTeamBto.getRoleTeamBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<RoleTeamBO> any =
                        roleBO.getRoleTeamBOSet().stream()
                                .filter(
                                        roleTeamBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                roleTeamBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull = (item.getTeamId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                roleTeamBOSet.getTeamId(),
                                                                item.getTeamId());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'team_id'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        RoleTeamBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRoleTeam());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "teamId")) {
                            bo.setTeamId(item.getTeamId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disableReason")) {
                            bo.setDisableReason(item.getDisableReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        RoleTeamBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRoleTeam());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "teamId")) {
                            bo.setTeamId(item.getTeamId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disableReason")) {
                            bo.setDisableReason(item.getDisableReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    RoleTeamBO subBo = new RoleTeamBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "teamId")) {
                        subBo.setTeamId(item.getTeamId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "disableReason")) {
                        subBo.setDisableReason(item.getDisableReason());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setRoleBO(roleBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("role_team")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    roleBO.getRoleTeamBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:UserRoleBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createUserRoleBtoOnDuplicateUpdate(
            MergeRoleUserBoResult boResult, MergeRoleUserBto mergeRoleUserBto, RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(mergeRoleUserBto.getUserRoleBtoList())) {
            for (MergeRoleUserBto.UserRoleBto item : mergeRoleUserBto.getUserRoleBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<UserRoleBO> any =
                        roleBO.getUserRoleBOSet().stream()
                                .filter(
                                        userRoleBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                userRoleBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull = (item.getUserId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                userRoleBOSet.getUserId(),
                                                                item.getUserId());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'user_id'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        UserRoleBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToUserRole());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "userId")) {
                            bo.setUserId(item.getUserId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disableReason")) {
                            bo.setDisableReason(item.getDisableReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        UserRoleBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToUserRole());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "userId")) {
                            bo.setUserId(item.getUserId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disableReason")) {
                            bo.setDisableReason(item.getDisableReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    UserRoleBO subBo = new UserRoleBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "userId")) {
                        subBo.setUserId(item.getUserId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "disableReason")) {
                        subBo.setDisableReason(item.getDisableReason());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setRoleBO(roleBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("user_role")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    roleBO.getUserRoleBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:UserRoleBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createUserRoleBtoOnDuplicateUpdate(
            MergeUserRoleBoResult boResult, MergeUserRoleBto mergeUserRoleBto, RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(mergeUserRoleBto.getUserRoleBtoList())) {
            for (MergeUserRoleBto.UserRoleBto item : mergeUserRoleBto.getUserRoleBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<UserRoleBO> any =
                        roleBO.getUserRoleBOSet().stream()
                                .filter(
                                        userRoleBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                userRoleBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull = (item.getUserId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                userRoleBOSet.getUserId(),
                                                                item.getUserId());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'user_id'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        UserRoleBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToUserRole());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "userId")) {
                            bo.setUserId(item.getUserId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        UserRoleBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToUserRole());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "userId")) {
                            bo.setUserId(item.getUserId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    UserRoleBO subBo = new UserRoleBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "userId")) {
                        subBo.setUserId(item.getUserId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setRoleBO(roleBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("user_role")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    roleBO.getUserRoleBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 删除对象:rolePermissionBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteRolePermissionBtoOnMissThrowEx(
            BatchDeleteRolePermissionBoResult boResult,
            BatchDeleteRolePermissionBto batchDeleteRolePermissionBto,
            RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(batchDeleteRolePermissionBto.getRolePermissionBtoList())) {
            for (BatchDeleteRolePermissionBto.RolePermissionBto item :
                    batchDeleteRolePermissionBto.getRolePermissionBtoList()) {
                Optional<RolePermissionBO> any =
                        roleBO.getRolePermissionBOSet().stream()
                                .filter(
                                        rolePermissionBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                rolePermissionBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    roleBO.getRolePermissionBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToRolePermission());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 删除对象:roleTeamBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteRoleTeamBtoOnMissThrowEx(
            BatchDeleteRoleTeamBoResult boResult,
            BatchDeleteRoleTeamBto batchDeleteRoleTeamBto,
            RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(batchDeleteRoleTeamBto.getRoleTeamBtoList())) {
            for (BatchDeleteRoleTeamBto.RoleTeamBto item :
                    batchDeleteRoleTeamBto.getRoleTeamBtoList()) {
                Optional<RoleTeamBO> any =
                        roleBO.getRoleTeamBOSet().stream()
                                .filter(
                                        roleTeamBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                roleTeamBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    roleBO.getRoleTeamBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToRoleTeam());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 删除用户的角色 */
    @AutoGenerated(locked = true)
    protected DeleteUserRoleBoResult deleteUserRoleBase(DeleteUserRoleBto deleteUserRoleBto) {
        if (deleteUserRoleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteUserRoleBoResult boResult = new DeleteUserRoleBoResult();
        RoleBO roleBO = updateDeleteUserRoleOnMissThrowEx(boResult, deleteUserRoleBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteUserRoleBto, "__$validPropertySet"),
                    "userRoleBtoList")) {
                deleteUserRoleBtoOnMissThrowEx(boResult, deleteUserRoleBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 删除对象:userRoleBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteUserRoleBtoOnMissThrowEx(
            BatchDeleteRoleUserBoResult boResult,
            BatchDeleteRoleUserBto batchDeleteRoleUserBto,
            RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(batchDeleteRoleUserBto.getUserRoleBtoList())) {
            for (BatchDeleteRoleUserBto.UserRoleBto item :
                    batchDeleteRoleUserBto.getUserRoleBtoList()) {
                Optional<UserRoleBO> any =
                        roleBO.getUserRoleBOSet().stream()
                                .filter(
                                        userRoleBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                userRoleBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    roleBO.getUserRoleBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToUserRole());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 删除对象:userRoleBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteUserRoleBtoOnMissThrowEx(
            DeleteUserRoleBoResult boResult, DeleteUserRoleBto deleteUserRoleBto, RoleBO roleBO) {
        if (CollectionUtil.isNotEmpty(deleteUserRoleBto.getUserRoleBtoList())) {
            for (DeleteUserRoleBto.UserRoleBto item : deleteUserRoleBto.getUserRoleBtoList()) {
                Optional<UserRoleBO> any =
                        roleBO.getUserRoleBOSet().stream()
                                .filter(
                                        userRoleBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                userRoleBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    roleBO.getUserRoleBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToUserRole());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 保存角色 */
    @AutoGenerated(locked = true)
    protected MergeRoleBoResult mergeRoleBase(MergeRoleBto mergeRoleBto) {
        if (mergeRoleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeRoleBoResult boResult = new MergeRoleBoResult();
        RoleBO roleBO = createMergeRoleOnDuplicateUpdate(boResult, mergeRoleBto);
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 批量保存角色的权限点 */
    @AutoGenerated(locked = true)
    protected MergeRolePermissionBoResult mergeRolePermissionBase(
            MergeRolePermissionBto mergeRolePermissionBto) {
        if (mergeRolePermissionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeRolePermissionBoResult boResult = new MergeRolePermissionBoResult();
        RoleBO roleBO = updateMergeRolePermissionOnMissThrowEx(boResult, mergeRolePermissionBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeRolePermissionBto, "__$validPropertySet"),
                    "rolePermissionBtoList")) {
                createRolePermissionBtoOnDuplicateUpdate(boResult, mergeRolePermissionBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 批量保存角色的团队 */
    @AutoGenerated(locked = true)
    protected MergeRoleTeamBoResult mergeRoleTeamBase(MergeRoleTeamBto mergeRoleTeamBto) {
        if (mergeRoleTeamBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeRoleTeamBoResult boResult = new MergeRoleTeamBoResult();
        RoleBO roleBO = createMergeRoleTeamOnDuplicateUpdate(boResult, mergeRoleTeamBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRoleTeamBto, "__$validPropertySet"),
                    "roleTeamBtoList")) {
                createRoleTeamBtoOnDuplicateUpdate(boResult, mergeRoleTeamBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 批量保存角色的用户 */
    @AutoGenerated(locked = true)
    protected MergeRoleUserBoResult mergeRoleUserBase(MergeRoleUserBto mergeRoleUserBto) {
        if (mergeRoleUserBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeRoleUserBoResult boResult = new MergeRoleUserBoResult();
        RoleBO roleBO = updateMergeRoleUserOnMissThrowEx(boResult, mergeRoleUserBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRoleUserBto, "__$validPropertySet"),
                    "userRoleBtoList")) {
                createUserRoleBtoOnDuplicateUpdate(boResult, mergeRoleUserBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 保存用户的角色（新增、更新） */
    @AutoGenerated(locked = true)
    protected MergeUserRoleBoResult mergeUserRoleBase(MergeUserRoleBto mergeUserRoleBto) {
        if (mergeUserRoleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeUserRoleBoResult boResult = new MergeUserRoleBoResult();
        RoleBO roleBO = updateMergeUserRoleOnMissThrowEx(boResult, mergeUserRoleBto);
        if (roleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeUserRoleBto, "__$validPropertySet"),
                    "userRoleBtoList")) {
                createUserRoleBtoOnDuplicateUpdate(boResult, mergeUserRoleBto, roleBO);
            }
        }
        boResult.setRootBo(roleBO);
        return boResult;
    }

    /** 更新对象:batchDeleteRolePermission,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RoleBO updateBatchDeleteRolePermissionOnMissThrowEx(
            BaseRoleBOService.BatchDeleteRolePermissionBoResult boResult,
            BatchDeleteRolePermissionBto batchDeleteRolePermissionBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (batchDeleteRolePermissionBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(batchDeleteRolePermissionBto.getId());
            found = true;
        }
        if (roleBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(roleBO.convertToRole());
            updatedBto.setBto(batchDeleteRolePermissionBto);
            updatedBto.setBo(roleBO);
            boResult.getUpdatedList().add(updatedBto);
            return roleBO;
        }
    }

    /** 更新对象:batchDeleteRoleTeam,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RoleBO updateBatchDeleteRoleTeamOnMissThrowEx(
            BaseRoleBOService.BatchDeleteRoleTeamBoResult boResult,
            BatchDeleteRoleTeamBto batchDeleteRoleTeamBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (batchDeleteRoleTeamBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(batchDeleteRoleTeamBto.getId());
            found = true;
        }
        if (roleBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(roleBO.convertToRole());
            updatedBto.setBto(batchDeleteRoleTeamBto);
            updatedBto.setBo(roleBO);
            boResult.getUpdatedList().add(updatedBto);
            return roleBO;
        }
    }

    /** 更新对象:batchDeleteRoleUser,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RoleBO updateBatchDeleteRoleUserOnMissThrowEx(
            BaseRoleBOService.BatchDeleteRoleUserBoResult boResult,
            BatchDeleteRoleUserBto batchDeleteRoleUserBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (batchDeleteRoleUserBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(batchDeleteRoleUserBto.getId());
            found = true;
        }
        if (roleBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(roleBO.convertToRole());
            updatedBto.setBto(batchDeleteRoleUserBto);
            updatedBto.setBo(roleBO);
            boResult.getUpdatedList().add(updatedBto);
            return roleBO;
        }
    }

    /** 更新对象:deleteUserRole,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RoleBO updateDeleteUserRoleOnMissThrowEx(
            BaseRoleBOService.DeleteUserRoleBoResult boResult,
            DeleteUserRoleBto deleteUserRoleBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteUserRoleBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(deleteUserRoleBto.getId());
            found = true;
        }
        if (roleBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(roleBO.convertToRole());
            updatedBto.setBto(deleteUserRoleBto);
            updatedBto.setBo(roleBO);
            boResult.getUpdatedList().add(updatedBto);
            return roleBO;
        }
    }

    /** 更新对象:mergeRolePermission,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RoleBO updateMergeRolePermissionOnMissThrowEx(
            BaseRoleBOService.MergeRolePermissionBoResult boResult,
            MergeRolePermissionBto mergeRolePermissionBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeRolePermissionBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(mergeRolePermissionBto.getId());
            found = true;
        }
        if (roleBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(roleBO.convertToRole());
            updatedBto.setBto(mergeRolePermissionBto);
            updatedBto.setBo(roleBO);
            boResult.getUpdatedList().add(updatedBto);
            return roleBO;
        }
    }

    /** 更新对象:mergeRoleUser,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RoleBO updateMergeRoleUserOnMissThrowEx(
            BaseRoleBOService.MergeRoleUserBoResult boResult, MergeRoleUserBto mergeRoleUserBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeRoleUserBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(mergeRoleUserBto.getId());
            found = true;
        }
        if (roleBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(roleBO.convertToRole());
            updatedBto.setBto(mergeRoleUserBto);
            updatedBto.setBo(roleBO);
            boResult.getUpdatedList().add(updatedBto);
            return roleBO;
        }
    }

    /** 更新对象:mergeUserRole,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RoleBO updateMergeUserRoleOnMissThrowEx(
            BaseRoleBOService.MergeUserRoleBoResult boResult, MergeUserRoleBto mergeUserRoleBto) {
        RoleBO roleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeUserRoleBto.getId() == null);
        if (!allNull && !found) {
            roleBO = RoleBO.getById(mergeUserRoleBto.getId());
            found = true;
        }
        if (roleBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(roleBO.convertToRole());
            updatedBto.setBto(mergeUserRoleBto);
            updatedBto.setBo(roleBO);
            boResult.getUpdatedList().add(updatedBto);
            return roleBO;
        }
    }

    public static class MergeRoleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRoleBto, RoleBO> getCreatedBto(MergeRoleBto mergeRoleBto) {
            return this.getAddedResult(mergeRoleBto);
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRoleBto, Role, RoleBO> getUpdatedBto(MergeRoleBto mergeRoleBto) {
            return super.getUpdatedResult(mergeRoleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRoleBto, RoleBO> getUnmodifiedBto(MergeRoleBto mergeRoleBto) {
            return super.getUnmodifiedResult(mergeRoleBto);
        }
    }

    public static class MergeRolePermissionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRolePermissionBto, RoleBO> getCreatedBto(
                MergeRolePermissionBto mergeRolePermissionBto) {
            return this.getAddedResult(mergeRolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRolePermissionBto.RolePermissionBto, RolePermissionBO> getCreatedBto(
                MergeRolePermissionBto.RolePermissionBto rolePermissionBto) {
            return this.getAddedResult(rolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public RolePermission getDeleted_RolePermission() {
            return (RolePermission)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RolePermission.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRolePermissionBto, Role, RoleBO> getUpdatedBto(
                MergeRolePermissionBto mergeRolePermissionBto) {
            return super.getUpdatedResult(mergeRolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeRolePermissionBto.RolePermissionBto, RolePermission, RolePermissionBO>
                getUpdatedBto(MergeRolePermissionBto.RolePermissionBto rolePermissionBto) {
            return super.getUpdatedResult(rolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRolePermissionBto, RoleBO> getUnmodifiedBto(
                MergeRolePermissionBto mergeRolePermissionBto) {
            return super.getUnmodifiedResult(mergeRolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRolePermissionBto.RolePermissionBto, RolePermissionBO>
                getUnmodifiedBto(MergeRolePermissionBto.RolePermissionBto rolePermissionBto) {
            return super.getUnmodifiedResult(rolePermissionBto);
        }
    }

    public static class MergeRoleUserBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRoleUserBto.UserRoleBto, UserRoleBO> getCreatedBto(
                MergeRoleUserBto.UserRoleBto userRoleBto) {
            return this.getAddedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRoleUserBto, RoleBO> getCreatedBto(MergeRoleUserBto mergeRoleUserBto) {
            return this.getAddedResult(mergeRoleUserBto);
        }

        @AutoGenerated(locked = true)
        public UserRole getDeleted_UserRole() {
            return (UserRole) CollectionUtil.getFirst(this.getDeletedEntityList(UserRole.class));
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRoleUserBto.UserRoleBto, UserRole, UserRoleBO> getUpdatedBto(
                MergeRoleUserBto.UserRoleBto userRoleBto) {
            return super.getUpdatedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRoleUserBto, Role, RoleBO> getUpdatedBto(
                MergeRoleUserBto mergeRoleUserBto) {
            return super.getUpdatedResult(mergeRoleUserBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRoleUserBto.UserRoleBto, UserRoleBO> getUnmodifiedBto(
                MergeRoleUserBto.UserRoleBto userRoleBto) {
            return super.getUnmodifiedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRoleUserBto, RoleBO> getUnmodifiedBto(
                MergeRoleUserBto mergeRoleUserBto) {
            return super.getUnmodifiedResult(mergeRoleUserBto);
        }
    }

    public static class MergeRoleTeamBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRoleTeamBto.RoleTeamBto, RoleTeamBO> getCreatedBto(
                MergeRoleTeamBto.RoleTeamBto roleTeamBto) {
            return this.getAddedResult(roleTeamBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRoleTeamBto, RoleBO> getCreatedBto(MergeRoleTeamBto mergeRoleTeamBto) {
            return this.getAddedResult(mergeRoleTeamBto);
        }

        @AutoGenerated(locked = true)
        public RoleTeam getDeleted_RoleTeam() {
            return (RoleTeam) CollectionUtil.getFirst(this.getDeletedEntityList(RoleTeam.class));
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRoleTeamBto.RoleTeamBto, RoleTeam, RoleTeamBO> getUpdatedBto(
                MergeRoleTeamBto.RoleTeamBto roleTeamBto) {
            return super.getUpdatedResult(roleTeamBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRoleTeamBto, Role, RoleBO> getUpdatedBto(
                MergeRoleTeamBto mergeRoleTeamBto) {
            return super.getUpdatedResult(mergeRoleTeamBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRoleTeamBto.RoleTeamBto, RoleTeamBO> getUnmodifiedBto(
                MergeRoleTeamBto.RoleTeamBto roleTeamBto) {
            return super.getUnmodifiedResult(roleTeamBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRoleTeamBto, RoleBO> getUnmodifiedBto(
                MergeRoleTeamBto mergeRoleTeamBto) {
            return super.getUnmodifiedResult(mergeRoleTeamBto);
        }
    }

    public static class BatchDeleteRoleUserBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<BatchDeleteRoleUserBto.UserRoleBto, UserRoleBO> getCreatedBto(
                BatchDeleteRoleUserBto.UserRoleBto userRoleBto) {
            return this.getAddedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<BatchDeleteRoleUserBto, RoleBO> getCreatedBto(
                BatchDeleteRoleUserBto batchDeleteRoleUserBto) {
            return this.getAddedResult(batchDeleteRoleUserBto);
        }

        @AutoGenerated(locked = true)
        public UserRole getDeleted_UserRole() {
            return (UserRole) CollectionUtil.getFirst(this.getDeletedEntityList(UserRole.class));
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<BatchDeleteRoleUserBto.UserRoleBto, UserRole, UserRoleBO> getUpdatedBto(
                BatchDeleteRoleUserBto.UserRoleBto userRoleBto) {
            return super.getUpdatedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<BatchDeleteRoleUserBto, Role, RoleBO> getUpdatedBto(
                BatchDeleteRoleUserBto batchDeleteRoleUserBto) {
            return super.getUpdatedResult(batchDeleteRoleUserBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<BatchDeleteRoleUserBto.UserRoleBto, UserRoleBO> getUnmodifiedBto(
                BatchDeleteRoleUserBto.UserRoleBto userRoleBto) {
            return super.getUnmodifiedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<BatchDeleteRoleUserBto, RoleBO> getUnmodifiedBto(
                BatchDeleteRoleUserBto batchDeleteRoleUserBto) {
            return super.getUnmodifiedResult(batchDeleteRoleUserBto);
        }
    }

    public static class BatchDeleteRoleTeamBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<BatchDeleteRoleTeamBto.RoleTeamBto, RoleTeamBO> getCreatedBto(
                BatchDeleteRoleTeamBto.RoleTeamBto roleTeamBto) {
            return this.getAddedResult(roleTeamBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<BatchDeleteRoleTeamBto, RoleBO> getCreatedBto(
                BatchDeleteRoleTeamBto batchDeleteRoleTeamBto) {
            return this.getAddedResult(batchDeleteRoleTeamBto);
        }

        @AutoGenerated(locked = true)
        public RoleTeam getDeleted_RoleTeam() {
            return (RoleTeam) CollectionUtil.getFirst(this.getDeletedEntityList(RoleTeam.class));
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<BatchDeleteRoleTeamBto.RoleTeamBto, RoleTeam, RoleTeamBO> getUpdatedBto(
                BatchDeleteRoleTeamBto.RoleTeamBto roleTeamBto) {
            return super.getUpdatedResult(roleTeamBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<BatchDeleteRoleTeamBto, Role, RoleBO> getUpdatedBto(
                BatchDeleteRoleTeamBto batchDeleteRoleTeamBto) {
            return super.getUpdatedResult(batchDeleteRoleTeamBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<BatchDeleteRoleTeamBto.RoleTeamBto, RoleTeamBO> getUnmodifiedBto(
                BatchDeleteRoleTeamBto.RoleTeamBto roleTeamBto) {
            return super.getUnmodifiedResult(roleTeamBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<BatchDeleteRoleTeamBto, RoleBO> getUnmodifiedBto(
                BatchDeleteRoleTeamBto batchDeleteRoleTeamBto) {
            return super.getUnmodifiedResult(batchDeleteRoleTeamBto);
        }
    }

    public static class BatchDeleteRolePermissionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<BatchDeleteRolePermissionBto.RolePermissionBto, RolePermissionBO>
                getCreatedBto(BatchDeleteRolePermissionBto.RolePermissionBto rolePermissionBto) {
            return this.getAddedResult(rolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<BatchDeleteRolePermissionBto, RoleBO> getCreatedBto(
                BatchDeleteRolePermissionBto batchDeleteRolePermissionBto) {
            return this.getAddedResult(batchDeleteRolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public RolePermission getDeleted_RolePermission() {
            return (RolePermission)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RolePermission.class));
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        BatchDeleteRolePermissionBto.RolePermissionBto,
                        RolePermission,
                        RolePermissionBO>
                getUpdatedBto(BatchDeleteRolePermissionBto.RolePermissionBto rolePermissionBto) {
            return super.getUpdatedResult(rolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<BatchDeleteRolePermissionBto, Role, RoleBO> getUpdatedBto(
                BatchDeleteRolePermissionBto batchDeleteRolePermissionBto) {
            return super.getUpdatedResult(batchDeleteRolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<BatchDeleteRolePermissionBto.RolePermissionBto, RolePermissionBO>
                getUnmodifiedBto(BatchDeleteRolePermissionBto.RolePermissionBto rolePermissionBto) {
            return super.getUnmodifiedResult(rolePermissionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<BatchDeleteRolePermissionBto, RoleBO> getUnmodifiedBto(
                BatchDeleteRolePermissionBto batchDeleteRolePermissionBto) {
            return super.getUnmodifiedResult(batchDeleteRolePermissionBto);
        }
    }

    public static class MergeUserRoleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeUserRoleBto.UserRoleBto, UserRoleBO> getCreatedBto(
                MergeUserRoleBto.UserRoleBto userRoleBto) {
            return this.getAddedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeUserRoleBto, RoleBO> getCreatedBto(MergeUserRoleBto mergeUserRoleBto) {
            return this.getAddedResult(mergeUserRoleBto);
        }

        @AutoGenerated(locked = true)
        public UserRole getDeleted_UserRole() {
            return (UserRole) CollectionUtil.getFirst(this.getDeletedEntityList(UserRole.class));
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeUserRoleBto.UserRoleBto, UserRole, UserRoleBO> getUpdatedBto(
                MergeUserRoleBto.UserRoleBto userRoleBto) {
            return super.getUpdatedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeUserRoleBto, Role, RoleBO> getUpdatedBto(
                MergeUserRoleBto mergeUserRoleBto) {
            return super.getUpdatedResult(mergeUserRoleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeUserRoleBto.UserRoleBto, UserRoleBO> getUnmodifiedBto(
                MergeUserRoleBto.UserRoleBto userRoleBto) {
            return super.getUnmodifiedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeUserRoleBto, RoleBO> getUnmodifiedBto(
                MergeUserRoleBto mergeUserRoleBto) {
            return super.getUnmodifiedResult(mergeUserRoleBto);
        }
    }

    public static class DeleteUserRoleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RoleBO getRootBo() {
            return (RoleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteUserRoleBto.UserRoleBto, UserRoleBO> getCreatedBto(
                DeleteUserRoleBto.UserRoleBto userRoleBto) {
            return this.getAddedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteUserRoleBto, RoleBO> getCreatedBto(
                DeleteUserRoleBto deleteUserRoleBto) {
            return this.getAddedResult(deleteUserRoleBto);
        }

        @AutoGenerated(locked = true)
        public UserRole getDeleted_UserRole() {
            return (UserRole) CollectionUtil.getFirst(this.getDeletedEntityList(UserRole.class));
        }

        @AutoGenerated(locked = true)
        public Role getDeleted_Role() {
            return (Role) CollectionUtil.getFirst(this.getDeletedEntityList(Role.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteUserRoleBto.UserRoleBto, UserRole, UserRoleBO> getUpdatedBto(
                DeleteUserRoleBto.UserRoleBto userRoleBto) {
            return super.getUpdatedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteUserRoleBto, Role, RoleBO> getUpdatedBto(
                DeleteUserRoleBto deleteUserRoleBto) {
            return super.getUpdatedResult(deleteUserRoleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteUserRoleBto.UserRoleBto, UserRoleBO> getUnmodifiedBto(
                DeleteUserRoleBto.UserRoleBto userRoleBto) {
            return super.getUnmodifiedResult(userRoleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteUserRoleBto, RoleBO> getUnmodifiedBto(
                DeleteUserRoleBto deleteUserRoleBto) {
            return super.getUnmodifiedResult(deleteUserRoleBto);
        }
    }
}
