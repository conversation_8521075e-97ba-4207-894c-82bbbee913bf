package com.pulse.permission.service.converter.voConverter;

import com.pulse.permission.persist.dos.RolePermission;
import com.pulse.permission.persist.dos.RolePermission.PermissionIdAndRoleId;
import com.pulse.permission.persist.eo.UkRolePermissionEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "205f976a-086c-3ef3-b936-a0e49f4ed370")
public class RolePermissionUkRolePermissionConverter {

    @AutoGenerated(locked = true)
    public static RolePermission.PermissionIdAndRoleId convertFromUkRolePermissionToInner(
            UkRolePermissionEo ukRolePermission) {
        if (null == ukRolePermission) {
            return null;
        }

        PermissionIdAndRoleId permissionIdAndRoleId = new PermissionIdAndRoleId();
        permissionIdAndRoleId.setRoleId(ukRolePermission.getRoleId());
        permissionIdAndRoleId.setPermissionId(ukRolePermission.getPermissionId());
        return permissionIdAndRoleId;
    }
}
