package com.pulse.permission.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.permission.persist.qto.ListPermissionByRoleQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "78321893-264b-456f-8edd-c9a7eff3045b|QTO|DAO")
public class ListPermissionByRoleQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询角色的权限基础信息列表 */
    @AutoGenerated(locked = false, uuid = "78321893-264b-456f-8edd-c9a7eff3045b-count")
    public Integer count(ListPermissionByRoleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(role_permission.id) FROM role_permission LEFT JOIN permission"
                    + " \"permission\" on role_permission.permission_id = \"permission\".id WHERE"
                    + " role_permission.role_id = #roleIdIs AND \"permission\".resource_type in"
                    + " #permissionResourceTypeIn AND \"permission\".resource_type not in"
                    + " #permissionResourceTypeNotIn AND \"permission\".id in #permissionIdIn AND"
                    + " \"permission\".resource_key in #permissionResourceKeyIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getPermissionResourceTypeNotIn())) {
            conditionToRemove.add("#permissionResourceTypeNotIn");
        }
        if (CollectionUtil.isEmpty(qto.getPermissionResourceTypeIn())) {
            conditionToRemove.add("#permissionResourceTypeIn");
        }
        if (CollectionUtil.isEmpty(qto.getPermissionResourceKeyIn())) {
            conditionToRemove.add("#permissionResourceKeyIn");
        }
        if (CollectionUtil.isEmpty(qto.getPermissionIdIn())) {
            conditionToRemove.add("#permissionIdIn");
        }
        if (qto.getRoleIdIs() == null) {
            conditionToRemove.add("#roleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"permission\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#permissionResourceTypeNotIn",
                                CollectionUtil.isEmpty(qto.getPermissionResourceTypeNotIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getPermissionResourceTypeNotIn().size()))
                        .replace(
                                "#permissionResourceTypeIn",
                                CollectionUtil.isEmpty(qto.getPermissionResourceTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getPermissionResourceTypeIn().size()))
                        .replace(
                                "#permissionResourceKeyIn",
                                CollectionUtil.isEmpty(qto.getPermissionResourceKeyIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getPermissionResourceKeyIn().size()))
                        .replace(
                                "#permissionIdIn",
                                CollectionUtil.isEmpty(qto.getPermissionIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getPermissionIdIn().size()))
                        .replace("#roleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#permissionResourceTypeNotIn")) {
                sqlParams.addAll(
                        qto.getPermissionResourceTypeNotIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#permissionResourceTypeIn")) {
                sqlParams.addAll(
                        qto.getPermissionResourceTypeIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#permissionResourceKeyIn")) {
                sqlParams.addAll(qto.getPermissionResourceKeyIn());
            } else if (paramName.equalsIgnoreCase("#permissionIdIn")) {
                sqlParams.addAll(qto.getPermissionIdIn());
            } else if (paramName.equalsIgnoreCase("#roleIdIs")) {
                sqlParams.add(qto.getRoleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询角色的权限基础信息列表 */
    @AutoGenerated(locked = false, uuid = "78321893-264b-456f-8edd-c9a7eff3045b-query-all")
    public List<String> query(ListPermissionByRoleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT role_permission.id FROM role_permission LEFT JOIN permission \"permission\""
                    + " on role_permission.permission_id = \"permission\".id WHERE"
                    + " role_permission.role_id = #roleIdIs AND \"permission\".resource_type in"
                    + " #permissionResourceTypeIn AND \"permission\".resource_type not in"
                    + " #permissionResourceTypeNotIn AND \"permission\".id in #permissionIdIn AND"
                    + " \"permission\".resource_key in #permissionResourceKeyIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getPermissionResourceTypeNotIn())) {
            conditionToRemove.add("#permissionResourceTypeNotIn");
        }
        if (CollectionUtil.isEmpty(qto.getPermissionResourceTypeIn())) {
            conditionToRemove.add("#permissionResourceTypeIn");
        }
        if (CollectionUtil.isEmpty(qto.getPermissionResourceKeyIn())) {
            conditionToRemove.add("#permissionResourceKeyIn");
        }
        if (CollectionUtil.isEmpty(qto.getPermissionIdIn())) {
            conditionToRemove.add("#permissionIdIn");
        }
        if (qto.getRoleIdIs() == null) {
            conditionToRemove.add("#roleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("\"permission\"");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#permissionResourceTypeNotIn",
                                CollectionUtil.isEmpty(qto.getPermissionResourceTypeNotIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getPermissionResourceTypeNotIn().size()))
                        .replace(
                                "#permissionResourceTypeIn",
                                CollectionUtil.isEmpty(qto.getPermissionResourceTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getPermissionResourceTypeIn().size()))
                        .replace(
                                "#permissionResourceKeyIn",
                                CollectionUtil.isEmpty(qto.getPermissionResourceKeyIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getPermissionResourceKeyIn().size()))
                        .replace(
                                "#permissionIdIn",
                                CollectionUtil.isEmpty(qto.getPermissionIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getPermissionIdIn().size()))
                        .replace("#roleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#permissionResourceTypeNotIn")) {
                sqlParams.addAll(
                        qto.getPermissionResourceTypeNotIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#permissionResourceTypeIn")) {
                sqlParams.addAll(
                        qto.getPermissionResourceTypeIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#permissionResourceKeyIn")) {
                sqlParams.addAll(qto.getPermissionResourceKeyIn());
            } else if (paramName.equalsIgnoreCase("#permissionIdIn")) {
                sqlParams.addAll(qto.getPermissionIdIn());
            } else if (paramName.equalsIgnoreCase("#roleIdIs")) {
                sqlParams.add(qto.getRoleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  \"permission\".created_at desc ";
        parsedSql += " OFFSET 0 ROWS FETCH NEXT 100 ROWS ONLY ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
