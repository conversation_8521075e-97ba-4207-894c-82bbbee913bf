package com.pulse.permission.entrance.web.query.collector;

import com.pulse.application.manager.dto.ApplicationMenuBaseDto;
import com.pulse.application.manager.dto.FeatureBaseDto;
import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.pulse.permission.entrance.web.converter.PermissionAggVoConverter;
import com.pulse.permission.entrance.web.converter.PermissionRefAppMenuBaseVoConverter;
import com.pulse.permission.entrance.web.converter.PermissionRefCategorySimpleVoConverter;
import com.pulse.permission.entrance.web.converter.PermissionRefFeatureSimpleVoConverter;
import com.pulse.permission.entrance.web.query.assembler.PermissionAggVoDataAssembler.PermissionAggVoDataHolder;
import com.pulse.permission.entrance.web.vo.PermissionRefAppMenuBaseVo;
import com.pulse.permission.entrance.web.vo.PermissionRefCategorySimpleVo;
import com.pulse.permission.entrance.web.vo.PermissionRefFeatureSimpleVo;
import com.pulse.permission.manager.dto.PermissionAggDto;
import com.pulse.permission.manager.dto.PermissionBaseDto;
import com.pulse.permission.manager.facade.application.ApplicationMenuBaseDtoServiceInPermissionRpcAdapter;
import com.pulse.permission.manager.facade.application.FeatureBaseDtoServiceInPermissionRpcAdapter;
import com.pulse.permission.manager.facade.dictionary_basic.CategoryBaseDtoServiceInPermissionRpcAdapter;
import com.pulse.permission.service.PermissionBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装PermissionAggVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "6a0217d6-16d6-31d9-bd68-5a33ace4dd7c")
public class PermissionAggVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationMenuBaseDtoServiceInPermissionRpcAdapter
            applicationMenuBaseDtoServiceInPermissionRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private CategoryBaseDtoServiceInPermissionRpcAdapter
            categoryBaseDtoServiceInPermissionRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureBaseDtoServiceInPermissionRpcAdapter featureBaseDtoServiceInPermissionRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private PermissionAggVoConverter permissionAggVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private PermissionAggVoDataCollector permissionAggVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private PermissionBaseDtoService permissionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private PermissionRefAppMenuBaseVoConverter permissionRefAppMenuBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private PermissionRefCategorySimpleVoConverter permissionRefCategorySimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private PermissionRefFeatureSimpleVoConverter permissionRefFeatureSimpleVoConverter;

    /** 获取PermissionAggDto数据填充PermissionAggVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "4d0fe919-42e3-3f0f-9431-f6f7e17ae955")
    public void collectDataWithDtoData(
            List<PermissionAggDto> dtoList, PermissionAggVoDataHolder dataHolder) {
        List<ApplicationMenuBaseDto> applicationMenuList = new ArrayList<>();

        for (PermissionAggDto rootDto : dtoList) {
            ApplicationMenuBaseDto applicationMenuDto = rootDto.getApplicationMenu();
            if (applicationMenuDto != null) {
                applicationMenuList.add(applicationMenuDto);
            }
        }

        // access applicationMenu
        Map<ApplicationMenuBaseDto, PermissionRefAppMenuBaseVo> applicationMenuVoMap =
                permissionRefAppMenuBaseVoConverter.convertToPermissionRefAppMenuBaseVoMap(
                        applicationMenuList);
        dataHolder.applicationMenu =
                applicationMenuList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> applicationMenuVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "c264089e-b54e-36f2-8c50-16b90f84d154")
    public void collectDataDefault(PermissionAggVoDataHolder dataHolder) {
        permissionAggVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "dbd62788-961d-37e7-9519-0c86b0b7915f")
    private void fillDataWhenNecessary(PermissionAggVoDataHolder dataHolder) {
        List<PermissionBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.feature == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(PermissionBaseDto::getFeatureId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<FeatureBaseDto> baseDtoList =
                    featureBaseDtoServiceInPermissionRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(FeatureBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, FeatureBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(FeatureBaseDto::getId, Function.identity()));
            Map<FeatureBaseDto, PermissionRefFeatureSimpleVo> dtoVoMap =
                    permissionRefFeatureSimpleVoConverter.convertToPermissionRefFeatureSimpleVoMap(
                            baseDtoList);
            Map<FeatureBaseDto, PermissionRefFeatureSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.feature =
                    rootDtoList.stream()
                            .map(PermissionBaseDto::getFeatureId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.category == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(PermissionBaseDto::getCategoryId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<CategoryBaseDto> baseDtoList =
                    categoryBaseDtoServiceInPermissionRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(CategoryBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, CategoryBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(CategoryBaseDto::getId, Function.identity()));
            Map<CategoryBaseDto, PermissionRefCategorySimpleVo> dtoVoMap =
                    permissionRefCategorySimpleVoConverter
                            .convertToPermissionRefCategorySimpleVoMap(baseDtoList);
            Map<CategoryBaseDto, PermissionRefCategorySimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.category =
                    rootDtoList.stream()
                            .map(PermissionBaseDto::getCategoryId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.applicationMenu == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(PermissionBaseDto::getApplicationMenuId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationMenuBaseDto> baseDtoList =
                    applicationMenuBaseDtoServiceInPermissionRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationMenuBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationMenuBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationMenuBaseDto::getId, Function.identity()));
            Map<ApplicationMenuBaseDto, PermissionRefAppMenuBaseVo> dtoVoMap =
                    permissionRefAppMenuBaseVoConverter.convertToPermissionRefAppMenuBaseVoMap(
                            baseDtoList);
            Map<ApplicationMenuBaseDto, PermissionRefAppMenuBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.applicationMenu =
                    rootDtoList.stream()
                            .map(PermissionBaseDto::getApplicationMenuId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
