package com.pulse.consulting_room.manager;

import com.pulse.consulting_room.manager.dto.ConsultingRoomDetailDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "d5d9f5f9-5e79-4584-93ea-03278dcfa1ac|DTO|MANAGER")
public interface ConsultingRoomDetailDtoManager {

    @AutoGenerated(locked = true, uuid = "0c0b0781-37e0-381d-8f7e-506be48acc91")
    List<ConsultingRoomDetailDto> getByCampusOrganizationIds(List<String> campusOrganizationId);

    @AutoGenerated(locked = true, uuid = "a1b98a44-2ea9-38fe-a3e9-db27d6ca5c55")
    List<ConsultingRoomDetailDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "eafeae3f-aaaf-3a3b-8d22-c4bb4f41d73e")
    ConsultingRoomDetailDto getById(String id);

    @AutoGenerated(locked = true, uuid = "ecbc94eb-3e9c-3fbe-88cb-52cfbaabce00")
    List<ConsultingRoomDetailDto> getByCampusOrganizationId(String campusOrganizationId);
}
