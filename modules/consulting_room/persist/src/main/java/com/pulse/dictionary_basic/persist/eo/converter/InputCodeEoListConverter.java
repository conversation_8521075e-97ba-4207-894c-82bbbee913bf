package com.pulse.dictionary_basic.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<InputCodeEo> */
@Converter
@AutoGenerated(locked = true, uuid = "ef39703b-3052-3a6a-8b39-f38cc6279793")
public class InputCodeEoListConverter implements AttributeConverter<List<InputCodeEo>, String> {

    /** convert List<InputCodeEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<InputCodeEo> inputCodeEoList) {
        if (inputCodeEoList == null || inputCodeEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(inputCodeEoList);
        }
    }

    /** convert DB column to List<InputCodeEo> */
    @AutoGenerated(locked = true)
    public List<InputCodeEo> convertToEntityAttribute(String inputCodeEoListJson) {
        if (StrUtil.isEmpty(inputCodeEoListJson)) {
            return new ArrayList<InputCodeEo>();
        } else {
            return JsonUtils.readObject(
                    inputCodeEoListJson, new TypeReference<List<InputCodeEo>>() {});
        }
    }
}
