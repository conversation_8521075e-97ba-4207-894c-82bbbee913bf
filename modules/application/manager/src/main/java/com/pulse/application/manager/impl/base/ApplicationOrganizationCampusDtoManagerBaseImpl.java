package com.pulse.application.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.ApplicationBaseDtoManager;
import com.pulse.application.manager.ApplicationOrganizationCampusDtoManager;
import com.pulse.application.manager.ApplicationOrganizationDtoManager;
import com.pulse.application.manager.converter.ApplicationOrganizationCampusDtoConverter;
import com.pulse.application.manager.converter.ApplicationOrganizationDtoConverter;
import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.application.manager.dto.ApplicationOrganizationCampusDto;
import com.pulse.application.manager.dto.ApplicationOrganizationDto;
import com.pulse.application.manager.facade.organization.OrganizationCampusDtoServiceInApplicationRpcAdapter;
import com.pulse.application.persist.dos.ApplicationOrganization;
import com.pulse.application.persist.mapper.ApplicationOrganizationDao;
import com.pulse.organization.manager.dto.OrganizationCampusDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "c49395b8-995e-4b11-929b-38f37eb04ada|DTO|BASE_MANAGER_IMPL")
public abstract class ApplicationOrganizationCampusDtoManagerBaseImpl
        implements ApplicationOrganizationCampusDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationBaseDtoManager applicationBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationOrganizationCampusDtoConverter applicationOrganizationCampusDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationOrganizationDao applicationOrganizationDao;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationOrganizationDtoConverter applicationOrganizationDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationOrganizationDtoManager applicationOrganizationDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OrganizationCampusDtoServiceInApplicationRpcAdapter
            organizationCampusDtoServiceInApplicationRpcAdapter;

    @AutoGenerated(locked = true, uuid = "144c2920-4feb-37eb-a568-485dbf067d6a")
    @Override
    public ApplicationOrganizationCampusDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationOrganizationCampusDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        ApplicationOrganizationCampusDto applicationOrganizationCampusDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return applicationOrganizationCampusDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "63935ffb-49d1-3ee2-9c75-cc0a613ef9d5")
    @Override
    public List<ApplicationOrganizationCampusDto> getByOrganizationIds(
            List<String> organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(organizationId)) {
            return Collections.emptyList();
        }

        List<ApplicationOrganization> applicationOrganizationList =
                applicationOrganizationDao.getByOrganizationIds(organizationId);
        if (CollectionUtil.isEmpty(applicationOrganizationList)) {
            return Collections.emptyList();
        }

        return doConvertFromApplicationOrganizationToApplicationOrganizationCampusDto(
                applicationOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "7f458ac9-1717-3cf8-8dc6-7b69a19ae442")
    @Override
    public List<ApplicationOrganizationCampusDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<ApplicationOrganization> applicationOrganizationList =
                applicationOrganizationDao.getByIds(id);
        if (CollectionUtil.isEmpty(applicationOrganizationList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, ApplicationOrganization> applicationOrganizationMap =
                applicationOrganizationList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        applicationOrganizationList =
                id.stream()
                        .map(i -> applicationOrganizationMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromApplicationOrganizationToApplicationOrganizationCampusDto(
                applicationOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b69a5552-4072-3f7f-8b7a-4229156d6b37")
    @Override
    public List<ApplicationOrganizationCampusDto> getByApplicationIds(List<String> applicationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applicationId)) {
            return Collections.emptyList();
        }

        List<ApplicationOrganization> applicationOrganizationList =
                applicationOrganizationDao.getByApplicationIds(applicationId);
        if (CollectionUtil.isEmpty(applicationOrganizationList)) {
            return Collections.emptyList();
        }

        return doConvertFromApplicationOrganizationToApplicationOrganizationCampusDto(
                applicationOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b85e4114-5345-3f98-8ec2-2079f71d5972")
    @Override
    public List<ApplicationOrganizationCampusDto> getByApplicationId(String applicationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationOrganizationCampusDto> applicationOrganizationCampusDtoList =
                getByApplicationIds(Arrays.asList(applicationId));
        return applicationOrganizationCampusDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c9bcdcb1-347d-3f8d-8b62-8422cc4607aa")
    public List<ApplicationOrganizationCampusDto>
            doConvertFromApplicationOrganizationToApplicationOrganizationCampusDto(
                    List<ApplicationOrganization> applicationOrganizationList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(applicationOrganizationList)) {
            return Collections.emptyList();
        }

        Map<String, String> applicationIdMap =
                applicationOrganizationList.stream()
                        .filter(i -> i.getApplicationId() != null)
                        .collect(
                                Collectors.toMap(
                                        ApplicationOrganization::getId,
                                        ApplicationOrganization::getApplicationId));
        List<ApplicationBaseDto> applicationIdApplicationBaseDtoList =
                applicationBaseDtoManager.getByIds(
                        new ArrayList<>(new HashSet<>(applicationIdMap.values())));
        Map<String, ApplicationBaseDto> applicationIdApplicationBaseDtoMapRaw =
                applicationIdApplicationBaseDtoList.stream()
                        .collect(Collectors.toMap(ApplicationBaseDto::getId, i -> i));
        Map<String, ApplicationBaseDto> applicationIdApplicationBaseDtoMap =
                applicationIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        applicationIdApplicationBaseDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                applicationIdApplicationBaseDtoMapRaw.get(
                                                        i.getValue())));
        Map<String, String> organizationIdMap =
                applicationOrganizationList.stream()
                        .filter(i -> i.getOrganizationId() != null)
                        .collect(
                                Collectors.toMap(
                                        ApplicationOrganization::getId,
                                        ApplicationOrganization::getOrganizationId));
        List<OrganizationCampusDto> organizationIdOrganizationCampusDtoList =
                organizationCampusDtoServiceInApplicationRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(organizationIdMap.values())));
        Map<String, OrganizationCampusDto> organizationIdOrganizationCampusDtoMapRaw =
                organizationIdOrganizationCampusDtoList.stream()
                        .collect(Collectors.toMap(OrganizationCampusDto::getId, i -> i));
        Map<String, OrganizationCampusDto> organizationIdOrganizationCampusDtoMap =
                organizationIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        organizationIdOrganizationCampusDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                organizationIdOrganizationCampusDtoMapRaw.get(
                                                        i.getValue())));

        List<ApplicationOrganizationDto> baseDtoList =
                applicationOrganizationDtoConverter
                        .convertFromApplicationOrganizationToApplicationOrganizationDto(
                                applicationOrganizationList);
        Map<String, ApplicationOrganizationCampusDto> dtoMap =
                applicationOrganizationCampusDtoConverter
                        .convertFromApplicationOrganizationDtoToApplicationOrganizationCampusDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        ApplicationOrganizationCampusDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<ApplicationOrganizationCampusDto> applicationOrganizationCampusDtoList =
                new ArrayList<>();
        for (ApplicationOrganization i : applicationOrganizationList) {
            ApplicationOrganizationCampusDto applicationOrganizationCampusDto =
                    dtoMap.get(i.getId());
            if (applicationOrganizationCampusDto == null) {
                continue;
            }

            if (null != i.getApplicationId()) {
                applicationOrganizationCampusDto.setApplication(
                        applicationIdApplicationBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getOrganizationId()) {
                applicationOrganizationCampusDto.setOrganization(
                        organizationIdOrganizationCampusDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            applicationOrganizationCampusDtoList.add(applicationOrganizationCampusDto);
        }
        return applicationOrganizationCampusDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "d55fa365-0639-3672-8897-ca919d66e83f")
    @Override
    public List<ApplicationOrganizationCampusDto> getByOrganizationId(String organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationOrganizationCampusDto> applicationOrganizationCampusDtoList =
                getByOrganizationIds(Arrays.asList(organizationId));
        return applicationOrganizationCampusDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
