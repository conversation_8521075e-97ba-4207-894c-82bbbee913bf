package com.pulse.application.entrance.web.query.collector;

import com.pulse.application.entrance.web.converter.ApplicationDepartmentVoConverter;
import com.pulse.application.entrance.web.query.assembler.ApplicationDepartmentVoDataAssembler.ApplicationDepartmentVoDataHolder;
import com.pulse.application.entrance.web.vo.ApplicationDepartmentVo;
import com.pulse.application.manager.dto.ApplicationDepartmentBaseDto;
import com.pulse.application.manager.dto.ApplicationDepartmentDto;
import com.pulse.application.manager.facade.organization.OrganizationBaseDtoServiceInApplicationRpcAdapter;
import com.pulse.application.service.ApplicationDepartmentBaseDtoService;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ApplicationDepartmentVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "597ac1a6-a667-3342-a6ae-ff0ae2badb9e")
public class ApplicationDepartmentVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentBaseDtoService applicationDepartmentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentVoConverter applicationDepartmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentVoDataCollector applicationDepartmentVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoServiceInApplicationRpcAdapter
            organizationBaseDtoServiceInApplicationRpcAdapter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "02f4c257-e942-3239-995b-7d83785963f1")
    public void collectDataDefault(ApplicationDepartmentVoDataHolder dataHolder) {
        applicationDepartmentVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "1e8e85e1-fbb4-3bad-9365-1205d292bfad")
    private void fillDataWhenNecessary(ApplicationDepartmentVoDataHolder dataHolder) {
        List<ApplicationDepartmentBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.department == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ApplicationDepartmentBaseDto::getDepartmentId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoServiceInApplicationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, ApplicationDepartmentVo.OrganizationBaseVo> dtoVoMap =
                    applicationDepartmentVoConverter.convertToOrganizationBaseVoMap(baseDtoList);
            Map<OrganizationBaseDto, ApplicationDepartmentVo.OrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.department =
                    rootDtoList.stream()
                            .map(ApplicationDepartmentBaseDto::getDepartmentId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取ApplicationDepartmentDto数据填充ApplicationDepartmentVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "c4075e6d-ddfe-3fe9-bb08-e5227a09807d")
    public void collectDataWithDtoData(
            List<ApplicationDepartmentDto> dtoList, ApplicationDepartmentVoDataHolder dataHolder) {
        List<OrganizationBaseDto> departmentList = new ArrayList<>();

        for (ApplicationDepartmentDto rootDto : dtoList) {
            OrganizationBaseDto departmentDto = rootDto.getDepartment();
            if (departmentDto != null) {
                departmentList.add(departmentDto);
            }
        }

        // access department
        Map<OrganizationBaseDto, ApplicationDepartmentVo.OrganizationBaseVo> departmentVoMap =
                applicationDepartmentVoConverter.convertToOrganizationBaseVoMap(departmentList);
        dataHolder.department =
                departmentList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> departmentVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
