package com.pulse.application.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.application.manager.bo.*;
import com.pulse.application.manager.bo.ApplicationBO;
import com.pulse.application.manager.bo.ApplicationOrganizationBO;
import com.pulse.application.persist.dos.Application;
import com.pulse.application.persist.dos.ApplicationDepartment;
import com.pulse.application.persist.dos.ApplicationDetail;
import com.pulse.application.persist.dos.ApplicationMenu;
import com.pulse.application.persist.dos.ApplicationOrganization;
import com.pulse.application.service.base.BaseApplicationBOService.CreateApplicationBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.CreateApplicationMenuBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.CreateApplicationOrganizationBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.CreateApplicationOrganizationDetailBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.CreateDepartmentBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.CreateDepartmentsBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.CreateDetailBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.CreateListApplicationMenuBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.DeleteApplicationMenuBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.DeleteApplicationOrganizationBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.DeleteDepartmentBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.DeleteDetailBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.MergeApplicationBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.MergeApplicationDetailBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.MergeApplicationMenuBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.MergeApplicationOrganizationBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.MergeDepartmentsBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.MergeDetailBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.UpdateApplicationBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.UpdateApplicationStatusBoResult;
import com.pulse.application.service.base.BaseApplicationBOService.UpdateListApplicationMenuBoResult;
import com.pulse.application.service.bto.CreateApplicationBto;
import com.pulse.application.service.bto.CreateApplicationMenuBto;
import com.pulse.application.service.bto.CreateApplicationOrganizationBto;
import com.pulse.application.service.bto.CreateApplicationOrganizationDetailBto;
import com.pulse.application.service.bto.CreateDepartmentBto;
import com.pulse.application.service.bto.CreateDepartmentsBto;
import com.pulse.application.service.bto.CreateDetailBto;
import com.pulse.application.service.bto.CreateListApplicationMenuBto;
import com.pulse.application.service.bto.DeleteApplicationMenuBto;
import com.pulse.application.service.bto.DeleteApplicationOrganizationBto;
import com.pulse.application.service.bto.DeleteDepartmentBto;
import com.pulse.application.service.bto.DeleteDetailBto;
import com.pulse.application.service.bto.MergeApplicationBto;
import com.pulse.application.service.bto.MergeApplicationDetailBto;
import com.pulse.application.service.bto.MergeApplicationMenuBto;
import com.pulse.application.service.bto.MergeApplicationOrganizationBto;
import com.pulse.application.service.bto.MergeDepartmentsBto;
import com.pulse.application.service.bto.MergeDetailBto;
import com.pulse.application.service.bto.UpdateApplicationBto;
import com.pulse.application.service.bto.UpdateApplicationStatusBto;
import com.pulse.application.service.bto.UpdateListApplicationMenuBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "37a31c7e-b87d-3324-a9eb-b7367d048755")
public class BaseApplicationBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 新增应用 */
    @AutoGenerated(locked = true)
    protected CreateApplicationBoResult createApplicationBase(
            CreateApplicationBto createApplicationBto) {
        if (createApplicationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateApplicationBoResult boResult = new CreateApplicationBoResult();
        ApplicationBO applicationBO =
                createCreateApplicationOnDuplicateThrowEx(boResult, createApplicationBto);
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 创建对象ApplicationDepartmentBto */
    @AutoGenerated(locked = true)
    private void createApplicationDepartmentBto(
            CreateDepartmentBoResult boResult,
            CreateDepartmentBto createDepartmentBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(createDepartmentBto.getApplicationDepartmentBtoList())) {
            for (CreateDepartmentBto.ApplicationDepartmentBto item :
                    createDepartmentBto.getApplicationDepartmentBtoList()) {
                ApplicationDepartmentBO subBo = new ApplicationDepartmentBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "departmentId")) {
                    subBo.setDepartmentId(item.getDepartmentId());
                }
                subBo.setApplicationBO(applicationBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("application_department")));
                applicationBO.getApplicationDepartmentBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象ApplicationDepartmentBto */
    @AutoGenerated(locked = true)
    private void createApplicationDepartmentBto(
            BaseApplicationBOService.CreateDepartmentsBoResult boResult,
            CreateDepartmentsBto createDepartmentsBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(createDepartmentsBto.getApplicationDepartmentBtoList())) {
            for (CreateDepartmentsBto.ApplicationDepartmentBto item :
                    createDepartmentsBto.getApplicationDepartmentBtoList()) {
                ApplicationDepartmentBO subBo = new ApplicationDepartmentBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "departmentId")) {
                    subBo.setDepartmentId(item.getDepartmentId());
                }
                subBo.setApplicationBO(applicationBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("application_department")));
                applicationBO.getApplicationDepartmentBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ApplicationDepartmentBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createApplicationDepartmentBtoOnDuplicateUpdate(
            MergeDepartmentsBoResult boResult,
            MergeDepartmentsBto mergeDepartmentsBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(mergeDepartmentsBto.getApplicationDepartmentBtoList())) {
            for (MergeDepartmentsBto.ApplicationDepartmentBto item :
                    mergeDepartmentsBto.getApplicationDepartmentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationDepartmentBO> any =
                        applicationBO.getApplicationDepartmentBOSet().stream()
                                .filter(
                                        applicationDepartmentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationDepartmentBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ApplicationDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                    } else {
                        ApplicationDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                    }
                } else {
                    ApplicationDepartmentBO subBo = new ApplicationDepartmentBO();
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("application_department")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationDepartmentBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象ApplicationDetailBto */
    @AutoGenerated(locked = true)
    private void createApplicationDetailBto(
            CreateApplicationOrganizationBoResult boResult,
            CreateApplicationOrganizationBto.ApplicationOrganizationBto applicationOrganizationBto,
            ApplicationOrganizationBO applicationOrganizationBO) {
        if (CollectionUtil.isNotEmpty(applicationOrganizationBto.getApplicationDetailBtoList())) {
            for (CreateApplicationOrganizationBto.ApplicationDetailBto item :
                    applicationOrganizationBto.getApplicationDetailBtoList()) {
                ApplicationDetailBO subBo = new ApplicationDetailBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "targetApplicationId")) {
                    subBo.setTargetApplicationId(item.getTargetApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "targetType")) {
                    subBo.setTargetType(item.getTargetType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "activeFlag")) {
                    subBo.setActiveFlag(item.getActiveFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "type")) {
                    subBo.setType(item.getType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "applicationType")) {
                    subBo.setApplicationType(item.getApplicationType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "defaultFlag")) {
                    subBo.setDefaultFlag(item.getDefaultFlag());
                }
                subBo.setApplicationOrganizationBO(applicationOrganizationBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("application_detail")));
                applicationOrganizationBO.getApplicationDetailBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象ApplicationDetailBto */
    @AutoGenerated(locked = true)
    private void createApplicationDetailBto(
            CreateDetailBoResult boResult,
            CreateDetailBto.ApplicationOrganizationBto applicationOrganizationBto,
            ApplicationOrganizationBO applicationOrganizationBO) {
        if (CollectionUtil.isNotEmpty(applicationOrganizationBto.getApplicationDetailBtoList())) {
            for (CreateDetailBto.ApplicationDetailBto item :
                    applicationOrganizationBto.getApplicationDetailBtoList()) {
                ApplicationDetailBO subBo = new ApplicationDetailBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "targetApplicationId")) {
                    subBo.setTargetApplicationId(item.getTargetApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "targetType")) {
                    subBo.setTargetType(item.getTargetType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "activeFlag")) {
                    subBo.setActiveFlag(item.getActiveFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "type")) {
                    subBo.setType(item.getType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "applicationType")) {
                    subBo.setApplicationType(item.getApplicationType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "defaultFlag")) {
                    subBo.setDefaultFlag(item.getDefaultFlag());
                }
                subBo.setApplicationOrganizationBO(applicationOrganizationBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("application_detail")));
                applicationOrganizationBO.getApplicationDetailBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ApplicationDetailBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createApplicationDetailBtoOnDuplicateThrowEx(
            CreateApplicationOrganizationDetailBoResult boResult,
            CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto
                    applicationOrganizationBto,
            ApplicationOrganizationBO applicationOrganizationBO) {
        if (CollectionUtil.isNotEmpty(applicationOrganizationBto.getApplicationDetailBtoList())) {
            for (CreateApplicationOrganizationDetailBto.ApplicationDetailBto item :
                    applicationOrganizationBto.getApplicationDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationDetailBO> any =
                        applicationOrganizationBO.getApplicationDetailBOSet().stream()
                                .filter(
                                        applicationDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "application_detail");
                        throw new IgnoredException(400, "应用详细对照已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "application_detail",
                                any.get().getId());
                        throw new IgnoredException(400, "应用详细对照已存在");
                    }
                } else {
                    ApplicationDetailBO subBo = new ApplicationDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "targetApplicationId")) {
                        subBo.setTargetApplicationId(item.getTargetApplicationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "targetType")) {
                        subBo.setTargetType(item.getTargetType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "activeFlag")) {
                        subBo.setActiveFlag(item.getActiveFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "type")) {
                        subBo.setType(item.getType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicationType")) {
                        subBo.setApplicationType(item.getApplicationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "defaultFlag")) {
                        subBo.setDefaultFlag(item.getDefaultFlag());
                    }
                    subBo.setApplicationOrganizationBO(applicationOrganizationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("application_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationOrganizationBO.getApplicationDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ApplicationDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createApplicationDetailBtoOnDuplicateUpdate(
            MergeApplicationDetailBoResult boResult,
            MergeApplicationDetailBto.ApplicationOrganizationBto applicationOrganizationBto,
            ApplicationOrganizationBO applicationOrganizationBO) {
        if (CollectionUtil.isNotEmpty(applicationOrganizationBto.getApplicationDetailBtoList())) {
            for (MergeApplicationDetailBto.ApplicationDetailBto item :
                    applicationOrganizationBto.getApplicationDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationDetailBO> any =
                        applicationOrganizationBO.getApplicationDetailBOSet().stream()
                                .filter(
                                        applicationDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ApplicationDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetApplicationId")) {
                            bo.setTargetApplicationId(item.getTargetApplicationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetType")) {
                            bo.setTargetType(item.getTargetType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "activeFlag")) {
                            bo.setActiveFlag(item.getActiveFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "type")) {
                            bo.setType(item.getType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicationType")) {
                            bo.setApplicationType(item.getApplicationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                    } else {
                        ApplicationDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetApplicationId")) {
                            bo.setTargetApplicationId(item.getTargetApplicationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetType")) {
                            bo.setTargetType(item.getTargetType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "activeFlag")) {
                            bo.setActiveFlag(item.getActiveFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "type")) {
                            bo.setType(item.getType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicationType")) {
                            bo.setApplicationType(item.getApplicationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                    }
                } else {
                    ApplicationDetailBO subBo = new ApplicationDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "targetApplicationId")) {
                        subBo.setTargetApplicationId(item.getTargetApplicationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "targetType")) {
                        subBo.setTargetType(item.getTargetType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "activeFlag")) {
                        subBo.setActiveFlag(item.getActiveFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "type")) {
                        subBo.setType(item.getType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicationType")) {
                        subBo.setApplicationType(item.getApplicationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "defaultFlag")) {
                        subBo.setDefaultFlag(item.getDefaultFlag());
                    }
                    subBo.setApplicationOrganizationBO(applicationOrganizationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("application_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationOrganizationBO.getApplicationDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ApplicationDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createApplicationDetailBtoOnDuplicateUpdate(
            MergeDetailBoResult boResult,
            MergeDetailBto.ApplicationOrganizationBto applicationOrganizationBto,
            ApplicationOrganizationBO applicationOrganizationBO) {
        if (CollectionUtil.isNotEmpty(applicationOrganizationBto.getApplicationDetailBtoList())) {
            for (MergeDetailBto.ApplicationDetailBto item :
                    applicationOrganizationBto.getApplicationDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationDetailBO> any =
                        applicationOrganizationBO.getApplicationDetailBOSet().stream()
                                .filter(
                                        applicationDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ApplicationDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetApplicationId")) {
                            bo.setTargetApplicationId(item.getTargetApplicationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetType")) {
                            bo.setTargetType(item.getTargetType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "activeFlag")) {
                            bo.setActiveFlag(item.getActiveFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "type")) {
                            bo.setType(item.getType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicationType")) {
                            bo.setApplicationType(item.getApplicationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                    } else {
                        ApplicationDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetApplicationId")) {
                            bo.setTargetApplicationId(item.getTargetApplicationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "targetType")) {
                            bo.setTargetType(item.getTargetType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "activeFlag")) {
                            bo.setActiveFlag(item.getActiveFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "type")) {
                            bo.setType(item.getType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicationType")) {
                            bo.setApplicationType(item.getApplicationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                    }
                } else {
                    ApplicationDetailBO subBo = new ApplicationDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "targetApplicationId")) {
                        subBo.setTargetApplicationId(item.getTargetApplicationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "targetType")) {
                        subBo.setTargetType(item.getTargetType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "activeFlag")) {
                        subBo.setActiveFlag(item.getActiveFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "type")) {
                        subBo.setType(item.getType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicationType")) {
                        subBo.setApplicationType(item.getApplicationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "defaultFlag")) {
                        subBo.setDefaultFlag(item.getDefaultFlag());
                    }
                    subBo.setApplicationOrganizationBO(applicationOrganizationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("application_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationOrganizationBO.getApplicationDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建应用菜单 */
    @AutoGenerated(locked = true)
    protected CreateApplicationMenuBoResult createApplicationMenuBase(
            CreateApplicationMenuBto createApplicationMenuBto) {
        if (createApplicationMenuBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateApplicationMenuBoResult boResult = new CreateApplicationMenuBoResult();
        ApplicationBO applicationBO =
                createCreateApplicationMenu(boResult, createApplicationMenuBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationMenuBto, "__$validPropertySet"),
                    "applicationMenuBtoList")) {
                createApplicationMenuBtoOnDuplicateThrowEx(
                        boResult, createApplicationMenuBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 创建对象:ApplicationMenuBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createApplicationMenuBtoOnDuplicateThrowEx(
            BaseApplicationBOService.CreateApplicationMenuBoResult boResult,
            CreateApplicationMenuBto createApplicationMenuBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(createApplicationMenuBto.getApplicationMenuBtoList())) {
            for (CreateApplicationMenuBto.ApplicationMenuBto item :
                    createApplicationMenuBto.getApplicationMenuBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationMenuBO> any =
                        applicationBO.getApplicationMenuBOSet().stream()
                                .filter(
                                        applicationMenuBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationMenuBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！", any.get().getId(), "application_menu");
                        throw new IgnoredException(400, "应用维护菜单已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "application_menu",
                                any.get().getId());
                        throw new IgnoredException(400, "应用维护菜单已存在");
                    }
                } else {
                    ApplicationMenuBO subBo = new ApplicationMenuBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "menuId")) {
                        subBo.setMenuId(item.getMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "customName")) {
                        subBo.setCustomName(item.getCustomName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "parentMenuId")) {
                        subBo.setParentMenuId(item.getParentMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "parentMenuName")) {
                        subBo.setParentMenuName(item.getParentMenuName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sameLevelGroupNumber")) {
                        subBo.setSameLevelGroupNumber(item.getSameLevelGroupNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "fixedFlag")) {
                        subBo.setFixedFlag(item.getFixedFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "initialFlag")) {
                        subBo.setInitialFlag(item.getInitialFlag());
                    }
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("application_menu")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationMenuBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ApplicationMenuBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createApplicationMenuBtoOnDuplicateThrowEx(
            CreateListApplicationMenuBoResult boResult,
            CreateListApplicationMenuBto createListApplicationMenuBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(createListApplicationMenuBto.getApplicationMenuBtoList())) {
            for (CreateListApplicationMenuBto.ApplicationMenuBto item :
                    createListApplicationMenuBto.getApplicationMenuBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationMenuBO> any =
                        applicationBO.getApplicationMenuBOSet().stream()
                                .filter(
                                        applicationMenuBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationMenuBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！", any.get().getId(), "application_menu");
                        throw new IgnoredException(400, "应用维护菜单已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "application_menu",
                                any.get().getId());
                        throw new IgnoredException(400, "应用维护菜单已存在");
                    }
                } else {
                    ApplicationMenuBO subBo = new ApplicationMenuBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "menuId")) {
                        subBo.setMenuId(item.getMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "customName")) {
                        subBo.setCustomName(item.getCustomName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "parentMenuId")) {
                        subBo.setParentMenuId(item.getParentMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "parentMenuName")) {
                        subBo.setParentMenuName(item.getParentMenuName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sameLevelGroupNumber")) {
                        subBo.setSameLevelGroupNumber(item.getSameLevelGroupNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "fixedFlag")) {
                        subBo.setFixedFlag(item.getFixedFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "initialFlag")) {
                        subBo.setInitialFlag(item.getInitialFlag());
                    }
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("application_menu")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationMenuBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ApplicationMenuBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createApplicationMenuBtoOnDuplicateUpdate(
            MergeApplicationMenuBoResult boResult,
            MergeApplicationMenuBto mergeApplicationMenuBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(mergeApplicationMenuBto.getApplicationMenuBtoList())) {
            for (MergeApplicationMenuBto.ApplicationMenuBto item :
                    mergeApplicationMenuBto.getApplicationMenuBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationMenuBO> any =
                        applicationBO.getApplicationMenuBOSet().stream()
                                .filter(
                                        applicationMenuBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationMenuBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ApplicationMenuBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationMenu());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "menuId")) {
                            bo.setMenuId(item.getMenuId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "customName")) {
                            bo.setCustomName(item.getCustomName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "parentMenuId")) {
                            bo.setParentMenuId(item.getParentMenuId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "parentMenuName")) {
                            bo.setParentMenuName(item.getParentMenuName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sameLevelGroupNumber")) {
                            bo.setSameLevelGroupNumber(item.getSameLevelGroupNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "fixedFlag")) {
                            bo.setFixedFlag(item.getFixedFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "initialFlag")) {
                            bo.setInitialFlag(item.getInitialFlag());
                        }
                    } else {
                        ApplicationMenuBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationMenu());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "menuId")) {
                            bo.setMenuId(item.getMenuId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "customName")) {
                            bo.setCustomName(item.getCustomName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "parentMenuId")) {
                            bo.setParentMenuId(item.getParentMenuId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "parentMenuName")) {
                            bo.setParentMenuName(item.getParentMenuName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sameLevelGroupNumber")) {
                            bo.setSameLevelGroupNumber(item.getSameLevelGroupNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "fixedFlag")) {
                            bo.setFixedFlag(item.getFixedFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "initialFlag")) {
                            bo.setInitialFlag(item.getInitialFlag());
                        }
                    }
                } else {
                    ApplicationMenuBO subBo = new ApplicationMenuBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "menuId")) {
                        subBo.setMenuId(item.getMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "customName")) {
                        subBo.setCustomName(item.getCustomName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "parentMenuId")) {
                        subBo.setParentMenuId(item.getParentMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "parentMenuName")) {
                        subBo.setParentMenuName(item.getParentMenuName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sameLevelGroupNumber")) {
                        subBo.setSameLevelGroupNumber(item.getSameLevelGroupNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "fixedFlag")) {
                        subBo.setFixedFlag(item.getFixedFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "initialFlag")) {
                        subBo.setInitialFlag(item.getInitialFlag());
                    }
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("application_menu")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationMenuBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 新增应用组织关系 */
    @AutoGenerated(locked = true)
    protected CreateApplicationOrganizationBoResult createApplicationOrganizationBase(
            CreateApplicationOrganizationBto createApplicationOrganizationBto) {
        if (createApplicationOrganizationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateApplicationOrganizationBoResult boResult =
                new CreateApplicationOrganizationBoResult();
        ApplicationBO applicationBO =
                updateCreateApplicationOrganizationOnMissThrowEx(
                        boResult, createApplicationOrganizationBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationOrganizationBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                createApplicationOrganizationBto(
                        boResult, createApplicationOrganizationBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 创建对象ApplicationOrganizationBto */
    @AutoGenerated(locked = true)
    private void createApplicationOrganizationBto(
            CreateApplicationOrganizationBoResult boResult,
            CreateApplicationOrganizationBto createApplicationOrganizationBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(
                createApplicationOrganizationBto.getApplicationOrganizationBtoList())) {
            for (CreateApplicationOrganizationBto.ApplicationOrganizationBto item :
                    createApplicationOrganizationBto.getApplicationOrganizationBtoList()) {
                ApplicationOrganizationBO subBo = new ApplicationOrganizationBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "organizationId")) {
                    subBo.setOrganizationId(item.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "description")) {
                    subBo.setDescription(item.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "sortNumber")) {
                    subBo.setSortNumber(item.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "shortName")) {
                    subBo.setShortName(item.getShortName());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "inputCode")) {
                    subBo.setInputCode(item.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "inventory")) {
                    subBo.setInventory(item.getInventory());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "emergencyFlag")) {
                    subBo.setEmergencyFlag(item.getEmergencyFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "outpatientFeeFlag")) {
                    subBo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "pharmacyFlag")) {
                    subBo.setPharmacyFlag(item.getPharmacyFlag());
                }
                subBo.setApplicationBO(applicationBO);
                subBo.setId(
                        String.valueOf(this.idGenerator.allocateId("application_organization")));
                applicationBO.getApplicationOrganizationBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
                createApplicationDetailBto(boResult, item, subBo);
            }
        }
    }

    /** 创建对象:ApplicationOrganizationBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createApplicationOrganizationBtoOnDuplicateThrowEx(
            CreateApplicationOrganizationDetailBoResult boResult,
            CreateApplicationOrganizationDetailBto createApplicationOrganizationDetailBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(
                createApplicationOrganizationDetailBto.getApplicationOrganizationBtoList())) {
            for (CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto item :
                    createApplicationOrganizationDetailBto.getApplicationOrganizationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationOrganizationBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "application_organization");
                        throw new IgnoredException(400, "应用组织关系已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "application_organization",
                                any.get().getId());
                        throw new IgnoredException(400, "应用组织关系已存在");
                    }
                } else {
                    ApplicationOrganizationBO subBo = new ApplicationOrganizationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "description")) {
                        subBo.setDescription(item.getDescription());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "shortName")) {
                        subBo.setShortName(item.getShortName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inputCode")) {
                        subBo.setInputCode(item.getInputCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inventory")) {
                        subBo.setInventory(item.getInventory());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "emergencyFlag")) {
                        subBo.setEmergencyFlag(item.getEmergencyFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "outpatientFeeFlag")) {
                        subBo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "pharmacyFlag")) {
                        subBo.setPharmacyFlag(item.getPharmacyFlag());
                    }
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("application_organization")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationOrganizationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                    createApplicationDetailBtoOnDuplicateThrowEx(boResult, item, subBo);
                }
            }
        }
    }

    /** 创建对象:ApplicationOrganizationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createApplicationOrganizationBtoOnDuplicateUpdate(
            BaseApplicationBOService.UpdateApplicationBoResult boResult,
            UpdateApplicationBto updateApplicationBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(updateApplicationBto.getApplicationOrganizationBtoList())) {
            for (UpdateApplicationBto.ApplicationOrganizationBto item :
                    updateApplicationBto.getApplicationOrganizationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationOrganizationBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ApplicationOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "description")) {
                            bo.setDescription(item.getDescription());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "shortName")) {
                            bo.setShortName(item.getShortName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inputCode")) {
                            bo.setInputCode(item.getInputCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inventory")) {
                            bo.setInventory(item.getInventory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "emergencyFlag")) {
                            bo.setEmergencyFlag(item.getEmergencyFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpatientFeeFlag")) {
                            bo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "pharmacyFlag")) {
                            bo.setPharmacyFlag(item.getPharmacyFlag());
                        }
                    } else {
                        ApplicationOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "description")) {
                            bo.setDescription(item.getDescription());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "shortName")) {
                            bo.setShortName(item.getShortName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inputCode")) {
                            bo.setInputCode(item.getInputCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inventory")) {
                            bo.setInventory(item.getInventory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "emergencyFlag")) {
                            bo.setEmergencyFlag(item.getEmergencyFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpatientFeeFlag")) {
                            bo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "pharmacyFlag")) {
                            bo.setPharmacyFlag(item.getPharmacyFlag());
                        }
                    }
                } else {
                    ApplicationOrganizationBO subBo = new ApplicationOrganizationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "description")) {
                        subBo.setDescription(item.getDescription());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "shortName")) {
                        subBo.setShortName(item.getShortName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inputCode")) {
                        subBo.setInputCode(item.getInputCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inventory")) {
                        subBo.setInventory(item.getInventory());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "emergencyFlag")) {
                        subBo.setEmergencyFlag(item.getEmergencyFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "outpatientFeeFlag")) {
                        subBo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "pharmacyFlag")) {
                        subBo.setPharmacyFlag(item.getPharmacyFlag());
                    }
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("application_organization")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationOrganizationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ApplicationOrganizationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createApplicationOrganizationBtoOnDuplicateUpdate(
            BaseApplicationBOService.MergeApplicationOrganizationBoResult boResult,
            MergeApplicationOrganizationBto mergeApplicationOrganizationBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(
                mergeApplicationOrganizationBto.getApplicationOrganizationBtoList())) {
            for (MergeApplicationOrganizationBto.ApplicationOrganizationBto item :
                    mergeApplicationOrganizationBto.getApplicationOrganizationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationOrganizationBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ApplicationOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "shortName")) {
                            bo.setShortName(item.getShortName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inputCode")) {
                            bo.setInputCode(item.getInputCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inventory")) {
                            bo.setInventory(item.getInventory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "emergencyFlag")) {
                            bo.setEmergencyFlag(item.getEmergencyFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpatientFeeFlag")) {
                            bo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "pharmacyFlag")) {
                            bo.setPharmacyFlag(item.getPharmacyFlag());
                        }
                    } else {
                        ApplicationOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "shortName")) {
                            bo.setShortName(item.getShortName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inputCode")) {
                            bo.setInputCode(item.getInputCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inventory")) {
                            bo.setInventory(item.getInventory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "emergencyFlag")) {
                            bo.setEmergencyFlag(item.getEmergencyFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpatientFeeFlag")) {
                            bo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "pharmacyFlag")) {
                            bo.setPharmacyFlag(item.getPharmacyFlag());
                        }
                    }
                } else {
                    ApplicationOrganizationBO subBo = new ApplicationOrganizationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "shortName")) {
                        subBo.setShortName(item.getShortName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inputCode")) {
                        subBo.setInputCode(item.getInputCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inventory")) {
                        subBo.setInventory(item.getInventory());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "emergencyFlag")) {
                        subBo.setEmergencyFlag(item.getEmergencyFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "outpatientFeeFlag")) {
                        subBo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "pharmacyFlag")) {
                        subBo.setPharmacyFlag(item.getPharmacyFlag());
                    }
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("application_organization")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationOrganizationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ApplicationOrganizationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createApplicationOrganizationBtoOnDuplicateUpdate(
            MergeApplicationDetailBoResult boResult,
            MergeApplicationDetailBto mergeApplicationDetailBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(
                mergeApplicationDetailBto.getApplicationOrganizationBtoList())) {
            for (MergeApplicationDetailBto.ApplicationOrganizationBto item :
                    mergeApplicationDetailBto.getApplicationOrganizationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationOrganizationBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ApplicationOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "description")) {
                            bo.setDescription(item.getDescription());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "shortName")) {
                            bo.setShortName(item.getShortName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inputCode")) {
                            bo.setInputCode(item.getInputCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inventory")) {
                            bo.setInventory(item.getInventory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "emergencyFlag")) {
                            bo.setEmergencyFlag(item.getEmergencyFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpatientFeeFlag")) {
                            bo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "pharmacyFlag")) {
                            bo.setPharmacyFlag(item.getPharmacyFlag());
                        }
                        createApplicationDetailBtoOnDuplicateUpdate(boResult, item, bo);
                    } else {
                        ApplicationOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToApplicationOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "description")) {
                            bo.setDescription(item.getDescription());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "shortName")) {
                            bo.setShortName(item.getShortName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inputCode")) {
                            bo.setInputCode(item.getInputCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inventory")) {
                            bo.setInventory(item.getInventory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "emergencyFlag")) {
                            bo.setEmergencyFlag(item.getEmergencyFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpatientFeeFlag")) {
                            bo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "pharmacyFlag")) {
                            bo.setPharmacyFlag(item.getPharmacyFlag());
                        }
                        createApplicationDetailBtoOnDuplicateUpdate(boResult, item, bo);
                    }
                } else {
                    ApplicationOrganizationBO subBo = new ApplicationOrganizationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "description")) {
                        subBo.setDescription(item.getDescription());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "shortName")) {
                        subBo.setShortName(item.getShortName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inputCode")) {
                        subBo.setInputCode(item.getInputCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inventory")) {
                        subBo.setInventory(item.getInventory());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "emergencyFlag")) {
                        subBo.setEmergencyFlag(item.getEmergencyFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "outpatientFeeFlag")) {
                        subBo.setOutpatientFeeFlag(item.getOutpatientFeeFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "pharmacyFlag")) {
                        subBo.setPharmacyFlag(item.getPharmacyFlag());
                    }
                    subBo.setApplicationBO(applicationBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("application_organization")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    applicationBO.getApplicationOrganizationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                    createApplicationDetailBtoOnDuplicateUpdate(boResult, item, subBo);
                }
            }
        }
    }

    /** 新增应用关系详细 */
    @AutoGenerated(locked = true)
    protected CreateApplicationOrganizationDetailBoResult createApplicationOrganizationDetailBase(
            CreateApplicationOrganizationDetailBto createApplicationOrganizationDetailBto) {
        if (createApplicationOrganizationDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateApplicationOrganizationDetailBoResult boResult =
                new CreateApplicationOrganizationDetailBoResult();
        ApplicationBO applicationBO =
                updateCreateApplicationOrganizationDetailOnMissThrowEx(
                        boResult, createApplicationOrganizationDetailBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationOrganizationDetailBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                createApplicationOrganizationBtoOnDuplicateThrowEx(
                        boResult, createApplicationOrganizationDetailBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ApplicationBO createCreateApplicationMenu(
            CreateApplicationMenuBoResult boResult,
            CreateApplicationMenuBto createApplicationMenuBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        if (applicationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", applicationBO.getId(), "application");
                throw new IgnoredException(400, "应用已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "application",
                        applicationBO.getId());
                throw new IgnoredException(400, "应用已存在");
            }
        } else {
            applicationBO = new ApplicationBO();
            if (pkExist) {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            } else {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationMenuBto, "__$validPropertySet"),
                    "routerId")) {
                applicationBO.setRouterId(createApplicationMenuBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationMenuBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(createApplicationMenuBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationMenuBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createApplicationMenuBto.getStatus());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createApplicationMenuBto);
            addedBto.setBo(applicationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return applicationBO;
    }

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO createCreateApplicationOnDuplicateThrowEx(
            BaseApplicationBOService.CreateApplicationBoResult boResult,
            CreateApplicationBto createApplicationBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createApplicationBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(createApplicationBto.getId());
            if (applicationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (applicationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", applicationBO.getId(), "application");
                throw new IgnoredException(400, "应用已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "application",
                        applicationBO.getId(),
                        "application");
                throw new IgnoredException(400, "应用已存在");
            }
        } else {
            applicationBO = new ApplicationBO();
            if (pkExist) {
                applicationBO.setId(createApplicationBto.getId());
            } else {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "name")) {
                applicationBO.setName(createApplicationBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "inputCode")) {
                applicationBO.setInputCode(createApplicationBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "abbreviation")) {
                applicationBO.setAbbreviation(createApplicationBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "description")) {
                applicationBO.setDescription(createApplicationBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "templateApplicationFlag")) {
                applicationBO.setTemplateApplicationFlag(
                        createApplicationBto.getTemplateApplicationFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "templateApplicationId")) {
                applicationBO.setTemplateApplicationId(
                        createApplicationBto.getTemplateApplicationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "categoryId")) {
                applicationBO.setCategoryId(createApplicationBto.getCategoryId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "enableFlag")) {
                applicationBO.setEnableFlag(createApplicationBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "multipleInstanceFlag")) {
                applicationBO.setMultipleInstanceFlag(
                        createApplicationBto.getMultipleInstanceFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "organizationId")) {
                applicationBO.setOrganizationId(createApplicationBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "parentApplicationId")) {
                applicationBO.setParentApplicationId(createApplicationBto.getParentApplicationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "routerId")) {
                applicationBO.setRouterId(createApplicationBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(createApplicationBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createApplicationBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createApplicationBto.getStatus());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createApplicationBto);
            addedBto.setBo(applicationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return applicationBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ApplicationBO createCreateDepartments(
            CreateDepartmentsBoResult boResult, CreateDepartmentsBto createDepartmentsBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        if (applicationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", applicationBO.getId(), "application");
                throw new IgnoredException(400, "应用已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "application",
                        applicationBO.getId());
                throw new IgnoredException(400, "应用已存在");
            }
        } else {
            applicationBO = new ApplicationBO();
            if (pkExist) {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            } else {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDepartmentsBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createDepartmentsBto.getStatus());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createDepartmentsBto);
            addedBto.setBo(applicationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return applicationBO;
    }

    /** 创建应用科室对照 */
    @AutoGenerated(locked = true)
    protected CreateDepartmentBoResult createDepartmentBase(
            CreateDepartmentBto createDepartmentBto) {
        if (createDepartmentBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateDepartmentBoResult boResult = new CreateDepartmentBoResult();
        ApplicationBO applicationBO =
                updateCreateDepartmentOnMissThrowEx(boResult, createDepartmentBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDepartmentBto, "__$validPropertySet"),
                    "applicationDepartmentBtoList")) {
                createApplicationDepartmentBto(boResult, createDepartmentBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    @AutoGenerated(locked = true)
    protected CreateDepartmentsBoResult createDepartmentsBase(
            CreateDepartmentsBto createDepartmentsBto) {
        if (createDepartmentsBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateDepartmentsBoResult boResult = new CreateDepartmentsBoResult();
        ApplicationBO applicationBO = createCreateDepartments(boResult, createDepartmentsBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDepartmentsBto, "__$validPropertySet"),
                    "applicationDepartmentBtoList")) {
                createApplicationDepartmentBto(boResult, createDepartmentsBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 新增应用对照 */
    @AutoGenerated(locked = true)
    protected CreateDetailBoResult createDetailBase(CreateDetailBto createDetailBto) {
        if (createDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateDetailBoResult boResult = new CreateDetailBoResult();
        ApplicationBO applicationBO = updateCreateDetailOnMissThrowEx(boResult, createDetailBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createDetailBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                updateApplicationOrganizationBtoOnMissThrowEx(
                        boResult, createDetailBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 创建应用维护菜单那 */
    @AutoGenerated(locked = true)
    protected CreateListApplicationMenuBoResult createListApplicationMenuBase(
            CreateListApplicationMenuBto createListApplicationMenuBto) {
        if (createListApplicationMenuBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateListApplicationMenuBoResult boResult = new CreateListApplicationMenuBoResult();
        ApplicationBO applicationBO =
                updateCreateListApplicationMenuOnMissThrowEx(
                        boResult, createListApplicationMenuBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createListApplicationMenuBto, "__$validPropertySet"),
                    "applicationMenuBtoList")) {
                createApplicationMenuBtoOnDuplicateThrowEx(
                        boResult, createListApplicationMenuBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ApplicationBO createMergeApplicationOnDuplicateUpdate(
            BaseApplicationBOService.MergeApplicationBoResult boResult,
            MergeApplicationBto mergeApplicationBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeApplicationBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(mergeApplicationBto.getId());
            if (applicationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (applicationBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(applicationBO.convertToApplication());
                updatedBto.setBto(mergeApplicationBto);
                updatedBto.setBo(applicationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "name")) {
                    applicationBO.setName(mergeApplicationBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "inputCode")) {
                    applicationBO.setInputCode(mergeApplicationBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "abbreviation")) {
                    applicationBO.setAbbreviation(mergeApplicationBto.getAbbreviation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "description")) {
                    applicationBO.setDescription(mergeApplicationBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "templateApplicationFlag")) {
                    applicationBO.setTemplateApplicationFlag(
                            mergeApplicationBto.getTemplateApplicationFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "templateApplicationId")) {
                    applicationBO.setTemplateApplicationId(
                            mergeApplicationBto.getTemplateApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "categoryId")) {
                    applicationBO.setCategoryId(mergeApplicationBto.getCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "enableFlag")) {
                    applicationBO.setEnableFlag(mergeApplicationBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "multipleInstanceFlag")) {
                    applicationBO.setMultipleInstanceFlag(
                            mergeApplicationBto.getMultipleInstanceFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "organizationId")) {
                    applicationBO.setOrganizationId(mergeApplicationBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "parentApplicationId")) {
                    applicationBO.setParentApplicationId(
                            mergeApplicationBto.getParentApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "routerId")) {
                    applicationBO.setRouterId(mergeApplicationBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "code")) {
                    applicationBO.setCode(mergeApplicationBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "status")) {
                    applicationBO.setStatus(mergeApplicationBto.getStatus());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(applicationBO.convertToApplication());
                updatedBto.setBto(mergeApplicationBto);
                updatedBto.setBo(applicationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "name")) {
                    applicationBO.setName(mergeApplicationBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "inputCode")) {
                    applicationBO.setInputCode(mergeApplicationBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "abbreviation")) {
                    applicationBO.setAbbreviation(mergeApplicationBto.getAbbreviation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "description")) {
                    applicationBO.setDescription(mergeApplicationBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "templateApplicationFlag")) {
                    applicationBO.setTemplateApplicationFlag(
                            mergeApplicationBto.getTemplateApplicationFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "templateApplicationId")) {
                    applicationBO.setTemplateApplicationId(
                            mergeApplicationBto.getTemplateApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "categoryId")) {
                    applicationBO.setCategoryId(mergeApplicationBto.getCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "enableFlag")) {
                    applicationBO.setEnableFlag(mergeApplicationBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "multipleInstanceFlag")) {
                    applicationBO.setMultipleInstanceFlag(
                            mergeApplicationBto.getMultipleInstanceFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "organizationId")) {
                    applicationBO.setOrganizationId(mergeApplicationBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "parentApplicationId")) {
                    applicationBO.setParentApplicationId(
                            mergeApplicationBto.getParentApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "routerId")) {
                    applicationBO.setRouterId(mergeApplicationBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "code")) {
                    applicationBO.setCode(mergeApplicationBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationBto, "__$validPropertySet"),
                        "status")) {
                    applicationBO.setStatus(mergeApplicationBto.getStatus());
                }
            }
        } else {
            applicationBO = new ApplicationBO();
            if (pkExist) {
                applicationBO.setId(mergeApplicationBto.getId());
            } else {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "name")) {
                applicationBO.setName(mergeApplicationBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "inputCode")) {
                applicationBO.setInputCode(mergeApplicationBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "abbreviation")) {
                applicationBO.setAbbreviation(mergeApplicationBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "description")) {
                applicationBO.setDescription(mergeApplicationBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "templateApplicationFlag")) {
                applicationBO.setTemplateApplicationFlag(
                        mergeApplicationBto.getTemplateApplicationFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "templateApplicationId")) {
                applicationBO.setTemplateApplicationId(
                        mergeApplicationBto.getTemplateApplicationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "categoryId")) {
                applicationBO.setCategoryId(mergeApplicationBto.getCategoryId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "enableFlag")) {
                applicationBO.setEnableFlag(mergeApplicationBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "multipleInstanceFlag")) {
                applicationBO.setMultipleInstanceFlag(
                        mergeApplicationBto.getMultipleInstanceFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "organizationId")) {
                applicationBO.setOrganizationId(mergeApplicationBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "parentApplicationId")) {
                applicationBO.setParentApplicationId(mergeApplicationBto.getParentApplicationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "routerId")) {
                applicationBO.setRouterId(mergeApplicationBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(mergeApplicationBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeApplicationBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(mergeApplicationBto.getStatus());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeApplicationBto);
            addedBto.setBo(applicationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return applicationBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ApplicationBO createMergeApplicationOrganizationOnDuplicateUpdate(
            MergeApplicationOrganizationBoResult boResult,
            MergeApplicationOrganizationBto mergeApplicationOrganizationBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeApplicationOrganizationBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(mergeApplicationOrganizationBto.getId());
            if (applicationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (applicationBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(applicationBO.convertToApplication());
                updatedBto.setBto(mergeApplicationOrganizationBto);
                updatedBto.setBo(applicationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationOrganizationBto, "__$validPropertySet"),
                        "routerId")) {
                    applicationBO.setRouterId(mergeApplicationOrganizationBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationOrganizationBto, "__$validPropertySet"),
                        "code")) {
                    applicationBO.setCode(mergeApplicationOrganizationBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationOrganizationBto, "__$validPropertySet"),
                        "status")) {
                    applicationBO.setStatus(mergeApplicationOrganizationBto.getStatus());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(applicationBO.convertToApplication());
                updatedBto.setBto(mergeApplicationOrganizationBto);
                updatedBto.setBo(applicationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationOrganizationBto, "__$validPropertySet"),
                        "routerId")) {
                    applicationBO.setRouterId(mergeApplicationOrganizationBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationOrganizationBto, "__$validPropertySet"),
                        "code")) {
                    applicationBO.setCode(mergeApplicationOrganizationBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeApplicationOrganizationBto, "__$validPropertySet"),
                        "status")) {
                    applicationBO.setStatus(mergeApplicationOrganizationBto.getStatus());
                }
            }
        } else {
            applicationBO = new ApplicationBO();
            if (pkExist) {
                applicationBO.setId(mergeApplicationOrganizationBto.getId());
            } else {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationOrganizationBto, "__$validPropertySet"),
                    "routerId")) {
                applicationBO.setRouterId(mergeApplicationOrganizationBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationOrganizationBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(mergeApplicationOrganizationBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationOrganizationBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(mergeApplicationOrganizationBto.getStatus());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeApplicationOrganizationBto);
            addedBto.setBo(applicationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return applicationBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ApplicationBO createUpdateApplicationOnDuplicateUpdate(
            UpdateApplicationBoResult boResult, UpdateApplicationBto updateApplicationBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (updateApplicationBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(updateApplicationBto.getId());
            if (applicationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (applicationBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(applicationBO.convertToApplication());
                updatedBto.setBto(updateApplicationBto);
                updatedBto.setBo(applicationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "name")) {
                    applicationBO.setName(updateApplicationBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "inputCode")) {
                    applicationBO.setInputCode(updateApplicationBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "abbreviation")) {
                    applicationBO.setAbbreviation(updateApplicationBto.getAbbreviation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "description")) {
                    applicationBO.setDescription(updateApplicationBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "templateApplicationFlag")) {
                    applicationBO.setTemplateApplicationFlag(
                            updateApplicationBto.getTemplateApplicationFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "templateApplicationId")) {
                    applicationBO.setTemplateApplicationId(
                            updateApplicationBto.getTemplateApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "categoryId")) {
                    applicationBO.setCategoryId(updateApplicationBto.getCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "enableFlag")) {
                    applicationBO.setEnableFlag(updateApplicationBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "multipleInstanceFlag")) {
                    applicationBO.setMultipleInstanceFlag(
                            updateApplicationBto.getMultipleInstanceFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "organizationId")) {
                    applicationBO.setOrganizationId(updateApplicationBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "parentApplicationId")) {
                    applicationBO.setParentApplicationId(
                            updateApplicationBto.getParentApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "routerId")) {
                    applicationBO.setRouterId(updateApplicationBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "code")) {
                    applicationBO.setCode(updateApplicationBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "status")) {
                    applicationBO.setStatus(updateApplicationBto.getStatus());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(applicationBO.convertToApplication());
                updatedBto.setBto(updateApplicationBto);
                updatedBto.setBo(applicationBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "name")) {
                    applicationBO.setName(updateApplicationBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "inputCode")) {
                    applicationBO.setInputCode(updateApplicationBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "abbreviation")) {
                    applicationBO.setAbbreviation(updateApplicationBto.getAbbreviation());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "description")) {
                    applicationBO.setDescription(updateApplicationBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "templateApplicationFlag")) {
                    applicationBO.setTemplateApplicationFlag(
                            updateApplicationBto.getTemplateApplicationFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "templateApplicationId")) {
                    applicationBO.setTemplateApplicationId(
                            updateApplicationBto.getTemplateApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "categoryId")) {
                    applicationBO.setCategoryId(updateApplicationBto.getCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "enableFlag")) {
                    applicationBO.setEnableFlag(updateApplicationBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "multipleInstanceFlag")) {
                    applicationBO.setMultipleInstanceFlag(
                            updateApplicationBto.getMultipleInstanceFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "organizationId")) {
                    applicationBO.setOrganizationId(updateApplicationBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "parentApplicationId")) {
                    applicationBO.setParentApplicationId(
                            updateApplicationBto.getParentApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "routerId")) {
                    applicationBO.setRouterId(updateApplicationBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "code")) {
                    applicationBO.setCode(updateApplicationBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        updateApplicationBto, "__$validPropertySet"),
                        "status")) {
                    applicationBO.setStatus(updateApplicationBto.getStatus());
                }
            }
        } else {
            applicationBO = new ApplicationBO();
            if (pkExist) {
                applicationBO.setId(updateApplicationBto.getId());
            } else {
                applicationBO.setId(String.valueOf(this.idGenerator.allocateId("application")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "name")) {
                applicationBO.setName(updateApplicationBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "inputCode")) {
                applicationBO.setInputCode(updateApplicationBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "abbreviation")) {
                applicationBO.setAbbreviation(updateApplicationBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "description")) {
                applicationBO.setDescription(updateApplicationBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "templateApplicationFlag")) {
                applicationBO.setTemplateApplicationFlag(
                        updateApplicationBto.getTemplateApplicationFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "templateApplicationId")) {
                applicationBO.setTemplateApplicationId(
                        updateApplicationBto.getTemplateApplicationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "categoryId")) {
                applicationBO.setCategoryId(updateApplicationBto.getCategoryId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "enableFlag")) {
                applicationBO.setEnableFlag(updateApplicationBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "multipleInstanceFlag")) {
                applicationBO.setMultipleInstanceFlag(
                        updateApplicationBto.getMultipleInstanceFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "organizationId")) {
                applicationBO.setOrganizationId(updateApplicationBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "parentApplicationId")) {
                applicationBO.setParentApplicationId(updateApplicationBto.getParentApplicationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "routerId")) {
                applicationBO.setRouterId(updateApplicationBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(updateApplicationBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(updateApplicationBto.getStatus());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(updateApplicationBto);
            addedBto.setBo(applicationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return applicationBO;
    }

    /** 删除对象:applicationDepartmentBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteApplicationDepartmentBtoOnMissThrowEx(
            DeleteDepartmentBoResult boResult,
            DeleteDepartmentBto deleteDepartmentBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(deleteDepartmentBto.getApplicationDepartmentBtoList())) {
            for (DeleteDepartmentBto.ApplicationDepartmentBto item :
                    deleteDepartmentBto.getApplicationDepartmentBtoList()) {
                Optional<ApplicationDepartmentBO> any =
                        applicationBO.getApplicationDepartmentBOSet().stream()
                                .filter(
                                        applicationDepartmentBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationDepartmentBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    applicationBO.getApplicationDepartmentBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToApplicationDepartment());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 删除对象:applicationDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteApplicationDetailBtoOnMissThrowEx(
            DeleteDetailBoResult boResult,
            DeleteDetailBto.ApplicationOrganizationBto applicationOrganizationBto,
            ApplicationOrganizationBO applicationOrganizationBO) {
        if (CollectionUtil.isNotEmpty(applicationOrganizationBto.getApplicationDetailBtoList())) {
            for (DeleteDetailBto.ApplicationDetailBto item :
                    applicationOrganizationBto.getApplicationDetailBtoList()) {
                Optional<ApplicationDetailBO> any =
                        applicationOrganizationBO.getApplicationDetailBOSet().stream()
                                .filter(
                                        applicationDetailBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationDetailBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    applicationOrganizationBO.getApplicationDetailBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToApplicationDetail());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 删除应用菜单 */
    @AutoGenerated(locked = true)
    protected DeleteApplicationMenuBoResult deleteApplicationMenuBase(
            DeleteApplicationMenuBto deleteApplicationMenuBto) {
        if (deleteApplicationMenuBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteApplicationMenuBoResult boResult = new DeleteApplicationMenuBoResult();
        ApplicationBO applicationBO =
                updateDeleteApplicationMenuOnMissThrowEx(boResult, deleteApplicationMenuBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteApplicationMenuBto, "__$validPropertySet"),
                    "applicationMenuBtoList")) {
                deleteApplicationMenuBtoOnMissThrowEx(
                        boResult, deleteApplicationMenuBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 删除对象:applicationMenuBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteApplicationMenuBtoOnMissThrowEx(
            DeleteApplicationMenuBoResult boResult,
            DeleteApplicationMenuBto deleteApplicationMenuBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(deleteApplicationMenuBto.getApplicationMenuBtoList())) {
            for (DeleteApplicationMenuBto.ApplicationMenuBto item :
                    deleteApplicationMenuBto.getApplicationMenuBtoList()) {
                Optional<ApplicationMenuBO> any =
                        applicationBO.getApplicationMenuBOSet().stream()
                                .filter(
                                        applicationMenuBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationMenuBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    applicationBO.getApplicationMenuBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToApplicationMenu());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 删除应用组织关系及对照 */
    @AutoGenerated(locked = true)
    protected DeleteApplicationOrganizationBoResult deleteApplicationOrganizationBase(
            DeleteApplicationOrganizationBto deleteApplicationOrganizationBto) {
        if (deleteApplicationOrganizationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteApplicationOrganizationBoResult boResult =
                new DeleteApplicationOrganizationBoResult();
        ApplicationBO applicationBO =
                updateDeleteApplicationOrganizationOnMissThrowEx(
                        boResult, deleteApplicationOrganizationBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteApplicationOrganizationBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                deleteApplicationOrganizationBtoOnMissThrowEx(
                        boResult, deleteApplicationOrganizationBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 删除对象:applicationOrganizationBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteApplicationOrganizationBtoOnMissThrowEx(
            DeleteApplicationOrganizationBoResult boResult,
            DeleteApplicationOrganizationBto deleteApplicationOrganizationBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(
                deleteApplicationOrganizationBto.getApplicationOrganizationBtoList())) {
            for (DeleteApplicationOrganizationBto.ApplicationOrganizationBto item :
                    deleteApplicationOrganizationBto.getApplicationOrganizationBtoList()) {
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                applicationOrganizationBOSet
                                                                        .getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    applicationBO.getApplicationOrganizationBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToApplicationOrganization());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 条件：应用ID,组织ID */
    @AutoGenerated(locked = true)
    protected DeleteDepartmentBoResult deleteDepartmentBase(
            DeleteDepartmentBto deleteDepartmentBto) {
        if (deleteDepartmentBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteDepartmentBoResult boResult = new DeleteDepartmentBoResult();
        ApplicationBO applicationBO =
                updateDeleteDepartmentOnMissThrowEx(boResult, deleteDepartmentBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteDepartmentBto, "__$validPropertySet"),
                    "applicationDepartmentBtoList")) {
                deleteApplicationDepartmentBtoOnMissThrowEx(
                        boResult, deleteDepartmentBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    @AutoGenerated(locked = true)
    protected DeleteDetailBoResult deleteDetailBase(DeleteDetailBto deleteDetailBto) {
        if (deleteDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteDetailBoResult boResult = new DeleteDetailBoResult();
        ApplicationBO applicationBO = updateDeleteDetailOnMissThrowEx(boResult, deleteDetailBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(deleteDetailBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                updateApplicationOrganizationBtoOnMissThrowEx(
                        boResult, deleteDetailBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** merge应用 */
    @AutoGenerated(locked = true)
    protected MergeApplicationBoResult mergeApplicationBase(
            MergeApplicationBto mergeApplicationBto) {
        if (mergeApplicationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeApplicationBoResult boResult = new MergeApplicationBoResult();
        ApplicationBO applicationBO =
                createMergeApplicationOnDuplicateUpdate(boResult, mergeApplicationBto);
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** merge应用关系和对照 */
    @AutoGenerated(locked = true)
    protected MergeApplicationDetailBoResult mergeApplicationDetailBase(
            MergeApplicationDetailBto mergeApplicationDetailBto) {
        if (mergeApplicationDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeApplicationDetailBoResult boResult = new MergeApplicationDetailBoResult();
        ApplicationBO applicationBO =
                updateMergeApplicationDetailOnMissThrowEx(boResult, mergeApplicationDetailBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationDetailBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                createApplicationOrganizationBtoOnDuplicateUpdate(
                        boResult, mergeApplicationDetailBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** merge应用菜单 */
    @AutoGenerated(locked = true)
    protected MergeApplicationMenuBoResult mergeApplicationMenuBase(
            MergeApplicationMenuBto mergeApplicationMenuBto) {
        if (mergeApplicationMenuBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeApplicationMenuBoResult boResult = new MergeApplicationMenuBoResult();
        ApplicationBO applicationBO =
                updateMergeApplicationMenuOnMissThrowEx(boResult, mergeApplicationMenuBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationMenuBto, "__$validPropertySet"),
                    "applicationMenuBtoList")) {
                createApplicationMenuBtoOnDuplicateUpdate(
                        boResult, mergeApplicationMenuBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** merge应用组织关系 */
    @AutoGenerated(locked = true)
    protected MergeApplicationOrganizationBoResult mergeApplicationOrganizationBase(
            MergeApplicationOrganizationBto mergeApplicationOrganizationBto) {
        if (mergeApplicationOrganizationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeApplicationOrganizationBoResult boResult = new MergeApplicationOrganizationBoResult();
        ApplicationBO applicationBO =
                createMergeApplicationOrganizationOnDuplicateUpdate(
                        boResult, mergeApplicationOrganizationBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationOrganizationBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                createApplicationOrganizationBtoOnDuplicateUpdate(
                        boResult, mergeApplicationOrganizationBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** merge应用科室 */
    @AutoGenerated(locked = true)
    protected MergeDepartmentsBoResult mergeDepartmentsBase(
            MergeDepartmentsBto mergeDepartmentsBto) {
        if (mergeDepartmentsBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeDepartmentsBoResult boResult = new MergeDepartmentsBoResult();
        ApplicationBO applicationBO =
                updateMergeDepartmentsOnMissThrowEx(boResult, mergeDepartmentsBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeDepartmentsBto, "__$validPropertySet"),
                    "applicationDepartmentBtoList")) {
                createApplicationDepartmentBtoOnDuplicateUpdate(
                        boResult, mergeDepartmentsBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 更新应用对照 */
    @AutoGenerated(locked = true)
    protected MergeDetailBoResult mergeDetailBase(MergeDetailBto mergeDetailBto) {
        if (mergeDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeDetailBoResult boResult = new MergeDetailBoResult();
        ApplicationBO applicationBO = updateMergeDetailOnMissThrowEx(boResult, mergeDetailBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeDetailBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                updateApplicationOrganizationBtoOnMissThrowEx(
                        boResult, mergeDetailBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** merge应用信息 */
    @AutoGenerated(locked = true)
    protected UpdateApplicationBoResult updateApplicationBase(
            UpdateApplicationBto updateApplicationBto) {
        if (updateApplicationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateApplicationBoResult boResult = new UpdateApplicationBoResult();
        ApplicationBO applicationBO =
                createUpdateApplicationOnDuplicateUpdate(boResult, updateApplicationBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateApplicationBto, "__$validPropertySet"),
                    "applicationOrganizationBtoList")) {
                createApplicationOrganizationBtoOnDuplicateUpdate(
                        boResult, updateApplicationBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 更新对象:applicationMenuBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateApplicationMenuBtoOnMissThrowEx(
            BaseApplicationBOService.UpdateListApplicationMenuBoResult boResult,
            UpdateListApplicationMenuBto updateListApplicationMenuBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(updateListApplicationMenuBto.getApplicationMenuBtoList())) {
            for (UpdateListApplicationMenuBto.ApplicationMenuBto bto :
                    updateListApplicationMenuBto.getApplicationMenuBtoList()) {
                Optional<ApplicationMenuBO> any =
                        applicationBO.getApplicationMenuBOSet().stream()
                                .filter(
                                        applicationMenuBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                applicationMenuBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ApplicationMenuBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToApplicationMenu());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "menuId")) {
                        bo.setMenuId(bto.getMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "customName")) {
                        bo.setCustomName(bto.getCustomName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "parentMenuId")) {
                        bo.setParentMenuId(bto.getParentMenuId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "parentMenuName")) {
                        bo.setParentMenuName(bto.getParentMenuName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "sameLevelGroupNumber")) {
                        bo.setSameLevelGroupNumber(bto.getSameLevelGroupNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "sortNumber")) {
                        bo.setSortNumber(bto.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "fixedFlag")) {
                        bo.setFixedFlag(bto.getFixedFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "initialFlag")) {
                        bo.setInitialFlag(bto.getInitialFlag());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:applicationOrganizationBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateApplicationOrganizationBtoOnMissThrowEx(
            BaseApplicationBOService.CreateDetailBoResult boResult,
            CreateDetailBto createDetailBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(createDetailBto.getApplicationOrganizationBtoList())) {
            for (CreateDetailBto.ApplicationOrganizationBto bto :
                    createDetailBto.getApplicationOrganizationBtoList()) {
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                applicationOrganizationBOSet
                                                                        .getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ApplicationOrganizationBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToApplicationOrganization());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "applicationDetailBtoList")) {
                        createApplicationDetailBto(boResult, bto, bo);
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:applicationOrganizationBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateApplicationOrganizationBtoOnMissThrowEx(
            BaseApplicationBOService.DeleteDetailBoResult boResult,
            DeleteDetailBto deleteDetailBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(deleteDetailBto.getApplicationOrganizationBtoList())) {
            for (DeleteDetailBto.ApplicationOrganizationBto bto :
                    deleteDetailBto.getApplicationOrganizationBtoList()) {
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                applicationOrganizationBOSet
                                                                        .getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ApplicationOrganizationBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToApplicationOrganization());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "applicationDetailBtoList")) {
                        deleteApplicationDetailBtoOnMissThrowEx(boResult, bto, bo);
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:applicationOrganizationBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateApplicationOrganizationBtoOnMissThrowEx(
            BaseApplicationBOService.MergeDetailBoResult boResult,
            MergeDetailBto mergeDetailBto,
            ApplicationBO applicationBO) {
        if (CollectionUtil.isNotEmpty(mergeDetailBto.getApplicationOrganizationBtoList())) {
            for (MergeDetailBto.ApplicationOrganizationBto bto :
                    mergeDetailBto.getApplicationOrganizationBtoList()) {
                Optional<ApplicationOrganizationBO> any =
                        applicationBO.getApplicationOrganizationBOSet().stream()
                                .filter(
                                        applicationOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                applicationOrganizationBOSet
                                                                        .getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    ApplicationOrganizationBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToApplicationOrganization());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "applicationDetailBtoList")) {
                        createApplicationDetailBtoOnDuplicateUpdate(boResult, bto, bo);
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 修改应用状态 */
    @AutoGenerated(locked = true)
    protected UpdateApplicationStatusBoResult updateApplicationStatusBase(
            UpdateApplicationStatusBto updateApplicationStatusBto) {
        if (updateApplicationStatusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateApplicationStatusBoResult boResult = new UpdateApplicationStatusBoResult();
        ApplicationBO applicationBO =
                updateUpdateApplicationStatusOnMissThrowEx(boResult, updateApplicationStatusBto);
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 更新对象:createApplicationOrganizationDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateCreateApplicationOrganizationDetailOnMissThrowEx(
            BaseApplicationBOService.CreateApplicationOrganizationDetailBoResult boResult,
            CreateApplicationOrganizationDetailBto createApplicationOrganizationDetailBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createApplicationOrganizationDetailBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(createApplicationOrganizationDetailBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(createApplicationOrganizationDetailBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationOrganizationDetailBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(createApplicationOrganizationDetailBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationOrganizationDetailBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createApplicationOrganizationDetailBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:createApplicationOrganization,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateCreateApplicationOrganizationOnMissThrowEx(
            BaseApplicationBOService.CreateApplicationOrganizationBoResult boResult,
            CreateApplicationOrganizationBto createApplicationOrganizationBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createApplicationOrganizationBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(createApplicationOrganizationBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(createApplicationOrganizationBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationOrganizationBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(createApplicationOrganizationBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createApplicationOrganizationBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createApplicationOrganizationBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:createDepartment,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateCreateDepartmentOnMissThrowEx(
            BaseApplicationBOService.CreateDepartmentBoResult boResult,
            CreateDepartmentBto createDepartmentBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createDepartmentBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(createDepartmentBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(createDepartmentBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createDepartmentBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createDepartmentBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:createDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateCreateDetailOnMissThrowEx(
            CreateDetailBoResult boResult, CreateDetailBto createDetailBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createDetailBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(createDetailBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(createDetailBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createDetailBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(createDetailBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createDetailBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createDetailBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:createListApplicationMenu,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateCreateListApplicationMenuOnMissThrowEx(
            BaseApplicationBOService.CreateListApplicationMenuBoResult boResult,
            CreateListApplicationMenuBto createListApplicationMenuBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createListApplicationMenuBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(createListApplicationMenuBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(createListApplicationMenuBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createListApplicationMenuBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(createListApplicationMenuBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createListApplicationMenuBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(createListApplicationMenuBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:deleteApplicationMenu,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateDeleteApplicationMenuOnMissThrowEx(
            BaseApplicationBOService.DeleteApplicationMenuBoResult boResult,
            DeleteApplicationMenuBto deleteApplicationMenuBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteApplicationMenuBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(deleteApplicationMenuBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(deleteApplicationMenuBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteApplicationMenuBto, "__$validPropertySet"),
                    "routerId")) {
                applicationBO.setRouterId(deleteApplicationMenuBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteApplicationMenuBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(deleteApplicationMenuBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteApplicationMenuBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(deleteApplicationMenuBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:deleteApplicationOrganization,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateDeleteApplicationOrganizationOnMissThrowEx(
            BaseApplicationBOService.DeleteApplicationOrganizationBoResult boResult,
            DeleteApplicationOrganizationBto deleteApplicationOrganizationBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteApplicationOrganizationBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(deleteApplicationOrganizationBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(deleteApplicationOrganizationBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteApplicationOrganizationBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(deleteApplicationOrganizationBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteApplicationOrganizationBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(deleteApplicationOrganizationBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:deleteDepartment,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateDeleteDepartmentOnMissThrowEx(
            BaseApplicationBOService.DeleteDepartmentBoResult boResult,
            DeleteDepartmentBto deleteDepartmentBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteDepartmentBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(deleteDepartmentBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(deleteDepartmentBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            return applicationBO;
        }
    }

    /** 更新对象:deleteDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateDeleteDetailOnMissThrowEx(
            DeleteDetailBoResult boResult, DeleteDetailBto deleteDetailBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteDetailBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(deleteDetailBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(deleteDetailBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(deleteDetailBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(deleteDetailBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(deleteDetailBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(deleteDetailBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 修改应用菜单 */
    @AutoGenerated(locked = true)
    protected UpdateListApplicationMenuBoResult updateListApplicationMenuBase(
            UpdateListApplicationMenuBto updateListApplicationMenuBto) {
        if (updateListApplicationMenuBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateListApplicationMenuBoResult boResult = new UpdateListApplicationMenuBoResult();
        ApplicationBO applicationBO =
                updateUpdateListApplicationMenuOnMissThrowEx(
                        boResult, updateListApplicationMenuBto);
        if (applicationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateListApplicationMenuBto, "__$validPropertySet"),
                    "applicationMenuBtoList")) {
                updateApplicationMenuBtoOnMissThrowEx(
                        boResult, updateListApplicationMenuBto, applicationBO);
            }
        }
        boResult.setRootBo(applicationBO);
        return boResult;
    }

    /** 更新对象:mergeApplicationDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateMergeApplicationDetailOnMissThrowEx(
            BaseApplicationBOService.MergeApplicationDetailBoResult boResult,
            MergeApplicationDetailBto mergeApplicationDetailBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeApplicationDetailBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(mergeApplicationDetailBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(mergeApplicationDetailBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationDetailBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(mergeApplicationDetailBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationDetailBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(mergeApplicationDetailBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:mergeApplicationMenu,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateMergeApplicationMenuOnMissThrowEx(
            BaseApplicationBOService.MergeApplicationMenuBoResult boResult,
            MergeApplicationMenuBto mergeApplicationMenuBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeApplicationMenuBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(mergeApplicationMenuBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(mergeApplicationMenuBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationMenuBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(mergeApplicationMenuBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeApplicationMenuBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(mergeApplicationMenuBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:mergeDepartments,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateMergeDepartmentsOnMissThrowEx(
            BaseApplicationBOService.MergeDepartmentsBoResult boResult,
            MergeDepartmentsBto mergeDepartmentsBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeDepartmentsBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(mergeDepartmentsBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(mergeDepartmentsBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            return applicationBO;
        }
    }

    /** 更新对象:mergeDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateMergeDetailOnMissThrowEx(
            MergeDetailBoResult boResult, MergeDetailBto mergeDetailBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeDetailBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(mergeDetailBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(mergeDetailBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeDetailBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(mergeDetailBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeDetailBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(mergeDetailBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:updateApplicationStatus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateUpdateApplicationStatusOnMissThrowEx(
            BaseApplicationBOService.UpdateApplicationStatusBoResult boResult,
            UpdateApplicationStatusBto updateApplicationStatusBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateApplicationStatusBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(updateApplicationStatusBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(updateApplicationStatusBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateApplicationStatusBto, "__$validPropertySet"),
                    "enableFlag")) {
                applicationBO.setEnableFlag(updateApplicationStatusBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateApplicationStatusBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(updateApplicationStatusBto.getStatus());
            }
            return applicationBO;
        }
    }

    /** 更新对象:updateListApplicationMenu,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ApplicationBO updateUpdateListApplicationMenuOnMissThrowEx(
            UpdateListApplicationMenuBoResult boResult,
            UpdateListApplicationMenuBto updateListApplicationMenuBto) {
        ApplicationBO applicationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateListApplicationMenuBto.getId() == null);
        if (!allNull && !found) {
            applicationBO = ApplicationBO.getById(updateListApplicationMenuBto.getId());
            found = true;
        }
        if (applicationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(applicationBO.convertToApplication());
            updatedBto.setBto(updateListApplicationMenuBto);
            updatedBto.setBo(applicationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateListApplicationMenuBto, "__$validPropertySet"),
                    "code")) {
                applicationBO.setCode(updateListApplicationMenuBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateListApplicationMenuBto, "__$validPropertySet"),
                    "status")) {
                applicationBO.setStatus(updateListApplicationMenuBto.getStatus());
            }
            return applicationBO;
        }
    }

    public static class UpdateApplicationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateApplicationBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getCreatedBto(
                        UpdateApplicationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateApplicationBto, ApplicationBO> getCreatedBto(
                UpdateApplicationBto updateApplicationBto) {
            return this.getAddedResult(updateApplicationBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateApplicationBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        UpdateApplicationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateApplicationBto, Application, ApplicationBO> getUpdatedBto(
                UpdateApplicationBto updateApplicationBto) {
            return super.getUpdatedResult(updateApplicationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateApplicationBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getUnmodifiedBto(
                        UpdateApplicationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateApplicationBto, ApplicationBO> getUnmodifiedBto(
                UpdateApplicationBto updateApplicationBto) {
            return super.getUnmodifiedResult(updateApplicationBto);
        }
    }

    public static class CreateApplicationOrganizationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getCreatedBto(
                        CreateApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateApplicationOrganizationBto, ApplicationBO> getCreatedBto(
                CreateApplicationOrganizationBto createApplicationOrganizationBto) {
            return this.getAddedResult(createApplicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateApplicationOrganizationBto.ApplicationDetailBto, ApplicationDetailBO>
                getCreatedBto(
                        CreateApplicationOrganizationBto.ApplicationDetailBto
                                applicationDetailBto) {
            return this.getAddedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public ApplicationDetail getDeleted_ApplicationDetail() {
            return (ApplicationDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDetail.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        CreateApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateApplicationOrganizationBto, Application, ApplicationBO>
                getUpdatedBto(CreateApplicationOrganizationBto createApplicationOrganizationBto) {
            return super.getUpdatedResult(createApplicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateApplicationOrganizationBto.ApplicationDetailBto,
                        ApplicationDetail,
                        ApplicationDetailBO>
                getUpdatedBto(
                        CreateApplicationOrganizationBto.ApplicationDetailBto
                                applicationDetailBto) {
            return super.getUpdatedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getUnmodifiedBto(
                        CreateApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateApplicationOrganizationBto, ApplicationBO> getUnmodifiedBto(
                CreateApplicationOrganizationBto createApplicationOrganizationBto) {
            return super.getUnmodifiedResult(createApplicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateApplicationOrganizationBto.ApplicationDetailBto, ApplicationDetailBO>
                getUnmodifiedBto(
                        CreateApplicationOrganizationBto.ApplicationDetailBto
                                applicationDetailBto) {
            return super.getUnmodifiedResult(applicationDetailBto);
        }
    }

    public static class CreateApplicationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateApplicationBto, ApplicationBO> getCreatedBto(
                CreateApplicationBto createApplicationBto) {
            return this.getAddedResult(createApplicationBto);
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateApplicationBto, Application, ApplicationBO> getUpdatedBto(
                CreateApplicationBto createApplicationBto) {
            return super.getUpdatedResult(createApplicationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateApplicationBto, ApplicationBO> getUnmodifiedBto(
                CreateApplicationBto createApplicationBto) {
            return super.getUnmodifiedResult(createApplicationBto);
        }
    }

    public static class MergeApplicationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeApplicationBto, ApplicationBO> getCreatedBto(
                MergeApplicationBto mergeApplicationBto) {
            return this.getAddedResult(mergeApplicationBto);
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeApplicationBto, Application, ApplicationBO> getUpdatedBto(
                MergeApplicationBto mergeApplicationBto) {
            return super.getUpdatedResult(mergeApplicationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeApplicationBto, ApplicationBO> getUnmodifiedBto(
                MergeApplicationBto mergeApplicationBto) {
            return super.getUnmodifiedResult(mergeApplicationBto);
        }
    }

    public static class MergeApplicationOrganizationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        MergeApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getCreatedBto(
                        MergeApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeApplicationOrganizationBto, ApplicationBO> getCreatedBto(
                MergeApplicationOrganizationBto mergeApplicationOrganizationBto) {
            return this.getAddedResult(mergeApplicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        MergeApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeApplicationOrganizationBto, Application, ApplicationBO>
                getUpdatedBto(MergeApplicationOrganizationBto mergeApplicationOrganizationBto) {
            return super.getUpdatedResult(mergeApplicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        MergeApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getUnmodifiedBto(
                        MergeApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeApplicationOrganizationBto, ApplicationBO> getUnmodifiedBto(
                MergeApplicationOrganizationBto mergeApplicationOrganizationBto) {
            return super.getUnmodifiedResult(mergeApplicationOrganizationBto);
        }
    }

    public static class CreateApplicationMenuBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getCreatedBto(CreateApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return this.getAddedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateApplicationMenuBto, ApplicationBO> getCreatedBto(
                CreateApplicationMenuBto createApplicationMenuBto) {
            return this.getAddedResult(createApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationMenu getDeleted_ApplicationMenu() {
            return (ApplicationMenu)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationMenu.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateApplicationMenuBto.ApplicationMenuBto,
                        ApplicationMenu,
                        ApplicationMenuBO>
                getUpdatedBto(CreateApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUpdatedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateApplicationMenuBto, Application, ApplicationBO> getUpdatedBto(
                CreateApplicationMenuBto createApplicationMenuBto) {
            return super.getUpdatedResult(createApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getUnmodifiedBto(CreateApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUnmodifiedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateApplicationMenuBto, ApplicationBO> getUnmodifiedBto(
                CreateApplicationMenuBto createApplicationMenuBto) {
            return super.getUnmodifiedResult(createApplicationMenuBto);
        }
    }

    public static class MergeApplicationMenuBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getCreatedBto(MergeApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return this.getAddedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeApplicationMenuBto, ApplicationBO> getCreatedBto(
                MergeApplicationMenuBto mergeApplicationMenuBto) {
            return this.getAddedResult(mergeApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationMenu getDeleted_ApplicationMenu() {
            return (ApplicationMenu)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationMenu.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeApplicationMenuBto.ApplicationMenuBto,
                        ApplicationMenu,
                        ApplicationMenuBO>
                getUpdatedBto(MergeApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUpdatedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeApplicationMenuBto, Application, ApplicationBO> getUpdatedBto(
                MergeApplicationMenuBto mergeApplicationMenuBto) {
            return super.getUpdatedResult(mergeApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getUnmodifiedBto(MergeApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUnmodifiedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeApplicationMenuBto, ApplicationBO> getUnmodifiedBto(
                MergeApplicationMenuBto mergeApplicationMenuBto) {
            return super.getUnmodifiedResult(mergeApplicationMenuBto);
        }
    }

    public static class DeleteApplicationMenuBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getCreatedBto(DeleteApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return this.getAddedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteApplicationMenuBto, ApplicationBO> getCreatedBto(
                DeleteApplicationMenuBto deleteApplicationMenuBto) {
            return this.getAddedResult(deleteApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationMenu getDeleted_ApplicationMenu() {
            return (ApplicationMenu)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationMenu.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteApplicationMenuBto.ApplicationMenuBto,
                        ApplicationMenu,
                        ApplicationMenuBO>
                getUpdatedBto(DeleteApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUpdatedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteApplicationMenuBto, Application, ApplicationBO> getUpdatedBto(
                DeleteApplicationMenuBto deleteApplicationMenuBto) {
            return super.getUpdatedResult(deleteApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getUnmodifiedBto(DeleteApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUnmodifiedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteApplicationMenuBto, ApplicationBO> getUnmodifiedBto(
                DeleteApplicationMenuBto deleteApplicationMenuBto) {
            return super.getUnmodifiedResult(deleteApplicationMenuBto);
        }
    }

    public static class CreateListApplicationMenuBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateListApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getCreatedBto(CreateListApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return this.getAddedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateListApplicationMenuBto, ApplicationBO> getCreatedBto(
                CreateListApplicationMenuBto createListApplicationMenuBto) {
            return this.getAddedResult(createListApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationMenu getDeleted_ApplicationMenu() {
            return (ApplicationMenu)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationMenu.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateListApplicationMenuBto.ApplicationMenuBto,
                        ApplicationMenu,
                        ApplicationMenuBO>
                getUpdatedBto(CreateListApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUpdatedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateListApplicationMenuBto, Application, ApplicationBO> getUpdatedBto(
                CreateListApplicationMenuBto createListApplicationMenuBto) {
            return super.getUpdatedResult(createListApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateListApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getUnmodifiedBto(
                        CreateListApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUnmodifiedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateListApplicationMenuBto, ApplicationBO> getUnmodifiedBto(
                CreateListApplicationMenuBto createListApplicationMenuBto) {
            return super.getUnmodifiedResult(createListApplicationMenuBto);
        }
    }

    public static class UpdateListApplicationMenuBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateListApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getCreatedBto(UpdateListApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return this.getAddedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateListApplicationMenuBto, ApplicationBO> getCreatedBto(
                UpdateListApplicationMenuBto updateListApplicationMenuBto) {
            return this.getAddedResult(updateListApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationMenu getDeleted_ApplicationMenu() {
            return (ApplicationMenu)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationMenu.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateListApplicationMenuBto.ApplicationMenuBto,
                        ApplicationMenu,
                        ApplicationMenuBO>
                getUpdatedBto(UpdateListApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUpdatedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateListApplicationMenuBto, Application, ApplicationBO> getUpdatedBto(
                UpdateListApplicationMenuBto updateListApplicationMenuBto) {
            return super.getUpdatedResult(updateListApplicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateListApplicationMenuBto.ApplicationMenuBto, ApplicationMenuBO>
                getUnmodifiedBto(
                        UpdateListApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
            return super.getUnmodifiedResult(applicationMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateListApplicationMenuBto, ApplicationBO> getUnmodifiedBto(
                UpdateListApplicationMenuBto updateListApplicationMenuBto) {
            return super.getUnmodifiedResult(updateListApplicationMenuBto);
        }
    }

    public static class MergeApplicationDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeApplicationDetailBto.ApplicationDetailBto, ApplicationDetailBO>
                getCreatedBto(MergeApplicationDetailBto.ApplicationDetailBto applicationDetailBto) {
            return this.getAddedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        MergeApplicationDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getCreatedBto(
                        MergeApplicationDetailBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeApplicationDetailBto, ApplicationBO> getCreatedBto(
                MergeApplicationDetailBto mergeApplicationDetailBto) {
            return this.getAddedResult(mergeApplicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDetail getDeleted_ApplicationDetail() {
            return (ApplicationDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDetail.class));
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeApplicationDetailBto.ApplicationDetailBto,
                        ApplicationDetail,
                        ApplicationDetailBO>
                getUpdatedBto(MergeApplicationDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUpdatedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeApplicationDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        MergeApplicationDetailBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeApplicationDetailBto, Application, ApplicationBO> getUpdatedBto(
                MergeApplicationDetailBto mergeApplicationDetailBto) {
            return super.getUpdatedResult(mergeApplicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeApplicationDetailBto.ApplicationDetailBto, ApplicationDetailBO>
                getUnmodifiedBto(
                        MergeApplicationDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUnmodifiedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        MergeApplicationDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getUnmodifiedBto(
                        MergeApplicationDetailBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeApplicationDetailBto, ApplicationBO> getUnmodifiedBto(
                MergeApplicationDetailBto mergeApplicationDetailBto) {
            return super.getUnmodifiedResult(mergeApplicationDetailBto);
        }
    }

    public static class CreateApplicationOrganizationDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getCreatedBto(
                        CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateApplicationOrganizationDetailBto.ApplicationDetailBto,
                        ApplicationDetailBO>
                getCreatedBto(
                        CreateApplicationOrganizationDetailBto.ApplicationDetailBto
                                applicationDetailBto) {
            return this.getAddedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateApplicationOrganizationDetailBto, ApplicationBO> getCreatedBto(
                CreateApplicationOrganizationDetailBto createApplicationOrganizationDetailBto) {
            return this.getAddedResult(createApplicationOrganizationDetailBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public ApplicationDetail getDeleted_ApplicationDetail() {
            return (ApplicationDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDetail.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateApplicationOrganizationDetailBto.ApplicationDetailBto,
                        ApplicationDetail,
                        ApplicationDetailBO>
                getUpdatedBto(
                        CreateApplicationOrganizationDetailBto.ApplicationDetailBto
                                applicationDetailBto) {
            return super.getUpdatedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateApplicationOrganizationDetailBto, Application, ApplicationBO>
                getUpdatedBto(
                        CreateApplicationOrganizationDetailBto
                                createApplicationOrganizationDetailBto) {
            return super.getUpdatedResult(createApplicationOrganizationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getUnmodifiedBto(
                        CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateApplicationOrganizationDetailBto.ApplicationDetailBto,
                        ApplicationDetailBO>
                getUnmodifiedBto(
                        CreateApplicationOrganizationDetailBto.ApplicationDetailBto
                                applicationDetailBto) {
            return super.getUnmodifiedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateApplicationOrganizationDetailBto, ApplicationBO>
                getUnmodifiedBto(
                        CreateApplicationOrganizationDetailBto
                                createApplicationOrganizationDetailBto) {
            return super.getUnmodifiedResult(createApplicationOrganizationDetailBto);
        }
    }

    public static class DeleteApplicationOrganizationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        DeleteApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getCreatedBto(
                        DeleteApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteApplicationOrganizationBto, ApplicationBO> getCreatedBto(
                DeleteApplicationOrganizationBto deleteApplicationOrganizationBto) {
            return this.getAddedResult(deleteApplicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        DeleteApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteApplicationOrganizationBto, Application, ApplicationBO>
                getUpdatedBto(DeleteApplicationOrganizationBto deleteApplicationOrganizationBto) {
            return super.getUpdatedResult(deleteApplicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        DeleteApplicationOrganizationBto.ApplicationOrganizationBto,
                        ApplicationOrganizationBO>
                getUnmodifiedBto(
                        DeleteApplicationOrganizationBto.ApplicationOrganizationBto
                                applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteApplicationOrganizationBto, ApplicationBO> getUnmodifiedBto(
                DeleteApplicationOrganizationBto deleteApplicationOrganizationBto) {
            return super.getUnmodifiedResult(deleteApplicationOrganizationBto);
        }
    }

    public static class CreateDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDetailBto.ApplicationDetailBto, ApplicationDetailBO> getCreatedBto(
                CreateDetailBto.ApplicationDetailBto applicationDetailBto) {
            return this.getAddedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDetailBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getCreatedBto(
                        CreateDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDetailBto, ApplicationBO> getCreatedBto(
                CreateDetailBto createDetailBto) {
            return this.getAddedResult(createDetailBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDetail getDeleted_ApplicationDetail() {
            return (ApplicationDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDetail.class));
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateDetailBto.ApplicationDetailBto,
                        ApplicationDetail,
                        ApplicationDetailBO>
                getUpdatedBto(CreateDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUpdatedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        CreateDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateDetailBto, Application, ApplicationBO> getUpdatedBto(
                CreateDetailBto createDetailBto) {
            return super.getUpdatedResult(createDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDetailBto.ApplicationDetailBto, ApplicationDetailBO>
                getUnmodifiedBto(CreateDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUnmodifiedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDetailBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getUnmodifiedBto(
                        CreateDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDetailBto, ApplicationBO> getUnmodifiedBto(
                CreateDetailBto createDetailBto) {
            return super.getUnmodifiedResult(createDetailBto);
        }
    }

    public static class DeleteDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDetailBto.ApplicationDetailBto, ApplicationDetailBO> getCreatedBto(
                DeleteDetailBto.ApplicationDetailBto applicationDetailBto) {
            return this.getAddedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDetailBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getCreatedBto(
                        DeleteDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDetailBto, ApplicationBO> getCreatedBto(
                DeleteDetailBto deleteDetailBto) {
            return this.getAddedResult(deleteDetailBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDetail getDeleted_ApplicationDetail() {
            return (ApplicationDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDetail.class));
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteDetailBto.ApplicationDetailBto,
                        ApplicationDetail,
                        ApplicationDetailBO>
                getUpdatedBto(DeleteDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUpdatedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        DeleteDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteDetailBto, Application, ApplicationBO> getUpdatedBto(
                DeleteDetailBto deleteDetailBto) {
            return super.getUpdatedResult(deleteDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDetailBto.ApplicationDetailBto, ApplicationDetailBO>
                getUnmodifiedBto(DeleteDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUnmodifiedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDetailBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getUnmodifiedBto(
                        DeleteDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDetailBto, ApplicationBO> getUnmodifiedBto(
                DeleteDetailBto deleteDetailBto) {
            return super.getUnmodifiedResult(deleteDetailBto);
        }
    }

    public static class MergeDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDetailBto.ApplicationDetailBto, ApplicationDetailBO> getCreatedBto(
                MergeDetailBto.ApplicationDetailBto applicationDetailBto) {
            return this.getAddedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDetailBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getCreatedBto(
                        MergeDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return this.getAddedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDetailBto, ApplicationBO> getCreatedBto(
                MergeDetailBto mergeDetailBto) {
            return this.getAddedResult(mergeDetailBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDetail getDeleted_ApplicationDetail() {
            return (ApplicationDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDetail.class));
        }

        @AutoGenerated(locked = true)
        public ApplicationOrganization getDeleted_ApplicationOrganization() {
            return (ApplicationOrganization)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ApplicationOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeDetailBto.ApplicationDetailBto, ApplicationDetail, ApplicationDetailBO>
                getUpdatedBto(MergeDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUpdatedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeDetailBto.ApplicationOrganizationBto,
                        ApplicationOrganization,
                        ApplicationOrganizationBO>
                getUpdatedBto(
                        MergeDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return super.getUpdatedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeDetailBto, Application, ApplicationBO> getUpdatedBto(
                MergeDetailBto mergeDetailBto) {
            return super.getUpdatedResult(mergeDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDetailBto.ApplicationDetailBto, ApplicationDetailBO>
                getUnmodifiedBto(MergeDetailBto.ApplicationDetailBto applicationDetailBto) {
            return super.getUnmodifiedResult(applicationDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDetailBto.ApplicationOrganizationBto, ApplicationOrganizationBO>
                getUnmodifiedBto(
                        MergeDetailBto.ApplicationOrganizationBto applicationOrganizationBto) {
            return super.getUnmodifiedResult(applicationOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDetailBto, ApplicationBO> getUnmodifiedBto(
                MergeDetailBto mergeDetailBto) {
            return super.getUnmodifiedResult(mergeDetailBto);
        }
    }

    public static class CreateDepartmentBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDepartmentBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getCreatedBto(
                        CreateDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return this.getAddedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDepartmentBto, ApplicationBO> getCreatedBto(
                CreateDepartmentBto createDepartmentBto) {
            return this.getAddedResult(createDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDepartment getDeleted_ApplicationDepartment() {
            return (ApplicationDepartment)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDepartment.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateDepartmentBto.ApplicationDepartmentBto,
                        ApplicationDepartment,
                        ApplicationDepartmentBO>
                getUpdatedBto(
                        CreateDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUpdatedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateDepartmentBto, Application, ApplicationBO> getUpdatedBto(
                CreateDepartmentBto createDepartmentBto) {
            return super.getUpdatedResult(createDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDepartmentBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getUnmodifiedBto(
                        CreateDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUnmodifiedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDepartmentBto, ApplicationBO> getUnmodifiedBto(
                CreateDepartmentBto createDepartmentBto) {
            return super.getUnmodifiedResult(createDepartmentBto);
        }
    }

    public static class CreateDepartmentsBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDepartmentsBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getCreatedBto(
                        CreateDepartmentsBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return this.getAddedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateDepartmentsBto, ApplicationBO> getCreatedBto(
                CreateDepartmentsBto createDepartmentsBto) {
            return this.getAddedResult(createDepartmentsBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDepartment getDeleted_ApplicationDepartment() {
            return (ApplicationDepartment)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDepartment.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateDepartmentsBto.ApplicationDepartmentBto,
                        ApplicationDepartment,
                        ApplicationDepartmentBO>
                getUpdatedBto(
                        CreateDepartmentsBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUpdatedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateDepartmentsBto, Application, ApplicationBO> getUpdatedBto(
                CreateDepartmentsBto createDepartmentsBto) {
            return super.getUpdatedResult(createDepartmentsBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDepartmentsBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getUnmodifiedBto(
                        CreateDepartmentsBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUnmodifiedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateDepartmentsBto, ApplicationBO> getUnmodifiedBto(
                CreateDepartmentsBto createDepartmentsBto) {
            return super.getUnmodifiedResult(createDepartmentsBto);
        }
    }

    public static class MergeDepartmentsBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDepartmentsBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getCreatedBto(
                        MergeDepartmentsBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return this.getAddedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeDepartmentsBto, ApplicationBO> getCreatedBto(
                MergeDepartmentsBto mergeDepartmentsBto) {
            return this.getAddedResult(mergeDepartmentsBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDepartment getDeleted_ApplicationDepartment() {
            return (ApplicationDepartment)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDepartment.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeDepartmentsBto.ApplicationDepartmentBto,
                        ApplicationDepartment,
                        ApplicationDepartmentBO>
                getUpdatedBto(
                        MergeDepartmentsBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUpdatedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeDepartmentsBto, Application, ApplicationBO> getUpdatedBto(
                MergeDepartmentsBto mergeDepartmentsBto) {
            return super.getUpdatedResult(mergeDepartmentsBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDepartmentsBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getUnmodifiedBto(
                        MergeDepartmentsBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUnmodifiedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeDepartmentsBto, ApplicationBO> getUnmodifiedBto(
                MergeDepartmentsBto mergeDepartmentsBto) {
            return super.getUnmodifiedResult(mergeDepartmentsBto);
        }
    }

    public static class UpdateApplicationStatusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateApplicationStatusBto, ApplicationBO> getCreatedBto(
                UpdateApplicationStatusBto updateApplicationStatusBto) {
            return this.getAddedResult(updateApplicationStatusBto);
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateApplicationStatusBto, Application, ApplicationBO> getUpdatedBto(
                UpdateApplicationStatusBto updateApplicationStatusBto) {
            return super.getUpdatedResult(updateApplicationStatusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateApplicationStatusBto, ApplicationBO> getUnmodifiedBto(
                UpdateApplicationStatusBto updateApplicationStatusBto) {
            return super.getUnmodifiedResult(updateApplicationStatusBto);
        }
    }

    public static class DeleteDepartmentBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ApplicationBO getRootBo() {
            return (ApplicationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDepartmentBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getCreatedBto(
                        DeleteDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return this.getAddedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteDepartmentBto, ApplicationBO> getCreatedBto(
                DeleteDepartmentBto deleteDepartmentBto) {
            return this.getAddedResult(deleteDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public ApplicationDepartment getDeleted_ApplicationDepartment() {
            return (ApplicationDepartment)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ApplicationDepartment.class));
        }

        @AutoGenerated(locked = true)
        public Application getDeleted_Application() {
            return (Application)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Application.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteDepartmentBto.ApplicationDepartmentBto,
                        ApplicationDepartment,
                        ApplicationDepartmentBO>
                getUpdatedBto(
                        DeleteDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUpdatedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteDepartmentBto, Application, ApplicationBO> getUpdatedBto(
                DeleteDepartmentBto deleteDepartmentBto) {
            return super.getUpdatedResult(deleteDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDepartmentBto.ApplicationDepartmentBto, ApplicationDepartmentBO>
                getUnmodifiedBto(
                        DeleteDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
            return super.getUnmodifiedResult(applicationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteDepartmentBto, ApplicationBO> getUnmodifiedBto(
                DeleteDepartmentBto deleteDepartmentBto) {
            return super.getUnmodifiedResult(deleteDepartmentBto);
        }
    }
}
