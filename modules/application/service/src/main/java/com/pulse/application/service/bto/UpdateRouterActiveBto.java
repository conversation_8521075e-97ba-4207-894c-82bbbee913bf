package com.pulse.application.service.bto;

import com.pulse.application.common.enums.CallTypeEnum;
import com.pulse.application.common.enums.ProgramEnum;
import com.pulse.application.common.enums.SourceSystemTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> Router
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "f7c0bd32-6de9-4fdb-bbd3-8cbddc89ceb4|BTO|DEFINITION")
public class UpdateRouterActiveBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 调用类型 */
    @AutoGenerated(locked = true, uuid = "40d970e7-4c7b-45ac-8c91-c6326a94635e")
    private CallTypeEnum callType;

    /** 启用标识 */
    @AutoGenerated(locked = true, uuid = "6af16c59-7a9c-4f01-996d-1fea68f9de59")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "4b2751b9-66b7-4276-860b-5239b0af3950")
    private String id;

    /** 打开程序名称 */
    @AutoGenerated(locked = true, uuid = "ae63eca4-16d7-43b1-bb28-35c35617a9e7")
    private ProgramEnum openProgramName;

    /** 打开方式 */
    @AutoGenerated(locked = true, uuid = "60ee9f09-cb1c-4ee1-8af7-b726d7a74ac3")
    private SourceSystemTypeEnum openType;

    /** 参数 */
    @AutoGenerated(locked = true, uuid = "bef41d82-555e-49d5-973c-21b873850b98")
    private String parameter;

    /** 路径 */
    @AutoGenerated(locked = true, uuid = "52f6d697-30ac-4389-978a-202511cd2920")
    private String path;

    /** 路由URL */
    @AutoGenerated(locked = true, uuid = "a1eecb66-d47c-4b01-9c30-2cc6a6d3a608")
    private String routerUrl;

    /** 系统架构 */
    @AutoGenerated(locked = true, uuid = "8de0be18-c889-46af-91da-14fabeb4ae8f")
    private String systemArchitecture;

    @AutoGenerated(locked = true)
    public void setCallType(CallTypeEnum callType) {
        this.__$validPropertySet.add("callType");
        this.callType = callType;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setOpenProgramName(ProgramEnum openProgramName) {
        this.__$validPropertySet.add("openProgramName");
        this.openProgramName = openProgramName;
    }

    @AutoGenerated(locked = true)
    public void setOpenType(SourceSystemTypeEnum openType) {
        this.__$validPropertySet.add("openType");
        this.openType = openType;
    }

    @AutoGenerated(locked = true)
    public void setParameter(String parameter) {
        this.__$validPropertySet.add("parameter");
        this.parameter = parameter;
    }

    @AutoGenerated(locked = true)
    public void setPath(String path) {
        this.__$validPropertySet.add("path");
        this.path = path;
    }

    @AutoGenerated(locked = true)
    public void setRouterUrl(String routerUrl) {
        this.__$validPropertySet.add("routerUrl");
        this.routerUrl = routerUrl;
    }

    @AutoGenerated(locked = true)
    public void setSystemArchitecture(String systemArchitecture) {
        this.__$validPropertySet.add("systemArchitecture");
        this.systemArchitecture = systemArchitecture;
    }
}
