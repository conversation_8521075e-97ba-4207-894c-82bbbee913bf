package com.pulse.application.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.ApplicationDetailDtoManager;
import com.pulse.application.manager.dto.ApplicationDetailDto;
import com.pulse.application.service.converter.ApplicationDetailDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "7a0b39d5-f8ad-4b7d-9bac-02598ca0cc2f|DTO|SERVICE")
public class ApplicationDetailDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationDetailDtoManager applicationDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ApplicationDetailDtoServiceConverter applicationDetailDtoServiceConverter;

    @PublicInterface(id = "a1190e04-9286-4339-ac76-719c70933933", module = "application")
    @AutoGenerated(locked = false, uuid = "02e33447-fb59-3ece-8203-28e61be1b9ec")
    public List<ApplicationDetailDto> getByApplicationId(
            @NotNull(message = "应用ID不能为空") String applicationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByApplicationIds(Arrays.asList(applicationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "0f2d8340-4ea9-4dad-a0c9-949ce8f24062", module = "application")
    @AutoGenerated(locked = false, uuid = "0b1bb584-fadc-3b53-9515-68e3260907df")
    public ApplicationDetailDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "b428080e-f6cd-4a3f-9abe-ec25a2729179", module = "application")
    @AutoGenerated(locked = false, uuid = "2271b2a6-a4fd-3f1d-a053-a5384f8d63c9")
    public List<ApplicationDetailDto> getByApplicationIds(
            @Valid @NotNull(message = "应用ID不能为空") List<String> applicationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        applicationId = new ArrayList<>(new HashSet<>(applicationId));
        List<ApplicationDetailDto> applicationDetailDtoList =
                applicationDetailDtoManager.getByApplicationIds(applicationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return applicationDetailDtoServiceConverter.ApplicationDetailDtoConverter(
                applicationDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "55aa4d8f-495e-455a-9f5a-e80346491b38", module = "application")
    @AutoGenerated(locked = false, uuid = "8c9b15a5-bceb-32ff-aa82-cb2ebd17d124")
    public List<ApplicationDetailDto> getByOrganizationIds(
            @Valid @NotNull(message = "组织ID不能为空") List<String> organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        organizationId = new ArrayList<>(new HashSet<>(organizationId));
        List<ApplicationDetailDto> applicationDetailDtoList =
                applicationDetailDtoManager.getByOrganizationIds(organizationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return applicationDetailDtoServiceConverter.ApplicationDetailDtoConverter(
                applicationDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "e17022e8-23dd-45d5-b052-1440c942e34b", module = "application")
    @AutoGenerated(locked = false, uuid = "c1908032-8622-3760-a172-7e5fcc7f4aa5")
    public List<ApplicationDetailDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ApplicationDetailDto> applicationDetailDtoList =
                applicationDetailDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return applicationDetailDtoServiceConverter.ApplicationDetailDtoConverter(
                applicationDetailDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "c679d40f-5d18-4fba-b3d1-e8d4ce63b0e1", module = "application")
    @AutoGenerated(locked = false, uuid = "c85f221b-a91e-3717-b041-a48795d47685")
    public List<ApplicationDetailDto> getByOrganizationId(
            @NotNull(message = "组织ID不能为空") String organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOrganizationIds(Arrays.asList(organizationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
