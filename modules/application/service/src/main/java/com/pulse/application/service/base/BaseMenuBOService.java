package com.pulse.application.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.application.manager.bo.*;
import com.pulse.application.manager.bo.MenuBO;
import com.pulse.application.persist.dos.Menu;
import com.pulse.application.service.base.BaseMenuBOService.CreateMenuBoResult;
import com.pulse.application.service.base.BaseMenuBOService.MergeMenuBoResult;
import com.pulse.application.service.base.BaseMenuBOService.UpdateMenuActiveBoResult;
import com.pulse.application.service.bto.CreateMenuBto;
import com.pulse.application.service.bto.MergeMenuBto;
import com.pulse.application.service.bto.UpdateMenuActiveBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "32c8102d-de2c-391b-a84a-2d0a92a484d9")
public class BaseMenuBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private MenuBO createCreateMenuOnDuplicateThrowEx(
            BaseMenuBOService.CreateMenuBoResult boResult, CreateMenuBto createMenuBto) {
        MenuBO menuBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createMenuBto.getId() == null);
        if (!allNull && !found) {
            menuBO = MenuBO.getById(createMenuBto.getId());
            if (menuBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (menuBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", menuBO.getId(), "menu");
                throw new IgnoredException(400, "菜单已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "menu",
                        menuBO.getId(),
                        "menu");
                throw new IgnoredException(400, "菜单已存在");
            }
        } else {
            menuBO = new MenuBO();
            if (pkExist) {
                menuBO.setId(createMenuBto.getId());
            } else {
                menuBO.setId(String.valueOf(this.idGenerator.allocateId("menu")));
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "name")) {
                menuBO.setName(createMenuBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "menuLogo")) {
                menuBO.setMenuLogo(createMenuBto.getMenuLogo());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "routerId")) {
                menuBO.setRouterId(createMenuBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "parentId")) {
                menuBO.setParentId(createMenuBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "sameLevelGroupNumber")) {
                menuBO.setSameLevelGroupNumber(createMenuBto.getSameLevelGroupNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "sortNumber")) {
                menuBO.setSortNumber(createMenuBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "visibleFlag")) {
                menuBO.setVisibleFlag(createMenuBto.getVisibleFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "enableFlag")) {
                menuBO.setEnableFlag(createMenuBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "shortcutKey")) {
                menuBO.setShortcutKey(createMenuBto.getShortcutKey());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "inputCode")) {
                menuBO.setInputCode(createMenuBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "menuType")) {
                menuBO.setMenuType(createMenuBto.getMenuType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "remark")) {
                menuBO.setRemark(createMenuBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(createMenuBto, "__$validPropertySet"),
                    "code")) {
                menuBO.setCode(createMenuBto.getCode());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createMenuBto);
            addedBto.setBo(menuBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return menuBO;
    }

    /** 新增菜单 */
    @AutoGenerated(locked = true)
    protected CreateMenuBoResult createMenuBase(CreateMenuBto createMenuBto) {
        if (createMenuBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateMenuBoResult boResult = new CreateMenuBoResult();
        MenuBO menuBO = createCreateMenuOnDuplicateThrowEx(boResult, createMenuBto);
        boResult.setRootBo(menuBO);
        return boResult;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private MenuBO createMergeMenuOnDuplicateUpdate(
            BaseMenuBOService.MergeMenuBoResult boResult, MergeMenuBto mergeMenuBto) {
        MenuBO menuBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeMenuBto.getId() == null);
        if (!allNull && !found) {
            menuBO = MenuBO.getById(mergeMenuBto.getId());
            if (menuBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (menuBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(menuBO.convertToMenu());
                updatedBto.setBto(mergeMenuBto);
                updatedBto.setBo(menuBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "name")) {
                    menuBO.setName(mergeMenuBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "menuLogo")) {
                    menuBO.setMenuLogo(mergeMenuBto.getMenuLogo());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "routerId")) {
                    menuBO.setRouterId(mergeMenuBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "parentId")) {
                    menuBO.setParentId(mergeMenuBto.getParentId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "sameLevelGroupNumber")) {
                    menuBO.setSameLevelGroupNumber(mergeMenuBto.getSameLevelGroupNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "sortNumber")) {
                    menuBO.setSortNumber(mergeMenuBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "visibleFlag")) {
                    menuBO.setVisibleFlag(mergeMenuBto.getVisibleFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "enableFlag")) {
                    menuBO.setEnableFlag(mergeMenuBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "shortcutKey")) {
                    menuBO.setShortcutKey(mergeMenuBto.getShortcutKey());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "inputCode")) {
                    menuBO.setInputCode(mergeMenuBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "menuType")) {
                    menuBO.setMenuType(mergeMenuBto.getMenuType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "remark")) {
                    menuBO.setRemark(mergeMenuBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "code")) {
                    menuBO.setCode(mergeMenuBto.getCode());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(menuBO.convertToMenu());
                updatedBto.setBto(mergeMenuBto);
                updatedBto.setBo(menuBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "name")) {
                    menuBO.setName(mergeMenuBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "menuLogo")) {
                    menuBO.setMenuLogo(mergeMenuBto.getMenuLogo());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "routerId")) {
                    menuBO.setRouterId(mergeMenuBto.getRouterId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "parentId")) {
                    menuBO.setParentId(mergeMenuBto.getParentId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "sameLevelGroupNumber")) {
                    menuBO.setSameLevelGroupNumber(mergeMenuBto.getSameLevelGroupNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "sortNumber")) {
                    menuBO.setSortNumber(mergeMenuBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "visibleFlag")) {
                    menuBO.setVisibleFlag(mergeMenuBto.getVisibleFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "enableFlag")) {
                    menuBO.setEnableFlag(mergeMenuBto.getEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "shortcutKey")) {
                    menuBO.setShortcutKey(mergeMenuBto.getShortcutKey());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "inputCode")) {
                    menuBO.setInputCode(mergeMenuBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "menuType")) {
                    menuBO.setMenuType(mergeMenuBto.getMenuType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "remark")) {
                    menuBO.setRemark(mergeMenuBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                        "code")) {
                    menuBO.setCode(mergeMenuBto.getCode());
                }
            }
        } else {
            menuBO = new MenuBO();
            if (pkExist) {
                menuBO.setId(mergeMenuBto.getId());
            } else {
                menuBO.setId(String.valueOf(this.idGenerator.allocateId("menu")));
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "name")) {
                menuBO.setName(mergeMenuBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "menuLogo")) {
                menuBO.setMenuLogo(mergeMenuBto.getMenuLogo());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "routerId")) {
                menuBO.setRouterId(mergeMenuBto.getRouterId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "parentId")) {
                menuBO.setParentId(mergeMenuBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "sameLevelGroupNumber")) {
                menuBO.setSameLevelGroupNumber(mergeMenuBto.getSameLevelGroupNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "sortNumber")) {
                menuBO.setSortNumber(mergeMenuBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "visibleFlag")) {
                menuBO.setVisibleFlag(mergeMenuBto.getVisibleFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "enableFlag")) {
                menuBO.setEnableFlag(mergeMenuBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "shortcutKey")) {
                menuBO.setShortcutKey(mergeMenuBto.getShortcutKey());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "inputCode")) {
                menuBO.setInputCode(mergeMenuBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "menuType")) {
                menuBO.setMenuType(mergeMenuBto.getMenuType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "remark")) {
                menuBO.setRemark(mergeMenuBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeMenuBto, "__$validPropertySet"),
                    "code")) {
                menuBO.setCode(mergeMenuBto.getCode());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeMenuBto);
            addedBto.setBo(menuBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return menuBO;
    }

    /** merge菜单 */
    @AutoGenerated(locked = true)
    protected MergeMenuBoResult mergeMenuBase(MergeMenuBto mergeMenuBto) {
        if (mergeMenuBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeMenuBoResult boResult = new MergeMenuBoResult();
        MenuBO menuBO = createMergeMenuOnDuplicateUpdate(boResult, mergeMenuBto);
        boResult.setRootBo(menuBO);
        return boResult;
    }

    /** 修改菜单启用标志 */
    @AutoGenerated(locked = true)
    protected UpdateMenuActiveBoResult updateMenuActiveBase(
            UpdateMenuActiveBto updateMenuActiveBto) {
        if (updateMenuActiveBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateMenuActiveBoResult boResult = new UpdateMenuActiveBoResult();
        MenuBO menuBO = updateUpdateMenuActiveOnMissThrowEx(boResult, updateMenuActiveBto);
        boResult.setRootBo(menuBO);
        return boResult;
    }

    /** 更新对象:updateMenuActive,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private MenuBO updateUpdateMenuActiveOnMissThrowEx(
            BaseMenuBOService.UpdateMenuActiveBoResult boResult,
            UpdateMenuActiveBto updateMenuActiveBto) {
        MenuBO menuBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateMenuActiveBto.getId() == null);
        if (!allNull && !found) {
            menuBO = MenuBO.getById(updateMenuActiveBto.getId());
            found = true;
        }
        if (menuBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(menuBO.convertToMenu());
            updatedBto.setBto(updateMenuActiveBto);
            updatedBto.setBo(menuBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateMenuActiveBto, "__$validPropertySet"),
                    "enableFlag")) {
                menuBO.setEnableFlag(updateMenuActiveBto.getEnableFlag());
            }
            return menuBO;
        }
    }

    public static class CreateMenuBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public MenuBO getRootBo() {
            return (MenuBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateMenuBto, MenuBO> getCreatedBto(CreateMenuBto createMenuBto) {
            return this.getAddedResult(createMenuBto);
        }

        @AutoGenerated(locked = true)
        public Menu getDeleted_Menu() {
            return (Menu) CollectionUtil.getFirst(this.getDeletedEntityList(Menu.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateMenuBto, Menu, MenuBO> getUpdatedBto(CreateMenuBto createMenuBto) {
            return super.getUpdatedResult(createMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateMenuBto, MenuBO> getUnmodifiedBto(CreateMenuBto createMenuBto) {
            return super.getUnmodifiedResult(createMenuBto);
        }
    }

    public static class MergeMenuBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public MenuBO getRootBo() {
            return (MenuBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeMenuBto, MenuBO> getCreatedBto(MergeMenuBto mergeMenuBto) {
            return this.getAddedResult(mergeMenuBto);
        }

        @AutoGenerated(locked = true)
        public Menu getDeleted_Menu() {
            return (Menu) CollectionUtil.getFirst(this.getDeletedEntityList(Menu.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeMenuBto, Menu, MenuBO> getUpdatedBto(MergeMenuBto mergeMenuBto) {
            return super.getUpdatedResult(mergeMenuBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeMenuBto, MenuBO> getUnmodifiedBto(MergeMenuBto mergeMenuBto) {
            return super.getUnmodifiedResult(mergeMenuBto);
        }
    }

    public static class UpdateMenuActiveBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public MenuBO getRootBo() {
            return (MenuBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateMenuActiveBto, MenuBO> getCreatedBto(
                UpdateMenuActiveBto updateMenuActiveBto) {
            return this.getAddedResult(updateMenuActiveBto);
        }

        @AutoGenerated(locked = true)
        public Menu getDeleted_Menu() {
            return (Menu) CollectionUtil.getFirst(this.getDeletedEntityList(Menu.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateMenuActiveBto, Menu, MenuBO> getUpdatedBto(
                UpdateMenuActiveBto updateMenuActiveBto) {
            return super.getUpdatedResult(updateMenuActiveBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateMenuActiveBto, MenuBO> getUnmodifiedBto(
                UpdateMenuActiveBto updateMenuActiveBto) {
            return super.getUnmodifiedResult(updateMenuActiveBto);
        }
    }
}
