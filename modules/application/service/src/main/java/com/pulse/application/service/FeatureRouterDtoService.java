package com.pulse.application.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.FeatureRouterDtoManager;
import com.pulse.application.manager.dto.FeatureRouterDto;
import com.pulse.application.service.converter.FeatureRouterDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "20a1183c-05f9-4c98-ba9f-b88f9cef3066|DTO|SERVICE")
public class FeatureRouterDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private FeatureRouterDtoManager featureRouterDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private FeatureRouterDtoServiceConverter featureRouterDtoServiceConverter;

    @PublicInterface(id = "baf78668-4c4d-4ab1-a80f-6852ddb0906f", module = "application")
    @AutoGenerated(locked = false, uuid = "5c32b810-46ea-304e-ac8e-5ff46bae63a2")
    public List<FeatureRouterDto> getByParentId(@NotNull(message = "父功能ID不能为空") String parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByParentIds(Arrays.asList(parentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "fb5c1794-80b4-4b42-9173-8d78e5412fc5", module = "application")
    @AutoGenerated(locked = false, uuid = "72ab09aa-fd94-392f-9217-215eecad881a")
    public FeatureRouterDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<FeatureRouterDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "ed29089f-593a-48fc-a945-c22bf1bad9ed", module = "application")
    @AutoGenerated(locked = false, uuid = "7b5007d6-71f5-349d-b6da-f9bc0685d8aa")
    public List<FeatureRouterDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<FeatureRouterDto> featureRouterDtoList = featureRouterDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return featureRouterDtoServiceConverter.FeatureRouterDtoConverter(featureRouterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "ea24a7c4-d0a5-441b-97af-23011021d432", module = "application")
    @AutoGenerated(locked = false, uuid = "e7950df5-51e7-38be-b7f5-18338b73b00a")
    public List<FeatureRouterDto> getByParentIds(
            @Valid @NotNull(message = "父功能ID不能为空") List<String> parentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        parentId = new ArrayList<>(new HashSet<>(parentId));
        List<FeatureRouterDto> featureRouterDtoList =
                featureRouterDtoManager.getByParentIds(parentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return featureRouterDtoServiceConverter.FeatureRouterDtoConverter(featureRouterDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
