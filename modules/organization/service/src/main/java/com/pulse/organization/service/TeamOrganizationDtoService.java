package com.pulse.organization.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.TeamOrganizationDtoManager;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.service.converter.TeamOrganizationDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "2c68ecb9-24e6-49af-9ffb-7174879beeed|DTO|SERVICE")
public class TeamOrganizationDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private TeamOrganizationDtoManager teamOrganizationDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamOrganizationDtoServiceConverter teamOrganizationDtoServiceConverter;

    @PublicInterface(
            id = "f14d71a4-21a6-4179-bed1-6c388baa5181",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1744709717254")
    @AutoGenerated(locked = false, uuid = "064ed799-d679-3b9f-a66a-5a6f119cd62e")
    public List<TeamOrganizationDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<TeamOrganizationDto> teamOrganizationDtoList = teamOrganizationDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return teamOrganizationDtoServiceConverter.TeamOrganizationDtoConverter(
                teamOrganizationDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "a222c8fd-0bcc-4e33-900e-e45fd041d097",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1744709717263")
    @AutoGenerated(locked = false, uuid = "4e07d77d-0268-3f1b-b873-8de159875848")
    public List<TeamOrganizationDto> getByTeamIds(
            @Valid @NotNull(message = "团队ID不能为空") List<String> teamId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        teamId = new ArrayList<>(new HashSet<>(teamId));
        List<TeamOrganizationDto> teamOrganizationDtoList =
                teamOrganizationDtoManager.getByTeamIds(teamId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return teamOrganizationDtoServiceConverter.TeamOrganizationDtoConverter(
                teamOrganizationDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "e8d6ebb2-77af-457c-bf36-069fecd17057",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1744709717269")
    @AutoGenerated(locked = false, uuid = "71ab68ea-96d2-3b8a-ac82-62b2804c9a21")
    public List<TeamOrganizationDto> getByOrganizationId(
            @NotNull(message = "组织ID不能为空") String organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOrganizationIds(Arrays.asList(organizationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "4ecb0abd-a879-40ad-b116-ed89c81d2149",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1744709717273")
    @AutoGenerated(locked = false, uuid = "7d4681d0-cc9e-3f75-b66f-f86fb5b753d7")
    public List<TeamOrganizationDto> getByOrganizationIds(
            @Valid @NotNull(message = "组织ID不能为空") List<String> organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        organizationId = new ArrayList<>(new HashSet<>(organizationId));
        List<TeamOrganizationDto> teamOrganizationDtoList =
                teamOrganizationDtoManager.getByOrganizationIds(organizationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return teamOrganizationDtoServiceConverter.TeamOrganizationDtoConverter(
                teamOrganizationDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "b4faaae7-ee9f-4d6c-87ab-ae46bd5ef95b",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1744709717259")
    @AutoGenerated(locked = false, uuid = "7d46ddc4-00bf-3d99-b42f-3a815ba34d7c")
    public List<TeamOrganizationDto> getByTeamId(@NotNull(message = "团队ID不能为空") String teamId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByTeamIds(Arrays.asList(teamId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "35bc9c39-3025-4ea8-86d6-dd034e64770d",
            module = "organization",
            moduleId = "a3b95408-2257-4bfa-aafe-59cc5547e63c",
            pubRpc = true,
            version = "1744709717248")
    @AutoGenerated(locked = false, uuid = "90b1dd1c-093e-35ff-887d-adc9c239fbd5")
    public TeamOrganizationDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TeamOrganizationDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
