package com.pulse.organization.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.organization.manager.bo.*;
import com.pulse.organization.manager.bo.OrganizationBO;
import com.pulse.organization.persist.dos.Campus;
import com.pulse.organization.persist.dos.Department;
import com.pulse.organization.persist.dos.Institution;
import com.pulse.organization.persist.dos.Organization;
import com.pulse.organization.persist.dos.Ward;
import com.pulse.organization.service.base.BaseOrganizationBOService.CreateOrganizationCampusBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.CreateOrganizationDepartmentBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.CreateOrganizationInstitutionBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.CreateOrganizationWardBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.DeleteOrganizationBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.UpdateOrganizationCampusBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.UpdateOrganizationDepartmentBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.UpdateOrganizationInstitutionBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.UpdateOrganizationStatusBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.UpdateOrganizationWardBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.UpdateRegisterDepartmentBoResult;
import com.pulse.organization.service.base.BaseOrganizationBOService.UpdateRegisterDepartmentEnableFlagBoResult;
import com.pulse.organization.service.bto.CreateOrganizationCampusBto;
import com.pulse.organization.service.bto.CreateOrganizationDepartmentBto;
import com.pulse.organization.service.bto.CreateOrganizationInstitutionBto;
import com.pulse.organization.service.bto.CreateOrganizationWardBto;
import com.pulse.organization.service.bto.DeleteOrganizationBto;
import com.pulse.organization.service.bto.UpdateOrganizationCampusBto;
import com.pulse.organization.service.bto.UpdateOrganizationDepartmentBto;
import com.pulse.organization.service.bto.UpdateOrganizationInstitutionBto;
import com.pulse.organization.service.bto.UpdateOrganizationStatusBto;
import com.pulse.organization.service.bto.UpdateOrganizationWardBto;
import com.pulse.organization.service.bto.UpdateRegisterDepartmentBto;
import com.pulse.organization.service.bto.UpdateRegisterDepartmentEnableFlagBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "*************-375d-9e53-be7041b87e9f")
public class BaseOrganizationBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 创建对象:CampusBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createCampusBtoOnDuplicateThrowEx(
            BaseOrganizationBOService.CreateOrganizationCampusBoResult boResult,
            CreateOrganizationCampusBto createOrganizationCampusBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getCampusBO() != null) {
            log.error("id:{}在数据库表:{}中已经存在！", organizationBO.getCampusBO().getId(), "campus");
            throw new IgnoredException(400, "院区已存在");
        } else {
            if (createOrganizationCampusBto.getCampusBto() == null) {
                return;
            }
            CampusBO campusBO = organizationBO.getOrCreateCampusBO();
            CreateOrganizationCampusBto.CampusBto campusBto =
                    createOrganizationCampusBto.getCampusBto();
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(campusBto, "__$validPropertySet"),
                    "campusNumber")) {
                campusBO.setCampusNumber(
                        createOrganizationCampusBto.getCampusBto().getCampusNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(campusBto, "__$validPropertySet"),
                    "medicalInsuranceCode")) {
                campusBO.setMedicalInsuranceCode(
                        createOrganizationCampusBto.getCampusBto().getMedicalInsuranceCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(campusBto, "__$validPropertySet"),
                    "createdBy")) {
                campusBO.setCreatedBy(createOrganizationCampusBto.getCampusBto().getCreatedBy());
            }

            campusBO.setId(String.valueOf(this.idGenerator.allocateId("campus")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(campusBto);
            addedBto.setBo(campusBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private OrganizationBO createCreateOrganizationCampus(
            CreateOrganizationCampusBoResult boResult,
            CreateOrganizationCampusBto createOrganizationCampusBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull =
                (createOrganizationCampusBto.getType() == null)
                        && (createOrganizationCampusBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            createOrganizationCampusBto.getType(),
                            createOrganizationCampusBto.getName());
            if (organizationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'type'";
                matchedUkName += ",";
                matchedUkName += "'name'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (organizationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", organizationBO.getId(), "organization");
                throw new IgnoredException(400, "组织已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "organization",
                        organizationBO.getId());
                throw new IgnoredException(400, "组织已存在");
            }
        } else {
            organizationBO = new OrganizationBO();
            if (pkExist) {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            } else {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(createOrganizationCampusBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(createOrganizationCampusBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(createOrganizationCampusBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(createOrganizationCampusBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(createOrganizationCampusBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(createOrganizationCampusBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(createOrganizationCampusBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        createOrganizationCampusBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(createOrganizationCampusBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(createOrganizationCampusBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(createOrganizationCampusBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(createOrganizationCampusBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(createOrganizationCampusBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(createOrganizationCampusBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "createdBy")) {
                organizationBO.setCreatedBy(createOrganizationCampusBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(createOrganizationCampusBto.getInvalidFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createOrganizationCampusBto);
            addedBto.setBo(organizationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return organizationBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private OrganizationBO createCreateOrganizationDepartment(
            CreateOrganizationDepartmentBoResult boResult,
            CreateOrganizationDepartmentBto createOrganizationDepartmentBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull =
                (createOrganizationDepartmentBto.getType() == null)
                        && (createOrganizationDepartmentBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            createOrganizationDepartmentBto.getType(),
                            createOrganizationDepartmentBto.getName());
            if (organizationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'type'";
                matchedUkName += ",";
                matchedUkName += "'name'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (organizationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", organizationBO.getId(), "organization");
                throw new IgnoredException(400, "组织已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "organization",
                        organizationBO.getId());
                throw new IgnoredException(400, "组织已存在");
            }
        } else {
            organizationBO = new OrganizationBO();
            if (pkExist) {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            } else {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(createOrganizationDepartmentBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(createOrganizationDepartmentBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(createOrganizationDepartmentBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(createOrganizationDepartmentBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(createOrganizationDepartmentBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(createOrganizationDepartmentBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(createOrganizationDepartmentBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        createOrganizationDepartmentBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(createOrganizationDepartmentBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(createOrganizationDepartmentBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(createOrganizationDepartmentBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(createOrganizationDepartmentBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(createOrganizationDepartmentBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(createOrganizationDepartmentBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(createOrganizationDepartmentBto.getInvalidFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "createdBy")) {
                organizationBO.setCreatedBy(createOrganizationDepartmentBto.getCreatedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createOrganizationDepartmentBto);
            addedBto.setBo(organizationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return organizationBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private OrganizationBO createCreateOrganizationInstitution(
            CreateOrganizationInstitutionBoResult boResult,
            CreateOrganizationInstitutionBto createOrganizationInstitutionBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull =
                (createOrganizationInstitutionBto.getType() == null)
                        && (createOrganizationInstitutionBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            createOrganizationInstitutionBto.getType(),
                            createOrganizationInstitutionBto.getName());
            if (organizationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'type'";
                matchedUkName += ",";
                matchedUkName += "'name'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (organizationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", organizationBO.getId(), "organization");
                throw new IgnoredException(400, "组织已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "organization",
                        organizationBO.getId());
                throw new IgnoredException(400, "组织已存在");
            }
        } else {
            organizationBO = new OrganizationBO();
            if (pkExist) {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            } else {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(createOrganizationInstitutionBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(createOrganizationInstitutionBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(createOrganizationInstitutionBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(createOrganizationInstitutionBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(createOrganizationInstitutionBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(createOrganizationInstitutionBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(createOrganizationInstitutionBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        createOrganizationInstitutionBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(createOrganizationInstitutionBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(
                        createOrganizationInstitutionBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(
                        createOrganizationInstitutionBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(createOrganizationInstitutionBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(createOrganizationInstitutionBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(createOrganizationInstitutionBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "createdBy")) {
                organizationBO.setCreatedBy(createOrganizationInstitutionBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(createOrganizationInstitutionBto.getInvalidFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createOrganizationInstitutionBto);
            addedBto.setBo(organizationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return organizationBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private OrganizationBO createCreateOrganizationWard(
            CreateOrganizationWardBoResult boResult,
            CreateOrganizationWardBto createOrganizationWardBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull =
                (createOrganizationWardBto.getType() == null)
                        && (createOrganizationWardBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            createOrganizationWardBto.getType(),
                            createOrganizationWardBto.getName());
            if (organizationBO != null) {
                matchedUkName += "(";
                matchedUkName += "'type'";
                matchedUkName += ",";
                matchedUkName += "'name'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (organizationBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", organizationBO.getId(), "organization");
                throw new IgnoredException(400, "组织已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "organization",
                        organizationBO.getId());
                throw new IgnoredException(400, "组织已存在");
            }
        } else {
            organizationBO = new OrganizationBO();
            if (pkExist) {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            } else {
                organizationBO.setId(String.valueOf(this.idGenerator.allocateId("organization")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(createOrganizationWardBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(createOrganizationWardBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(createOrganizationWardBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(createOrganizationWardBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(createOrganizationWardBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(createOrganizationWardBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(createOrganizationWardBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        createOrganizationWardBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(createOrganizationWardBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(createOrganizationWardBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(createOrganizationWardBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(createOrganizationWardBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(createOrganizationWardBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(createOrganizationWardBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(createOrganizationWardBto.getInvalidFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "createdBy")) {
                organizationBO.setCreatedBy(createOrganizationWardBto.getCreatedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createOrganizationWardBto);
            addedBto.setBo(organizationBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return organizationBO;
    }

    /** 创建对象:DepartmentBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createDepartmentBtoOnDuplicateThrowEx(
            BaseOrganizationBOService.CreateOrganizationDepartmentBoResult boResult,
            CreateOrganizationDepartmentBto createOrganizationDepartmentBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getDepartmentBO() != null) {
            log.error(
                    "id:{}在数据库表:{}中已经存在！", organizationBO.getDepartmentBO().getId(), "department");
            throw new IgnoredException(400, "科室已存在");
        } else {
            if (createOrganizationDepartmentBto.getDepartmentBto() == null) {
                return;
            }
            DepartmentBO departmentBO = organizationBO.getOrCreateDepartmentBO();
            CreateOrganizationDepartmentBto.DepartmentBto departmentBto =
                    createOrganizationDepartmentBto.getDepartmentBto();
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "departmentProperty")) {
                departmentBO.setDepartmentProperty(
                        createOrganizationDepartmentBto.getDepartmentBto().getDepartmentProperty());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                departmentBO.setCampusOrganizationId(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getCampusOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "departmentLevel")) {
                departmentBO.setDepartmentLevel(
                        createOrganizationDepartmentBto.getDepartmentBto().getDepartmentLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "standardDepartmentCatalogId")) {
                departmentBO.setStandardDepartmentCatalogId(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getStandardDepartmentCatalogId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "parentDepartmentOrganizationId")) {
                departmentBO.setParentDepartmentOrganizationId(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getParentDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "accountingDepartmentOrganizationId")) {
                departmentBO.setAccountingDepartmentOrganizationId(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getAccountingDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "costDepartmentOrganizationId")) {
                departmentBO.setCostDepartmentOrganizationId(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getCostDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "hrDepartmentOrganizationId")) {
                departmentBO.setHrDepartmentOrganizationId(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getHrDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "medicalServiceType")) {
                departmentBO.setMedicalServiceType(
                        createOrganizationDepartmentBto.getDepartmentBto().getMedicalServiceType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "averagePrescriptionLimit")) {
                departmentBO.setAveragePrescriptionLimit(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getAveragePrescriptionLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "injectionSecondDayAmount")) {
                departmentBO.setInjectionSecondDayAmount(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getInjectionSecondDayAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "ageUpperLimit")) {
                departmentBO.setAgeUpperLimit(
                        createOrganizationDepartmentBto.getDepartmentBto().getAgeUpperLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "ageLowerLimit")) {
                departmentBO.setAgeLowerLimit(
                        createOrganizationDepartmentBto.getDepartmentBto().getAgeLowerLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "medicalRecordDepartmentCode")) {
                departmentBO.setMedicalRecordDepartmentCode(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getMedicalRecordDepartmentCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "defaultPrescriptionType")) {
                departmentBO.setDefaultPrescriptionType(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getDefaultPrescriptionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "specialtyFlag")) {
                departmentBO.setSpecialtyFlag(
                        createOrganizationDepartmentBto.getDepartmentBto().getSpecialtyFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "createdBy")) {
                departmentBO.setCreatedBy(
                        createOrganizationDepartmentBto.getDepartmentBto().getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "openTimePeriod")) {
                departmentBO.setOpenTimePeriod(
                        createOrganizationDepartmentBto.getDepartmentBto().getOpenTimePeriod());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "drugTypeList")) {
                departmentBO.setDrugTypeList(
                        createOrganizationDepartmentBto.getDepartmentBto().getDrugTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "drugCatalogList")) {
                departmentBO.setDrugCatalogList(
                        createOrganizationDepartmentBto.getDepartmentBto().getDrugCatalogList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "storageType")) {
                departmentBO.setStorageType(
                        createOrganizationDepartmentBto.getDepartmentBto().getStorageType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                departmentBO.setProvincePlatformCancelFlag(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "genderLimit")) {
                departmentBO.setGenderLimit(
                        createOrganizationDepartmentBto.getDepartmentBto().getGenderLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "registerDepartmentFlag")) {
                departmentBO.setRegisterDepartmentFlag(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getRegisterDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "manageDepartmentOrganizationId")) {
                departmentBO.setManageDepartmentOrganizationId(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getManageDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(departmentBto, "__$validPropertySet"),
                    "registerDepartmentEnableFlag")) {
                departmentBO.setRegisterDepartmentEnableFlag(
                        createOrganizationDepartmentBto
                                .getDepartmentBto()
                                .getRegisterDepartmentEnableFlag());
            }

            departmentBO.setId(String.valueOf(this.idGenerator.allocateId("department")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(departmentBto);
            addedBto.setBo(departmentBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:InstitutionBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createInstitutionBtoOnDuplicateThrowEx(
            BaseOrganizationBOService.CreateOrganizationInstitutionBoResult boResult,
            CreateOrganizationInstitutionBto createOrganizationInstitutionBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getInstitutionBO() != null) {
            log.error(
                    "id:{}在数据库表:{}中已经存在！",
                    organizationBO.getInstitutionBO().getId(),
                    "institution");
            throw new IgnoredException(400, "医疗机构已存在");
        } else {
            if (createOrganizationInstitutionBto.getInstitutionBto() == null) {
                return;
            }
            InstitutionBO institutionBO = organizationBO.getOrCreateInstitutionBO();
            CreateOrganizationInstitutionBto.InstitutionBto institutionBto =
                    createOrganizationInstitutionBto.getInstitutionBto();
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "ownershipType")) {
                institutionBO.setOwnershipType(
                        createOrganizationInstitutionBto.getInstitutionBto().getOwnershipType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "institutionType")) {
                institutionBO.setInstitutionType(
                        createOrganizationInstitutionBto.getInstitutionBto().getInstitutionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "institutionLevel")) {
                institutionBO.setInstitutionLevel(
                        createOrganizationInstitutionBto.getInstitutionBto().getInstitutionLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "grade")) {
                institutionBO.setGrade(
                        createOrganizationInstitutionBto.getInstitutionBto().getGrade());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "affiliation")) {
                institutionBO.setAffiliation(
                        createOrganizationInstitutionBto.getInstitutionBto().getAffiliation());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "medicalRecordInstitutionCode")) {
                institutionBO.setMedicalRecordInstitutionCode(
                        createOrganizationInstitutionBto
                                .getInstitutionBto()
                                .getMedicalRecordInstitutionCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "medicalInsuranceCode")) {
                institutionBO.setMedicalInsuranceCode(
                        createOrganizationInstitutionBto
                                .getInstitutionBto()
                                .getMedicalInsuranceCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "createdBy")) {
                institutionBO.setCreatedBy(
                        createOrganizationInstitutionBto.getInstitutionBto().getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(institutionBto, "__$validPropertySet"),
                    "unitCode")) {
                institutionBO.setUnitCode(
                        createOrganizationInstitutionBto.getInstitutionBto().getUnitCode());
            }

            institutionBO.setId(String.valueOf(this.idGenerator.allocateId("institution")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(institutionBto);
            addedBto.setBo(institutionBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建组织（院区） */
    @AutoGenerated(locked = true)
    protected CreateOrganizationCampusBoResult createOrganizationCampusBase(
            CreateOrganizationCampusBto createOrganizationCampusBto) {
        if (createOrganizationCampusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateOrganizationCampusBoResult boResult = new CreateOrganizationCampusBoResult();
        OrganizationBO organizationBO =
                createCreateOrganizationCampus(boResult, createOrganizationCampusBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationCampusBto, "__$validPropertySet"),
                    "campusBto")) {
                createCampusBtoOnDuplicateThrowEx(
                        boResult, createOrganizationCampusBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 创建组织（科室） */
    @AutoGenerated(locked = true)
    protected CreateOrganizationDepartmentBoResult createOrganizationDepartmentBase(
            CreateOrganizationDepartmentBto createOrganizationDepartmentBto) {
        if (createOrganizationDepartmentBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateOrganizationDepartmentBoResult boResult = new CreateOrganizationDepartmentBoResult();
        OrganizationBO organizationBO =
                createCreateOrganizationDepartment(boResult, createOrganizationDepartmentBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationDepartmentBto, "__$validPropertySet"),
                    "departmentBto")) {
                createDepartmentBtoOnDuplicateThrowEx(
                        boResult, createOrganizationDepartmentBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 创建组织（机构） */
    @AutoGenerated(locked = true)
    protected CreateOrganizationInstitutionBoResult createOrganizationInstitutionBase(
            CreateOrganizationInstitutionBto createOrganizationInstitutionBto) {
        if (createOrganizationInstitutionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateOrganizationInstitutionBoResult boResult =
                new CreateOrganizationInstitutionBoResult();
        OrganizationBO organizationBO =
                createCreateOrganizationInstitution(boResult, createOrganizationInstitutionBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationInstitutionBto, "__$validPropertySet"),
                    "institutionBto")) {
                createInstitutionBtoOnDuplicateThrowEx(
                        boResult, createOrganizationInstitutionBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 创建组织（病区） */
    @AutoGenerated(locked = true)
    protected CreateOrganizationWardBoResult createOrganizationWardBase(
            CreateOrganizationWardBto createOrganizationWardBto) {
        if (createOrganizationWardBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateOrganizationWardBoResult boResult = new CreateOrganizationWardBoResult();
        OrganizationBO organizationBO =
                createCreateOrganizationWard(boResult, createOrganizationWardBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createOrganizationWardBto, "__$validPropertySet"),
                    "wardBto")) {
                createWardBtoOnDuplicateThrowEx(
                        boResult, createOrganizationWardBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 创建对象:WardBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createWardBtoOnDuplicateThrowEx(
            BaseOrganizationBOService.CreateOrganizationWardBoResult boResult,
            CreateOrganizationWardBto createOrganizationWardBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getWardBO() != null) {
            log.error("id:{}在数据库表:{}中已经存在！", organizationBO.getWardBO().getId(), "ward");
            throw new IgnoredException(400, "病区已存在");
        } else {
            if (createOrganizationWardBto.getWardBto() == null) {
                return;
            }
            WardBO wardBO = organizationBO.getOrCreateWardBO();
            CreateOrganizationWardBto.WardBto wardBto = createOrganizationWardBto.getWardBto();
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                wardBO.setCampusOrganizationId(
                        createOrganizationWardBto.getWardBto().getCampusOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "wardType")) {
                wardBO.setWardType(createOrganizationWardBto.getWardBto().getWardType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "virtualWardFlag")) {
                wardBO.setVirtualWardFlag(
                        createOrganizationWardBto.getWardBto().getVirtualWardFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "maxBedCount")) {
                wardBO.setMaxBedCount(createOrganizationWardBto.getWardBto().getMaxBedCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "injectionStartTime")) {
                wardBO.setInjectionStartTime(
                        createOrganizationWardBto.getWardBto().getInjectionStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "treatmentStartTime")) {
                wardBO.setTreatmentStartTime(
                        createOrganizationWardBto.getWardBto().getTreatmentStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "oralMedicationStartTime")) {
                wardBO.setOralMedicationStartTime(
                        createOrganizationWardBto.getWardBto().getOralMedicationStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "shellPosition")) {
                wardBO.setShellPosition(createOrganizationWardBto.getWardBto().getShellPosition());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(wardBto, "__$validPropertySet"),
                    "deletedBy")) {
                wardBO.setDeletedBy(createOrganizationWardBto.getWardBto().getDeletedBy());
            }

            wardBO.setId(String.valueOf(this.idGenerator.allocateId("ward")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(wardBto);
            addedBto.setBo(wardBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 删除对象:deleteOrganization,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO deleteDeleteOrganizationOnMissThrowEx(
            BaseOrganizationBOService.DeleteOrganizationBoResult boResult,
            DeleteOrganizationBto deleteOrganizationBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteOrganizationBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(deleteOrganizationBto.getId());
            found = true;
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(organizationBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteOrganizationBto);
            deletedBto.setEntity(organizationBO.convertToOrganization());
            boResult.getDeletedBtoList().add(deletedBto);
            return organizationBO;
        }
    }

    /** 删除组织 */
    @AutoGenerated(locked = true)
    protected DeleteOrganizationBoResult deleteOrganizationBase(
            DeleteOrganizationBto deleteOrganizationBto) {
        if (deleteOrganizationBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteOrganizationBoResult boResult = new DeleteOrganizationBoResult();
        OrganizationBO organizationBO =
                deleteDeleteOrganizationOnMissThrowEx(boResult, deleteOrganizationBto);
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新对象:campusBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateCampusBtoOnMissThrowEx(
            BaseOrganizationBOService.UpdateOrganizationCampusBoResult boResult,
            UpdateOrganizationCampusBto updateOrganizationCampusBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getCampusBO() == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        }
        if (updateOrganizationCampusBto.getCampusBto() != null) {
            CampusBO bo = organizationBO.getOrCreateCampusBO();
            UpdateOrganizationCampusBto.CampusBto bto = updateOrganizationCampusBto.getCampusBto();
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setEntity(bo.convertToCampus());
            updatedBto.setBo(bo);
            boResult.getUpdatedList().add(updatedBto);

            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "campusNumber")) {
                bo.setCampusNumber(bto.getCampusNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "medicalInsuranceCode")) {
                bo.setMedicalInsuranceCode(bto.getMedicalInsuranceCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
        } else {
            CampusBO bo = organizationBO.getOrCreateCampusBO();
            if (bo != null) {
                Campus deletedItem = bo.convertToCampus();
                boResult.getDeletedList().add(deletedItem);
                organizationBO.setCampusBO(null);
            }
        }
    }

    /** 更新对象:departmentBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDepartmentBtoOnMissThrowEx(
            BaseOrganizationBOService.UpdateOrganizationDepartmentBoResult boResult,
            UpdateOrganizationDepartmentBto updateOrganizationDepartmentBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getDepartmentBO() == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        }
        if (updateOrganizationDepartmentBto.getDepartmentBto() != null) {
            DepartmentBO bo = organizationBO.getOrCreateDepartmentBO();
            UpdateOrganizationDepartmentBto.DepartmentBto bto =
                    updateOrganizationDepartmentBto.getDepartmentBto();
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setEntity(bo.convertToDepartment());
            updatedBto.setBo(bo);
            boResult.getUpdatedList().add(updatedBto);

            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "departmentProperty")) {
                bo.setDepartmentProperty(bto.getDepartmentProperty());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                bo.setCampusOrganizationId(bto.getCampusOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "parentDepartmentOrganizationId")) {
                bo.setParentDepartmentOrganizationId(bto.getParentDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "departmentLevel")) {
                bo.setDepartmentLevel(bto.getDepartmentLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "accountingDepartmentOrganizationId")) {
                bo.setAccountingDepartmentOrganizationId(
                        bto.getAccountingDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "costDepartmentOrganizationId")) {
                bo.setCostDepartmentOrganizationId(bto.getCostDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "hrDepartmentOrganizationId")) {
                bo.setHrDepartmentOrganizationId(bto.getHrDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "medicalServiceType")) {
                bo.setMedicalServiceType(bto.getMedicalServiceType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "averagePrescriptionLimit")) {
                bo.setAveragePrescriptionLimit(bto.getAveragePrescriptionLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "injectionSecondDayAmount")) {
                bo.setInjectionSecondDayAmount(bto.getInjectionSecondDayAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "ageUpperLimit")) {
                bo.setAgeUpperLimit(bto.getAgeUpperLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "ageLowerLimit")) {
                bo.setAgeLowerLimit(bto.getAgeLowerLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "medicalRecordDepartmentCode")) {
                bo.setMedicalRecordDepartmentCode(bto.getMedicalRecordDepartmentCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "defaultPrescriptionType")) {
                bo.setDefaultPrescriptionType(bto.getDefaultPrescriptionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "specialtyFlag")) {
                bo.setSpecialtyFlag(bto.getSpecialtyFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "standardDepartmentCatalogId")) {
                bo.setStandardDepartmentCatalogId(bto.getStandardDepartmentCatalogId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "openTimePeriod")) {
                bo.setOpenTimePeriod(bto.getOpenTimePeriod());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "drugTypeList")) {
                bo.setDrugTypeList(bto.getDrugTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "drugCatalogList")) {
                bo.setDrugCatalogList(bto.getDrugCatalogList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "storageType")) {
                bo.setStorageType(bto.getStorageType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                bo.setProvincePlatformCancelFlag(bto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "genderLimit")) {
                bo.setGenderLimit(bto.getGenderLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "registerDepartmentFlag")) {
                bo.setRegisterDepartmentFlag(bto.getRegisterDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "manageDepartmentOrganizationId")) {
                bo.setManageDepartmentOrganizationId(bto.getManageDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "registerDepartmentEnableFlag")) {
                bo.setRegisterDepartmentEnableFlag(bto.getRegisterDepartmentEnableFlag());
            }
        } else {
            DepartmentBO bo = organizationBO.getOrCreateDepartmentBO();
            if (bo != null) {
                Department deletedItem = bo.convertToDepartment();
                boResult.getDeletedList().add(deletedItem);
                organizationBO.setDepartmentBO(null);
            }
        }
    }

    /** 更新对象:departmentBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDepartmentBtoOnMissThrowEx(
            BaseOrganizationBOService.UpdateRegisterDepartmentBoResult boResult,
            UpdateRegisterDepartmentBto updateRegisterDepartmentBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getDepartmentBO() == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        }
        if (updateRegisterDepartmentBto.getDepartmentBto() != null) {
            DepartmentBO bo = organizationBO.getOrCreateDepartmentBO();
            UpdateRegisterDepartmentBto.DepartmentBto bto =
                    updateRegisterDepartmentBto.getDepartmentBto();
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setEntity(bo.convertToDepartment());
            updatedBto.setBo(bo);
            boResult.getUpdatedList().add(updatedBto);

            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "ageUpperLimit")) {
                bo.setAgeUpperLimit(bto.getAgeUpperLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "ageLowerLimit")) {
                bo.setAgeLowerLimit(bto.getAgeLowerLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                bo.setProvincePlatformCancelFlag(bto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "genderLimit")) {
                bo.setGenderLimit(bto.getGenderLimit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "registerDepartmentFlag")) {
                bo.setRegisterDepartmentFlag(bto.getRegisterDepartmentFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "manageDepartmentOrganizationId")) {
                bo.setManageDepartmentOrganizationId(bto.getManageDepartmentOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "registerDepartmentEnableFlag")) {
                bo.setRegisterDepartmentEnableFlag(bto.getRegisterDepartmentEnableFlag());
            }
        } else {
            DepartmentBO bo = organizationBO.getOrCreateDepartmentBO();
            if (bo != null) {
                Department deletedItem = bo.convertToDepartment();
                boResult.getDeletedList().add(deletedItem);
                organizationBO.setDepartmentBO(null);
            }
        }
    }

    /** 更新对象:departmentBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateDepartmentBtoOnMissThrowEx(
            BaseOrganizationBOService.UpdateRegisterDepartmentEnableFlagBoResult boResult,
            UpdateRegisterDepartmentEnableFlagBto updateRegisterDepartmentEnableFlagBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getDepartmentBO() == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        }
        if (updateRegisterDepartmentEnableFlagBto.getDepartmentBto() != null) {
            DepartmentBO bo = organizationBO.getOrCreateDepartmentBO();
            UpdateRegisterDepartmentEnableFlagBto.DepartmentBto bto =
                    updateRegisterDepartmentEnableFlagBto.getDepartmentBto();
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setEntity(bo.convertToDepartment());
            updatedBto.setBo(bo);
            boResult.getUpdatedList().add(updatedBto);

            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "registerDepartmentEnableFlag")) {
                bo.setRegisterDepartmentEnableFlag(bto.getRegisterDepartmentEnableFlag());
            }
        } else {
            DepartmentBO bo = organizationBO.getOrCreateDepartmentBO();
            if (bo != null) {
                Department deletedItem = bo.convertToDepartment();
                boResult.getDeletedList().add(deletedItem);
                organizationBO.setDepartmentBO(null);
            }
        }
    }

    /** 更新对象:institutionBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateInstitutionBtoOnMissThrowEx(
            BaseOrganizationBOService.UpdateOrganizationInstitutionBoResult boResult,
            UpdateOrganizationInstitutionBto updateOrganizationInstitutionBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getInstitutionBO() == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        }
        if (updateOrganizationInstitutionBto.getInstitutionBto() != null) {
            InstitutionBO bo = organizationBO.getOrCreateInstitutionBO();
            UpdateOrganizationInstitutionBto.InstitutionBto bto =
                    updateOrganizationInstitutionBto.getInstitutionBto();
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setEntity(bo.convertToInstitution());
            updatedBto.setBo(bo);
            boResult.getUpdatedList().add(updatedBto);

            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "ownershipType")) {
                bo.setOwnershipType(bto.getOwnershipType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "institutionType")) {
                bo.setInstitutionType(bto.getInstitutionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "institutionLevel")) {
                bo.setInstitutionLevel(bto.getInstitutionLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"), "grade")) {
                bo.setGrade(bto.getGrade());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "affiliation")) {
                bo.setAffiliation(bto.getAffiliation());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "medicalRecordInstitutionCode")) {
                bo.setMedicalRecordInstitutionCode(bto.getMedicalRecordInstitutionCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "medicalInsuranceCode")) {
                bo.setMedicalInsuranceCode(bto.getMedicalInsuranceCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "createdAt")) {
                bo.setCreatedAt(bto.getCreatedAt());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedAt")) {
                bo.setUpdatedAt(bto.getUpdatedAt());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "createdBy")) {
                bo.setCreatedBy(bto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "deletedBy")) {
                bo.setDeletedBy(bto.getDeletedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "unitCode")) {
                bo.setUnitCode(bto.getUnitCode());
            }
        } else {
            InstitutionBO bo = organizationBO.getOrCreateInstitutionBO();
            if (bo != null) {
                Institution deletedItem = bo.convertToInstitution();
                boResult.getDeletedList().add(deletedItem);
                organizationBO.setInstitutionBO(null);
            }
        }
    }

    /** 更新组织（院区） */
    @AutoGenerated(locked = true)
    protected UpdateOrganizationCampusBoResult updateOrganizationCampusBase(
            UpdateOrganizationCampusBto updateOrganizationCampusBto) {
        if (updateOrganizationCampusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateOrganizationCampusBoResult boResult = new UpdateOrganizationCampusBoResult();
        OrganizationBO organizationBO =
                updateUpdateOrganizationCampusOnMissThrowEx(boResult, updateOrganizationCampusBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "campusBto")) {
                updateCampusBtoOnMissThrowEx(boResult, updateOrganizationCampusBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新组织（科室） */
    @AutoGenerated(locked = true)
    protected UpdateOrganizationDepartmentBoResult updateOrganizationDepartmentBase(
            UpdateOrganizationDepartmentBto updateOrganizationDepartmentBto) {
        if (updateOrganizationDepartmentBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateOrganizationDepartmentBoResult boResult = new UpdateOrganizationDepartmentBoResult();
        OrganizationBO organizationBO =
                updateUpdateOrganizationDepartmentOnMissThrowEx(
                        boResult, updateOrganizationDepartmentBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "departmentBto")) {
                updateDepartmentBtoOnMissThrowEx(
                        boResult, updateOrganizationDepartmentBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新组织（机构） */
    @AutoGenerated(locked = true)
    protected UpdateOrganizationInstitutionBoResult updateOrganizationInstitutionBase(
            UpdateOrganizationInstitutionBto updateOrganizationInstitutionBto) {
        if (updateOrganizationInstitutionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateOrganizationInstitutionBoResult boResult =
                new UpdateOrganizationInstitutionBoResult();
        OrganizationBO organizationBO =
                updateUpdateOrganizationInstitutionOnMissThrowEx(
                        boResult, updateOrganizationInstitutionBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "institutionBto")) {
                updateInstitutionBtoOnMissThrowEx(
                        boResult, updateOrganizationInstitutionBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新组织状态 */
    @AutoGenerated(locked = true)
    protected UpdateOrganizationStatusBoResult updateOrganizationStatusBase(
            UpdateOrganizationStatusBto updateOrganizationStatusBto) {
        if (updateOrganizationStatusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateOrganizationStatusBoResult boResult = new UpdateOrganizationStatusBoResult();
        OrganizationBO organizationBO =
                updateUpdateOrganizationStatusOnMissThrowEx(boResult, updateOrganizationStatusBto);
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新组织（病区） */
    @AutoGenerated(locked = true)
    protected UpdateOrganizationWardBoResult updateOrganizationWardBase(
            UpdateOrganizationWardBto updateOrganizationWardBto) {
        if (updateOrganizationWardBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateOrganizationWardBoResult boResult = new UpdateOrganizationWardBoResult();
        OrganizationBO organizationBO =
                updateUpdateOrganizationWardOnMissThrowEx(boResult, updateOrganizationWardBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "wardBto")) {
                updateWardBtoOnMissThrowEx(boResult, updateOrganizationWardBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新挂号科室信息 */
    @AutoGenerated(locked = true)
    protected UpdateRegisterDepartmentBoResult updateRegisterDepartmentBase(
            UpdateRegisterDepartmentBto updateRegisterDepartmentBto) {
        if (updateRegisterDepartmentBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateRegisterDepartmentBoResult boResult = new UpdateRegisterDepartmentBoResult();
        OrganizationBO organizationBO =
                updateUpdateRegisterDepartmentOnMissThrowEx(boResult, updateRegisterDepartmentBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDepartmentBto, "__$validPropertySet"),
                    "departmentBto")) {
                updateDepartmentBtoOnMissThrowEx(
                        boResult, updateRegisterDepartmentBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新挂号科室挂号启用停用标志 */
    @AutoGenerated(locked = true)
    protected UpdateRegisterDepartmentEnableFlagBoResult updateRegisterDepartmentEnableFlagBase(
            UpdateRegisterDepartmentEnableFlagBto updateRegisterDepartmentEnableFlagBto) {
        if (updateRegisterDepartmentEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateRegisterDepartmentEnableFlagBoResult boResult =
                new UpdateRegisterDepartmentEnableFlagBoResult();
        OrganizationBO organizationBO =
                updateUpdateRegisterDepartmentEnableFlagOnMissThrowEx(
                        boResult, updateRegisterDepartmentEnableFlagBto);
        if (organizationBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDepartmentEnableFlagBto, "__$validPropertySet"),
                    "departmentBto")) {
                updateDepartmentBtoOnMissThrowEx(
                        boResult, updateRegisterDepartmentEnableFlagBto, organizationBO);
            }
        }
        boResult.setRootBo(organizationBO);
        return boResult;
    }

    /** 更新对象:updateOrganizationCampus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO updateUpdateOrganizationCampusOnMissThrowEx(
            UpdateOrganizationCampusBoResult boResult,
            UpdateOrganizationCampusBto updateOrganizationCampusBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateOrganizationCampusBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(updateOrganizationCampusBto.getId());
            found = true;
        }
        allNull =
                (updateOrganizationCampusBto.getType() == null)
                        && (updateOrganizationCampusBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            updateOrganizationCampusBto.getType(),
                            updateOrganizationCampusBto.getName());
            if (organizationBO != null) {
                found = true;
            }
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(organizationBO.convertToOrganization());
            updatedBto.setBto(updateOrganizationCampusBto);
            updatedBto.setBo(organizationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(updateOrganizationCampusBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(updateOrganizationCampusBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(updateOrganizationCampusBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(updateOrganizationCampusBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(updateOrganizationCampusBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(updateOrganizationCampusBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(updateOrganizationCampusBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        updateOrganizationCampusBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(updateOrganizationCampusBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(updateOrganizationCampusBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(updateOrganizationCampusBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(updateOrganizationCampusBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(updateOrganizationCampusBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(updateOrganizationCampusBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "updatedBy")) {
                organizationBO.setUpdatedBy(updateOrganizationCampusBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationCampusBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(updateOrganizationCampusBto.getInvalidFlag());
            }
            return organizationBO;
        }
    }

    /** 更新对象:updateOrganizationDepartment,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO updateUpdateOrganizationDepartmentOnMissThrowEx(
            UpdateOrganizationDepartmentBoResult boResult,
            UpdateOrganizationDepartmentBto updateOrganizationDepartmentBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateOrganizationDepartmentBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(updateOrganizationDepartmentBto.getId());
            found = true;
        }
        allNull =
                (updateOrganizationDepartmentBto.getType() == null)
                        && (updateOrganizationDepartmentBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            updateOrganizationDepartmentBto.getType(),
                            updateOrganizationDepartmentBto.getName());
            if (organizationBO != null) {
                found = true;
            }
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(organizationBO.convertToOrganization());
            updatedBto.setBto(updateOrganizationDepartmentBto);
            updatedBto.setBo(organizationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(updateOrganizationDepartmentBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(updateOrganizationDepartmentBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(updateOrganizationDepartmentBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(updateOrganizationDepartmentBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(updateOrganizationDepartmentBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(updateOrganizationDepartmentBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(updateOrganizationDepartmentBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        updateOrganizationDepartmentBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(updateOrganizationDepartmentBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(updateOrganizationDepartmentBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(updateOrganizationDepartmentBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(updateOrganizationDepartmentBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(updateOrganizationDepartmentBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(updateOrganizationDepartmentBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "updatedBy")) {
                organizationBO.setUpdatedBy(updateOrganizationDepartmentBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationDepartmentBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(updateOrganizationDepartmentBto.getInvalidFlag());
            }
            return organizationBO;
        }
    }

    /** 更新对象:updateOrganizationInstitution,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO updateUpdateOrganizationInstitutionOnMissThrowEx(
            UpdateOrganizationInstitutionBoResult boResult,
            UpdateOrganizationInstitutionBto updateOrganizationInstitutionBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateOrganizationInstitutionBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(updateOrganizationInstitutionBto.getId());
            found = true;
        }
        allNull =
                (updateOrganizationInstitutionBto.getType() == null)
                        && (updateOrganizationInstitutionBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            updateOrganizationInstitutionBto.getType(),
                            updateOrganizationInstitutionBto.getName());
            if (organizationBO != null) {
                found = true;
            }
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(organizationBO.convertToOrganization());
            updatedBto.setBto(updateOrganizationInstitutionBto);
            updatedBto.setBo(organizationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(updateOrganizationInstitutionBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(updateOrganizationInstitutionBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(updateOrganizationInstitutionBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(updateOrganizationInstitutionBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(updateOrganizationInstitutionBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(updateOrganizationInstitutionBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(updateOrganizationInstitutionBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        updateOrganizationInstitutionBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(updateOrganizationInstitutionBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(
                        updateOrganizationInstitutionBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(
                        updateOrganizationInstitutionBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(updateOrganizationInstitutionBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(updateOrganizationInstitutionBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(updateOrganizationInstitutionBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "updatedBy")) {
                organizationBO.setUpdatedBy(updateOrganizationInstitutionBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationInstitutionBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(updateOrganizationInstitutionBto.getInvalidFlag());
            }
            return organizationBO;
        }
    }

    /** 更新对象:updateOrganizationStatus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO updateUpdateOrganizationStatusOnMissThrowEx(
            BaseOrganizationBOService.UpdateOrganizationStatusBoResult boResult,
            UpdateOrganizationStatusBto updateOrganizationStatusBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateOrganizationStatusBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(updateOrganizationStatusBto.getId());
            found = true;
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(organizationBO.convertToOrganization());
            updatedBto.setBto(updateOrganizationStatusBto);
            updatedBto.setBo(organizationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationStatusBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(updateOrganizationStatusBto.getStatus());
            }
            return organizationBO;
        }
    }

    /** 更新对象:updateOrganizationWard,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO updateUpdateOrganizationWardOnMissThrowEx(
            UpdateOrganizationWardBoResult boResult,
            UpdateOrganizationWardBto updateOrganizationWardBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateOrganizationWardBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(updateOrganizationWardBto.getId());
            found = true;
        }
        allNull =
                (updateOrganizationWardBto.getType() == null)
                        && (updateOrganizationWardBto.getName() == null);
        if (!allNull && !found) {
            organizationBO =
                    OrganizationBO.getByTypeAndName(
                            updateOrganizationWardBto.getType(),
                            updateOrganizationWardBto.getName());
            if (organizationBO != null) {
                found = true;
            }
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(organizationBO.convertToOrganization());
            updatedBto.setBto(updateOrganizationWardBto);
            updatedBto.setBo(organizationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "type")) {
                organizationBO.setType(updateOrganizationWardBto.getType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "name")) {
                organizationBO.setName(updateOrganizationWardBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "inputCode")) {
                organizationBO.setInputCode(updateOrganizationWardBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "abbreviation")) {
                organizationBO.setAbbreviation(updateOrganizationWardBto.getAbbreviation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "alias")) {
                organizationBO.setAlias(updateOrganizationWardBto.getAlias());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "englishName")) {
                organizationBO.setEnglishName(updateOrganizationWardBto.getEnglishName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "parentId")) {
                organizationBO.setParentId(updateOrganizationWardBto.getParentId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "organizationLevel")) {
                organizationBO.setOrganizationLevel(
                        updateOrganizationWardBto.getOrganizationLevel());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(updateOrganizationWardBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "contactPerson")) {
                organizationBO.setContactPerson(updateOrganizationWardBto.getContactPerson());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "contactNumber")) {
                organizationBO.setContactNumber(updateOrganizationWardBto.getContactNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "address")) {
                organizationBO.setAddress(updateOrganizationWardBto.getAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "status")) {
                organizationBO.setStatus(updateOrganizationWardBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(updateOrganizationWardBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "updatedBy")) {
                organizationBO.setUpdatedBy(updateOrganizationWardBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateOrganizationWardBto, "__$validPropertySet"),
                    "invalidFlag")) {
                organizationBO.setInvalidFlag(updateOrganizationWardBto.getInvalidFlag());
            }
            return organizationBO;
        }
    }

    /** 更新对象:updateRegisterDepartmentEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO updateUpdateRegisterDepartmentEnableFlagOnMissThrowEx(
            UpdateRegisterDepartmentEnableFlagBoResult boResult,
            UpdateRegisterDepartmentEnableFlagBto updateRegisterDepartmentEnableFlagBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateRegisterDepartmentEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(updateRegisterDepartmentEnableFlagBto.getId());
            found = true;
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(organizationBO.convertToOrganization());
            updatedBto.setBto(updateRegisterDepartmentEnableFlagBto);
            updatedBto.setBo(organizationBO);
            boResult.getUpdatedList().add(updatedBto);
            return organizationBO;
        }
    }

    /** 更新对象:updateRegisterDepartment,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private OrganizationBO updateUpdateRegisterDepartmentOnMissThrowEx(
            UpdateRegisterDepartmentBoResult boResult,
            UpdateRegisterDepartmentBto updateRegisterDepartmentBto) {
        OrganizationBO organizationBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateRegisterDepartmentBto.getId() == null);
        if (!allNull && !found) {
            organizationBO = OrganizationBO.getById(updateRegisterDepartmentBto.getId());
            found = true;
        }
        if (organizationBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(organizationBO.convertToOrganization());
            updatedBto.setBto(updateRegisterDepartmentBto);
            updatedBto.setBo(organizationBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDepartmentBto, "__$validPropertySet"),
                    "description")) {
                organizationBO.setDescription(updateRegisterDepartmentBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDepartmentBto, "__$validPropertySet"),
                    "sortNumber")) {
                organizationBO.setSortNumber(updateRegisterDepartmentBto.getSortNumber());
            }
            return organizationBO;
        }
    }

    /** 更新对象:wardBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateWardBtoOnMissThrowEx(
            BaseOrganizationBOService.UpdateOrganizationWardBoResult boResult,
            UpdateOrganizationWardBto updateOrganizationWardBto,
            OrganizationBO organizationBO) {
        if (organizationBO.getWardBO() == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        }
        if (updateOrganizationWardBto.getWardBto() != null) {
            WardBO bo = organizationBO.getOrCreateWardBO();
            UpdateOrganizationWardBto.WardBto bto = updateOrganizationWardBto.getWardBto();
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setEntity(bo.convertToWard());
            updatedBto.setBo(bo);
            boResult.getUpdatedList().add(updatedBto);

            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                bo.setCampusOrganizationId(bto.getCampusOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "wardType")) {
                bo.setWardType(bto.getWardType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "virtualWardFlag")) {
                bo.setVirtualWardFlag(bto.getVirtualWardFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "maxBedCount")) {
                bo.setMaxBedCount(bto.getMaxBedCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "injectionStartTime")) {
                bo.setInjectionStartTime(bto.getInjectionStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "treatmentStartTime")) {
                bo.setTreatmentStartTime(bto.getTreatmentStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "oralMedicationStartTime")) {
                bo.setOralMedicationStartTime(bto.getOralMedicationStartTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "shellPosition")) {
                bo.setShellPosition(bto.getShellPosition());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
        } else {
            WardBO bo = organizationBO.getOrCreateWardBO();
            if (bo != null) {
                Ward deletedItem = bo.convertToWard();
                boResult.getDeletedList().add(deletedItem);
                organizationBO.setWardBO(null);
            }
        }
    }

    public static class CreateOrganizationInstitutionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationInstitutionBto, OrganizationBO> getCreatedBto(
                CreateOrganizationInstitutionBto createOrganizationInstitutionBto) {
            return this.getAddedResult(createOrganizationInstitutionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationInstitutionBto.InstitutionBto, InstitutionBO>
                getCreatedBto(CreateOrganizationInstitutionBto.InstitutionBto institutionBto) {
            return this.getAddedResult(institutionBto);
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public Institution getDeleted_Institution() {
            return (Institution)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Institution.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOrganizationInstitutionBto, Organization, OrganizationBO>
                getUpdatedBto(CreateOrganizationInstitutionBto createOrganizationInstitutionBto) {
            return super.getUpdatedResult(createOrganizationInstitutionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateOrganizationInstitutionBto.InstitutionBto, Institution, InstitutionBO>
                getUpdatedBto(CreateOrganizationInstitutionBto.InstitutionBto institutionBto) {
            return super.getUpdatedResult(institutionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationInstitutionBto, OrganizationBO> getUnmodifiedBto(
                CreateOrganizationInstitutionBto createOrganizationInstitutionBto) {
            return super.getUnmodifiedResult(createOrganizationInstitutionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationInstitutionBto.InstitutionBto, InstitutionBO>
                getUnmodifiedBto(CreateOrganizationInstitutionBto.InstitutionBto institutionBto) {
            return super.getUnmodifiedResult(institutionBto);
        }
    }

    public static class CreateOrganizationCampusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationCampusBto.CampusBto, CampusBO> getCreatedBto(
                CreateOrganizationCampusBto.CampusBto campusBto) {
            return this.getAddedResult(campusBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationCampusBto, OrganizationBO> getCreatedBto(
                CreateOrganizationCampusBto createOrganizationCampusBto) {
            return this.getAddedResult(createOrganizationCampusBto);
        }

        @AutoGenerated(locked = true)
        public Campus getDeleted_Campus() {
            return (Campus) CollectionUtil.getFirst(this.getDeletedEntityList(Campus.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOrganizationCampusBto.CampusBto, Campus, CampusBO> getUpdatedBto(
                CreateOrganizationCampusBto.CampusBto campusBto) {
            return super.getUpdatedResult(campusBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOrganizationCampusBto, Organization, OrganizationBO> getUpdatedBto(
                CreateOrganizationCampusBto createOrganizationCampusBto) {
            return super.getUpdatedResult(createOrganizationCampusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationCampusBto.CampusBto, CampusBO> getUnmodifiedBto(
                CreateOrganizationCampusBto.CampusBto campusBto) {
            return super.getUnmodifiedResult(campusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationCampusBto, OrganizationBO> getUnmodifiedBto(
                CreateOrganizationCampusBto createOrganizationCampusBto) {
            return super.getUnmodifiedResult(createOrganizationCampusBto);
        }
    }

    public static class CreateOrganizationDepartmentBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationDepartmentBto.DepartmentBto, DepartmentBO> getCreatedBto(
                CreateOrganizationDepartmentBto.DepartmentBto departmentBto) {
            return this.getAddedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationDepartmentBto, OrganizationBO> getCreatedBto(
                CreateOrganizationDepartmentBto createOrganizationDepartmentBto) {
            return this.getAddedResult(createOrganizationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public Department getDeleted_Department() {
            return (Department)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Department.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOrganizationDepartmentBto.DepartmentBto, Department, DepartmentBO>
                getUpdatedBto(CreateOrganizationDepartmentBto.DepartmentBto departmentBto) {
            return super.getUpdatedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOrganizationDepartmentBto, Organization, OrganizationBO>
                getUpdatedBto(CreateOrganizationDepartmentBto createOrganizationDepartmentBto) {
            return super.getUpdatedResult(createOrganizationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationDepartmentBto.DepartmentBto, DepartmentBO>
                getUnmodifiedBto(CreateOrganizationDepartmentBto.DepartmentBto departmentBto) {
            return super.getUnmodifiedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationDepartmentBto, OrganizationBO> getUnmodifiedBto(
                CreateOrganizationDepartmentBto createOrganizationDepartmentBto) {
            return super.getUnmodifiedResult(createOrganizationDepartmentBto);
        }
    }

    public static class CreateOrganizationWardBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationWardBto.WardBto, WardBO> getCreatedBto(
                CreateOrganizationWardBto.WardBto wardBto) {
            return this.getAddedResult(wardBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateOrganizationWardBto, OrganizationBO> getCreatedBto(
                CreateOrganizationWardBto createOrganizationWardBto) {
            return this.getAddedResult(createOrganizationWardBto);
        }

        @AutoGenerated(locked = true)
        public Ward getDeleted_Ward() {
            return (Ward) CollectionUtil.getFirst(this.getDeletedEntityList(Ward.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOrganizationWardBto.WardBto, Ward, WardBO> getUpdatedBto(
                CreateOrganizationWardBto.WardBto wardBto) {
            return super.getUpdatedResult(wardBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateOrganizationWardBto, Organization, OrganizationBO> getUpdatedBto(
                CreateOrganizationWardBto createOrganizationWardBto) {
            return super.getUpdatedResult(createOrganizationWardBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationWardBto.WardBto, WardBO> getUnmodifiedBto(
                CreateOrganizationWardBto.WardBto wardBto) {
            return super.getUnmodifiedResult(wardBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateOrganizationWardBto, OrganizationBO> getUnmodifiedBto(
                CreateOrganizationWardBto createOrganizationWardBto) {
            return super.getUnmodifiedResult(createOrganizationWardBto);
        }
    }

    public static class UpdateOrganizationInstitutionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationInstitutionBto, OrganizationBO> getCreatedBto(
                UpdateOrganizationInstitutionBto updateOrganizationInstitutionBto) {
            return this.getAddedResult(updateOrganizationInstitutionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationInstitutionBto.InstitutionBto, InstitutionBO>
                getCreatedBto(UpdateOrganizationInstitutionBto.InstitutionBto institutionBto) {
            return this.getAddedResult(institutionBto);
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public Institution getDeleted_Institution() {
            return (Institution)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Institution.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationInstitutionBto, Organization, OrganizationBO>
                getUpdatedBto(UpdateOrganizationInstitutionBto updateOrganizationInstitutionBto) {
            return super.getUpdatedResult(updateOrganizationInstitutionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateOrganizationInstitutionBto.InstitutionBto, Institution, InstitutionBO>
                getUpdatedBto(UpdateOrganizationInstitutionBto.InstitutionBto institutionBto) {
            return super.getUpdatedResult(institutionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationInstitutionBto, OrganizationBO> getUnmodifiedBto(
                UpdateOrganizationInstitutionBto updateOrganizationInstitutionBto) {
            return super.getUnmodifiedResult(updateOrganizationInstitutionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationInstitutionBto.InstitutionBto, InstitutionBO>
                getUnmodifiedBto(UpdateOrganizationInstitutionBto.InstitutionBto institutionBto) {
            return super.getUnmodifiedResult(institutionBto);
        }
    }

    public static class UpdateOrganizationCampusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationCampusBto.CampusBto, CampusBO> getCreatedBto(
                UpdateOrganizationCampusBto.CampusBto campusBto) {
            return this.getAddedResult(campusBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationCampusBto, OrganizationBO> getCreatedBto(
                UpdateOrganizationCampusBto updateOrganizationCampusBto) {
            return this.getAddedResult(updateOrganizationCampusBto);
        }

        @AutoGenerated(locked = true)
        public Campus getDeleted_Campus() {
            return (Campus) CollectionUtil.getFirst(this.getDeletedEntityList(Campus.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationCampusBto.CampusBto, Campus, CampusBO> getUpdatedBto(
                UpdateOrganizationCampusBto.CampusBto campusBto) {
            return super.getUpdatedResult(campusBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationCampusBto, Organization, OrganizationBO> getUpdatedBto(
                UpdateOrganizationCampusBto updateOrganizationCampusBto) {
            return super.getUpdatedResult(updateOrganizationCampusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationCampusBto.CampusBto, CampusBO> getUnmodifiedBto(
                UpdateOrganizationCampusBto.CampusBto campusBto) {
            return super.getUnmodifiedResult(campusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationCampusBto, OrganizationBO> getUnmodifiedBto(
                UpdateOrganizationCampusBto updateOrganizationCampusBto) {
            return super.getUnmodifiedResult(updateOrganizationCampusBto);
        }
    }

    public static class UpdateOrganizationDepartmentBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationDepartmentBto.DepartmentBto, DepartmentBO> getCreatedBto(
                UpdateOrganizationDepartmentBto.DepartmentBto departmentBto) {
            return this.getAddedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationDepartmentBto, OrganizationBO> getCreatedBto(
                UpdateOrganizationDepartmentBto updateOrganizationDepartmentBto) {
            return this.getAddedResult(updateOrganizationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public Department getDeleted_Department() {
            return (Department)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Department.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationDepartmentBto.DepartmentBto, Department, DepartmentBO>
                getUpdatedBto(UpdateOrganizationDepartmentBto.DepartmentBto departmentBto) {
            return super.getUpdatedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationDepartmentBto, Organization, OrganizationBO>
                getUpdatedBto(UpdateOrganizationDepartmentBto updateOrganizationDepartmentBto) {
            return super.getUpdatedResult(updateOrganizationDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationDepartmentBto.DepartmentBto, DepartmentBO>
                getUnmodifiedBto(UpdateOrganizationDepartmentBto.DepartmentBto departmentBto) {
            return super.getUnmodifiedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationDepartmentBto, OrganizationBO> getUnmodifiedBto(
                UpdateOrganizationDepartmentBto updateOrganizationDepartmentBto) {
            return super.getUnmodifiedResult(updateOrganizationDepartmentBto);
        }
    }

    public static class UpdateOrganizationWardBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationWardBto.WardBto, WardBO> getCreatedBto(
                UpdateOrganizationWardBto.WardBto wardBto) {
            return this.getAddedResult(wardBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationWardBto, OrganizationBO> getCreatedBto(
                UpdateOrganizationWardBto updateOrganizationWardBto) {
            return this.getAddedResult(updateOrganizationWardBto);
        }

        @AutoGenerated(locked = true)
        public Ward getDeleted_Ward() {
            return (Ward) CollectionUtil.getFirst(this.getDeletedEntityList(Ward.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationWardBto.WardBto, Ward, WardBO> getUpdatedBto(
                UpdateOrganizationWardBto.WardBto wardBto) {
            return super.getUpdatedResult(wardBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationWardBto, Organization, OrganizationBO> getUpdatedBto(
                UpdateOrganizationWardBto updateOrganizationWardBto) {
            return super.getUpdatedResult(updateOrganizationWardBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationWardBto.WardBto, WardBO> getUnmodifiedBto(
                UpdateOrganizationWardBto.WardBto wardBto) {
            return super.getUnmodifiedResult(wardBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationWardBto, OrganizationBO> getUnmodifiedBto(
                UpdateOrganizationWardBto updateOrganizationWardBto) {
            return super.getUnmodifiedResult(updateOrganizationWardBto);
        }
    }

    public static class UpdateOrganizationStatusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateOrganizationStatusBto, OrganizationBO> getCreatedBto(
                UpdateOrganizationStatusBto updateOrganizationStatusBto) {
            return this.getAddedResult(updateOrganizationStatusBto);
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateOrganizationStatusBto, Organization, OrganizationBO> getUpdatedBto(
                UpdateOrganizationStatusBto updateOrganizationStatusBto) {
            return super.getUpdatedResult(updateOrganizationStatusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateOrganizationStatusBto, OrganizationBO> getUnmodifiedBto(
                UpdateOrganizationStatusBto updateOrganizationStatusBto) {
            return super.getUnmodifiedResult(updateOrganizationStatusBto);
        }
    }

    public static class DeleteOrganizationBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteOrganizationBto, OrganizationBO> getCreatedBto(
                DeleteOrganizationBto deleteOrganizationBto) {
            return this.getAddedResult(deleteOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteOrganizationBto, Organization, OrganizationBO> getUpdatedBto(
                DeleteOrganizationBto deleteOrganizationBto) {
            return super.getUpdatedResult(deleteOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteOrganizationBto, OrganizationBO> getUnmodifiedBto(
                DeleteOrganizationBto deleteOrganizationBto) {
            return super.getUnmodifiedResult(deleteOrganizationBto);
        }
    }

    public static class UpdateRegisterDepartmentBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateRegisterDepartmentBto.DepartmentBto, DepartmentBO> getCreatedBto(
                UpdateRegisterDepartmentBto.DepartmentBto departmentBto) {
            return this.getAddedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateRegisterDepartmentBto, OrganizationBO> getCreatedBto(
                UpdateRegisterDepartmentBto updateRegisterDepartmentBto) {
            return this.getAddedResult(updateRegisterDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public Department getDeleted_Department() {
            return (Department)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Department.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateRegisterDepartmentBto.DepartmentBto, Department, DepartmentBO>
                getUpdatedBto(UpdateRegisterDepartmentBto.DepartmentBto departmentBto) {
            return super.getUpdatedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateRegisterDepartmentBto, Organization, OrganizationBO> getUpdatedBto(
                UpdateRegisterDepartmentBto updateRegisterDepartmentBto) {
            return super.getUpdatedResult(updateRegisterDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateRegisterDepartmentBto.DepartmentBto, DepartmentBO>
                getUnmodifiedBto(UpdateRegisterDepartmentBto.DepartmentBto departmentBto) {
            return super.getUnmodifiedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateRegisterDepartmentBto, OrganizationBO> getUnmodifiedBto(
                UpdateRegisterDepartmentBto updateRegisterDepartmentBto) {
            return super.getUnmodifiedResult(updateRegisterDepartmentBto);
        }
    }

    public static class UpdateRegisterDepartmentEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public OrganizationBO getRootBo() {
            return (OrganizationBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateRegisterDepartmentEnableFlagBto.DepartmentBto, DepartmentBO>
                getCreatedBto(UpdateRegisterDepartmentEnableFlagBto.DepartmentBto departmentBto) {
            return this.getAddedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateRegisterDepartmentEnableFlagBto, OrganizationBO> getCreatedBto(
                UpdateRegisterDepartmentEnableFlagBto updateRegisterDepartmentEnableFlagBto) {
            return this.getAddedResult(updateRegisterDepartmentEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public Department getDeleted_Department() {
            return (Department)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Department.class));
        }

        @AutoGenerated(locked = true)
        public Organization getDeleted_Organization() {
            return (Organization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(Organization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateRegisterDepartmentEnableFlagBto.DepartmentBto,
                        Department,
                        DepartmentBO>
                getUpdatedBto(UpdateRegisterDepartmentEnableFlagBto.DepartmentBto departmentBto) {
            return super.getUpdatedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateRegisterDepartmentEnableFlagBto, Organization, OrganizationBO>
                getUpdatedBto(
                        UpdateRegisterDepartmentEnableFlagBto
                                updateRegisterDepartmentEnableFlagBto) {
            return super.getUpdatedResult(updateRegisterDepartmentEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateRegisterDepartmentEnableFlagBto.DepartmentBto, DepartmentBO>
                getUnmodifiedBto(
                        UpdateRegisterDepartmentEnableFlagBto.DepartmentBto departmentBto) {
            return super.getUnmodifiedResult(departmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateRegisterDepartmentEnableFlagBto, OrganizationBO>
                getUnmodifiedBto(
                        UpdateRegisterDepartmentEnableFlagBto
                                updateRegisterDepartmentEnableFlagBto) {
            return super.getUnmodifiedResult(updateRegisterDepartmentEnableFlagBto);
        }
    }
}
