package com.pulse.organization.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.organization.manager.bo.*;
import com.pulse.organization.manager.bo.StaffBO;
import com.pulse.organization.persist.dos.Staff;
import com.pulse.organization.persist.dos.StaffConsultingRoom;
import com.pulse.organization.persist.dos.StaffEducation;
import com.pulse.organization.persist.dos.StaffEmployment;
import com.pulse.organization.persist.dos.StaffExtension;
import com.pulse.organization.persist.dos.StaffOrganization;
import com.pulse.organization.persist.dos.StaffPractice;
import com.pulse.organization.persist.dos.StaffUser;
import com.pulse.organization.persist.dos.StaffWorkExperience;
import com.pulse.organization.service.base.BaseStaffBOService.CreateStaffDetailBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.CreateStaffUserBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.DeleteStaffDetailBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.DeleteStaffUserBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.MergeStaffConsultingRoomBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.MergeStaffDetailBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.UpdateRegisterDoctorBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.UpdateRegisterDoctorEnableFlagBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.UpdateStaffConsultingRoomEnableFlagBoResult;
import com.pulse.organization.service.base.BaseStaffBOService.UpdateStaffStatusBoResult;
import com.pulse.organization.service.bto.CreateStaffDetailBto;
import com.pulse.organization.service.bto.CreateStaffUserBto;
import com.pulse.organization.service.bto.DeleteStaffDetailBto;
import com.pulse.organization.service.bto.DeleteStaffUserBto;
import com.pulse.organization.service.bto.MergeStaffConsultingRoomBto;
import com.pulse.organization.service.bto.MergeStaffDetailBto;
import com.pulse.organization.service.bto.UpdateRegisterDoctorBto;
import com.pulse.organization.service.bto.UpdateRegisterDoctorEnableFlagBto;
import com.pulse.organization.service.bto.UpdateStaffConsultingRoomEnableFlagBto;
import com.pulse.organization.service.bto.UpdateStaffStatusBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "e2efff34-9f23-315a-a756-51a6a08e712e")
public class BaseStaffBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private StaffBO createCreateStaffDetail(
            CreateStaffDetailBoResult boResult, CreateStaffDetailBto createStaffDetailBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createStaffDetailBto.getStaffNumber() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getByStaffNumber(createStaffDetailBto.getStaffNumber());
            if (staffBO != null) {
                matchedUkName += "(";
                matchedUkName += "'staff_number'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (staffBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", staffBO.getId(), "staff");
                throw new IgnoredException(400, "员工已存在");
            } else {
                log.error("唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突", matchedUkName, "staff", staffBO.getId());
                throw new IgnoredException(400, "员工已存在");
            }
        } else {
            staffBO = new StaffBO();
            if (pkExist) {
                staffBO.setId(String.valueOf(this.idGenerator.allocateId("staff")));
            } else {
                staffBO.setId(String.valueOf(this.idGenerator.allocateId("staff")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffNumber")) {
                staffBO.setStaffNumber(createStaffDetailBto.getStaffNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "name")) {
                staffBO.setName(createStaffDetailBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "inputCode")) {
                staffBO.setInputCode(createStaffDetailBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "gender")) {
                staffBO.setGender(createStaffDetailBto.getGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "certificateTypeId")) {
                staffBO.setCertificateTypeId(createStaffDetailBto.getCertificateTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "certificateNumber")) {
                staffBO.setCertificateNumber(createStaffDetailBto.getCertificateNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "birthDate")) {
                staffBO.setBirthDate(createStaffDetailBto.getBirthDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffTypeId")) {
                staffBO.setStaffTypeId(createStaffDetailBto.getStaffTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "professionalTitleId")) {
                staffBO.setProfessionalTitleId(createStaffDetailBto.getProfessionalTitleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "phoneNumber")) {
                staffBO.setPhoneNumber(createStaffDetailBto.getPhoneNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "shortPhoneNumber")) {
                staffBO.setShortPhoneNumber(createStaffDetailBto.getShortPhoneNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "emailAddress")) {
                staffBO.setEmailAddress(createStaffDetailBto.getEmailAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "expertFlag")) {
                staffBO.setExpertFlag(createStaffDetailBto.getExpertFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "description")) {
                staffBO.setDescription(createStaffDetailBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "status")) {
                staffBO.setStatus(createStaffDetailBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "createdBy")) {
                staffBO.setCreatedBy(createStaffDetailBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "updateBy")) {
                staffBO.setUpdateBy(createStaffDetailBto.getUpdateBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "deletedBy")) {
                staffBO.setDeletedBy(createStaffDetailBto.getDeletedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "sortNumber")) {
                staffBO.setSortNumber(createStaffDetailBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "organizationId")) {
                staffBO.setOrganizationId(createStaffDetailBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "accountingOrganizationId")) {
                staffBO.setAccountingOrganizationId(
                        createStaffDetailBto.getAccountingOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "hrOrganizationId")) {
                staffBO.setHrOrganizationId(createStaffDetailBto.getHrOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "positionId")) {
                staffBO.setPositionId(createStaffDetailBto.getPositionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "promotionDate")) {
                staffBO.setPromotionDate(createStaffDetailBto.getPromotionDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "registerTypeList")) {
                staffBO.setRegisterTypeList(createStaffDetailBto.getRegisterTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                staffBO.setProvincePlatformCancelFlag(
                        createStaffDetailBto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "countrysideStartDate")) {
                staffBO.setCountrysideStartDate(createStaffDetailBto.getCountrysideStartDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "doctorRemark")) {
                staffBO.setDoctorRemark(createStaffDetailBto.getDoctorRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "registerDoctorFlag")) {
                staffBO.setRegisterDoctorFlag(createStaffDetailBto.getRegisterDoctorFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "countrysideEndDate")) {
                staffBO.setCountrysideEndDate(createStaffDetailBto.getCountrysideEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "registerDoctorEnableFlag")) {
                staffBO.setRegisterDoctorEnableFlag(
                        createStaffDetailBto.getRegisterDoctorEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(createStaffDetailBto.getCampusOrganizationId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createStaffDetailBto);
            addedBto.setBo(staffBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return staffBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private StaffBO createMergeStaffConsultingRoomOnDuplicateUpdate(
            MergeStaffConsultingRoomBoResult boResult,
            MergeStaffConsultingRoomBto mergeStaffConsultingRoomBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeStaffConsultingRoomBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(mergeStaffConsultingRoomBto.getId());
            if (staffBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (staffBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(staffBO.convertToStaff());
                updatedBto.setBto(mergeStaffConsultingRoomBto);
                updatedBto.setBo(staffBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffConsultingRoomBto, "__$validPropertySet"),
                        "campusOrganizationId")) {
                    staffBO.setCampusOrganizationId(
                            mergeStaffConsultingRoomBto.getCampusOrganizationId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(staffBO.convertToStaff());
                updatedBto.setBto(mergeStaffConsultingRoomBto);
                updatedBto.setBo(staffBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffConsultingRoomBto, "__$validPropertySet"),
                        "campusOrganizationId")) {
                    staffBO.setCampusOrganizationId(
                            mergeStaffConsultingRoomBto.getCampusOrganizationId());
                }
            }
        } else {
            staffBO = new StaffBO();
            if (pkExist) {
                staffBO.setId(mergeStaffConsultingRoomBto.getId());
            } else {
                staffBO.setId(String.valueOf(this.idGenerator.allocateId("staff")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeStaffConsultingRoomBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(
                        mergeStaffConsultingRoomBto.getCampusOrganizationId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeStaffConsultingRoomBto);
            addedBto.setBo(staffBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return staffBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private StaffBO createMergeStaffDetailOnDuplicateUpdate(
            MergeStaffDetailBoResult boResult, MergeStaffDetailBto mergeStaffDetailBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeStaffDetailBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(mergeStaffDetailBto.getId());
            if (staffBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull = (mergeStaffDetailBto.getStaffNumber() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getByStaffNumber(mergeStaffDetailBto.getStaffNumber());
            if (staffBO != null) {
                matchedUkName += "(";
                matchedUkName += "'staff_number'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (staffBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(staffBO.convertToStaff());
                updatedBto.setBto(mergeStaffDetailBto);
                updatedBto.setBo(staffBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "staffNumber")) {
                    staffBO.setStaffNumber(mergeStaffDetailBto.getStaffNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "name")) {
                    staffBO.setName(mergeStaffDetailBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "inputCode")) {
                    staffBO.setInputCode(mergeStaffDetailBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "gender")) {
                    staffBO.setGender(mergeStaffDetailBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "certificateTypeId")) {
                    staffBO.setCertificateTypeId(mergeStaffDetailBto.getCertificateTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "certificateNumber")) {
                    staffBO.setCertificateNumber(mergeStaffDetailBto.getCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "birthDate")) {
                    staffBO.setBirthDate(mergeStaffDetailBto.getBirthDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "staffTypeId")) {
                    staffBO.setStaffTypeId(mergeStaffDetailBto.getStaffTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "professionalTitleId")) {
                    staffBO.setProfessionalTitleId(mergeStaffDetailBto.getProfessionalTitleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "phoneNumber")) {
                    staffBO.setPhoneNumber(mergeStaffDetailBto.getPhoneNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "shortPhoneNumber")) {
                    staffBO.setShortPhoneNumber(mergeStaffDetailBto.getShortPhoneNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "emailAddress")) {
                    staffBO.setEmailAddress(mergeStaffDetailBto.getEmailAddress());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "expertFlag")) {
                    staffBO.setExpertFlag(mergeStaffDetailBto.getExpertFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "description")) {
                    staffBO.setDescription(mergeStaffDetailBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "status")) {
                    staffBO.setStatus(mergeStaffDetailBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "createdBy")) {
                    staffBO.setCreatedBy(mergeStaffDetailBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "updateBy")) {
                    staffBO.setUpdateBy(mergeStaffDetailBto.getUpdateBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "deletedBy")) {
                    staffBO.setDeletedBy(mergeStaffDetailBto.getDeletedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "sortNumber")) {
                    staffBO.setSortNumber(mergeStaffDetailBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "organizationId")) {
                    staffBO.setOrganizationId(mergeStaffDetailBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "accountingOrganizationId")) {
                    staffBO.setAccountingOrganizationId(
                            mergeStaffDetailBto.getAccountingOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "hrOrganizationId")) {
                    staffBO.setHrOrganizationId(mergeStaffDetailBto.getHrOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "positionId")) {
                    staffBO.setPositionId(mergeStaffDetailBto.getPositionId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "promotionDate")) {
                    staffBO.setPromotionDate(mergeStaffDetailBto.getPromotionDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "registerTypeList")) {
                    staffBO.setRegisterTypeList(mergeStaffDetailBto.getRegisterTypeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "provincePlatformCancelFlag")) {
                    staffBO.setProvincePlatformCancelFlag(
                            mergeStaffDetailBto.getProvincePlatformCancelFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "countrysideStartDate")) {
                    staffBO.setCountrysideStartDate(mergeStaffDetailBto.getCountrysideStartDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "doctorRemark")) {
                    staffBO.setDoctorRemark(mergeStaffDetailBto.getDoctorRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "registerDoctorFlag")) {
                    staffBO.setRegisterDoctorFlag(mergeStaffDetailBto.getRegisterDoctorFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "countrysideEndDate")) {
                    staffBO.setCountrysideEndDate(mergeStaffDetailBto.getCountrysideEndDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "registerDoctorEnableFlag")) {
                    staffBO.setRegisterDoctorEnableFlag(
                            mergeStaffDetailBto.getRegisterDoctorEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "campusOrganizationId")) {
                    staffBO.setCampusOrganizationId(mergeStaffDetailBto.getCampusOrganizationId());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(staffBO.convertToStaff());
                updatedBto.setBto(mergeStaffDetailBto);
                updatedBto.setBo(staffBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "staffNumber")) {
                    staffBO.setStaffNumber(mergeStaffDetailBto.getStaffNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "name")) {
                    staffBO.setName(mergeStaffDetailBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "inputCode")) {
                    staffBO.setInputCode(mergeStaffDetailBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "gender")) {
                    staffBO.setGender(mergeStaffDetailBto.getGender());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "certificateTypeId")) {
                    staffBO.setCertificateTypeId(mergeStaffDetailBto.getCertificateTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "certificateNumber")) {
                    staffBO.setCertificateNumber(mergeStaffDetailBto.getCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "birthDate")) {
                    staffBO.setBirthDate(mergeStaffDetailBto.getBirthDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "staffTypeId")) {
                    staffBO.setStaffTypeId(mergeStaffDetailBto.getStaffTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "professionalTitleId")) {
                    staffBO.setProfessionalTitleId(mergeStaffDetailBto.getProfessionalTitleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "phoneNumber")) {
                    staffBO.setPhoneNumber(mergeStaffDetailBto.getPhoneNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "shortPhoneNumber")) {
                    staffBO.setShortPhoneNumber(mergeStaffDetailBto.getShortPhoneNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "emailAddress")) {
                    staffBO.setEmailAddress(mergeStaffDetailBto.getEmailAddress());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "expertFlag")) {
                    staffBO.setExpertFlag(mergeStaffDetailBto.getExpertFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "description")) {
                    staffBO.setDescription(mergeStaffDetailBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "status")) {
                    staffBO.setStatus(mergeStaffDetailBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "createdBy")) {
                    staffBO.setCreatedBy(mergeStaffDetailBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "updateBy")) {
                    staffBO.setUpdateBy(mergeStaffDetailBto.getUpdateBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "deletedBy")) {
                    staffBO.setDeletedBy(mergeStaffDetailBto.getDeletedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "sortNumber")) {
                    staffBO.setSortNumber(mergeStaffDetailBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "organizationId")) {
                    staffBO.setOrganizationId(mergeStaffDetailBto.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "accountingOrganizationId")) {
                    staffBO.setAccountingOrganizationId(
                            mergeStaffDetailBto.getAccountingOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "hrOrganizationId")) {
                    staffBO.setHrOrganizationId(mergeStaffDetailBto.getHrOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "positionId")) {
                    staffBO.setPositionId(mergeStaffDetailBto.getPositionId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "promotionDate")) {
                    staffBO.setPromotionDate(mergeStaffDetailBto.getPromotionDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "registerTypeList")) {
                    staffBO.setRegisterTypeList(mergeStaffDetailBto.getRegisterTypeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "provincePlatformCancelFlag")) {
                    staffBO.setProvincePlatformCancelFlag(
                            mergeStaffDetailBto.getProvincePlatformCancelFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "countrysideStartDate")) {
                    staffBO.setCountrysideStartDate(mergeStaffDetailBto.getCountrysideStartDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "doctorRemark")) {
                    staffBO.setDoctorRemark(mergeStaffDetailBto.getDoctorRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "registerDoctorFlag")) {
                    staffBO.setRegisterDoctorFlag(mergeStaffDetailBto.getRegisterDoctorFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "countrysideEndDate")) {
                    staffBO.setCountrysideEndDate(mergeStaffDetailBto.getCountrysideEndDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "registerDoctorEnableFlag")) {
                    staffBO.setRegisterDoctorEnableFlag(
                            mergeStaffDetailBto.getRegisterDoctorEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStaffDetailBto, "__$validPropertySet"),
                        "campusOrganizationId")) {
                    staffBO.setCampusOrganizationId(mergeStaffDetailBto.getCampusOrganizationId());
                }
            }
        } else {
            staffBO = new StaffBO();
            if (pkExist) {
                staffBO.setId(mergeStaffDetailBto.getId());
            } else {
                staffBO.setId(String.valueOf(this.idGenerator.allocateId("staff")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffNumber")) {
                staffBO.setStaffNumber(mergeStaffDetailBto.getStaffNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "name")) {
                staffBO.setName(mergeStaffDetailBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "inputCode")) {
                staffBO.setInputCode(mergeStaffDetailBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "gender")) {
                staffBO.setGender(mergeStaffDetailBto.getGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "certificateTypeId")) {
                staffBO.setCertificateTypeId(mergeStaffDetailBto.getCertificateTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "certificateNumber")) {
                staffBO.setCertificateNumber(mergeStaffDetailBto.getCertificateNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "birthDate")) {
                staffBO.setBirthDate(mergeStaffDetailBto.getBirthDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffTypeId")) {
                staffBO.setStaffTypeId(mergeStaffDetailBto.getStaffTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "professionalTitleId")) {
                staffBO.setProfessionalTitleId(mergeStaffDetailBto.getProfessionalTitleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "phoneNumber")) {
                staffBO.setPhoneNumber(mergeStaffDetailBto.getPhoneNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "shortPhoneNumber")) {
                staffBO.setShortPhoneNumber(mergeStaffDetailBto.getShortPhoneNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "emailAddress")) {
                staffBO.setEmailAddress(mergeStaffDetailBto.getEmailAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "expertFlag")) {
                staffBO.setExpertFlag(mergeStaffDetailBto.getExpertFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "description")) {
                staffBO.setDescription(mergeStaffDetailBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "status")) {
                staffBO.setStatus(mergeStaffDetailBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "createdBy")) {
                staffBO.setCreatedBy(mergeStaffDetailBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "updateBy")) {
                staffBO.setUpdateBy(mergeStaffDetailBto.getUpdateBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "deletedBy")) {
                staffBO.setDeletedBy(mergeStaffDetailBto.getDeletedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "sortNumber")) {
                staffBO.setSortNumber(mergeStaffDetailBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "organizationId")) {
                staffBO.setOrganizationId(mergeStaffDetailBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "accountingOrganizationId")) {
                staffBO.setAccountingOrganizationId(
                        mergeStaffDetailBto.getAccountingOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "hrOrganizationId")) {
                staffBO.setHrOrganizationId(mergeStaffDetailBto.getHrOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "positionId")) {
                staffBO.setPositionId(mergeStaffDetailBto.getPositionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "promotionDate")) {
                staffBO.setPromotionDate(mergeStaffDetailBto.getPromotionDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "registerTypeList")) {
                staffBO.setRegisterTypeList(mergeStaffDetailBto.getRegisterTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                staffBO.setProvincePlatformCancelFlag(
                        mergeStaffDetailBto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "countrysideStartDate")) {
                staffBO.setCountrysideStartDate(mergeStaffDetailBto.getCountrysideStartDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "doctorRemark")) {
                staffBO.setDoctorRemark(mergeStaffDetailBto.getDoctorRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "registerDoctorFlag")) {
                staffBO.setRegisterDoctorFlag(mergeStaffDetailBto.getRegisterDoctorFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "countrysideEndDate")) {
                staffBO.setCountrysideEndDate(mergeStaffDetailBto.getCountrysideEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "registerDoctorEnableFlag")) {
                staffBO.setRegisterDoctorEnableFlag(
                        mergeStaffDetailBto.getRegisterDoctorEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(mergeStaffDetailBto.getCampusOrganizationId());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeStaffDetailBto);
            addedBto.setBo(staffBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return staffBO;
    }

    /** 创建对象:StaffConsultingRoomBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createStaffConsultingRoomBtoOnDuplicateUpdate(
            BaseStaffBOService.MergeStaffConsultingRoomBoResult boResult,
            MergeStaffConsultingRoomBto mergeStaffConsultingRoomBto,
            StaffBO staffBO) {
        if (CollectionUtil.isEmpty(mergeStaffConsultingRoomBto.getStaffConsultingRoomBtoList())) {
            mergeStaffConsultingRoomBto.setStaffConsultingRoomBtoList(List.of());
        }
        staffBO.getStaffConsultingRoomBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeStaffConsultingRoomBto
                                            .getStaffConsultingRoomBtoList()
                                            .stream()
                                            .filter(
                                                    staffConsultingRoomBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (staffConsultingRoomBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            staffConsultingRoomBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToStaffConsultingRoom());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                mergeStaffConsultingRoomBto.getStaffConsultingRoomBtoList())) {
            for (MergeStaffConsultingRoomBto.StaffConsultingRoomBto item :
                    mergeStaffConsultingRoomBto.getStaffConsultingRoomBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<StaffConsultingRoomBO> any =
                        staffBO.getStaffConsultingRoomBOSet().stream()
                                .filter(
                                        staffConsultingRoomBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                staffConsultingRoomBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        StaffConsultingRoomBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffConsultingRoom());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "consultingRoomId")) {
                            bo.setConsultingRoomId(item.getConsultingRoomId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "deletedBy")) {
                            bo.setDeletedBy(item.getDeletedBy());
                        }
                    } else {
                        StaffConsultingRoomBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffConsultingRoom());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "consultingRoomId")) {
                            bo.setConsultingRoomId(item.getConsultingRoomId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "enableFlag")) {
                            bo.setEnableFlag(item.getEnableFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "deletedBy")) {
                            bo.setDeletedBy(item.getDeletedBy());
                        }
                    }
                } else {
                    StaffConsultingRoomBO subBo = new StaffConsultingRoomBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "consultingRoomId")) {
                        subBo.setConsultingRoomId(item.getConsultingRoomId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "enableFlag")) {
                        subBo.setEnableFlag(item.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "deletedBy")) {
                        subBo.setDeletedBy(item.getDeletedBy());
                    }
                    subBo.setStaffBO(staffBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("staff_consulting_room")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    staffBO.getStaffConsultingRoomBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建员工信息 */
    @AutoGenerated(locked = true)
    protected CreateStaffDetailBoResult createStaffDetailBase(
            CreateStaffDetailBto createStaffDetailBto) {
        if (createStaffDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateStaffDetailBoResult boResult = new CreateStaffDetailBoResult();
        StaffBO staffBO = createCreateStaffDetail(boResult, createStaffDetailBto);
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffExtensionBto")) {
                createStaffExtensionBtoOnDuplicateThrowEx(boResult, createStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffPracticeBtoList")) {
                createStaffPracticeBto(boResult, createStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffEducationBtoList")) {
                createStaffEducationBto(boResult, createStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffWorkExperienceBtoList")) {
                createStaffWorkExperienceBto(boResult, createStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffEmploymentBtoList")) {
                createStaffEmploymentBto(boResult, createStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffOrganizationBtoList")) {
                createStaffOrganizationBto(boResult, createStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffDetailBto, "__$validPropertySet"),
                    "staffUserBtoList")) {
                createStaffUserBto(boResult, createStaffDetailBto, staffBO);
            }
        }
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 创建对象StaffEducationBto */
    @AutoGenerated(locked = true)
    private void createStaffEducationBto(
            CreateStaffDetailBoResult boResult,
            CreateStaffDetailBto createStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(createStaffDetailBto.getStaffEducationBtoList())) {
            for (CreateStaffDetailBto.StaffEducationBto item :
                    createStaffDetailBto.getStaffEducationBtoList()) {
                StaffEducationBO subBo = new StaffEducationBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "startDate")) {
                    subBo.setStartDate(item.getStartDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "endDate")) {
                    subBo.setEndDate(item.getEndDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "graduatedInstitution")) {
                    subBo.setGraduatedInstitution(item.getGraduatedInstitution());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "qualification")) {
                    subBo.setQualification(item.getQualification());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "studyMode")) {
                    subBo.setStudyMode(item.getStudyMode());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "major")) {
                    subBo.setMajor(item.getMajor());
                }
                subBo.setStaffBO(staffBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_education")));
                staffBO.getStaffEducationBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:StaffEducationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createStaffEducationBtoOnDuplicateUpdate(
            MergeStaffDetailBoResult boResult,
            MergeStaffDetailBto mergeStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isEmpty(mergeStaffDetailBto.getStaffEducationBtoList())) {
            mergeStaffDetailBto.setStaffEducationBtoList(List.of());
        }
        staffBO.getStaffEducationBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeStaffDetailBto.getStaffEducationBtoList().stream()
                                            .filter(
                                                    staffEducationBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (staffEducationBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            staffEducationBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToStaffEducation());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeStaffDetailBto.getStaffEducationBtoList())) {
            for (MergeStaffDetailBto.StaffEducationBto item :
                    mergeStaffDetailBto.getStaffEducationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<StaffEducationBO> any =
                        staffBO.getStaffEducationBOSet().stream()
                                .filter(
                                        staffEducationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                staffEducationBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        StaffEducationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffEducation());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startDate")) {
                            bo.setStartDate(item.getStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endDate")) {
                            bo.setEndDate(item.getEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graduatedInstitution")) {
                            bo.setGraduatedInstitution(item.getGraduatedInstitution());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "qualification")) {
                            bo.setQualification(item.getQualification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "studyMode")) {
                            bo.setStudyMode(item.getStudyMode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "major")) {
                            bo.setMajor(item.getMajor());
                        }
                    } else {
                        StaffEducationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffEducation());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startDate")) {
                            bo.setStartDate(item.getStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endDate")) {
                            bo.setEndDate(item.getEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "graduatedInstitution")) {
                            bo.setGraduatedInstitution(item.getGraduatedInstitution());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "qualification")) {
                            bo.setQualification(item.getQualification());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "studyMode")) {
                            bo.setStudyMode(item.getStudyMode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "major")) {
                            bo.setMajor(item.getMajor());
                        }
                    }
                } else {
                    StaffEducationBO subBo = new StaffEducationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "startDate")) {
                        subBo.setStartDate(item.getStartDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "endDate")) {
                        subBo.setEndDate(item.getEndDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "graduatedInstitution")) {
                        subBo.setGraduatedInstitution(item.getGraduatedInstitution());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "qualification")) {
                        subBo.setQualification(item.getQualification());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "studyMode")) {
                        subBo.setStudyMode(item.getStudyMode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "major")) {
                        subBo.setMajor(item.getMajor());
                    }
                    subBo.setStaffBO(staffBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_education")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    staffBO.getStaffEducationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象StaffEmploymentBto */
    @AutoGenerated(locked = true)
    private void createStaffEmploymentBto(
            CreateStaffDetailBoResult boResult,
            CreateStaffDetailBto createStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(createStaffDetailBto.getStaffEmploymentBtoList())) {
            for (CreateStaffDetailBto.StaffEmploymentBto item :
                    createStaffDetailBto.getStaffEmploymentBtoList()) {
                StaffEmploymentBO subBo = new StaffEmploymentBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "type")) {
                    subBo.setType(item.getType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "contractStartDate")) {
                    subBo.setContractStartDate(item.getContractStartDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "contractEndDate")) {
                    subBo.setContractEndDate(item.getContractEndDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "contractYearCount")) {
                    subBo.setContractYearCount(item.getContractYearCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "signingStatus")) {
                    subBo.setSigningStatus(item.getSigningStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "contractStatus")) {
                    subBo.setContractStatus(item.getContractStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "departmentName")) {
                    subBo.setDepartmentName(item.getDepartmentName());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "position")) {
                    subBo.setPosition(item.getPosition());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "contractNumber")) {
                    subBo.setContractNumber(item.getContractNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "terminationDate")) {
                    subBo.setTerminationDate(item.getTerminationDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "terminationReason")) {
                    subBo.setTerminationReason(item.getTerminationReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "dissolveDate")) {
                    subBo.setDissolveDate(item.getDissolveDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "dissolveReason")) {
                    subBo.setDissolveReason(item.getDissolveReason());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "bankCardNumber")) {
                    subBo.setBankCardNumber(item.getBankCardNumber());
                }
                subBo.setStaffBO(staffBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_employment")));
                staffBO.getStaffEmploymentBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:StaffEmploymentBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createStaffEmploymentBtoOnDuplicateUpdate(
            MergeStaffDetailBoResult boResult,
            MergeStaffDetailBto mergeStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isEmpty(mergeStaffDetailBto.getStaffEmploymentBtoList())) {
            mergeStaffDetailBto.setStaffEmploymentBtoList(List.of());
        }
        staffBO.getStaffEmploymentBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeStaffDetailBto.getStaffEmploymentBtoList().stream()
                                            .filter(
                                                    staffEmploymentBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (staffEmploymentBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            staffEmploymentBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToStaffEmployment());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeStaffDetailBto.getStaffEmploymentBtoList())) {
            for (MergeStaffDetailBto.StaffEmploymentBto item :
                    mergeStaffDetailBto.getStaffEmploymentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<StaffEmploymentBO> any =
                        staffBO.getStaffEmploymentBOSet().stream()
                                .filter(
                                        staffEmploymentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                staffEmploymentBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        StaffEmploymentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffEmployment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "type")) {
                            bo.setType(item.getType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractStartDate")) {
                            bo.setContractStartDate(item.getContractStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractEndDate")) {
                            bo.setContractEndDate(item.getContractEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractYearCount")) {
                            bo.setContractYearCount(item.getContractYearCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "signingStatus")) {
                            bo.setSigningStatus(item.getSigningStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractStatus")) {
                            bo.setContractStatus(item.getContractStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentName")) {
                            bo.setDepartmentName(item.getDepartmentName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "position")) {
                            bo.setPosition(item.getPosition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractNumber")) {
                            bo.setContractNumber(item.getContractNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "terminationDate")) {
                            bo.setTerminationDate(item.getTerminationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "terminationReason")) {
                            bo.setTerminationReason(item.getTerminationReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "dissolveDate")) {
                            bo.setDissolveDate(item.getDissolveDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "dissolveReason")) {
                            bo.setDissolveReason(item.getDissolveReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "bankCardNumber")) {
                            bo.setBankCardNumber(item.getBankCardNumber());
                        }
                    } else {
                        StaffEmploymentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffEmployment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "type")) {
                            bo.setType(item.getType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractStartDate")) {
                            bo.setContractStartDate(item.getContractStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractEndDate")) {
                            bo.setContractEndDate(item.getContractEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractYearCount")) {
                            bo.setContractYearCount(item.getContractYearCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "signingStatus")) {
                            bo.setSigningStatus(item.getSigningStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractStatus")) {
                            bo.setContractStatus(item.getContractStatus());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentName")) {
                            bo.setDepartmentName(item.getDepartmentName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "position")) {
                            bo.setPosition(item.getPosition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contractNumber")) {
                            bo.setContractNumber(item.getContractNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "terminationDate")) {
                            bo.setTerminationDate(item.getTerminationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "terminationReason")) {
                            bo.setTerminationReason(item.getTerminationReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "dissolveDate")) {
                            bo.setDissolveDate(item.getDissolveDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "dissolveReason")) {
                            bo.setDissolveReason(item.getDissolveReason());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "bankCardNumber")) {
                            bo.setBankCardNumber(item.getBankCardNumber());
                        }
                    }
                } else {
                    StaffEmploymentBO subBo = new StaffEmploymentBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "type")) {
                        subBo.setType(item.getType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "contractStartDate")) {
                        subBo.setContractStartDate(item.getContractStartDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "contractEndDate")) {
                        subBo.setContractEndDate(item.getContractEndDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "contractYearCount")) {
                        subBo.setContractYearCount(item.getContractYearCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "signingStatus")) {
                        subBo.setSigningStatus(item.getSigningStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "contractStatus")) {
                        subBo.setContractStatus(item.getContractStatus());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "departmentName")) {
                        subBo.setDepartmentName(item.getDepartmentName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "position")) {
                        subBo.setPosition(item.getPosition());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "contractNumber")) {
                        subBo.setContractNumber(item.getContractNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "terminationDate")) {
                        subBo.setTerminationDate(item.getTerminationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "terminationReason")) {
                        subBo.setTerminationReason(item.getTerminationReason());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "dissolveDate")) {
                        subBo.setDissolveDate(item.getDissolveDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "dissolveReason")) {
                        subBo.setDissolveReason(item.getDissolveReason());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "bankCardNumber")) {
                        subBo.setBankCardNumber(item.getBankCardNumber());
                    }
                    subBo.setStaffBO(staffBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("staff_employment")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    staffBO.getStaffEmploymentBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:StaffExtensionBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createStaffExtensionBtoOnDuplicateThrowEx(
            BaseStaffBOService.CreateStaffDetailBoResult boResult,
            CreateStaffDetailBto createStaffDetailBto,
            StaffBO staffBO) {
        if (staffBO.getStaffExtensionBO() != null) {
            log.error(
                    "id:{}在数据库表:{}中已经存在！",
                    staffBO.getStaffExtensionBO().getId(),
                    "staff_extension");
            throw new IgnoredException(400, "员工扩展已存在");
        } else {
            if (createStaffDetailBto.getStaffExtensionBto() == null) {
                return;
            }
            StaffExtensionBO staffExtensionBO = staffBO.getOrCreateStaffExtensionBO();
            CreateStaffDetailBto.StaffExtensionBto staffExtensionBto =
                    createStaffDetailBto.getStaffExtensionBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "nationalCode")) {
                staffExtensionBO.setNationalCode(
                        createStaffDetailBto.getStaffExtensionBto().getNationalCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "ethnicityCode")) {
                staffExtensionBO.setEthnicityCode(
                        createStaffDetailBto.getStaffExtensionBto().getEthnicityCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "authenticationInfo")) {
                staffExtensionBO.setAuthenticationInfo(
                        createStaffDetailBto.getStaffExtensionBto().getAuthenticationInfo());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "professionalField")) {
                staffExtensionBO.setProfessionalField(
                        createStaffDetailBto.getStaffExtensionBto().getProfessionalField());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "professionalExpertise")) {
                staffExtensionBO.setProfessionalExpertise(
                        createStaffDetailBto.getStaffExtensionBto().getProfessionalExpertise());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "labelIdList")) {
                staffExtensionBO.setLabelIdList(
                        createStaffDetailBto.getStaffExtensionBto().getLabelIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "workTypeCode")) {
                staffExtensionBO.setWorkTypeCode(
                        createStaffDetailBto.getStaffExtensionBto().getWorkTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "socialAccount")) {
                staffExtensionBO.setSocialAccount(
                        createStaffDetailBto.getStaffExtensionBto().getSocialAccount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "recruitmentTime")) {
                staffExtensionBO.setRecruitmentTime(
                        createStaffDetailBto.getStaffExtensionBto().getRecruitmentTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "internFlag")) {
                staffExtensionBO.setInternFlag(
                        createStaffDetailBto.getStaffExtensionBto().getInternFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "tutorId")) {
                staffExtensionBO.setTutorId(
                        createStaffDetailBto.getStaffExtensionBto().getTutorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "politicalAffiliation")) {
                staffExtensionBO.setPoliticalAffiliation(
                        createStaffDetailBto.getStaffExtensionBto().getPoliticalAffiliation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "maritalStatus")) {
                staffExtensionBO.setMaritalStatus(
                        createStaffDetailBto.getStaffExtensionBto().getMaritalStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "homeAddress")) {
                staffExtensionBO.setHomeAddress(
                        createStaffDetailBto.getStaffExtensionBto().getHomeAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "createdBy")) {
                staffExtensionBO.setCreatedBy(
                        createStaffDetailBto.getStaffExtensionBto().getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "updatedBy")) {
                staffExtensionBO.setUpdatedBy(
                        createStaffDetailBto.getStaffExtensionBto().getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "deletedBy")) {
                staffExtensionBO.setDeletedBy(
                        createStaffDetailBto.getStaffExtensionBto().getDeletedBy());
            }

            staffExtensionBO.setId(String.valueOf(this.idGenerator.allocateId("staff_extension")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(staffExtensionBto);
            addedBto.setBo(staffExtensionBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:StaffExtensionBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createStaffExtensionBtoOnDuplicateUpdate(
            BaseStaffBOService.MergeStaffDetailBoResult boResult,
            MergeStaffDetailBto mergeStaffDetailBto,
            StaffBO staffBO) {
        if (staffBO.getStaffExtensionBO() != null) {
            StaffExtensionBO bo = staffBO.getOrCreateStaffExtensionBO();
            MergeStaffDetailBto.StaffExtensionBto bto = mergeStaffDetailBto.getStaffExtensionBto();
            if (bto == null) {
                StaffExtension deletedItem =
                        staffBO.getStaffExtensionBO().convertToStaffExtension();
                boResult.getDeletedList().add(deletedItem);
                staffBO.setStaffExtensionBO(null);
                return;
            }
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setBo(bo);
            updatedBto.setEntity(bo.convertToStaffExtension());
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "nationalCode")) {
                bo.setNationalCode(bto.getNationalCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "ethnicityCode")) {
                bo.setEthnicityCode(bto.getEthnicityCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "authenticationInfo")) {
                bo.setAuthenticationInfo(bto.getAuthenticationInfo());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "professionalField")) {
                bo.setProfessionalField(bto.getProfessionalField());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "professionalExpertise")) {
                bo.setProfessionalExpertise(bto.getProfessionalExpertise());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "labelIdList")) {
                bo.setLabelIdList(bto.getLabelIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "workTypeCode")) {
                bo.setWorkTypeCode(bto.getWorkTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "socialAccount")) {
                bo.setSocialAccount(bto.getSocialAccount());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "recruitmentTime")) {
                bo.setRecruitmentTime(bto.getRecruitmentTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "internFlag")) {
                bo.setInternFlag(bto.getInternFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "tutorId")) {
                bo.setTutorId(bto.getTutorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "politicalAffiliation")) {
                bo.setPoliticalAffiliation(bto.getPoliticalAffiliation());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "maritalStatus")) {
                bo.setMaritalStatus(bto.getMaritalStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "homeAddress")) {
                bo.setHomeAddress(bto.getHomeAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "createdBy")) {
                bo.setCreatedBy(bto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "deletedBy")) {
                bo.setDeletedBy(bto.getDeletedBy());
            }
            boResult.getUpdatedList().add(updatedBto);
        } else {
            if (mergeStaffDetailBto.getStaffExtensionBto() == null) {
                return;
            }
            StaffExtensionBO staffExtensionBO = staffBO.getOrCreateStaffExtensionBO();
            MergeStaffDetailBto.StaffExtensionBto staffExtensionBto =
                    mergeStaffDetailBto.getStaffExtensionBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "nationalCode")) {
                staffExtensionBO.setNationalCode(
                        mergeStaffDetailBto.getStaffExtensionBto().getNationalCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "ethnicityCode")) {
                staffExtensionBO.setEthnicityCode(
                        mergeStaffDetailBto.getStaffExtensionBto().getEthnicityCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "authenticationInfo")) {
                staffExtensionBO.setAuthenticationInfo(
                        mergeStaffDetailBto.getStaffExtensionBto().getAuthenticationInfo());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "professionalField")) {
                staffExtensionBO.setProfessionalField(
                        mergeStaffDetailBto.getStaffExtensionBto().getProfessionalField());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "professionalExpertise")) {
                staffExtensionBO.setProfessionalExpertise(
                        mergeStaffDetailBto.getStaffExtensionBto().getProfessionalExpertise());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "labelIdList")) {
                staffExtensionBO.setLabelIdList(
                        mergeStaffDetailBto.getStaffExtensionBto().getLabelIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "workTypeCode")) {
                staffExtensionBO.setWorkTypeCode(
                        mergeStaffDetailBto.getStaffExtensionBto().getWorkTypeCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "socialAccount")) {
                staffExtensionBO.setSocialAccount(
                        mergeStaffDetailBto.getStaffExtensionBto().getSocialAccount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "recruitmentTime")) {
                staffExtensionBO.setRecruitmentTime(
                        mergeStaffDetailBto.getStaffExtensionBto().getRecruitmentTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "internFlag")) {
                staffExtensionBO.setInternFlag(
                        mergeStaffDetailBto.getStaffExtensionBto().getInternFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "tutorId")) {
                staffExtensionBO.setTutorId(
                        mergeStaffDetailBto.getStaffExtensionBto().getTutorId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "politicalAffiliation")) {
                staffExtensionBO.setPoliticalAffiliation(
                        mergeStaffDetailBto.getStaffExtensionBto().getPoliticalAffiliation());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "maritalStatus")) {
                staffExtensionBO.setMaritalStatus(
                        mergeStaffDetailBto.getStaffExtensionBto().getMaritalStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "homeAddress")) {
                staffExtensionBO.setHomeAddress(
                        mergeStaffDetailBto.getStaffExtensionBto().getHomeAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "createdBy")) {
                staffExtensionBO.setCreatedBy(
                        mergeStaffDetailBto.getStaffExtensionBto().getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "updatedBy")) {
                staffExtensionBO.setUpdatedBy(
                        mergeStaffDetailBto.getStaffExtensionBto().getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(staffExtensionBto, "__$validPropertySet"),
                    "deletedBy")) {
                staffExtensionBO.setDeletedBy(
                        mergeStaffDetailBto.getStaffExtensionBto().getDeletedBy());
            }

            staffExtensionBO.setId(String.valueOf(this.idGenerator.allocateId("staff_extension")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(staffExtensionBto);
            addedBto.setBo(staffExtensionBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象StaffOrganizationBto */
    @AutoGenerated(locked = true)
    private void createStaffOrganizationBto(
            CreateStaffDetailBoResult boResult,
            CreateStaffDetailBto createStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(createStaffDetailBto.getStaffOrganizationBtoList())) {
            for (CreateStaffDetailBto.StaffOrganizationBto item :
                    createStaffDetailBto.getStaffOrganizationBtoList()) {
                StaffOrganizationBO subBo = new StaffOrganizationBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "applicationId")) {
                    subBo.setApplicationId(item.getApplicationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "organizationId")) {
                    subBo.setOrganizationId(item.getOrganizationId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "deletedBy")) {
                    subBo.setDeletedBy(item.getDeletedBy());
                }
                subBo.setStaffBO(staffBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_organization")));
                staffBO.getStaffOrganizationBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:StaffOrganizationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createStaffOrganizationBtoOnDuplicateUpdate(
            MergeStaffDetailBoResult boResult,
            MergeStaffDetailBto mergeStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isEmpty(mergeStaffDetailBto.getStaffOrganizationBtoList())) {
            mergeStaffDetailBto.setStaffOrganizationBtoList(List.of());
        }
        staffBO.getStaffOrganizationBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeStaffDetailBto.getStaffOrganizationBtoList().stream()
                                            .filter(
                                                    staffOrganizationBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (staffOrganizationBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            staffOrganizationBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToStaffOrganization());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeStaffDetailBto.getStaffOrganizationBtoList())) {
            for (MergeStaffDetailBto.StaffOrganizationBto item :
                    mergeStaffDetailBto.getStaffOrganizationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<StaffOrganizationBO> any =
                        staffBO.getStaffOrganizationBOSet().stream()
                                .filter(
                                        staffOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                staffOrganizationBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        StaffOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicationId")) {
                            bo.setApplicationId(item.getApplicationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "deletedBy")) {
                            bo.setDeletedBy(item.getDeletedBy());
                        }
                    } else {
                        StaffOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicationId")) {
                            bo.setApplicationId(item.getApplicationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "deletedBy")) {
                            bo.setDeletedBy(item.getDeletedBy());
                        }
                    }
                } else {
                    StaffOrganizationBO subBo = new StaffOrganizationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicationId")) {
                        subBo.setApplicationId(item.getApplicationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "deletedBy")) {
                        subBo.setDeletedBy(item.getDeletedBy());
                    }
                    subBo.setStaffBO(staffBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("staff_organization")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    staffBO.getStaffOrganizationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象StaffPracticeBto */
    @AutoGenerated(locked = true)
    private void createStaffPracticeBto(
            CreateStaffDetailBoResult boResult,
            CreateStaffDetailBto createStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(createStaffDetailBto.getStaffPracticeBtoList())) {
            for (CreateStaffDetailBto.StaffPracticeBto item :
                    createStaffDetailBto.getStaffPracticeBtoList()) {
                StaffPracticeBO subBo = new StaffPracticeBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "qualificationCertificateNumber")) {
                    subBo.setQualificationCertificateNumber(
                            item.getQualificationCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "practiceCertificateNumber")) {
                    subBo.setPracticeCertificateNumber(item.getPracticeCertificateNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "startDate")) {
                    subBo.setStartDate(item.getStartDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "endDate")) {
                    subBo.setEndDate(item.getEndDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "category")) {
                    subBo.setCategory(item.getCategory());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "practiceScope")) {
                    subBo.setPracticeScope(item.getPracticeScope());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "technicalTitle")) {
                    subBo.setTechnicalTitle(item.getTechnicalTitle());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "practiceDepartment")) {
                    subBo.setPracticeDepartment(item.getPracticeDepartment());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "doctorInsuranceId")) {
                    subBo.setDoctorInsuranceId(item.getDoctorInsuranceId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "anesthesiologistNumber")) {
                    subBo.setAnesthesiologistNumber(item.getAnesthesiologistNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "radiologistNumber")) {
                    subBo.setRadiologistNumber(item.getRadiologistNumber());
                }
                subBo.setStaffBO(staffBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_practice")));
                staffBO.getStaffPracticeBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:StaffPracticeBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createStaffPracticeBtoOnDuplicateUpdate(
            MergeStaffDetailBoResult boResult,
            MergeStaffDetailBto mergeStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isEmpty(mergeStaffDetailBto.getStaffPracticeBtoList())) {
            mergeStaffDetailBto.setStaffPracticeBtoList(List.of());
        }
        staffBO.getStaffPracticeBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeStaffDetailBto.getStaffPracticeBtoList().stream()
                                            .filter(
                                                    staffPracticeBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (staffPracticeBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            staffPracticeBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToStaffPractice());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeStaffDetailBto.getStaffPracticeBtoList())) {
            for (MergeStaffDetailBto.StaffPracticeBto item :
                    mergeStaffDetailBto.getStaffPracticeBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<StaffPracticeBO> any =
                        staffBO.getStaffPracticeBOSet().stream()
                                .filter(
                                        staffPracticeBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                staffPracticeBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        StaffPracticeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffPractice());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "qualificationCertificateNumber")) {
                            bo.setQualificationCertificateNumber(
                                    item.getQualificationCertificateNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "practiceCertificateNumber")) {
                            bo.setPracticeCertificateNumber(item.getPracticeCertificateNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startDate")) {
                            bo.setStartDate(item.getStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endDate")) {
                            bo.setEndDate(item.getEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "category")) {
                            bo.setCategory(item.getCategory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "practiceScope")) {
                            bo.setPracticeScope(item.getPracticeScope());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "technicalTitle")) {
                            bo.setTechnicalTitle(item.getTechnicalTitle());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "practiceDepartment")) {
                            bo.setPracticeDepartment(item.getPracticeDepartment());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "doctorInsuranceId")) {
                            bo.setDoctorInsuranceId(item.getDoctorInsuranceId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "anesthesiologistNumber")) {
                            bo.setAnesthesiologistNumber(item.getAnesthesiologistNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "radiologistNumber")) {
                            bo.setRadiologistNumber(item.getRadiologistNumber());
                        }
                    } else {
                        StaffPracticeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffPractice());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "qualificationCertificateNumber")) {
                            bo.setQualificationCertificateNumber(
                                    item.getQualificationCertificateNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "practiceCertificateNumber")) {
                            bo.setPracticeCertificateNumber(item.getPracticeCertificateNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startDate")) {
                            bo.setStartDate(item.getStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endDate")) {
                            bo.setEndDate(item.getEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "category")) {
                            bo.setCategory(item.getCategory());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "practiceScope")) {
                            bo.setPracticeScope(item.getPracticeScope());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "technicalTitle")) {
                            bo.setTechnicalTitle(item.getTechnicalTitle());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "practiceDepartment")) {
                            bo.setPracticeDepartment(item.getPracticeDepartment());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "doctorInsuranceId")) {
                            bo.setDoctorInsuranceId(item.getDoctorInsuranceId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "anesthesiologistNumber")) {
                            bo.setAnesthesiologistNumber(item.getAnesthesiologistNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "radiologistNumber")) {
                            bo.setRadiologistNumber(item.getRadiologistNumber());
                        }
                    }
                } else {
                    StaffPracticeBO subBo = new StaffPracticeBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "qualificationCertificateNumber")) {
                        subBo.setQualificationCertificateNumber(
                                item.getQualificationCertificateNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "practiceCertificateNumber")) {
                        subBo.setPracticeCertificateNumber(item.getPracticeCertificateNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "startDate")) {
                        subBo.setStartDate(item.getStartDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "endDate")) {
                        subBo.setEndDate(item.getEndDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "category")) {
                        subBo.setCategory(item.getCategory());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "practiceScope")) {
                        subBo.setPracticeScope(item.getPracticeScope());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "technicalTitle")) {
                        subBo.setTechnicalTitle(item.getTechnicalTitle());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "practiceDepartment")) {
                        subBo.setPracticeDepartment(item.getPracticeDepartment());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "doctorInsuranceId")) {
                        subBo.setDoctorInsuranceId(item.getDoctorInsuranceId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "anesthesiologistNumber")) {
                        subBo.setAnesthesiologistNumber(item.getAnesthesiologistNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "radiologistNumber")) {
                        subBo.setRadiologistNumber(item.getRadiologistNumber());
                    }
                    subBo.setStaffBO(staffBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_practice")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    staffBO.getStaffPracticeBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建员工用户关系 */
    @AutoGenerated(locked = true)
    protected CreateStaffUserBoResult createStaffUserBase(CreateStaffUserBto createStaffUserBto) {
        if (createStaffUserBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateStaffUserBoResult boResult = new CreateStaffUserBoResult();
        StaffBO staffBO = updateCreateStaffUserOnMissThrowEx(boResult, createStaffUserBto);
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "staffUserBtoList")) {
                createStaffUserBto(boResult, createStaffUserBto, staffBO);
            }
        }
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 创建对象StaffUserBto */
    @AutoGenerated(locked = true)
    private void createStaffUserBto(
            CreateStaffUserBoResult boResult,
            CreateStaffUserBto createStaffUserBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(createStaffUserBto.getStaffUserBtoList())) {
            for (CreateStaffUserBto.StaffUserBto item : createStaffUserBto.getStaffUserBtoList()) {
                StaffUserBO subBo = new StaffUserBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "userId")) {
                    subBo.setUserId(item.getUserId());
                }
                subBo.setStaffBO(staffBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_user")));
                staffBO.getStaffUserBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象StaffUserBto */
    @AutoGenerated(locked = true)
    private void createStaffUserBto(
            CreateStaffDetailBoResult boResult,
            CreateStaffDetailBto createStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(createStaffDetailBto.getStaffUserBtoList())) {
            for (CreateStaffDetailBto.StaffUserBto item :
                    createStaffDetailBto.getStaffUserBtoList()) {
                StaffUserBO subBo = new StaffUserBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "userId")) {
                    subBo.setUserId(item.getUserId());
                }
                subBo.setStaffBO(staffBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_user")));
                staffBO.getStaffUserBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象StaffWorkExperienceBto */
    @AutoGenerated(locked = true)
    private void createStaffWorkExperienceBto(
            CreateStaffDetailBoResult boResult,
            CreateStaffDetailBto createStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(createStaffDetailBto.getStaffWorkExperienceBtoList())) {
            for (CreateStaffDetailBto.StaffWorkExperienceBto item :
                    createStaffDetailBto.getStaffWorkExperienceBtoList()) {
                StaffWorkExperienceBO subBo = new StaffWorkExperienceBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "startDate")) {
                    subBo.setStartDate(item.getStartDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "endDate")) {
                    subBo.setEndDate(item.getEndDate());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "workUnit")) {
                    subBo.setWorkUnit(item.getWorkUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "position")) {
                    subBo.setPosition(item.getPosition());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "belongToDepartment")) {
                    subBo.setBelongToDepartment(item.getBelongToDepartment());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "accountingDepartment")) {
                    subBo.setAccountingDepartment(item.getAccountingDepartment());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "hrDepartment")) {
                    subBo.setHrDepartment(item.getHrDepartment());
                }
                subBo.setStaffBO(staffBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("staff_work_experience")));
                staffBO.getStaffWorkExperienceBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:StaffWorkExperienceBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createStaffWorkExperienceBtoOnDuplicateUpdate(
            MergeStaffDetailBoResult boResult,
            MergeStaffDetailBto mergeStaffDetailBto,
            StaffBO staffBO) {
        if (CollectionUtil.isEmpty(mergeStaffDetailBto.getStaffWorkExperienceBtoList())) {
            mergeStaffDetailBto.setStaffWorkExperienceBtoList(List.of());
        }
        staffBO.getStaffWorkExperienceBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeStaffDetailBto.getStaffWorkExperienceBtoList().stream()
                                            .filter(
                                                    staffWorkExperienceBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (staffWorkExperienceBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            staffWorkExperienceBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToStaffWorkExperience());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeStaffDetailBto.getStaffWorkExperienceBtoList())) {
            for (MergeStaffDetailBto.StaffWorkExperienceBto item :
                    mergeStaffDetailBto.getStaffWorkExperienceBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<StaffWorkExperienceBO> any =
                        staffBO.getStaffWorkExperienceBOSet().stream()
                                .filter(
                                        staffWorkExperienceBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                staffWorkExperienceBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        StaffWorkExperienceBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffWorkExperience());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startDate")) {
                            bo.setStartDate(item.getStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endDate")) {
                            bo.setEndDate(item.getEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "workUnit")) {
                            bo.setWorkUnit(item.getWorkUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "position")) {
                            bo.setPosition(item.getPosition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "belongToDepartment")) {
                            bo.setBelongToDepartment(item.getBelongToDepartment());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountingDepartment")) {
                            bo.setAccountingDepartment(item.getAccountingDepartment());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "hrDepartment")) {
                            bo.setHrDepartment(item.getHrDepartment());
                        }
                    } else {
                        StaffWorkExperienceBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToStaffWorkExperience());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "startDate")) {
                            bo.setStartDate(item.getStartDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "endDate")) {
                            bo.setEndDate(item.getEndDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "workUnit")) {
                            bo.setWorkUnit(item.getWorkUnit());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "position")) {
                            bo.setPosition(item.getPosition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "belongToDepartment")) {
                            bo.setBelongToDepartment(item.getBelongToDepartment());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "accountingDepartment")) {
                            bo.setAccountingDepartment(item.getAccountingDepartment());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "hrDepartment")) {
                            bo.setHrDepartment(item.getHrDepartment());
                        }
                    }
                } else {
                    StaffWorkExperienceBO subBo = new StaffWorkExperienceBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "startDate")) {
                        subBo.setStartDate(item.getStartDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "endDate")) {
                        subBo.setEndDate(item.getEndDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "workUnit")) {
                        subBo.setWorkUnit(item.getWorkUnit());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "position")) {
                        subBo.setPosition(item.getPosition());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "belongToDepartment")) {
                        subBo.setBelongToDepartment(item.getBelongToDepartment());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "accountingDepartment")) {
                        subBo.setAccountingDepartment(item.getAccountingDepartment());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "hrDepartment")) {
                        subBo.setHrDepartment(item.getHrDepartment());
                    }
                    subBo.setStaffBO(staffBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId("staff_work_experience")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    staffBO.getStaffWorkExperienceBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 删除对象:deleteStaffDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private StaffBO deleteDeleteStaffDetailOnMissThrowEx(
            BaseStaffBOService.DeleteStaffDetailBoResult boResult,
            DeleteStaffDetailBto deleteStaffDetailBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteStaffDetailBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(deleteStaffDetailBto.getId());
            found = true;
        }
        if (staffBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(staffBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteStaffDetailBto);
            deletedBto.setEntity(staffBO.convertToStaff());
            boResult.getDeletedBtoList().add(deletedBto);
            return staffBO;
        }
    }

    /** 删除员工信息 */
    @AutoGenerated(locked = true)
    protected DeleteStaffDetailBoResult deleteStaffDetailBase(
            DeleteStaffDetailBto deleteStaffDetailBto) {
        if (deleteStaffDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteStaffDetailBoResult boResult = new DeleteStaffDetailBoResult();
        StaffBO staffBO = deleteDeleteStaffDetailOnMissThrowEx(boResult, deleteStaffDetailBto);
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 删除员工用户关系 */
    @AutoGenerated(locked = true)
    protected DeleteStaffUserBoResult deleteStaffUserBase(DeleteStaffUserBto deleteStaffUserBto) {
        if (deleteStaffUserBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteStaffUserBoResult boResult = new DeleteStaffUserBoResult();
        StaffBO staffBO = updateDeleteStaffUserOnMissThrowEx(boResult, deleteStaffUserBto);
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "staffUserBtoList")) {
                deleteStaffUserBtoOnMissThrowEx(boResult, deleteStaffUserBto, staffBO);
            }
        }
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 删除对象:staffUserBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteStaffUserBtoOnMissThrowEx(
            DeleteStaffUserBoResult boResult,
            DeleteStaffUserBto deleteStaffUserBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(deleteStaffUserBto.getStaffUserBtoList())) {
            for (DeleteStaffUserBto.StaffUserBto item : deleteStaffUserBto.getStaffUserBtoList()) {
                Optional<StaffUserBO> any =
                        staffBO.getStaffUserBOSet().stream()
                                .filter(
                                        staffUserBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                staffUserBOSet.getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    staffBO.getStaffUserBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToStaffUser());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 保存员工诊室信息列表 */
    @AutoGenerated(locked = true)
    protected MergeStaffConsultingRoomBoResult mergeStaffConsultingRoomBase(
            MergeStaffConsultingRoomBto mergeStaffConsultingRoomBto) {
        if (mergeStaffConsultingRoomBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeStaffConsultingRoomBoResult boResult = new MergeStaffConsultingRoomBoResult();
        StaffBO staffBO =
                createMergeStaffConsultingRoomOnDuplicateUpdate(
                        boResult, mergeStaffConsultingRoomBto);
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeStaffConsultingRoomBto, "__$validPropertySet"),
                    "staffConsultingRoomBtoList")) {
                createStaffConsultingRoomBtoOnDuplicateUpdate(
                        boResult, mergeStaffConsultingRoomBto, staffBO);
            }
        }
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 编辑员工信息 */
    @AutoGenerated(locked = true)
    protected MergeStaffDetailBoResult mergeStaffDetailBase(
            MergeStaffDetailBto mergeStaffDetailBto) {
        if (mergeStaffDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeStaffDetailBoResult boResult = new MergeStaffDetailBoResult();
        StaffBO staffBO = createMergeStaffDetailOnDuplicateUpdate(boResult, mergeStaffDetailBto);
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffExtensionBto")) {
                createStaffExtensionBtoOnDuplicateUpdate(boResult, mergeStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffPracticeBtoList")) {
                createStaffPracticeBtoOnDuplicateUpdate(boResult, mergeStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffEducationBtoList")) {
                createStaffEducationBtoOnDuplicateUpdate(boResult, mergeStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffWorkExperienceBtoList")) {
                createStaffWorkExperienceBtoOnDuplicateUpdate(
                        boResult, mergeStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffEmploymentBtoList")) {
                createStaffEmploymentBtoOnDuplicateUpdate(boResult, mergeStaffDetailBto, staffBO);
            }
        }
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStaffDetailBto, "__$validPropertySet"),
                    "staffOrganizationBtoList")) {
                createStaffOrganizationBtoOnDuplicateUpdate(boResult, mergeStaffDetailBto, staffBO);
            }
        }
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 更新对象:createStaffUser,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private StaffBO updateCreateStaffUserOnMissThrowEx(
            BaseStaffBOService.CreateStaffUserBoResult boResult,
            CreateStaffUserBto createStaffUserBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (createStaffUserBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(createStaffUserBto.getId());
            found = true;
        }
        if (staffBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(staffBO.convertToStaff());
            updatedBto.setBto(createStaffUserBto);
            updatedBto.setBo(staffBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "organizationId")) {
                staffBO.setOrganizationId(createStaffUserBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "accountingOrganizationId")) {
                staffBO.setAccountingOrganizationId(
                        createStaffUserBto.getAccountingOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "hrOrganizationId")) {
                staffBO.setHrOrganizationId(createStaffUserBto.getHrOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "positionId")) {
                staffBO.setPositionId(createStaffUserBto.getPositionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "promotionDate")) {
                staffBO.setPromotionDate(createStaffUserBto.getPromotionDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "registerTypeList")) {
                staffBO.setRegisterTypeList(createStaffUserBto.getRegisterTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                staffBO.setProvincePlatformCancelFlag(
                        createStaffUserBto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "countrysideStartDate")) {
                staffBO.setCountrysideStartDate(createStaffUserBto.getCountrysideStartDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "doctorRemark")) {
                staffBO.setDoctorRemark(createStaffUserBto.getDoctorRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "registerDoctorFlag")) {
                staffBO.setRegisterDoctorFlag(createStaffUserBto.getRegisterDoctorFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "countrysideEndDate")) {
                staffBO.setCountrysideEndDate(createStaffUserBto.getCountrysideEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "registerDoctorEnableFlag")) {
                staffBO.setRegisterDoctorEnableFlag(
                        createStaffUserBto.getRegisterDoctorEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createStaffUserBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(createStaffUserBto.getCampusOrganizationId());
            }
            return staffBO;
        }
    }

    /** 更新对象:deleteStaffUser,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private StaffBO updateDeleteStaffUserOnMissThrowEx(
            BaseStaffBOService.DeleteStaffUserBoResult boResult,
            DeleteStaffUserBto deleteStaffUserBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteStaffUserBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(deleteStaffUserBto.getId());
            found = true;
        }
        if (staffBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(staffBO.convertToStaff());
            updatedBto.setBto(deleteStaffUserBto);
            updatedBto.setBo(staffBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "organizationId")) {
                staffBO.setOrganizationId(deleteStaffUserBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "accountingOrganizationId")) {
                staffBO.setAccountingOrganizationId(
                        deleteStaffUserBto.getAccountingOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "hrOrganizationId")) {
                staffBO.setHrOrganizationId(deleteStaffUserBto.getHrOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "positionId")) {
                staffBO.setPositionId(deleteStaffUserBto.getPositionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "promotionDate")) {
                staffBO.setPromotionDate(deleteStaffUserBto.getPromotionDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "registerTypeList")) {
                staffBO.setRegisterTypeList(deleteStaffUserBto.getRegisterTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                staffBO.setProvincePlatformCancelFlag(
                        deleteStaffUserBto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "countrysideStartDate")) {
                staffBO.setCountrysideStartDate(deleteStaffUserBto.getCountrysideStartDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "doctorRemark")) {
                staffBO.setDoctorRemark(deleteStaffUserBto.getDoctorRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "registerDoctorFlag")) {
                staffBO.setRegisterDoctorFlag(deleteStaffUserBto.getRegisterDoctorFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "countrysideEndDate")) {
                staffBO.setCountrysideEndDate(deleteStaffUserBto.getCountrysideEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "registerDoctorEnableFlag")) {
                staffBO.setRegisterDoctorEnableFlag(
                        deleteStaffUserBto.getRegisterDoctorEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(deleteStaffUserBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(deleteStaffUserBto.getCampusOrganizationId());
            }
            return staffBO;
        }
    }

    /** 修改挂号医生信息 */
    @AutoGenerated(locked = true)
    protected UpdateRegisterDoctorBoResult updateRegisterDoctorBase(
            UpdateRegisterDoctorBto updateRegisterDoctorBto) {
        if (updateRegisterDoctorBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateRegisterDoctorBoResult boResult = new UpdateRegisterDoctorBoResult();
        StaffBO staffBO =
                updateUpdateRegisterDoctorOnMissThrowEx(boResult, updateRegisterDoctorBto);
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 更新医生挂号启用标志 */
    @AutoGenerated(locked = true)
    protected UpdateRegisterDoctorEnableFlagBoResult updateRegisterDoctorEnableFlagBase(
            UpdateRegisterDoctorEnableFlagBto updateRegisterDoctorEnableFlagBto) {
        if (updateRegisterDoctorEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateRegisterDoctorEnableFlagBoResult boResult =
                new UpdateRegisterDoctorEnableFlagBoResult();
        StaffBO staffBO =
                updateUpdateRegisterDoctorEnableFlagOnMissThrowEx(
                        boResult, updateRegisterDoctorEnableFlagBto);
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 更新对象:staffConsultingRoomBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void updateStaffConsultingRoomBtoOnMissThrowEx(
            BaseStaffBOService.UpdateStaffConsultingRoomEnableFlagBoResult boResult,
            UpdateStaffConsultingRoomEnableFlagBto updateStaffConsultingRoomEnableFlagBto,
            StaffBO staffBO) {
        if (CollectionUtil.isNotEmpty(
                updateStaffConsultingRoomEnableFlagBto.getStaffConsultingRoomBtoList())) {
            for (UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto bto :
                    updateStaffConsultingRoomEnableFlagBto.getStaffConsultingRoomBtoList()) {
                Optional<StaffConsultingRoomBO> any =
                        staffBO.getStaffConsultingRoomBOSet().stream()
                                .filter(
                                        staffConsultingRoomBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (bto.getId() == null);

                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                bto.getId(),
                                                                staffConsultingRoomBOSet.getId());

                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    StaffConsultingRoomBO bo = any.get();
                    UpdatedBto updatedBto = new UpdatedBto();
                    updatedBto.setEntity(bo.convertToStaffConsultingRoom());
                    updatedBto.setBto(bto);
                    updatedBto.setBo(bo);
                    boResult.getUpdatedList().add(updatedBto);
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "enableFlag")) {
                        bo.setEnableFlag(bto.getEnableFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                            "updatedBy")) {
                        bo.setUpdatedBy(bto.getUpdatedBy());
                    }
                } else {
                    throw new IgnoredException(400, "更新失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新员工诊室启用停用 */
    @AutoGenerated(locked = true)
    protected UpdateStaffConsultingRoomEnableFlagBoResult updateStaffConsultingRoomEnableFlagBase(
            UpdateStaffConsultingRoomEnableFlagBto updateStaffConsultingRoomEnableFlagBto) {
        if (updateStaffConsultingRoomEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateStaffConsultingRoomEnableFlagBoResult boResult =
                new UpdateStaffConsultingRoomEnableFlagBoResult();
        StaffBO staffBO =
                updateUpdateStaffConsultingRoomEnableFlagOnMissThrowEx(
                        boResult, updateStaffConsultingRoomEnableFlagBto);
        if (staffBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateStaffConsultingRoomEnableFlagBto, "__$validPropertySet"),
                    "staffConsultingRoomBtoList")) {
                updateStaffConsultingRoomBtoOnMissThrowEx(
                        boResult, updateStaffConsultingRoomEnableFlagBto, staffBO);
            }
        }
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 更新员工状态 */
    @AutoGenerated(locked = true)
    protected UpdateStaffStatusBoResult updateStaffStatusBase(
            UpdateStaffStatusBto updateStaffStatusBto) {
        if (updateStaffStatusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateStaffStatusBoResult boResult = new UpdateStaffStatusBoResult();
        StaffBO staffBO = updateUpdateStaffStatusOnMissThrowEx(boResult, updateStaffStatusBto);
        boResult.setRootBo(staffBO);
        return boResult;
    }

    /** 更新对象:updateRegisterDoctorEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private StaffBO updateUpdateRegisterDoctorEnableFlagOnMissThrowEx(
            BaseStaffBOService.UpdateRegisterDoctorEnableFlagBoResult boResult,
            UpdateRegisterDoctorEnableFlagBto updateRegisterDoctorEnableFlagBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateRegisterDoctorEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(updateRegisterDoctorEnableFlagBto.getId());
            found = true;
        }
        if (staffBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(staffBO.convertToStaff());
            updatedBto.setBto(updateRegisterDoctorEnableFlagBto);
            updatedBto.setBo(staffBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorEnableFlagBto, "__$validPropertySet"),
                    "registerDoctorEnableFlag")) {
                staffBO.setRegisterDoctorEnableFlag(
                        updateRegisterDoctorEnableFlagBto.getRegisterDoctorEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorEnableFlagBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(
                        updateRegisterDoctorEnableFlagBto.getCampusOrganizationId());
            }
            return staffBO;
        }
    }

    /** 更新对象:updateRegisterDoctor,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private StaffBO updateUpdateRegisterDoctorOnMissThrowEx(
            BaseStaffBOService.UpdateRegisterDoctorBoResult boResult,
            UpdateRegisterDoctorBto updateRegisterDoctorBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateRegisterDoctorBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(updateRegisterDoctorBto.getId());
            found = true;
        }
        if (staffBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(staffBO.convertToStaff());
            updatedBto.setBto(updateRegisterDoctorBto);
            updatedBto.setBo(staffBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "professionalTitleId")) {
                staffBO.setProfessionalTitleId(updateRegisterDoctorBto.getProfessionalTitleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "positionId")) {
                staffBO.setPositionId(updateRegisterDoctorBto.getPositionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "registerDoctorFlag")) {
                staffBO.setRegisterDoctorFlag(updateRegisterDoctorBto.getRegisterDoctorFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "registerDoctorEnableFlag")) {
                staffBO.setRegisterDoctorEnableFlag(
                        updateRegisterDoctorBto.getRegisterDoctorEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "doctorRemark")) {
                staffBO.setDoctorRemark(updateRegisterDoctorBto.getDoctorRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "description")) {
                staffBO.setDescription(updateRegisterDoctorBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "promotionDate")) {
                staffBO.setPromotionDate(updateRegisterDoctorBto.getPromotionDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "registerTypeList")) {
                staffBO.setRegisterTypeList(updateRegisterDoctorBto.getRegisterTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                staffBO.setProvincePlatformCancelFlag(
                        updateRegisterDoctorBto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "countrysideStartDate")) {
                staffBO.setCountrysideStartDate(updateRegisterDoctorBto.getCountrysideStartDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "countrysideEndDate")) {
                staffBO.setCountrysideEndDate(updateRegisterDoctorBto.getCountrysideEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "sortNumber")) {
                staffBO.setSortNumber(updateRegisterDoctorBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateRegisterDoctorBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(updateRegisterDoctorBto.getCampusOrganizationId());
            }
            return staffBO;
        }
    }

    /** 更新对象:updateStaffConsultingRoomEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private StaffBO updateUpdateStaffConsultingRoomEnableFlagOnMissThrowEx(
            UpdateStaffConsultingRoomEnableFlagBoResult boResult,
            UpdateStaffConsultingRoomEnableFlagBto updateStaffConsultingRoomEnableFlagBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateStaffConsultingRoomEnableFlagBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(updateStaffConsultingRoomEnableFlagBto.getId());
            found = true;
        }
        if (staffBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(staffBO.convertToStaff());
            updatedBto.setBto(updateStaffConsultingRoomEnableFlagBto);
            updatedBto.setBo(staffBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateStaffConsultingRoomEnableFlagBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(
                        updateStaffConsultingRoomEnableFlagBto.getCampusOrganizationId());
            }
            return staffBO;
        }
    }

    /** 更新对象:updateStaffStatus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private StaffBO updateUpdateStaffStatusOnMissThrowEx(
            BaseStaffBOService.UpdateStaffStatusBoResult boResult,
            UpdateStaffStatusBto updateStaffStatusBto) {
        StaffBO staffBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateStaffStatusBto.getId() == null);
        if (!allNull && !found) {
            staffBO = StaffBO.getById(updateStaffStatusBto.getId());
            found = true;
        }
        if (staffBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(staffBO.convertToStaff());
            updatedBto.setBto(updateStaffStatusBto);
            updatedBto.setBo(staffBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "status")) {
                staffBO.setStatus(updateStaffStatusBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "organizationId")) {
                staffBO.setOrganizationId(updateStaffStatusBto.getOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "accountingOrganizationId")) {
                staffBO.setAccountingOrganizationId(
                        updateStaffStatusBto.getAccountingOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "hrOrganizationId")) {
                staffBO.setHrOrganizationId(updateStaffStatusBto.getHrOrganizationId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "positionId")) {
                staffBO.setPositionId(updateStaffStatusBto.getPositionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "promotionDate")) {
                staffBO.setPromotionDate(updateStaffStatusBto.getPromotionDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "registerTypeList")) {
                staffBO.setRegisterTypeList(updateStaffStatusBto.getRegisterTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "provincePlatformCancelFlag")) {
                staffBO.setProvincePlatformCancelFlag(
                        updateStaffStatusBto.getProvincePlatformCancelFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "countrysideStartDate")) {
                staffBO.setCountrysideStartDate(updateStaffStatusBto.getCountrysideStartDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "doctorRemark")) {
                staffBO.setDoctorRemark(updateStaffStatusBto.getDoctorRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "registerDoctorFlag")) {
                staffBO.setRegisterDoctorFlag(updateStaffStatusBto.getRegisterDoctorFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "countrysideEndDate")) {
                staffBO.setCountrysideEndDate(updateStaffStatusBto.getCountrysideEndDate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "registerDoctorEnableFlag")) {
                staffBO.setRegisterDoctorEnableFlag(
                        updateStaffStatusBto.getRegisterDoctorEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateStaffStatusBto, "__$validPropertySet"),
                    "campusOrganizationId")) {
                staffBO.setCampusOrganizationId(updateStaffStatusBto.getCampusOrganizationId());
            }
            return staffBO;
        }
    }

    public static class CreateStaffUserBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffUserBto.StaffUserBto, StaffUserBO> getCreatedBto(
                CreateStaffUserBto.StaffUserBto staffUserBto) {
            return this.getAddedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffUserBto, StaffBO> getCreatedBto(
                CreateStaffUserBto createStaffUserBto) {
            return this.getAddedResult(createStaffUserBto);
        }

        @AutoGenerated(locked = true)
        public StaffUser getDeleted_StaffUser() {
            return (StaffUser) CollectionUtil.getFirst(this.getDeletedEntityList(StaffUser.class));
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateStaffUserBto.StaffUserBto, StaffUser, StaffUserBO> getUpdatedBto(
                CreateStaffUserBto.StaffUserBto staffUserBto) {
            return super.getUpdatedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateStaffUserBto, Staff, StaffBO> getUpdatedBto(
                CreateStaffUserBto createStaffUserBto) {
            return super.getUpdatedResult(createStaffUserBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffUserBto.StaffUserBto, StaffUserBO> getUnmodifiedBto(
                CreateStaffUserBto.StaffUserBto staffUserBto) {
            return super.getUnmodifiedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffUserBto, StaffBO> getUnmodifiedBto(
                CreateStaffUserBto createStaffUserBto) {
            return super.getUnmodifiedResult(createStaffUserBto);
        }
    }

    public static class DeleteStaffUserBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteStaffUserBto.StaffUserBto, StaffUserBO> getCreatedBto(
                DeleteStaffUserBto.StaffUserBto staffUserBto) {
            return this.getAddedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteStaffUserBto, StaffBO> getCreatedBto(
                DeleteStaffUserBto deleteStaffUserBto) {
            return this.getAddedResult(deleteStaffUserBto);
        }

        @AutoGenerated(locked = true)
        public StaffUser getDeleted_StaffUser() {
            return (StaffUser) CollectionUtil.getFirst(this.getDeletedEntityList(StaffUser.class));
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteStaffUserBto.StaffUserBto, StaffUser, StaffUserBO> getUpdatedBto(
                DeleteStaffUserBto.StaffUserBto staffUserBto) {
            return super.getUpdatedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteStaffUserBto, Staff, StaffBO> getUpdatedBto(
                DeleteStaffUserBto deleteStaffUserBto) {
            return super.getUpdatedResult(deleteStaffUserBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteStaffUserBto.StaffUserBto, StaffUserBO> getUnmodifiedBto(
                DeleteStaffUserBto.StaffUserBto staffUserBto) {
            return super.getUnmodifiedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteStaffUserBto, StaffBO> getUnmodifiedBto(
                DeleteStaffUserBto deleteStaffUserBto) {
            return super.getUnmodifiedResult(deleteStaffUserBto);
        }
    }

    public static class MergeStaffDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffDetailBto, StaffBO> getCreatedBto(
                MergeStaffDetailBto mergeStaffDetailBto) {
            return this.getAddedResult(mergeStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffDetailBto.StaffExtensionBto, StaffExtensionBO> getCreatedBto(
                MergeStaffDetailBto.StaffExtensionBto staffExtensionBto) {
            return this.getAddedResult(staffExtensionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffDetailBto.StaffPracticeBto, StaffPracticeBO> getCreatedBto(
                MergeStaffDetailBto.StaffPracticeBto staffPracticeBto) {
            return this.getAddedResult(staffPracticeBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffDetailBto.StaffEducationBto, StaffEducationBO> getCreatedBto(
                MergeStaffDetailBto.StaffEducationBto staffEducationBto) {
            return this.getAddedResult(staffEducationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffDetailBto.StaffWorkExperienceBto, StaffWorkExperienceBO>
                getCreatedBto(MergeStaffDetailBto.StaffWorkExperienceBto staffWorkExperienceBto) {
            return this.getAddedResult(staffWorkExperienceBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffDetailBto.StaffEmploymentBto, StaffEmploymentBO> getCreatedBto(
                MergeStaffDetailBto.StaffEmploymentBto staffEmploymentBto) {
            return this.getAddedResult(staffEmploymentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffDetailBto.StaffOrganizationBto, StaffOrganizationBO>
                getCreatedBto(MergeStaffDetailBto.StaffOrganizationBto staffOrganizationBto) {
            return this.getAddedResult(staffOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public StaffExtension getDeleted_StaffExtension() {
            return (StaffExtension)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffExtension.class));
        }

        @AutoGenerated(locked = true)
        public StaffPractice getDeleted_StaffPractice() {
            return (StaffPractice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffPractice.class));
        }

        @AutoGenerated(locked = true)
        public StaffEducation getDeleted_StaffEducation() {
            return (StaffEducation)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffEducation.class));
        }

        @AutoGenerated(locked = true)
        public StaffWorkExperience getDeleted_StaffWorkExperience() {
            return (StaffWorkExperience)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffWorkExperience.class));
        }

        @AutoGenerated(locked = true)
        public StaffEmployment getDeleted_StaffEmployment() {
            return (StaffEmployment)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffEmployment.class));
        }

        @AutoGenerated(locked = true)
        public StaffOrganization getDeleted_StaffOrganization() {
            return (StaffOrganization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffOrganization.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeStaffDetailBto, Staff, StaffBO> getUpdatedBto(
                MergeStaffDetailBto mergeStaffDetailBto) {
            return super.getUpdatedResult(mergeStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeStaffDetailBto.StaffExtensionBto, StaffExtension, StaffExtensionBO>
                getUpdatedBto(MergeStaffDetailBto.StaffExtensionBto staffExtensionBto) {
            return super.getUpdatedResult(staffExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeStaffDetailBto.StaffPracticeBto, StaffPractice, StaffPracticeBO>
                getUpdatedBto(MergeStaffDetailBto.StaffPracticeBto staffPracticeBto) {
            return super.getUpdatedResult(staffPracticeBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeStaffDetailBto.StaffEducationBto, StaffEducation, StaffEducationBO>
                getUpdatedBto(MergeStaffDetailBto.StaffEducationBto staffEducationBto) {
            return super.getUpdatedResult(staffEducationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeStaffDetailBto.StaffWorkExperienceBto,
                        StaffWorkExperience,
                        StaffWorkExperienceBO>
                getUpdatedBto(MergeStaffDetailBto.StaffWorkExperienceBto staffWorkExperienceBto) {
            return super.getUpdatedResult(staffWorkExperienceBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeStaffDetailBto.StaffEmploymentBto, StaffEmployment, StaffEmploymentBO>
                getUpdatedBto(MergeStaffDetailBto.StaffEmploymentBto staffEmploymentBto) {
            return super.getUpdatedResult(staffEmploymentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeStaffDetailBto.StaffOrganizationBto,
                        StaffOrganization,
                        StaffOrganizationBO>
                getUpdatedBto(MergeStaffDetailBto.StaffOrganizationBto staffOrganizationBto) {
            return super.getUpdatedResult(staffOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffDetailBto, StaffBO> getUnmodifiedBto(
                MergeStaffDetailBto mergeStaffDetailBto) {
            return super.getUnmodifiedResult(mergeStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffDetailBto.StaffExtensionBto, StaffExtensionBO>
                getUnmodifiedBto(MergeStaffDetailBto.StaffExtensionBto staffExtensionBto) {
            return super.getUnmodifiedResult(staffExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffDetailBto.StaffPracticeBto, StaffPracticeBO>
                getUnmodifiedBto(MergeStaffDetailBto.StaffPracticeBto staffPracticeBto) {
            return super.getUnmodifiedResult(staffPracticeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffDetailBto.StaffEducationBto, StaffEducationBO>
                getUnmodifiedBto(MergeStaffDetailBto.StaffEducationBto staffEducationBto) {
            return super.getUnmodifiedResult(staffEducationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffDetailBto.StaffWorkExperienceBto, StaffWorkExperienceBO>
                getUnmodifiedBto(
                        MergeStaffDetailBto.StaffWorkExperienceBto staffWorkExperienceBto) {
            return super.getUnmodifiedResult(staffWorkExperienceBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffDetailBto.StaffEmploymentBto, StaffEmploymentBO>
                getUnmodifiedBto(MergeStaffDetailBto.StaffEmploymentBto staffEmploymentBto) {
            return super.getUnmodifiedResult(staffEmploymentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffDetailBto.StaffOrganizationBto, StaffOrganizationBO>
                getUnmodifiedBto(MergeStaffDetailBto.StaffOrganizationBto staffOrganizationBto) {
            return super.getUnmodifiedResult(staffOrganizationBto);
        }
    }

    public static class CreateStaffDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto.StaffExtensionBto, StaffExtensionBO> getCreatedBto(
                CreateStaffDetailBto.StaffExtensionBto staffExtensionBto) {
            return this.getAddedResult(staffExtensionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto.StaffPracticeBto, StaffPracticeBO> getCreatedBto(
                CreateStaffDetailBto.StaffPracticeBto staffPracticeBto) {
            return this.getAddedResult(staffPracticeBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto.StaffEducationBto, StaffEducationBO> getCreatedBto(
                CreateStaffDetailBto.StaffEducationBto staffEducationBto) {
            return this.getAddedResult(staffEducationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto.StaffWorkExperienceBto, StaffWorkExperienceBO>
                getCreatedBto(CreateStaffDetailBto.StaffWorkExperienceBto staffWorkExperienceBto) {
            return this.getAddedResult(staffWorkExperienceBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto.StaffEmploymentBto, StaffEmploymentBO> getCreatedBto(
                CreateStaffDetailBto.StaffEmploymentBto staffEmploymentBto) {
            return this.getAddedResult(staffEmploymentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto.StaffOrganizationBto, StaffOrganizationBO>
                getCreatedBto(CreateStaffDetailBto.StaffOrganizationBto staffOrganizationBto) {
            return this.getAddedResult(staffOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto, StaffBO> getCreatedBto(
                CreateStaffDetailBto createStaffDetailBto) {
            return this.getAddedResult(createStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateStaffDetailBto.StaffUserBto, StaffUserBO> getCreatedBto(
                CreateStaffDetailBto.StaffUserBto staffUserBto) {
            return this.getAddedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public StaffExtension getDeleted_StaffExtension() {
            return (StaffExtension)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffExtension.class));
        }

        @AutoGenerated(locked = true)
        public StaffPractice getDeleted_StaffPractice() {
            return (StaffPractice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffPractice.class));
        }

        @AutoGenerated(locked = true)
        public StaffEducation getDeleted_StaffEducation() {
            return (StaffEducation)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffEducation.class));
        }

        @AutoGenerated(locked = true)
        public StaffWorkExperience getDeleted_StaffWorkExperience() {
            return (StaffWorkExperience)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffWorkExperience.class));
        }

        @AutoGenerated(locked = true)
        public StaffEmployment getDeleted_StaffEmployment() {
            return (StaffEmployment)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffEmployment.class));
        }

        @AutoGenerated(locked = true)
        public StaffOrganization getDeleted_StaffOrganization() {
            return (StaffOrganization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public StaffUser getDeleted_StaffUser() {
            return (StaffUser) CollectionUtil.getFirst(this.getDeletedEntityList(StaffUser.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateStaffDetailBto.StaffExtensionBto, StaffExtension, StaffExtensionBO>
                getUpdatedBto(CreateStaffDetailBto.StaffExtensionBto staffExtensionBto) {
            return super.getUpdatedResult(staffExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateStaffDetailBto.StaffPracticeBto, StaffPractice, StaffPracticeBO>
                getUpdatedBto(CreateStaffDetailBto.StaffPracticeBto staffPracticeBto) {
            return super.getUpdatedResult(staffPracticeBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateStaffDetailBto.StaffEducationBto, StaffEducation, StaffEducationBO>
                getUpdatedBto(CreateStaffDetailBto.StaffEducationBto staffEducationBto) {
            return super.getUpdatedResult(staffEducationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateStaffDetailBto.StaffWorkExperienceBto,
                        StaffWorkExperience,
                        StaffWorkExperienceBO>
                getUpdatedBto(CreateStaffDetailBto.StaffWorkExperienceBto staffWorkExperienceBto) {
            return super.getUpdatedResult(staffWorkExperienceBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateStaffDetailBto.StaffEmploymentBto, StaffEmployment, StaffEmploymentBO>
                getUpdatedBto(CreateStaffDetailBto.StaffEmploymentBto staffEmploymentBto) {
            return super.getUpdatedResult(staffEmploymentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateStaffDetailBto.StaffOrganizationBto,
                        StaffOrganization,
                        StaffOrganizationBO>
                getUpdatedBto(CreateStaffDetailBto.StaffOrganizationBto staffOrganizationBto) {
            return super.getUpdatedResult(staffOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateStaffDetailBto, Staff, StaffBO> getUpdatedBto(
                CreateStaffDetailBto createStaffDetailBto) {
            return super.getUpdatedResult(createStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateStaffDetailBto.StaffUserBto, StaffUser, StaffUserBO> getUpdatedBto(
                CreateStaffDetailBto.StaffUserBto staffUserBto) {
            return super.getUpdatedResult(staffUserBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto.StaffExtensionBto, StaffExtensionBO>
                getUnmodifiedBto(CreateStaffDetailBto.StaffExtensionBto staffExtensionBto) {
            return super.getUnmodifiedResult(staffExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto.StaffPracticeBto, StaffPracticeBO>
                getUnmodifiedBto(CreateStaffDetailBto.StaffPracticeBto staffPracticeBto) {
            return super.getUnmodifiedResult(staffPracticeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto.StaffEducationBto, StaffEducationBO>
                getUnmodifiedBto(CreateStaffDetailBto.StaffEducationBto staffEducationBto) {
            return super.getUnmodifiedResult(staffEducationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto.StaffWorkExperienceBto, StaffWorkExperienceBO>
                getUnmodifiedBto(
                        CreateStaffDetailBto.StaffWorkExperienceBto staffWorkExperienceBto) {
            return super.getUnmodifiedResult(staffWorkExperienceBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto.StaffEmploymentBto, StaffEmploymentBO>
                getUnmodifiedBto(CreateStaffDetailBto.StaffEmploymentBto staffEmploymentBto) {
            return super.getUnmodifiedResult(staffEmploymentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto.StaffOrganizationBto, StaffOrganizationBO>
                getUnmodifiedBto(CreateStaffDetailBto.StaffOrganizationBto staffOrganizationBto) {
            return super.getUnmodifiedResult(staffOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto, StaffBO> getUnmodifiedBto(
                CreateStaffDetailBto createStaffDetailBto) {
            return super.getUnmodifiedResult(createStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateStaffDetailBto.StaffUserBto, StaffUserBO> getUnmodifiedBto(
                CreateStaffDetailBto.StaffUserBto staffUserBto) {
            return super.getUnmodifiedResult(staffUserBto);
        }
    }

    public static class DeleteStaffDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteStaffDetailBto, StaffBO> getCreatedBto(
                DeleteStaffDetailBto deleteStaffDetailBto) {
            return this.getAddedResult(deleteStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteStaffDetailBto, Staff, StaffBO> getUpdatedBto(
                DeleteStaffDetailBto deleteStaffDetailBto) {
            return super.getUpdatedResult(deleteStaffDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteStaffDetailBto, StaffBO> getUnmodifiedBto(
                DeleteStaffDetailBto deleteStaffDetailBto) {
            return super.getUnmodifiedResult(deleteStaffDetailBto);
        }
    }

    public static class UpdateStaffStatusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateStaffStatusBto, StaffBO> getCreatedBto(
                UpdateStaffStatusBto updateStaffStatusBto) {
            return this.getAddedResult(updateStaffStatusBto);
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateStaffStatusBto, Staff, StaffBO> getUpdatedBto(
                UpdateStaffStatusBto updateStaffStatusBto) {
            return super.getUpdatedResult(updateStaffStatusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateStaffStatusBto, StaffBO> getUnmodifiedBto(
                UpdateStaffStatusBto updateStaffStatusBto) {
            return super.getUnmodifiedResult(updateStaffStatusBto);
        }
    }

    public static class UpdateRegisterDoctorBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateRegisterDoctorBto, StaffBO> getCreatedBto(
                UpdateRegisterDoctorBto updateRegisterDoctorBto) {
            return this.getAddedResult(updateRegisterDoctorBto);
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateRegisterDoctorBto, Staff, StaffBO> getUpdatedBto(
                UpdateRegisterDoctorBto updateRegisterDoctorBto) {
            return super.getUpdatedResult(updateRegisterDoctorBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateRegisterDoctorBto, StaffBO> getUnmodifiedBto(
                UpdateRegisterDoctorBto updateRegisterDoctorBto) {
            return super.getUnmodifiedResult(updateRegisterDoctorBto);
        }
    }

    public static class UpdateRegisterDoctorEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateRegisterDoctorEnableFlagBto, StaffBO> getCreatedBto(
                UpdateRegisterDoctorEnableFlagBto updateRegisterDoctorEnableFlagBto) {
            return this.getAddedResult(updateRegisterDoctorEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateRegisterDoctorEnableFlagBto, Staff, StaffBO> getUpdatedBto(
                UpdateRegisterDoctorEnableFlagBto updateRegisterDoctorEnableFlagBto) {
            return super.getUpdatedResult(updateRegisterDoctorEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateRegisterDoctorEnableFlagBto, StaffBO> getUnmodifiedBto(
                UpdateRegisterDoctorEnableFlagBto updateRegisterDoctorEnableFlagBto) {
            return super.getUnmodifiedResult(updateRegisterDoctorEnableFlagBto);
        }
    }

    public static class MergeStaffConsultingRoomBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffConsultingRoomBto.StaffConsultingRoomBto, StaffConsultingRoomBO>
                getCreatedBto(
                        MergeStaffConsultingRoomBto.StaffConsultingRoomBto staffConsultingRoomBto) {
            return this.getAddedResult(staffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStaffConsultingRoomBto, StaffBO> getCreatedBto(
                MergeStaffConsultingRoomBto mergeStaffConsultingRoomBto) {
            return this.getAddedResult(mergeStaffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public StaffConsultingRoom getDeleted_StaffConsultingRoom() {
            return (StaffConsultingRoom)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffConsultingRoom.class));
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        MergeStaffConsultingRoomBto.StaffConsultingRoomBto,
                        StaffConsultingRoom,
                        StaffConsultingRoomBO>
                getUpdatedBto(
                        MergeStaffConsultingRoomBto.StaffConsultingRoomBto staffConsultingRoomBto) {
            return super.getUpdatedResult(staffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeStaffConsultingRoomBto, Staff, StaffBO> getUpdatedBto(
                MergeStaffConsultingRoomBto mergeStaffConsultingRoomBto) {
            return super.getUpdatedResult(mergeStaffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        MergeStaffConsultingRoomBto.StaffConsultingRoomBto, StaffConsultingRoomBO>
                getUnmodifiedBto(
                        MergeStaffConsultingRoomBto.StaffConsultingRoomBto staffConsultingRoomBto) {
            return super.getUnmodifiedResult(staffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStaffConsultingRoomBto, StaffBO> getUnmodifiedBto(
                MergeStaffConsultingRoomBto mergeStaffConsultingRoomBto) {
            return super.getUnmodifiedResult(mergeStaffConsultingRoomBto);
        }
    }

    public static class UpdateStaffConsultingRoomEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public StaffBO getRootBo() {
            return (StaffBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto,
                        StaffConsultingRoomBO>
                getCreatedBto(
                        UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto
                                staffConsultingRoomBto) {
            return this.getAddedResult(staffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateStaffConsultingRoomEnableFlagBto, StaffBO> getCreatedBto(
                UpdateStaffConsultingRoomEnableFlagBto updateStaffConsultingRoomEnableFlagBto) {
            return this.getAddedResult(updateStaffConsultingRoomEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public StaffConsultingRoom getDeleted_StaffConsultingRoom() {
            return (StaffConsultingRoom)
                    CollectionUtil.getFirst(this.getDeletedEntityList(StaffConsultingRoom.class));
        }

        @AutoGenerated(locked = true)
        public Staff getDeleted_Staff() {
            return (Staff) CollectionUtil.getFirst(this.getDeletedEntityList(Staff.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto,
                        StaffConsultingRoom,
                        StaffConsultingRoomBO>
                getUpdatedBto(
                        UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto
                                staffConsultingRoomBto) {
            return super.getUpdatedResult(staffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateStaffConsultingRoomEnableFlagBto, Staff, StaffBO> getUpdatedBto(
                UpdateStaffConsultingRoomEnableFlagBto updateStaffConsultingRoomEnableFlagBto) {
            return super.getUpdatedResult(updateStaffConsultingRoomEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto,
                        StaffConsultingRoomBO>
                getUnmodifiedBto(
                        UpdateStaffConsultingRoomEnableFlagBto.StaffConsultingRoomBto
                                staffConsultingRoomBto) {
            return super.getUnmodifiedResult(staffConsultingRoomBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateStaffConsultingRoomEnableFlagBto, StaffBO> getUnmodifiedBto(
                UpdateStaffConsultingRoomEnableFlagBto updateStaffConsultingRoomEnableFlagBto) {
            return super.getUnmodifiedResult(updateStaffConsultingRoomEnableFlagBto);
        }
    }
}
