package com.pulse.organization.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.common.enums.UseScopeEnum;
import com.pulse.organization.common.enums.TeamCollaborationScopeEnum;
import com.pulse.organization.common.enums.TeamRoleEnum;
import com.pulse.organization.common.enums.TeamStatusEnum;
import com.pulse.organization.common.enums.TeamTagEnum;
import com.pulse.organization.common.enums.TeamTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> Team
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "affc9e17-264d-4008-a3b3-e70c78c9b86a|BTO|DEFINITION")
public class CreateTeamBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 协作范围类型 */
    @AutoGenerated(locked = true, uuid = "7fe7a296-0991-4f4d-8cf4-459d0163c79d")
    private TeamCollaborationScopeEnum collaborationScope;

    /** 创建者ID */
    @AutoGenerated(locked = true, uuid = "5e70e61d-d21d-42a3-8b69-e87fc84e2916")
    private String createdBy;

    /** 删除者ID */
    @AutoGenerated(locked = true, uuid = "1fc26619-f68a-4c3c-9583-ced4e8b50969")
    private String deletedBy;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "25c654b2-04af-4038-99ea-9255931839a9")
    private String description;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "3c5c69b9-4ca0-4bba-a3e3-ef232359d212")
    private InputCodeEo inputCode;

    /** 牵头组织ID */
    @AutoGenerated(locked = true, uuid = "7f0daeec-3141-4d79-9c2a-02ad654789de")
    private String leadOrganizationId;

    /** MDT自选科室标志 */
    @AutoGenerated(locked = true, uuid = "32afdf6e-523c-45c3-9f4c-3e77d20382d3")
    private Boolean mdtSelfSelectFlag;

    /** 团队名称 */
    @AutoGenerated(locked = true, uuid = "7dfb13a0-5f51-4386-87cd-16221765ef06")
    private String name;

    /** 排序 */
    @AutoGenerated(locked = true, uuid = "8ef4d6c6-cba6-4031-9386-75cb48fc3109")
    private Long sortNumber;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "*************-4642-b15a-2324d9e36f57")
    private TeamStatusEnum status;

    @Valid
    @AutoGenerated(locked = true, uuid = "979e9cf2-3358-4ac0-b2c9-b1dc51dff621")
    private List<CreateTeamBto.TeamMemberBto> teamMemberBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "5a4806dc-d0f3-4fa2-9d36-bf4b8252e454")
    private List<CreateTeamBto.TeamOrganizationBto> teamOrganizationBtoList;

    /** 团队类型 */
    @AutoGenerated(locked = true, uuid = "e6c64fa6-4f3a-4f37-8160-c61d03a11a6b")
    private TeamTypeEnum type;

    /** 更新者ID */
    @AutoGenerated(locked = true, uuid = "b7893bf8-54c2-49a8-9b59-9f277a047539")
    private String updatedBy;

    /** 适用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "5b76923d-cbef-4563-bbd6-7909aaff439c")
    private List<UseScopeEnum> useScopeList;

    @AutoGenerated(locked = true)
    public void setCollaborationScope(TeamCollaborationScopeEnum collaborationScope) {
        this.__$validPropertySet.add("collaborationScope");
        this.collaborationScope = collaborationScope;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setDeletedBy(String deletedBy) {
        this.__$validPropertySet.add("deletedBy");
        this.deletedBy = deletedBy;
    }

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setLeadOrganizationId(String leadOrganizationId) {
        this.__$validPropertySet.add("leadOrganizationId");
        this.leadOrganizationId = leadOrganizationId;
    }

    @AutoGenerated(locked = true)
    public void setMdtSelfSelectFlag(Boolean mdtSelfSelectFlag) {
        this.__$validPropertySet.add("mdtSelfSelectFlag");
        this.mdtSelfSelectFlag = mdtSelfSelectFlag;
    }

    @AutoGenerated(locked = true)
    public void setName(String name) {
        this.__$validPropertySet.add("name");
        this.name = name;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setStatus(TeamStatusEnum status) {
        this.__$validPropertySet.add("status");
        this.status = status;
    }

    @AutoGenerated(locked = true)
    public void setTeamMemberBtoList(List<CreateTeamBto.TeamMemberBto> teamMemberBtoList) {
        this.__$validPropertySet.add("teamMemberBtoList");
        this.teamMemberBtoList = teamMemberBtoList;
    }

    @AutoGenerated(locked = true)
    public void setTeamOrganizationBtoList(
            List<CreateTeamBto.TeamOrganizationBto> teamOrganizationBtoList) {
        this.__$validPropertySet.add("teamOrganizationBtoList");
        this.teamOrganizationBtoList = teamOrganizationBtoList;
    }

    @AutoGenerated(locked = true)
    public void setType(TeamTypeEnum type) {
        this.__$validPropertySet.add("type");
        this.type = type;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    @AutoGenerated(locked = true)
    public void setUseScope(List<UseScopeEnum> useScope) {
        this.__$validPropertySet.add("useScopeList");
        this.useScopeList = useScope;
    }

    @AutoGenerated(locked = true)
    public void setUseScopeList(List<UseScopeEnum> useScopeList) {
        this.__$validPropertySet.add("useScopeList");
        this.useScopeList = useScopeList;
    }

    /**
     * <b>[源自]</b> TeamMember
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class TeamMemberBto {
        /** 员工ID */
        @AutoGenerated(locked = true, uuid = "3228687d-777b-43cd-8705-b82724d1da25")
        private String staffId;

        /** 身份标签(组长、组员) */
        @AutoGenerated(locked = true, uuid = "83752528-3cf9-40c7-9877-5db174198df4")
        private TeamTagEnum identityTag;

        /** 角色 */
        @AutoGenerated(locked = true, uuid = "577021fa-b529-4021-9491-fd9b9acfe21e")
        private TeamRoleEnum role;

        /** 更新时间 */
        @AutoGenerated(locked = true, uuid = "0f5a633d-4604-4611-bad3-0a8b479d5d44")
        private Date updatedAt;

        /** 创建者ID */
        @AutoGenerated(locked = true, uuid = "387aa405-8f1a-48d1-acd2-a7d03ebe25b7")
        private String createdBy;

        /** 更新者ID */
        @AutoGenerated(locked = true, uuid = "5ef27f89-0be5-4255-8302-18b82eb767c6")
        private String updatedBy;

        /** 删除者ID */
        @AutoGenerated(locked = true, uuid = "423757d2-2595-4673-b1cb-bbe3a36f1bd3")
        private String deletedBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setStaffId(String staffId) {
            this.__$validPropertySet.add("staffId");
            this.staffId = staffId;
        }

        @AutoGenerated(locked = true)
        public void setIdentityTag(TeamTagEnum identityTag) {
            this.__$validPropertySet.add("identityTag");
            this.identityTag = identityTag;
        }

        @AutoGenerated(locked = true)
        public void setRole(TeamRoleEnum role) {
            this.__$validPropertySet.add("role");
            this.role = role;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedAt(Date updatedAt) {
            this.__$validPropertySet.add("updatedAt");
            this.updatedAt = updatedAt;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setDeletedBy(String deletedBy) {
            this.__$validPropertySet.add("deletedBy");
            this.deletedBy = deletedBy;
        }
    }

    /**
     * <b>[源自]</b> TeamOrganization
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class TeamOrganizationBto {
        /** 组织ID */
        @AutoGenerated(locked = true, uuid = "1e4ad096-0011-48df-bc66-3fea8b959fe7")
        private String organizationId;

        /** 排序编号 */
        @AutoGenerated(locked = true, uuid = "2045da89-c9c9-4e35-bb4c-a23707c60d27")
        private Long sortNumber;

        /** 挂号类别费范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "ed333d3d-d724-49e4-9b77-37e033a9f7d4")
        private List<String> registerTypeIdList;

        /** 默认标志 */
        @AutoGenerated(locked = true, uuid = "7ea21fd9-84f9-4447-8182-4e269a9004c7")
        private Boolean defaultFlag;

        /** 创建者ID */
        @AutoGenerated(locked = true, uuid = "045b9fa7-392a-4da7-842b-477f61402234")
        private String createdBy;

        /** 更新者ID */
        @AutoGenerated(locked = true, uuid = "220a0b93-e0c9-40a5-aa8b-6505db29be53")
        private String updatedBy;

        /** 删除者ID */
        @AutoGenerated(locked = true, uuid = "c9983df9-4cbd-4b82-b671-6e637c621829")
        private String deletedBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setOrganizationId(String organizationId) {
            this.__$validPropertySet.add("organizationId");
            this.organizationId = organizationId;
        }

        @AutoGenerated(locked = true)
        public void setSortNumber(Long sortNumber) {
            this.__$validPropertySet.add("sortNumber");
            this.sortNumber = sortNumber;
        }

        @AutoGenerated(locked = true)
        public void setRegisterTypeIdList(List<String> registerTypeIdList) {
            this.__$validPropertySet.add("registerTypeIdList");
            this.registerTypeIdList = registerTypeIdList;
        }

        @AutoGenerated(locked = true)
        public void setDefaultFlag(Boolean defaultFlag) {
            this.__$validPropertySet.add("defaultFlag");
            this.defaultFlag = defaultFlag;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setDeletedBy(String deletedBy) {
            this.__$validPropertySet.add("deletedBy");
            this.deletedBy = deletedBy;
        }
    }
}
