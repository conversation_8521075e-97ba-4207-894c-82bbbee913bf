package com.pulse.organization.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.dto.OrganizationRelationshipBaseDto;
import com.pulse.organization.persist.dos.OrganizationRelationship;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "a04b7026-f685-4de9-b128-5349da11a8cf|DTO|BASE_CONVERTER")
public class OrganizationRelationshipBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public OrganizationRelationshipBaseDto
            convertFromOrganizationRelationshipToOrganizationRelationshipBaseDto(
                    OrganizationRelationship organizationRelationship) {
        return convertFromOrganizationRelationshipToOrganizationRelationshipBaseDto(
                        List.of(organizationRelationship))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OrganizationRelationshipBaseDto>
            convertFromOrganizationRelationshipToOrganizationRelationshipBaseDto(
                    List<OrganizationRelationship> organizationRelationshipList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(organizationRelationshipList)) {
            return new ArrayList<>();
        }
        List<OrganizationRelationshipBaseDto> organizationRelationshipBaseDtoList =
                new ArrayList<>();
        for (OrganizationRelationship organizationRelationship : organizationRelationshipList) {
            if (organizationRelationship == null) {
                continue;
            }
            OrganizationRelationshipBaseDto organizationRelationshipBaseDto =
                    new OrganizationRelationshipBaseDto();
            organizationRelationshipBaseDto.setId(organizationRelationship.getId());
            organizationRelationshipBaseDto.setSourceOrganizationType(
                    organizationRelationship.getSourceOrganizationType());
            organizationRelationshipBaseDto.setSourceOrganizationId(
                    organizationRelationship.getSourceOrganizationId());
            organizationRelationshipBaseDto.setTargetOrganizationType(
                    organizationRelationship.getTargetOrganizationType());
            organizationRelationshipBaseDto.setTargetOrganizationId(
                    organizationRelationship.getTargetOrganizationId());
            organizationRelationshipBaseDto.setLockVersion(
                    organizationRelationship.getLockVersion());
            organizationRelationshipBaseDto.setCreatedAt(organizationRelationship.getCreatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            organizationRelationshipBaseDtoList.add(organizationRelationshipBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return organizationRelationshipBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
