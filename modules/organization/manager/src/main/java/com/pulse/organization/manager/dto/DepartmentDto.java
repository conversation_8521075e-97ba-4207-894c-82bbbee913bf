package com.pulse.organization.manager.dto;

import com.pulse.dictionary_basic.persist.eo.TimePeriodEo;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.organization.common.enums.GenderLimitEnum;
import com.pulse.organization.common.enums.MedicalServiceTypeEnum;
import com.pulse.organization.common.enums.OrganizationPropertyEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.StorageTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "b93cf7e9-e091-4883-9294-6c51053f04e1|DTO|DEFINITION")
public class DepartmentDto {
    /** 核算科室名称 */
    @AutoGenerated(locked = true, uuid = "8ed22127-9274-42bd-b870-9d5207169a5a")
    private String accountingDepartmentName;

    /** 核算科室组织id */
    @AutoGenerated(locked = true, uuid = "6435b648-e13f-430a-b100-a7fb3e0f1db6")
    private String accountingDepartmentOrganizationId;

    /** 年龄下限 */
    @AutoGenerated(locked = true, uuid = "504e1ef9-4924-4666-b9fd-25718d88663a")
    private Long ageLowerLimit;

    /** 年龄上限 */
    @AutoGenerated(locked = true, uuid = "c1c3423a-9c3c-4e4e-96f6-79bf4180026e")
    private Long ageUpperLimit;

    /** 平均处方限额 */
    @AutoGenerated(locked = true, uuid = "279c1642-5c7d-4a55-9527-9c5be16d3bd9")
    private BigDecimal averagePrescriptionLimit;

    /** 院区名称 */
    @AutoGenerated(locked = true, uuid = "4b2b0a93-054c-4267-896c-000d287be0cd")
    private String campusName;

    /** 院区组织id */
    @AutoGenerated(locked = true, uuid = "4c2fd90e-826d-4b5e-aebf-349383a91e62")
    private String campusOrganizationId;

    /** 成本科室名称 */
    @AutoGenerated(locked = true, uuid = "908a3410-713f-4bbf-9749-5d03c28598a1")
    private String costDepartmentName;

    /** 成本科室组织id */
    @AutoGenerated(locked = true, uuid = "fbc4805b-4c8b-41ba-80a0-233c6fdd04ad")
    private String costDepartmentOrganizationId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9de10603-4aa7-4a5b-bbad-f3244bfe6378")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "baca6825-80c0-471a-b470-0043878fcdcf")
    private String createdBy;

    /** 默认处方类型 */
    @AutoGenerated(locked = true, uuid = "c585c305-3fd3-4e93-ab36-c60e208702d8")
    private String defaultPrescriptionType;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "12681fa8-d467-4410-a952-550904bbf7c0")
    private Long deletedAt;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "0d9148b9-a55c-481f-9b36-66e6b8216b4b")
    private String deletedBy;

    /** 科室层级 */
    @AutoGenerated(locked = true, uuid = "88f26bc3-d0ca-4487-a1ce-323960edad5d")
    private Long departmentLevel;

    /** 科室性质 */
    @AutoGenerated(locked = true, uuid = "996572a5-27d4-4ed3-b73e-5038f8837f29")
    private OrganizationPropertyEnum departmentProperty;

    /** 药品分类列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "99cf3320-63d6-4d95-81e0-cafb7b88cd05")
    private List<String> drugCatalogList;

    /** 药物类别列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d68cae25-c862-4d6e-9674-802be9a7d717")
    private List<DrugTypeEnum> drugTypeList;

    /** 性别限制 */
    @AutoGenerated(locked = true, uuid = "98ce1dea-6513-4c03-8a73-8a805d1ae5c2")
    private GenderLimitEnum genderLimit;

    /** 人事科室名称 */
    @AutoGenerated(locked = true, uuid = "b17453c4-5814-4279-94b7-5a550b6988de")
    private String hrDepartmentName;

    /** 人事科室组织id */
    @AutoGenerated(locked = true, uuid = "e165d72c-9fd6-4f45-9841-ebcff562a25f")
    private String hrDepartmentOrganizationId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "c7d28e47-e4fe-478d-a32a-5d48c50edbcf")
    private String id;

    /** 针剂领第二天量 */
    @AutoGenerated(locked = true, uuid = "132f1ba6-7f3f-4f6c-93cd-3f17813e6cab")
    private BigDecimal injectionSecondDayAmount;

    /** 管理科室名称 */
    @AutoGenerated(locked = true, uuid = "3ddc88ae-87e9-4f1f-820c-df5dbdd24426")
    private String manageDepartmentName;

    /** 管理科室组织id */
    @AutoGenerated(locked = true, uuid = "a72cdf92-c263-481f-bc32-faf52dda8f2b")
    private String manageDepartmentOrganizationId;

    /** 病案科室代码 */
    @AutoGenerated(locked = true, uuid = "745889ba-a1c7-42d9-9195-ce91b3072f9d")
    private String medicalRecordDepartmentCode;

    /** 医疗服务类型 */
    @AutoGenerated(locked = true, uuid = "f6ce9c9f-d200-4df7-9081-e47ce26346fa")
    private MedicalServiceTypeEnum medicalServiceType;

    /** 开放时间段 */
    @Valid
    @AutoGenerated(locked = true, uuid = "438e5a07-11d8-46b2-8f62-7f075c00bdfa")
    private TimePeriodEo openTimePeriod;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "02763f02-c8dd-45c0-a92a-0ec5e5cac07c")
    private String organizationId;

    /** 上级科室名称 */
    @AutoGenerated(locked = true, uuid = "858fe149-f4a8-4508-b041-4b4a55cdb4b1")
    private String parentDepartmentName;

    /** 上级科室组织id */
    @AutoGenerated(locked = true, uuid = "2975a1b5-4bf4-4a6d-95e7-072333ba680e")
    private String parentDepartmentOrganizationId;

    /** 省平台作废标志 */
    @AutoGenerated(locked = true, uuid = "96d49d48-af25-4254-80fc-0dc446b5f2d8")
    private Boolean provincePlatformCancelFlag;

    /** 挂号科室启用标志 */
    @AutoGenerated(locked = true, uuid = "8854f48f-569a-4238-9dda-429274e5766b")
    private Boolean registerDepartmentEnableFlag;

    /** 挂号科室标志 */
    @AutoGenerated(locked = true, uuid = "04e2a601-52ba-4aff-b936-1fee869aa938")
    private Boolean registerDepartmentFlag;

    /** 专科标志 */
    @AutoGenerated(locked = true, uuid = "ed223a3e-8778-42b9-a1c4-69c50df85633")
    private Boolean specialtyFlag;

    /** 标准科室目录id */
    @AutoGenerated(locked = true, uuid = "7270a76b-5c71-474b-8110-7f2f07277482")
    private String standardDepartmentCatalogId;

    /** 库房类型 */
    @AutoGenerated(locked = true, uuid = "8ed79167-f76f-4643-aeb0-8d4fd6ddcba8")
    private StorageTypeEnum storageType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b8eb05bf-010c-45c9-9e00-aa58516430ff")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "9cabe877-43b4-4ef3-9d95-caff060c4829")
    private String updatedBy;
}
