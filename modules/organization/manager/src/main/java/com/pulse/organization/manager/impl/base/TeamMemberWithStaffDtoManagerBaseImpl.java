package com.pulse.organization.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.manager.StaffWithExtensionDtoManager;
import com.pulse.organization.manager.TeamMemberBaseDtoManager;
import com.pulse.organization.manager.TeamMemberWithStaffDtoManager;
import com.pulse.organization.manager.converter.TeamMemberBaseDtoConverter;
import com.pulse.organization.manager.converter.TeamMemberWithStaffDtoConverter;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.manager.dto.TeamMemberWithStaffDto;
import com.pulse.organization.persist.dos.TeamMember;
import com.pulse.organization.persist.mapper.TeamMemberDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "9a178ba0-4ef3-4a85-9801-8bf960bd949c|DTO|BASE_MANAGER_IMPL")
public abstract class TeamMemberWithStaffDtoManagerBaseImpl
        implements TeamMemberWithStaffDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private StaffWithExtensionDtoManager staffWithExtensionDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamMemberBaseDtoConverter teamMemberBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamMemberBaseDtoManager teamMemberBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamMemberDao teamMemberDao;

    @AutoGenerated(locked = true)
    @Autowired
    private TeamMemberWithStaffDtoConverter teamMemberWithStaffDtoConverter;

    @AutoGenerated(locked = true, uuid = "065cc904-267c-38e3-870e-82a81002a7fd")
    @Override
    public List<TeamMemberWithStaffDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<TeamMember> teamMemberList = teamMemberDao.getByIds(id);
        if (CollectionUtil.isEmpty(teamMemberList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, TeamMember> teamMemberMap =
                teamMemberList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        teamMemberList =
                id.stream()
                        .map(i -> teamMemberMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromTeamMemberToTeamMemberWithStaffDto(teamMemberList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "0e1b16e9-4901-3e34-861e-538bec3a30ff")
    @Override
    public TeamMemberWithStaffDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TeamMemberWithStaffDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        TeamMemberWithStaffDto teamMemberWithStaffDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return teamMemberWithStaffDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1b222c95-e1d6-30da-a010-6017f2bf87cc")
    public List<TeamMemberWithStaffDto> doConvertFromTeamMemberToTeamMemberWithStaffDto(
            List<TeamMember> teamMemberList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(teamMemberList)) {
            return Collections.emptyList();
        }

        Map<String, String> staffIdMap =
                teamMemberList.stream()
                        .filter(i -> i.getStaffId() != null)
                        .collect(Collectors.toMap(TeamMember::getId, TeamMember::getStaffId));
        List<StaffWithExtensionDto> staffIdStaffWithExtensionDtoList =
                staffWithExtensionDtoManager.getByIds(
                        new ArrayList<>(new HashSet<>(staffIdMap.values())));
        Map<String, StaffWithExtensionDto> staffIdStaffWithExtensionDtoMapRaw =
                staffIdStaffWithExtensionDtoList.stream()
                        .collect(Collectors.toMap(StaffWithExtensionDto::getId, i -> i));
        Map<String, StaffWithExtensionDto> staffIdStaffWithExtensionDtoMap =
                staffIdMap.entrySet().stream()
                        .filter(i -> staffIdStaffWithExtensionDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> staffIdStaffWithExtensionDtoMapRaw.get(i.getValue())));

        List<TeamMemberBaseDto> baseDtoList =
                teamMemberBaseDtoConverter.convertFromTeamMemberToTeamMemberBaseDto(teamMemberList);
        Map<String, TeamMemberWithStaffDto> dtoMap =
                teamMemberWithStaffDtoConverter
                        .convertFromTeamMemberBaseDtoToTeamMemberWithStaffDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        TeamMemberWithStaffDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<TeamMemberWithStaffDto> teamMemberWithStaffDtoList = new ArrayList<>();
        for (TeamMember i : teamMemberList) {
            TeamMemberWithStaffDto teamMemberWithStaffDto = dtoMap.get(i.getId());
            if (teamMemberWithStaffDto == null) {
                continue;
            }

            if (null != i.getStaffId()) {
                teamMemberWithStaffDto.setStaff(
                        staffIdStaffWithExtensionDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            teamMemberWithStaffDtoList.add(teamMemberWithStaffDto);
        }
        return teamMemberWithStaffDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "435c3dcd-5f01-3c32-8074-bdb238e62bf8")
    @Override
    public List<TeamMemberWithStaffDto> getByTeamId(String teamId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TeamMemberWithStaffDto> teamMemberWithStaffDtoList =
                getByTeamIds(Arrays.asList(teamId));
        return teamMemberWithStaffDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4ed1fe98-efe2-39e5-860b-4bee6976bfc2")
    @Override
    public List<TeamMemberWithStaffDto> getByStaffIds(List<String> staffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(staffId)) {
            return Collections.emptyList();
        }

        List<TeamMember> teamMemberList = teamMemberDao.getByStaffIds(staffId);
        if (CollectionUtil.isEmpty(teamMemberList)) {
            return Collections.emptyList();
        }

        return doConvertFromTeamMemberToTeamMemberWithStaffDto(teamMemberList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e93926df-a994-3fec-beab-9561fae81c94")
    @Override
    public List<TeamMemberWithStaffDto> getByStaffId(String staffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TeamMemberWithStaffDto> teamMemberWithStaffDtoList =
                getByStaffIds(Arrays.asList(staffId));
        return teamMemberWithStaffDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ec9c6773-4db0-3c92-b533-771ad19e42d6")
    @Override
    public List<TeamMemberWithStaffDto> getByTeamIds(List<String> teamId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(teamId)) {
            return Collections.emptyList();
        }

        List<TeamMember> teamMemberList = teamMemberDao.getByTeamIds(teamId);
        if (CollectionUtil.isEmpty(teamMemberList)) {
            return Collections.emptyList();
        }

        return doConvertFromTeamMemberToTeamMemberWithStaffDto(teamMemberList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
