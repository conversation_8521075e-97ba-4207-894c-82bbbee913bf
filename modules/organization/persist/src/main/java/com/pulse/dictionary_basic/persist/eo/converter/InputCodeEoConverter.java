package com.pulse.dictionary_basic.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for InputCodeEo */
@Converter
@AutoGenerated(locked = true, uuid = "ef39703b-3052-3a6a-8b39-f38cc6279793")
public class InputCodeEoConverter implements AttributeConverter<InputCodeEo, String> {

    /** convert DB column to InputCodeEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(InputCodeEo inputCodeEo) {
        if (inputCodeEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(inputCodeEo);
        }
    }

    /** convert DB column to InputCodeEo */
    @AutoGenerated(locked = true)
    public InputCodeEo convertToEntityAttribute(String inputCodeEoJson) {
        if (StrUtil.isEmpty(inputCodeEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(inputCodeEoJson, new TypeReference<InputCodeEo>() {});
        }
    }
}
