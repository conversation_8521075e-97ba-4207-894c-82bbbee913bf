package com.pulse.organization.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.organization.persist.dos.AssetUseLog;
import com.pulse.organization.persist.mapper.AssetUseLogDao;
import com.pulse.organization.persist.mapper.mybatis.AssetUseLogMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "8c5d11f0-d122-3425-ba7c-1a6cb39de184|ENTITY|DAO")
public class AssetUseLogDaoImpl implements AssetUseLogDao {
    @AutoGenerated(locked = true)
    @Resource
    private AssetUseLogMapper assetUseLogMapper;

    @AutoGenerated(locked = true, uuid = "47a444be-9f47-39ea-b641-4d8ec520f6dd")
    @Override
    public AssetUseLog getById(String id) {
        QueryWrapper<AssetUseLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return assetUseLogMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "793537b5-df1d-37b8-843e-10281cff9d0e")
    @Override
    public List<AssetUseLog> getByIds(List<String> id) {
        QueryWrapper<AssetUseLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return assetUseLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e4fc4179-78c5-3ed5-b039-769b2f9c874b")
    @Override
    public List<AssetUseLog> getByAssetId(String assetId) {
        QueryWrapper<AssetUseLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("asset_id", assetId).eq("deleted_at", 0L).orderByAsc("id");
        return assetUseLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "ecf6e9fa-5283-337b-a5ed-9382989a157f")
    @Override
    public List<AssetUseLog> getByAssetIds(List<String> assetId) {
        QueryWrapper<AssetUseLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("asset_id", assetId).eq("deleted_at", 0L).orderByAsc("id");
        return assetUseLogMapper.selectList(queryWrapper);
    }
}
