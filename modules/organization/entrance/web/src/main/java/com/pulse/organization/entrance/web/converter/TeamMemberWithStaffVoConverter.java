package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.TeamMemberWithStaffVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.TeamMemberWithStaffVoDataAssembler.TeamMemberWithStaffVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.TeamMemberWithStaffVoDataCollector;
import com.pulse.organization.entrance.web.vo.StaffWithExtensionVo;
import com.pulse.organization.entrance.web.vo.TeamMemberWithStaffVo;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.manager.dto.TeamMemberWithStaffDto;
import com.pulse.organization.service.TeamMemberBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TeamMemberWithStaffVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "0a4092e8-d907-49e6-a6f6-b1e0bf734dbc|VO|CONVERTER")
public class TeamMemberWithStaffVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionVoConverter staffWithExtensionVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberBaseDtoService teamMemberBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberWithStaffVoDataAssembler teamMemberWithStaffVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberWithStaffVoDataCollector teamMemberWithStaffVoDataCollector;

    /** 把TeamMemberWithStaffDto转换成TeamMemberWithStaffVo */
    @AutoGenerated(locked = false, uuid = "0a4092e8-d907-49e6-a6f6-b1e0bf734dbc-converter-Map")
    public Map<TeamMemberWithStaffDto, TeamMemberWithStaffVo> convertToTeamMemberWithStaffVoMap(
            List<TeamMemberWithStaffDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffWithExtensionDto, StaffWithExtensionVo> staffMap =
                staffWithExtensionVoConverter.convertToStaffWithExtensionVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(TeamMemberWithStaffDto::getStaff)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<TeamMemberWithStaffDto, TeamMemberWithStaffVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TeamMemberWithStaffVo vo = new TeamMemberWithStaffVo();
                                            vo.setId(dto.getId());
                                            vo.setStaff(
                                                    dto.getStaff() == null
                                                            ? null
                                                            : staffMap.get(dto.getStaff()));
                                            vo.setTeamId(dto.getTeamId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setIdentityTag(dto.getIdentityTag());
                                            vo.setRole(dto.getRole());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TeamMemberWithStaffDto转换成TeamMemberWithStaffVo */
    @AutoGenerated(locked = true, uuid = "0a4092e8-d907-49e6-a6f6-b1e0bf734dbc-converter-list")
    public List<TeamMemberWithStaffVo> convertToTeamMemberWithStaffVoList(
            List<TeamMemberWithStaffDto> dtoList) {
        return new ArrayList<>(convertToTeamMemberWithStaffVoMap(dtoList).values());
    }

    /** 把TeamMemberWithStaffDto转换成TeamMemberWithStaffVo */
    @AutoGenerated(locked = true, uuid = "1fe6ee5c-97ac-3a7a-9770-d2f8b4faf807")
    public TeamMemberWithStaffVo convertToTeamMemberWithStaffVo(TeamMemberWithStaffDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTeamMemberWithStaffVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装TeamMemberWithStaffVo列表数据 */
    @AutoGenerated(locked = true, uuid = "70888974-f2a0-36df-bf5b-63cbe954172c")
    public List<TeamMemberWithStaffVo> convertAndAssembleDataList(
            List<TeamMemberWithStaffDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        TeamMemberWithStaffVoDataHolder dataHolder = new TeamMemberWithStaffVoDataHolder();
        dataHolder.setRootBaseDtoList(
                teamMemberBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(TeamMemberWithStaffDto::getId)
                                .collect(Collectors.toList())));
        Map<String, TeamMemberWithStaffVo> voMap =
                convertToTeamMemberWithStaffVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        teamMemberWithStaffVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        teamMemberWithStaffVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装TeamMemberWithStaffVo数据 */
    @AutoGenerated(locked = true, uuid = "80e1f872-05d3-3884-86c2-2b8d9114dee5")
    public TeamMemberWithStaffVo convertAndAssembleData(TeamMemberWithStaffDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
