package com.pulse.organization.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.organization.common.enums.OrganizationStatusEnum;
import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "20591812-2d36-403c-9124-a96fcced0adf|VO|DEFINITION")
public class OrganizationWardVo {
    /** 简称 */
    @AutoGenerated(locked = true, uuid = "5e038e5c-9876-427a-9c5a-60652b88824d")
    private String abbreviation;

    /** 组织地址 */
    @AutoGenerated(locked = true, uuid = "62d9c7a2-b170-4adc-a035-d635350d30d6")
    private String address;

    /** 别名 */
    @AutoGenerated(locked = true, uuid = "46ca3d94-2b85-4b46-ba26-f855f4fbcf47")
    private String alias;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "495cc363-365b-4e8b-ac59-d2cebd2a6bfa")
    private String contactNumber;

    /** 联系人 */
    @AutoGenerated(locked = true, uuid = "61cd53ae-b28b-4cb2-900b-8475c790d3a1")
    private String contactPerson;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "5708354c-88d2-4eec-b17a-637f35d297fa")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "d91319b9-3d5d-4161-8c84-6aed4c29946c")
    private String createdBy;

    /** 删除者 */
    @AutoGenerated(locked = true, uuid = "db5f75ef-f68b-4ae2-aad7-199244a125f4")
    private String deletedBy;

    /** 组织描述 */
    @AutoGenerated(locked = true, uuid = "d423ff35-9f86-48cc-8dfa-792590b67fbe")
    private String description;

    /** 英文名 */
    @AutoGenerated(locked = true, uuid = "b0cc4a74-6999-4da5-8de9-d32defc2c5f9")
    private String englishName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "99eb279e-3215-4f1f-b695-9d1177a791e6")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "0d4f9133-94c0-4b50-97ed-09d11bbb79cb")
    private InputCodeEo inputCode;

    /** 作废标记 */
    @AutoGenerated(locked = true, uuid = "8933ceb5-9402-4c45-9c32-b5dfbd65f412")
    private Boolean invalidFlag;

    /** 组织名称 */
    @AutoGenerated(locked = true, uuid = "7fdef70d-02c2-4998-936a-dac945734936")
    private String name;

    /** 组织层级 */
    @AutoGenerated(locked = true, uuid = "7f959133-9162-4ce5-be72-17b2f8a4c73c")
    private Long organizationLevel;

    /** 上级组织ID */
    @AutoGenerated(locked = true, uuid = "*************-4a2f-8a8f-57631474d2e2")
    private String parentId;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "83d5611b-163f-4243-88a2-cb71ac844007")
    private Long sortNumber;

    /** 组织状态 */
    @AutoGenerated(locked = true, uuid = "500fad6a-43f4-4491-9aec-12b0f1c395eb")
    private OrganizationStatusEnum status;

    /** 组织类型 */
    @AutoGenerated(locked = true, uuid = "6f4eee4e-96e7-4826-aeb5-7dfada1e8355")
    private OrganizationTypeEnum type;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "594cdd85-d942-4508-9447-acc684ed172b")
    private Date updatedAt;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "e6a14757-e752-4d0d-b428-943204c6613c")
    private String updatedBy;

    /** 主键 */
    @Valid
    @AutoGenerated(locked = true, uuid = "72c680ea-bbb6-49b4-a78d-19f31e6cae3e")
    private WardBaseVo ward;
}
