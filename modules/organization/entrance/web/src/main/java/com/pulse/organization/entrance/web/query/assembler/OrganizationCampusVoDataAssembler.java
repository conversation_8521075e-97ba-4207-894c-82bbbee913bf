package com.pulse.organization.entrance.web.query.assembler;

import com.pulse.organization.entrance.web.vo.CampusBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationCampusVo;
import com.pulse.organization.manager.dto.CampusBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** OrganizationCampusVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "f153e907-852d-3c49-bb04-7a9e5dd03c1b")
public class OrganizationCampusVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    /** 批量自定义组装OrganizationCampusVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "03adc5a5-178c-3cc7-80e5-187e98978f8f")
    public void assembleDataCustomized(List<OrganizationCampusVo> dataList) {
        // 自定义数据组装

    }

    /** 组装OrganizationCampusVo数据 */
    @AutoGenerated(locked = true, uuid = "fe9e8a01-c168-36a2-9e3e-1c3da3c9ab0b")
    public void assembleData(
            Map<String, OrganizationCampusVo> voMap,
            OrganizationCampusVoDataAssembler.OrganizationCampusVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<OrganizationBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<CampusBaseDto, CampusBaseVo>> campus =
                dataHolder.campus.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getOrganizationId(),
                                        dto -> Pair.of(dto, dataHolder.campus.get(dto)),
                                        (o1, o2) -> o1));

        for (OrganizationBaseDto baseDto : baseDtoList) {
            OrganizationCampusVo vo = voMap.get(baseDto.getId());
            vo.setCampus(
                    Optional.ofNullable(campus.get(baseDto.getId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class OrganizationCampusVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<OrganizationBaseDto> rootBaseDtoList;

        /** 持有字段campus的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<CampusBaseDto, CampusBaseVo> campus;
    }
}
