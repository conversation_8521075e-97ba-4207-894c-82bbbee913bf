package com.pulse.organization.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.converter.OrganizationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationSimpleVoConverter;
import com.pulse.organization.entrance.web.converter.StaffExtensionBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffWithExtensionVoConverter;
import com.pulse.organization.entrance.web.converter.TeamDetailVoConverter;
import com.pulse.organization.entrance.web.converter.TeamMemberWithStaffVoConverter;
import com.pulse.organization.entrance.web.converter.TeamOrganizationVoConverter;
import com.pulse.organization.entrance.web.query.assembler.TeamDetailVoDataAssembler.TeamDetailVoDataHolder;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationSimpleVo;
import com.pulse.organization.entrance.web.vo.StaffExtensionBaseVo;
import com.pulse.organization.entrance.web.vo.StaffWithExtensionVo;
import com.pulse.organization.entrance.web.vo.TeamMemberWithStaffVo;
import com.pulse.organization.entrance.web.vo.TeamOrganizationVo;
import com.pulse.organization.manager.converter.StaffWithExtensionDtoConverter;
import com.pulse.organization.manager.converter.TeamMemberWithStaffDtoConverter;
import com.pulse.organization.manager.converter.TeamOrganizationDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffExtensionBaseDto;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.manager.dto.TeamBaseDto;
import com.pulse.organization.manager.dto.TeamDetailDto;
import com.pulse.organization.manager.dto.TeamMemberBaseDto;
import com.pulse.organization.manager.dto.TeamMemberWithStaffDto;
import com.pulse.organization.manager.dto.TeamOrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.StaffBaseDtoService;
import com.pulse.organization.service.StaffExtensionBaseDtoService;
import com.pulse.organization.service.TeamBaseDtoService;
import com.pulse.organization.service.TeamMemberBaseDtoService;
import com.pulse.organization.service.TeamOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装TeamDetailVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "ccedc708-fe2d-38ad-9d44-5e27c481b840")
public class TeamDetailVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationSimpleVoConverter organizationSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseDtoService staffExtensionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffExtensionBaseVoConverter staffExtensionBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionDtoConverter staffWithExtensionDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionVoConverter staffWithExtensionVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailVoConverter teamDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailVoDataCollector teamDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberBaseDtoService teamMemberBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberWithStaffDtoConverter teamMemberWithStaffDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberWithStaffVoConverter teamMemberWithStaffVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationBaseDtoService teamOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationDtoConverter teamOrganizationDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationVoConverter teamOrganizationVoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "30a1352a-945e-3759-9960-e28958103f97")
    public void collectDataDefault(TeamDetailVoDataHolder dataHolder) {
        teamDetailVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取TeamDetailDto数据填充TeamDetailVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "bf3175a5-eb56-3048-adb5-7759e69d97df")
    public void collectDataWithDtoData(
            List<TeamDetailDto> dtoList, TeamDetailVoDataHolder dataHolder) {
        Map<TeamOrganizationBaseDto, TeamOrganizationDto> teamOrganizationListBaseDtoDtoMap =
                new LinkedHashMap<>();
        List<OrganizationBaseDto> teamOrganizationList2OrganizationList = new ArrayList<>();
        List<OrganizationBaseDto> leadOrganizationList = new ArrayList<>();
        Map<TeamMemberBaseDto, TeamMemberWithStaffDto> teamMemberWithStaffListBaseDtoDtoMap =
                new LinkedHashMap<>();
        Map<StaffBaseDto, StaffWithExtensionDto> teamMemberWithStaffList2StaffBaseDtoDtoMap =
                new LinkedHashMap<>();
        List<OrganizationBaseDto> teamMemberWithStaffList2Staff2AccountingOrganizationList =
                new ArrayList<>();
        List<OrganizationBaseDto> teamMemberWithStaffList2Staff2HrOrganizationList =
                new ArrayList<>();
        List<StaffExtensionBaseDto> teamMemberWithStaffList2Staff2StaffExtensionList =
                new ArrayList<>();
        List<OrganizationBaseDto> teamMemberWithStaffList2Staff2OrganizationList =
                new ArrayList<>();

        for (TeamDetailDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getTeamOrganizationList())) {
                Map<String, TeamOrganizationBaseDto> teamOrganizationListBaseDtoMap =
                        teamOrganizationDtoConverter
                                .convertFromTeamOrganizationDtoToTeamOrganizationBaseDto(
                                        rootDto.getTeamOrganizationList())
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                TeamOrganizationBaseDto::getId,
                                                Function.identity()));
                for (TeamOrganizationDto teamOrganizationListDto :
                        rootDto.getTeamOrganizationList()) {
                    TeamOrganizationBaseDto teamOrganizationListBaseDto =
                            teamOrganizationListBaseDtoMap.get(teamOrganizationListDto.getId());
                    teamOrganizationListBaseDto.setTeamId(rootDto.getId());
                    teamOrganizationListBaseDtoDtoMap.put(
                            teamOrganizationListBaseDto, teamOrganizationListDto);
                    OrganizationBaseDto teamOrganizationList2OrganizationDto =
                            teamOrganizationListDto.getOrganization();
                    if (teamOrganizationList2OrganizationDto != null) {
                        teamOrganizationList2OrganizationList.add(
                                teamOrganizationList2OrganizationDto);
                    }
                }
            }
            OrganizationBaseDto leadOrganizationDto = rootDto.getLeadOrganization();
            if (leadOrganizationDto != null) {
                leadOrganizationList.add(leadOrganizationDto);
            }
            if (CollectionUtil.isNotEmpty(rootDto.getTeamMemberWithStaffList())) {
                Map<String, TeamMemberBaseDto> teamMemberWithStaffListBaseDtoMap =
                        teamMemberWithStaffDtoConverter
                                .convertFromTeamMemberWithStaffDtoToTeamMemberBaseDto(
                                        rootDto.getTeamMemberWithStaffList())
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                TeamMemberBaseDto::getId, Function.identity()));
                for (TeamMemberWithStaffDto teamMemberWithStaffListDto :
                        rootDto.getTeamMemberWithStaffList()) {
                    TeamMemberBaseDto teamMemberWithStaffListBaseDto =
                            teamMemberWithStaffListBaseDtoMap.get(
                                    teamMemberWithStaffListDto.getId());
                    teamMemberWithStaffListBaseDto.setTeamId(rootDto.getId());
                    teamMemberWithStaffListBaseDtoDtoMap.put(
                            teamMemberWithStaffListBaseDto, teamMemberWithStaffListDto);
                    StaffWithExtensionDto teamMemberWithStaffList2StaffDto =
                            teamMemberWithStaffListDto.getStaff();
                    if (teamMemberWithStaffList2StaffDto != null) {
                        teamMemberWithStaffList2StaffBaseDtoDtoMap.put(
                                staffWithExtensionDtoConverter
                                        .convertFromStaffWithExtensionDtoToStaffBaseDto(
                                                teamMemberWithStaffList2StaffDto),
                                teamMemberWithStaffList2StaffDto);
                        OrganizationBaseDto
                                teamMemberWithStaffList2Staff2AccountingOrganizationDto =
                                        teamMemberWithStaffList2StaffDto
                                                .getAccountingOrganization();
                        if (teamMemberWithStaffList2Staff2AccountingOrganizationDto != null) {
                            teamMemberWithStaffList2Staff2AccountingOrganizationList.add(
                                    teamMemberWithStaffList2Staff2AccountingOrganizationDto);
                        }
                        OrganizationBaseDto teamMemberWithStaffList2Staff2HrOrganizationDto =
                                teamMemberWithStaffList2StaffDto.getHrOrganization();
                        if (teamMemberWithStaffList2Staff2HrOrganizationDto != null) {
                            teamMemberWithStaffList2Staff2HrOrganizationList.add(
                                    teamMemberWithStaffList2Staff2HrOrganizationDto);
                        }
                        StaffExtensionBaseDto teamMemberWithStaffList2Staff2StaffExtensionDto =
                                teamMemberWithStaffList2StaffDto.getStaffExtension();
                        if (teamMemberWithStaffList2Staff2StaffExtensionDto != null) {
                            teamMemberWithStaffList2Staff2StaffExtensionList.add(
                                    teamMemberWithStaffList2Staff2StaffExtensionDto);
                        }
                        OrganizationBaseDto teamMemberWithStaffList2Staff2OrganizationDto =
                                teamMemberWithStaffList2StaffDto.getOrganization();
                        if (teamMemberWithStaffList2Staff2OrganizationDto != null) {
                            teamMemberWithStaffList2Staff2OrganizationList.add(
                                    teamMemberWithStaffList2Staff2OrganizationDto);
                        }
                    }
                }
            }
        }

        // access teamOrganizationList
        Map<TeamOrganizationDto, TeamOrganizationVo> teamOrganizationListVoMap =
                teamOrganizationVoConverter.convertToTeamOrganizationVoMap(
                        new ArrayList<>(teamOrganizationListBaseDtoDtoMap.values()));
        dataHolder.teamOrganizationList =
                teamOrganizationListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamOrganizationListVoMap.get(
                                                        teamOrganizationListBaseDtoDtoMap.get(
                                                                baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access teamOrganizationList2Organization
        Map<OrganizationBaseDto, OrganizationBaseVo> teamOrganizationList2OrganizationVoMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(
                        teamOrganizationList2OrganizationList);
        dataHolder.teamOrganizationList2Organization =
                teamOrganizationList2OrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamOrganizationList2OrganizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access leadOrganization
        Map<OrganizationBaseDto, OrganizationBaseVo> leadOrganizationVoMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(leadOrganizationList);
        dataHolder.leadOrganization =
                leadOrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> leadOrganizationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access teamMemberWithStaffList
        Map<TeamMemberWithStaffDto, TeamMemberWithStaffVo> teamMemberWithStaffListVoMap =
                teamMemberWithStaffVoConverter.convertToTeamMemberWithStaffVoMap(
                        new ArrayList<>(teamMemberWithStaffListBaseDtoDtoMap.values()));
        dataHolder.teamMemberWithStaffList =
                teamMemberWithStaffListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamMemberWithStaffListVoMap.get(
                                                        teamMemberWithStaffListBaseDtoDtoMap.get(
                                                                baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access teamMemberWithStaffList2Staff
        Map<StaffWithExtensionDto, StaffWithExtensionVo> teamMemberWithStaffList2StaffVoMap =
                staffWithExtensionVoConverter.convertToStaffWithExtensionVoMap(
                        new ArrayList<>(teamMemberWithStaffList2StaffBaseDtoDtoMap.values()));
        dataHolder.teamMemberWithStaffList2Staff =
                teamMemberWithStaffList2StaffBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamMemberWithStaffList2StaffVoMap.get(
                                                        teamMemberWithStaffList2StaffBaseDtoDtoMap
                                                                .get(baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access teamMemberWithStaffList2Staff2AccountingOrganization
        Map<OrganizationBaseDto, OrganizationSimpleVo>
                teamMemberWithStaffList2Staff2AccountingOrganizationVoMap =
                        organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                                teamMemberWithStaffList2Staff2AccountingOrganizationList);
        dataHolder.teamMemberWithStaffList2Staff2AccountingOrganization =
                teamMemberWithStaffList2Staff2AccountingOrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamMemberWithStaffList2Staff2AccountingOrganizationVoMap
                                                        .get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access teamMemberWithStaffList2Staff2HrOrganization
        Map<OrganizationBaseDto, OrganizationSimpleVo>
                teamMemberWithStaffList2Staff2HrOrganizationVoMap =
                        organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                                teamMemberWithStaffList2Staff2HrOrganizationList);
        dataHolder.teamMemberWithStaffList2Staff2HrOrganization =
                teamMemberWithStaffList2Staff2HrOrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamMemberWithStaffList2Staff2HrOrganizationVoMap
                                                        .get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access teamMemberWithStaffList2Staff2StaffExtension
        Map<StaffExtensionBaseDto, StaffExtensionBaseVo>
                teamMemberWithStaffList2Staff2StaffExtensionVoMap =
                        staffExtensionBaseVoConverter.convertToStaffExtensionBaseVoMap(
                                teamMemberWithStaffList2Staff2StaffExtensionList);
        dataHolder.teamMemberWithStaffList2Staff2StaffExtension =
                teamMemberWithStaffList2Staff2StaffExtensionList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamMemberWithStaffList2Staff2StaffExtensionVoMap
                                                        .get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access teamMemberWithStaffList2Staff2Organization
        Map<OrganizationBaseDto, OrganizationSimpleVo>
                teamMemberWithStaffList2Staff2OrganizationVoMap =
                        organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(
                                teamMemberWithStaffList2Staff2OrganizationList);
        dataHolder.teamMemberWithStaffList2Staff2Organization =
                teamMemberWithStaffList2Staff2OrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                teamMemberWithStaffList2Staff2OrganizationVoMap.get(
                                                        baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "d718aed2-e923-3d7e-a67f-0eae74b76c81")
    private void fillDataWhenNecessary(TeamDetailVoDataHolder dataHolder) {
        List<TeamBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.teamOrganizationList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TeamOrganizationBaseDto> baseDtoList =
                    teamOrganizationBaseDtoService.getByTeamIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TeamOrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TeamOrganizationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(TeamOrganizationBaseDto::getTeamId));
            Map<String, TeamOrganizationDto> teamOrganizationDtoMap =
                    teamOrganizationDtoConverter
                            .convertFromTeamOrganizationBaseDtoToTeamOrganizationDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            TeamOrganizationDto::getId, Function.identity()));
            Map<TeamOrganizationDto, TeamOrganizationVo> dtoVoMap =
                    teamOrganizationVoConverter.convertToTeamOrganizationVoMap(
                            new ArrayList<>(teamOrganizationDtoMap.values()));
            Map<TeamOrganizationBaseDto, TeamOrganizationVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> teamOrganizationDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            teamOrganizationDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamOrganizationList =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.leadOrganization == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getLeadOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationBaseVo> dtoVoMap =
                    organizationBaseVoConverter.convertToOrganizationBaseVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.leadOrganization =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getLeadOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamMemberWithStaffList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<TeamMemberBaseDto> baseDtoList =
                    teamMemberBaseDtoService.getByTeamIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(TeamMemberBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<TeamMemberBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(TeamMemberBaseDto::getTeamId));
            Map<String, TeamMemberWithStaffDto> teamMemberWithStaffDtoMap =
                    teamMemberWithStaffDtoConverter
                            .convertFromTeamMemberBaseDtoToTeamMemberWithStaffDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            TeamMemberWithStaffDto::getId, Function.identity()));
            Map<TeamMemberWithStaffDto, TeamMemberWithStaffVo> dtoVoMap =
                    teamMemberWithStaffVoConverter.convertToTeamMemberWithStaffVoMap(
                            new ArrayList<>(teamMemberWithStaffDtoMap.values()));
            Map<TeamMemberBaseDto, TeamMemberWithStaffVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            teamMemberWithStaffDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            teamMemberWithStaffDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamMemberWithStaffList =
                    rootDtoList.stream()
                            .map(TeamBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamOrganizationList2Organization == null) {
            Set<String> ids =
                    dataHolder.teamOrganizationList.keySet().stream()
                            .map(TeamOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationBaseVo> dtoVoMap =
                    organizationBaseVoConverter.convertToOrganizationBaseVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamOrganizationList2Organization =
                    dataHolder.teamOrganizationList.keySet().stream()
                            .map(TeamOrganizationBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamMemberWithStaffList2Staff == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList.keySet().stream()
                            .map(TeamMemberBaseDto::getStaffId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffBaseDto> baseDtoList =
                    staffBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, StaffBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(StaffBaseDto::getId, Function.identity()));
            Map<String, StaffWithExtensionDto> staffWithExtensionDtoMap =
                    staffWithExtensionDtoConverter
                            .convertFromStaffBaseDtoToStaffWithExtensionDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            StaffWithExtensionDto::getId, Function.identity()));
            Map<StaffWithExtensionDto, StaffWithExtensionVo> dtoVoMap =
                    staffWithExtensionVoConverter.convertToStaffWithExtensionVoMap(
                            new ArrayList<>(staffWithExtensionDtoMap.values()));
            Map<StaffBaseDto, StaffWithExtensionVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(
                                    baseDto ->
                                            staffWithExtensionDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            staffWithExtensionDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamMemberWithStaffList2Staff =
                    dataHolder.teamMemberWithStaffList.keySet().stream()
                            .map(TeamMemberBaseDto::getStaffId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamMemberWithStaffList2Staff2AccountingOrganization == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamMemberWithStaffList2Staff2AccountingOrganization =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getAccountingOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamMemberWithStaffList2Staff2HrOrganization == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamMemberWithStaffList2Staff2HrOrganization =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getHrOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamMemberWithStaffList2Staff2StaffExtension == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffExtensionBaseDto> baseDtoList =
                    staffExtensionBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffExtensionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffExtensionBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffExtensionBaseDto::getStaffId));
            Map<StaffExtensionBaseDto, StaffExtensionBaseVo> dtoVoMap =
                    staffExtensionBaseVoConverter.convertToStaffExtensionBaseVoMap(baseDtoList);
            Map<StaffExtensionBaseDto, StaffExtensionBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamMemberWithStaffList2Staff2StaffExtension =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.teamMemberWithStaffList2Staff2Organization == null) {
            Set<String> ids =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationSimpleVo> dtoVoMap =
                    organizationSimpleVoConverter.convertToOrganizationSimpleVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationSimpleVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.teamMemberWithStaffList2Staff2Organization =
                    dataHolder.teamMemberWithStaffList2Staff.keySet().stream()
                            .map(StaffBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
