package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.consulting_room.manager.dto.ConsultingRoomBaseDto;
import com.pulse.organization.entrance.web.query.assembler.ConsultingRoomBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.ConsultingRoomBaseVo;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到ConsultingRoomBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "c182c1be-ac46-4153-b0ad-85df7de5a02f|VO|CONVERTER")
public class ConsultingRoomBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private ConsultingRoomBaseVoDataAssembler consultingRoomBaseVoDataAssembler;

    /** 使用默认方式组装ConsultingRoomBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "ab4a2224-17c2-36d6-9b70-acc40b492ec8")
    public ConsultingRoomBaseVo convertAndAssembleData(ConsultingRoomBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把ConsultingRoomBaseDto转换成ConsultingRoomBaseVo */
    @AutoGenerated(locked = false, uuid = "c182c1be-ac46-4153-b0ad-85df7de5a02f-converter-Map")
    public Map<ConsultingRoomBaseDto, ConsultingRoomBaseVo> convertToConsultingRoomBaseVoMap(
            List<ConsultingRoomBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<ConsultingRoomBaseDto, ConsultingRoomBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            ConsultingRoomBaseVo vo = new ConsultingRoomBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setCode(dto.getCode());
                                            vo.setDisplayName(dto.getDisplayName());
                                            vo.setClinicRoomNumber(dto.getClinicRoomNumber());
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            vo.setFloorUnit(dto.getFloorUnit());
                                            vo.setArea(dto.getArea());
                                            vo.setIp(dto.getIp());
                                            vo.setProperty(dto.getProperty());
                                            vo.setName(dto.getName());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setInvalidFlag(dto.getInvalidFlag());
                                            vo.setInvalidReason(dto.getInvalidReason());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setAreaEnglishName(dto.getAreaEnglishName());
                                            vo.setShortCode(dto.getShortCode());
                                            vo.setInputCode(dto.getInputCode());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把ConsultingRoomBaseDto转换成ConsultingRoomBaseVo */
    @AutoGenerated(locked = true, uuid = "c182c1be-ac46-4153-b0ad-85df7de5a02f-converter-list")
    public List<ConsultingRoomBaseVo> convertToConsultingRoomBaseVoList(
            List<ConsultingRoomBaseDto> dtoList) {
        return new ArrayList<>(convertToConsultingRoomBaseVoMap(dtoList).values());
    }

    /** 把ConsultingRoomBaseDto转换成ConsultingRoomBaseVo */
    @AutoGenerated(locked = true, uuid = "fa898184-6ee4-3ced-a928-286630b04262")
    public ConsultingRoomBaseVo convertToConsultingRoomBaseVo(ConsultingRoomBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToConsultingRoomBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装ConsultingRoomBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fcdea4b5-dda0-3e0d-8a1e-66ef6ce3ca21")
    public List<ConsultingRoomBaseVo> convertAndAssembleDataList(
            List<ConsultingRoomBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, ConsultingRoomBaseVo> voMap =
                convertToConsultingRoomBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        consultingRoomBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
