package com.pulse.organization.entrance.web.controller;

import com.pulse.organization.entrance.web.converter.StaffDetailVoConverter;
import com.pulse.organization.entrance.web.converter.StaffWithExtensionVoConverter;
import com.pulse.organization.entrance.web.vo.StaffDetailVo;
import com.pulse.organization.entrance.web.vo.StaffWithExtensionVo;
import com.pulse.organization.manager.dto.StaffDetailDto;
import com.pulse.organization.manager.dto.StaffWithExtensionDto;
import com.pulse.organization.persist.qto.SearchRegisterDoctorQto;
import com.pulse.organization.persist.qto.SearchStaffWithExtensionQto;
import com.pulse.organization.service.StaffBOService;
import com.pulse.organization.service.StaffDetailDtoService;
import com.pulse.organization.service.StaffWithExtensionDtoService;
import com.pulse.organization.service.bto.CreateStaffDetailBto;
import com.pulse.organization.service.bto.DeleteStaffDetailBto;
import com.pulse.organization.service.bto.MergeStaffDetailBto;
import com.pulse.organization.service.bto.UpdateRegisterDoctorBto;
import com.pulse.organization.service.bto.UpdateRegisterDoctorEnableFlagBto;
import com.pulse.organization.service.bto.UpdateStaffStatusBto;
import com.pulse.organization.service.query.StaffWithExtensionDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "17b19259-03ff-34a3-9f1a-28350389060a")
public class StaffController {
    @AutoGenerated(locked = true)
    @Resource
    private StaffBOService staffBOService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionDtoQueryService staffWithExtensionDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionDtoService staffWithExtensionDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithExtensionVoConverter staffWithExtensionVoConverter;

    @Resource private StaffDetailDtoService staffDetailDtoService;

    @Resource private StaffDetailVoConverter staffDetailVoConverter;

    /** 创建员工信息 */
    @PublicInterface(id = "015db5bc-ee7e-4ae5-83b0-041e0a646886", version = "1743071177730")
    @AutoGenerated(locked = false, uuid = "015db5bc-ee7e-4ae5-83b0-041e0a646886")
    @RequestMapping(
            value = {"/api/organization/create-staff-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createStaffDetail(@Valid CreateStaffDetailBto createStaffDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = staffBOService.createStaffDetail(createStaffDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据主键获取员工及扩展信息 */
    @PublicInterface(id = "3943853e-32ed-43b7-a058-23b223e9fbcb", version = "1745212878836")
    @AutoGenerated(locked = false, uuid = "3943853e-32ed-43b7-a058-23b223e9fbcb")
    @RequestMapping(
            value = {"/api/organization/get-staff-with-extension-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public StaffWithExtensionVo getStaffWithExtensionById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        StaffWithExtensionDto rpcResult = staffWithExtensionDtoService.getById(id);
        StaffWithExtensionVo result =
                staffWithExtensionVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查找挂号医生人员信息 */
    @PublicInterface(id = "4ddd0a33-044d-40d2-be1d-9fa118bf7c36", version = "1745372624468")
    @AutoGenerated(locked = false, uuid = "4ddd0a33-044d-40d2-be1d-9fa118bf7c36")
    @RequestMapping(
            value = {"/api/organization/search-register-doctor-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<StaffWithExtensionVo> searchRegisterDoctorPaged(
            @Valid SearchRegisterDoctorQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<StaffWithExtensionDto> dtoResult =
                staffWithExtensionDtoQueryService.searchRegisterDoctorPaged(qto);
        VSQueryResult<StaffWithExtensionVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                staffWithExtensionVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询员工分页列表（按职工类别、姓名、工号、状态） */
    @PublicInterface(id = "51a8c12f-2f73-4ca6-ab2a-f9cff01e5d3d", version = "1743669038600")
    @AutoGenerated(locked = false, uuid = "51a8c12f-2f73-4ca6-ab2a-f9cff01e5d3d")
    @RequestMapping(
            value = {"/api/organization/search-staff-with-extension-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<StaffWithExtensionVo> searchStaffWithExtensionPaged(
            @Valid SearchStaffWithExtensionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<StaffWithExtensionDto> dtoResult =
                staffWithExtensionDtoQueryService.searchStaffWithExtensionPaged(qto);
        VSQueryResult<StaffWithExtensionVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                staffWithExtensionVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据主键获取员工完整信息 */
    @PublicInterface(id = "5245a62e-0ff0-4d69-ac72-11a0396c1e58", version = "1743070819252")
    @AutoGenerated(locked = false, uuid = "5245a62e-0ff0-4d69-ac72-11a0396c1e58")
    @RequestMapping(
            value = {"/api/organization/get-staff-detail-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public StaffDetailVo getStaffDetailById(String id) {
        // TODO implement method
        StaffDetailDto rpcResult = staffDetailDtoService.getById(id);
        StaffDetailVo result = staffDetailVoConverter.convertAndAssembleData(rpcResult);
        return result;
    }

    /** 更新员工状态 */
    @PublicInterface(id = "77f40945-264c-4842-92e0-27e8ea94506e", version = "1743062234800")
    @AutoGenerated(locked = false, uuid = "77f40945-264c-4842-92e0-27e8ea94506e")
    @RequestMapping(
            value = {"/api/organization/update-staff-status"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateStaffStatus(@Valid UpdateStaffStatusBto updateStaffStatusBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = staffBOService.updateStaffStatus(updateStaffStatusBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查找挂号医生人员信息 */
    @PublicInterface(id = "97ea4833-b3db-4b69-8a2f-ea108d726ee4", version = "1745372608960")
    @AutoGenerated(locked = false, uuid = "97ea4833-b3db-4b69-8a2f-ea108d726ee4")
    @RequestMapping(
            value = {"/api/organization/search-register-doctor"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<StaffWithExtensionVo> searchRegisterDoctor(@Valid SearchRegisterDoctorQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<StaffWithExtensionDto> rpcResult =
                staffWithExtensionDtoQueryService.searchRegisterDoctor(qto);
        List<StaffWithExtensionVo> result =
                staffWithExtensionVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改挂号医生信息 */
    @PublicInterface(id = "a1d114fc-e0ed-45c6-9f1b-f4691cf236c8", version = "1745372650313")
    @AutoGenerated(locked = false, uuid = "a1d114fc-e0ed-45c6-9f1b-f4691cf236c8")
    @RequestMapping(
            value = {"/api/organization/update-register-doctor"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateRegisterDoctor(@Valid UpdateRegisterDoctorBto updateRegisterDoctorBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = staffBOService.updateRegisterDoctor(updateRegisterDoctorBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查找挂号医生人员信息 */
    @PublicInterface(id = "abe23f47-4bcd-4b39-ab50-230343e76931", version = "1745388318131")
    @AutoGenerated(locked = false, uuid = "abe23f47-4bcd-4b39-ab50-230343e76931")
    @RequestMapping(
            value = {"/api/organization/search-register-doctor-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<StaffWithExtensionVo> searchRegisterDoctorWaterfall(
            @Valid SearchRegisterDoctorQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<StaffWithExtensionDto> dtoResult =
                staffWithExtensionDtoQueryService.searchRegisterDoctorWaterfall(qto);
        VSQueryResult<StaffWithExtensionVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                staffWithExtensionVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 编辑员工信息 */
    @PublicInterface(id = "cb79f97a-a2de-461d-a455-13a6eec0830f", version = "1745212986122")
    @AutoGenerated(locked = false, uuid = "cb79f97a-a2de-461d-a455-13a6eec0830f")
    @RequestMapping(
            value = {"/api/organization/merge-staff-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeStaffDetail(@Valid MergeStaffDetailBto mergeStaffDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = staffBOService.mergeStaffDetail(mergeStaffDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除员工信息 */
    @PublicInterface(id = "d1e2af82-f6bb-477d-bc2a-c3b6ca03419c", version = "1743071198716")
    @AutoGenerated(locked = false, uuid = "d1e2af82-f6bb-477d-bc2a-c3b6ca03419c")
    @RequestMapping(
            value = {"/api/organization/delete-staff-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteStaffDetail(@Valid DeleteStaffDetailBto deleteStaffDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = staffBOService.deleteStaffDetail(deleteStaffDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询员工列表（按职工类别、工号、状态、关键词） */
    @PublicInterface(id = "d82d3a06-65a0-47df-a112-d821d5ca1ed5", version = "1744773422002")
    @AutoGenerated(locked = false, uuid = "d82d3a06-65a0-47df-a112-d821d5ca1ed5")
    @RequestMapping(
            value = {"/api/organization/search-staff-with-extension-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<StaffWithExtensionVo> searchStaffWithExtensionWaterfall(
            @Valid SearchStaffWithExtensionQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<StaffWithExtensionDto> dtoResult =
                staffWithExtensionDtoQueryService.searchStaffWithExtensionWaterfall(qto);
        VSQueryResult<StaffWithExtensionVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                staffWithExtensionVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更新挂号医生挂号启用标志 */
    @PublicInterface(id = "dcb6111d-5e81-4332-a75a-3a26ccd8e3d1", version = "1745313715364")
    @AutoGenerated(locked = false, uuid = "dcb6111d-5e81-4332-a75a-3a26ccd8e3d1")
    @RequestMapping(
            value = {"/api/organization/update-register-doctor-enable-flag"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateRegisterDoctorEnableFlag(
            @Valid UpdateRegisterDoctorEnableFlagBto updateRegisterDoctorEnableFlagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                staffBOService.updateRegisterDoctorEnableFlag(updateRegisterDoctorEnableFlagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
