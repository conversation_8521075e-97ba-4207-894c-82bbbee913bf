package com.pulse.organization.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.organization.entrance.web.converter.OrganizationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.OrganizationRefApplicationBaseVoConverter;
import com.pulse.organization.entrance.web.converter.StaffOrganizationVoConverter;
import com.pulse.organization.entrance.web.converter.StaffWithStaffOrganizationListVoConverter;
import com.pulse.organization.entrance.web.query.assembler.StaffWithStaffOrganizationListVoDataAssembler.StaffWithStaffOrganizationListVoDataHolder;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.OrganizationRefApplicationBaseVo;
import com.pulse.organization.entrance.web.vo.StaffOrganizationVo;
import com.pulse.organization.manager.converter.StaffOrganizationDtoConverter;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffOrganizationDto;
import com.pulse.organization.manager.dto.StaffWithStaffOrganizationListDto;
import com.pulse.organization.manager.facade.application.ApplicationBaseDtoServiceInOrganizationRpcAdapter;
import com.pulse.organization.service.OrganizationBaseDtoService;
import com.pulse.organization.service.StaffBaseDtoService;
import com.pulse.organization.service.StaffOrganizationBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装StaffWithStaffOrganizationListVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "422abbd4-952f-3f1f-aa64-191b3b7ba0a8")
public class StaffWithStaffOrganizationListVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoServiceInOrganizationRpcAdapter
            applicationBaseDtoServiceInOrganizationRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseDtoService organizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationRefApplicationBaseVoConverter organizationRefApplicationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffBaseDtoService staffBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationBaseDtoService staffOrganizationBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationDtoConverter staffOrganizationDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffOrganizationVoConverter staffOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithStaffOrganizationListVoConverter staffWithStaffOrganizationListVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private StaffWithStaffOrganizationListVoDataCollector
            staffWithStaffOrganizationListVoDataCollector;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "39dd92a2-75d3-3994-9c7d-2c2a0bfe7f9d")
    public void collectDataDefault(StaffWithStaffOrganizationListVoDataHolder dataHolder) {
        staffWithStaffOrganizationListVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 获取StaffWithStaffOrganizationListDto数据填充StaffWithStaffOrganizationListVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "6f8c7776-1e8b-3052-932c-3558a7241d1c")
    public void collectDataWithDtoData(
            List<StaffWithStaffOrganizationListDto> dtoList,
            StaffWithStaffOrganizationListVoDataHolder dataHolder) {
        Map<StaffOrganizationBaseDto, StaffOrganizationDto> staffOrganizationListBaseDtoDtoMap =
                new LinkedHashMap<>();
        List<OrganizationBaseDto> staffOrganizationList2OrganizationList = new ArrayList<>();
        List<ApplicationBaseDto> staffOrganizationList2ApplicationList = new ArrayList<>();

        for (StaffWithStaffOrganizationListDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getStaffOrganizationList())) {
                Map<String, StaffOrganizationBaseDto> staffOrganizationListBaseDtoMap =
                        staffOrganizationDtoConverter
                                .convertFromStaffOrganizationDtoToStaffOrganizationBaseDto(
                                        rootDto.getStaffOrganizationList())
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                StaffOrganizationBaseDto::getId,
                                                Function.identity()));
                for (StaffOrganizationDto staffOrganizationListDto :
                        rootDto.getStaffOrganizationList()) {
                    StaffOrganizationBaseDto staffOrganizationListBaseDto =
                            staffOrganizationListBaseDtoMap.get(staffOrganizationListDto.getId());
                    staffOrganizationListBaseDto.setStaffId(rootDto.getId());
                    staffOrganizationListBaseDtoDtoMap.put(
                            staffOrganizationListBaseDto, staffOrganizationListDto);
                    OrganizationBaseDto staffOrganizationList2OrganizationDto =
                            staffOrganizationListDto.getOrganization();
                    if (staffOrganizationList2OrganizationDto != null) {
                        staffOrganizationList2OrganizationList.add(
                                staffOrganizationList2OrganizationDto);
                    }
                    ApplicationBaseDto staffOrganizationList2ApplicationDto =
                            staffOrganizationListDto.getApplication();
                    if (staffOrganizationList2ApplicationDto != null) {
                        staffOrganizationList2ApplicationList.add(
                                staffOrganizationList2ApplicationDto);
                    }
                }
            }
        }

        // access staffOrganizationList
        Map<StaffOrganizationDto, StaffOrganizationVo> staffOrganizationListVoMap =
                staffOrganizationVoConverter.convertToStaffOrganizationVoMap(
                        new ArrayList<>(staffOrganizationListBaseDtoDtoMap.values()));
        dataHolder.staffOrganizationList =
                staffOrganizationListBaseDtoDtoMap.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                staffOrganizationListVoMap.get(
                                                        staffOrganizationListBaseDtoDtoMap.get(
                                                                baseDto)),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffOrganizationList2Organization
        Map<OrganizationBaseDto, OrganizationBaseVo> staffOrganizationList2OrganizationVoMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(
                        staffOrganizationList2OrganizationList);
        dataHolder.staffOrganizationList2Organization =
                staffOrganizationList2OrganizationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                staffOrganizationList2OrganizationVoMap.get(
                                                        baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        // access staffOrganizationList2Application
        Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo>
                staffOrganizationList2ApplicationVoMap =
                        organizationRefApplicationBaseVoConverter
                                .convertToOrganizationRefApplicationBaseVoMap(
                                        staffOrganizationList2ApplicationList);
        dataHolder.staffOrganizationList2Application =
                staffOrganizationList2ApplicationList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto ->
                                                staffOrganizationList2ApplicationVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "8287a10f-8913-3b47-81cc-87d3e56e5b24")
    private void fillDataWhenNecessary(StaffWithStaffOrganizationListVoDataHolder dataHolder) {
        List<StaffBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.staffOrganizationList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<StaffOrganizationBaseDto> baseDtoList =
                    staffOrganizationBaseDtoService.getByStaffIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(StaffOrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<StaffOrganizationBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(StaffOrganizationBaseDto::getStaffId));
            Map<String, StaffOrganizationDto> staffOrganizationDtoMap =
                    staffOrganizationDtoConverter
                            .convertFromStaffOrganizationBaseDtoToStaffOrganizationDto(baseDtoList)
                            .stream()
                            .collect(
                                    Collectors.toMap(
                                            StaffOrganizationDto::getId, Function.identity()));
            Map<StaffOrganizationDto, StaffOrganizationVo> dtoVoMap =
                    staffOrganizationVoConverter.convertToStaffOrganizationVoMap(
                            new ArrayList<>(staffOrganizationDtoMap.values()));
            Map<StaffOrganizationBaseDto, StaffOrganizationVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .filter(baseDto -> staffOrganizationDtoMap.containsKey(baseDto.getId()))
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto ->
                                                    dtoVoMap.get(
                                                            staffOrganizationDtoMap.get(
                                                                    baseDto.getId())),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffOrganizationList =
                    rootDtoList.stream()
                            .map(StaffBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffOrganizationList2Organization == null) {
            Set<String> ids =
                    dataHolder.staffOrganizationList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getOrganizationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<OrganizationBaseDto> baseDtoList =
                    organizationBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(OrganizationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, OrganizationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            OrganizationBaseDto::getId, Function.identity()));
            Map<OrganizationBaseDto, OrganizationBaseVo> dtoVoMap =
                    organizationBaseVoConverter.convertToOrganizationBaseVoMap(baseDtoList);
            Map<OrganizationBaseDto, OrganizationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffOrganizationList2Organization =
                    dataHolder.staffOrganizationList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getOrganizationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
        if (dataHolder.staffOrganizationList2Application == null) {
            Set<String> ids =
                    dataHolder.staffOrganizationList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getApplicationId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ApplicationBaseDto> baseDtoList =
                    applicationBaseDtoServiceInOrganizationRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(ApplicationBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, ApplicationBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ApplicationBaseDto::getId, Function.identity()));
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> dtoVoMap =
                    organizationRefApplicationBaseVoConverter
                            .convertToOrganizationRefApplicationBaseVoMap(baseDtoList);
            Map<ApplicationBaseDto, OrganizationRefApplicationBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.staffOrganizationList2Application =
                    dataHolder.staffOrganizationList.keySet().stream()
                            .map(StaffOrganizationBaseDto::getApplicationId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }
}
