package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.TeamDetailVoDataAssembler;
import com.pulse.organization.entrance.web.query.assembler.TeamDetailVoDataAssembler.TeamDetailVoDataHolder;
import com.pulse.organization.entrance.web.query.collector.TeamDetailVoDataCollector;
import com.pulse.organization.entrance.web.vo.OrganizationBaseVo;
import com.pulse.organization.entrance.web.vo.TeamDetailVo;
import com.pulse.organization.entrance.web.vo.TeamMemberWithStaffVo;
import com.pulse.organization.entrance.web.vo.TeamOrganizationVo;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.TeamDetailDto;
import com.pulse.organization.manager.dto.TeamMemberWithStaffDto;
import com.pulse.organization.manager.dto.TeamOrganizationDto;
import com.pulse.organization.service.TeamBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到TeamDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "d49b5040-fdd7-4623-aa27-e213cbd646c2|VO|CONVERTER")
public class TeamDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationBaseVoConverter organizationBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamBaseDtoService teamBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailVoDataAssembler teamDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private TeamDetailVoDataCollector teamDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TeamMemberWithStaffVoConverter teamMemberWithStaffVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private TeamOrganizationVoConverter teamOrganizationVoConverter;

    /** 把TeamDetailDto转换成TeamDetailVo */
    @AutoGenerated(locked = true, uuid = "5ce12ec5-9e1e-3ff4-9b41-3a9615695c64")
    public TeamDetailVo convertToTeamDetailVo(TeamDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToTeamDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装TeamDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "732df76c-9afe-34a6-a61f-befaaeafd6fe")
    public TeamDetailVo convertAndAssembleData(TeamDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装TeamDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d0a75c60-8a7a-376b-a7d5-9dbdec0323ea")
    public List<TeamDetailVo> convertAndAssembleDataList(List<TeamDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        TeamDetailVoDataHolder dataHolder = new TeamDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                teamBaseDtoService.getByIds(
                        dtoList.stream().map(TeamDetailDto::getId).collect(Collectors.toList())));
        Map<String, TeamDetailVo> voMap =
                convertToTeamDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        teamDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        teamDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把TeamDetailDto转换成TeamDetailVo */
    @AutoGenerated(locked = false, uuid = "d49b5040-fdd7-4623-aa27-e213cbd646c2-converter-Map")
    public Map<TeamDetailDto, TeamDetailVo> convertToTeamDetailVoMap(List<TeamDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<OrganizationBaseDto, OrganizationBaseVo> leadOrganizationMap =
                organizationBaseVoConverter.convertToOrganizationBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(TeamDetailDto::getLeadOrganization)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<TeamOrganizationDto, TeamOrganizationVo> teamOrganizationListMap =
                teamOrganizationVoConverter.convertToTeamOrganizationVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getTeamOrganizationList()))
                                .flatMap(dto -> dto.getTeamOrganizationList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<TeamMemberWithStaffDto, TeamMemberWithStaffVo> teamMemberWithStaffListMap =
                teamMemberWithStaffVoConverter.convertToTeamMemberWithStaffVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getTeamMemberWithStaffList()))
                                .flatMap(dto -> dto.getTeamMemberWithStaffList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<TeamDetailDto, TeamDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            TeamDetailVo vo = new TeamDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setLeadOrganization(
                                                    dto.getLeadOrganization() == null
                                                            ? null
                                                            : leadOrganizationMap.get(
                                                                    dto.getLeadOrganization()));
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setDeletedBy(dto.getDeletedBy());
                                            vo.setDescription(dto.getDescription());
                                            vo.setType(dto.getType());
                                            vo.setName(dto.getName());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setStatus(dto.getStatus());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setUseScope(dto.getUseScopeList());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setMdtSelfSelectFlag(dto.getMdtSelfSelectFlag());
                                            vo.setTeamOrganizationList(
                                                    dto.getTeamOrganizationList() == null
                                                            ? null
                                                            : dto.getTeamOrganizationList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    teamOrganizationListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setCollaborationScope(dto.getCollaborationScope());
                                            vo.setTeamMemberWithStaffList(
                                                    dto.getTeamMemberWithStaffList() == null
                                                            ? null
                                                            : dto
                                                                    .getTeamMemberWithStaffList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    teamMemberWithStaffListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把TeamDetailDto转换成TeamDetailVo */
    @AutoGenerated(locked = true, uuid = "d49b5040-fdd7-4623-aa27-e213cbd646c2-converter-list")
    public List<TeamDetailVo> convertToTeamDetailVoList(List<TeamDetailDto> dtoList) {
        return new ArrayList<>(convertToTeamDetailVoMap(dtoList).values());
    }
}
