package com.pulse.appointment_schedule.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.entrance.web.query.assembler.AppointmentScheduleRoomsVoDataAssembler;
import com.pulse.appointment_schedule.entrance.web.query.assembler.AppointmentScheduleRoomsVoDataAssembler.AppointmentScheduleRoomsVoDataHolder;
import com.pulse.appointment_schedule.entrance.web.query.collector.AppointmentScheduleRoomsVoDataCollector;
import com.pulse.appointment_schedule.entrance.web.vo.AppointmentScheduleRoomsVo;
import com.pulse.appointment_schedule.entrance.web.vo.ConsultingRoomAllVo;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleRoomsDto;
import com.pulse.appointment_schedule.service.AppointmentScheduleBaseDtoService;
import com.pulse.consulting_room.manager.dto.ConsultingRoomBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到AppointmentScheduleRoomsVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "a664fd2b-5bde-412e-8c33-ff034fbeca4d|VO|CONVERTER")
public class AppointmentScheduleRoomsVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleBaseDtoService appointmentScheduleBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleRoomsVoDataAssembler appointmentScheduleRoomsVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleRoomsVoDataCollector appointmentScheduleRoomsVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ConsultingRoomAllVoConverter consultingRoomAllVoConverter;

    /** 使用默认方式组装AppointmentScheduleRoomsVo列表数据 */
    @AutoGenerated(locked = true, uuid = "245427b3-99d2-3202-9213-697eebce119c")
    public List<AppointmentScheduleRoomsVo> convertAndAssembleDataList(
            List<AppointmentScheduleRoomsDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        AppointmentScheduleRoomsVoDataHolder dataHolder =
                new AppointmentScheduleRoomsVoDataHolder();
        dataHolder.setRootBaseDtoList(
                appointmentScheduleBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(AppointmentScheduleRoomsDto::getId)
                                .collect(Collectors.toList())));
        Map<String, AppointmentScheduleRoomsVo> voMap =
                convertToAppointmentScheduleRoomsVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        appointmentScheduleRoomsVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        appointmentScheduleRoomsVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把AppointmentScheduleRoomsDto转换成AppointmentScheduleRoomsVo */
    @AutoGenerated(locked = false, uuid = "a664fd2b-5bde-412e-8c33-ff034fbeca4d-converter-Map")
    public Map<AppointmentScheduleRoomsDto, AppointmentScheduleRoomsVo>
            convertToAppointmentScheduleRoomsVoMap(List<AppointmentScheduleRoomsDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<ConsultingRoomBaseDto, ConsultingRoomAllVo> roomMap =
                consultingRoomAllVoConverter.convertToConsultingRoomAllVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(AppointmentScheduleRoomsDto::getRoom)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<AppointmentScheduleRoomsDto, AppointmentScheduleRoomsVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            AppointmentScheduleRoomsVo vo =
                                                    new AppointmentScheduleRoomsVo();
                                            vo.setId(dto.getId());
                                            vo.setDayOfWeek(dto.getDayOfWeek());
                                            vo.setBranchInstitutionId(dto.getBranchInstitutionId());
                                            vo.setDepartmentId(dto.getDepartmentId());
                                            vo.setTimeDescription(dto.getTimeDescription());
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            vo.setDoctorId(dto.getDoctorId());
                                            vo.setRoom(
                                                    dto.getRoom() == null
                                                            ? null
                                                            : roomMap.get(dto.getRoom()));
                                            vo.setRegisterTime(dto.getRegisterTime());
                                            vo.setLimitNumber(dto.getLimitNumber());
                                            vo.setAddNumber(dto.getAddNumber());
                                            vo.setRemark(dto.getRemark());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setSchedulePlanId(dto.getSchedulePlanId());
                                            vo.setStatus(dto.getStatus());
                                            vo.setAppId(dto.getAppId());
                                            vo.setSchedulingTemplateId(
                                                    dto.getSchedulingTemplateId());
                                            vo.setSchedulingBusinessType(
                                                    dto.getSchedulingBusinessType());
                                            vo.setReviewFlag(dto.getReviewFlag());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把AppointmentScheduleRoomsDto转换成AppointmentScheduleRoomsVo */
    @AutoGenerated(locked = true, uuid = "a664fd2b-5bde-412e-8c33-ff034fbeca4d-converter-list")
    public List<AppointmentScheduleRoomsVo> convertToAppointmentScheduleRoomsVoList(
            List<AppointmentScheduleRoomsDto> dtoList) {
        return new ArrayList<>(convertToAppointmentScheduleRoomsVoMap(dtoList).values());
    }

    /** 使用默认方式组装AppointmentScheduleRoomsVo数据 */
    @AutoGenerated(locked = true, uuid = "bf69b412-ecbf-336b-937a-8f5a53f6d60d")
    public AppointmentScheduleRoomsVo convertAndAssembleData(AppointmentScheduleRoomsDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把AppointmentScheduleRoomsDto转换成AppointmentScheduleRoomsVo */
    @AutoGenerated(locked = true, uuid = "c030a6f9-ea4a-32e7-889b-dadc358f50e6")
    public AppointmentScheduleRoomsVo convertToAppointmentScheduleRoomsVo(
            AppointmentScheduleRoomsDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToAppointmentScheduleRoomsVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }
}
