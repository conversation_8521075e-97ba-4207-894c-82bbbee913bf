package com.pulse.appointment_schedule.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.entrance.web.query.assembler.AppointmentScheduleTimeDetailVoDataAssembler;
import com.pulse.appointment_schedule.entrance.web.query.assembler.AppointmentScheduleTimeDetailVoDataAssembler.AppointmentScheduleTimeDetailVoDataHolder;
import com.pulse.appointment_schedule.entrance.web.query.collector.AppointmentScheduleTimeDetailVoDataCollector;
import com.pulse.appointment_schedule.entrance.web.vo.AppointmentScheduleTimeDetailVo;
import com.pulse.appointment_schedule.entrance.web.vo.ClinicWaitRegisterTimeVo;
import com.pulse.appointment_schedule.entrance.web.vo.SchedulingNumberDetailVo;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleDto;
import com.pulse.appointment_schedule.manager.dto.SchedulingSlotDetailBaseDto;
import com.pulse.appointment_schedule.manager.dto.WaitTimeConfigBaseDto;
import com.pulse.appointment_schedule.service.AppointmentScheduleBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到AppointmentScheduleTimeDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "b849faf0-bf2a-432e-b90a-27d9ffb09ba2|VO|CONVERTER")
public class AppointmentScheduleTimeDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleBaseDtoService appointmentScheduleBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleTimeDetailVoDataAssembler
            appointmentScheduleTimeDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleTimeDetailVoDataCollector
            appointmentScheduleTimeDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ClinicWaitRegisterTimeVoConverter clinicWaitRegisterTimeVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulingNumberDetailVoConverter schedulingNumberDetailVoConverter;

    /** 使用默认方式组装AppointmentScheduleTimeDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "06ca6c70-ae7b-3138-ae10-92b3bd98e9cd")
    public List<AppointmentScheduleTimeDetailVo> convertAndAssembleDataList(
            List<AppointmentScheduleDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        AppointmentScheduleTimeDetailVoDataHolder dataHolder =
                new AppointmentScheduleTimeDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                appointmentScheduleBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(AppointmentScheduleDto::getId)
                                .collect(Collectors.toList())));
        Map<String, AppointmentScheduleTimeDetailVo> voMap =
                convertToAppointmentScheduleTimeDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        appointmentScheduleTimeDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        appointmentScheduleTimeDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装AppointmentScheduleTimeDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "1151454e-2c03-3a57-b205-a082df9e0d74")
    public AppointmentScheduleTimeDetailVo convertAndAssembleData(AppointmentScheduleDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把AppointmentScheduleDto转换成AppointmentScheduleTimeDetailVo */
    @AutoGenerated(locked = true, uuid = "229f6b09-3284-3b92-8d17-088c315dca6d")
    public AppointmentScheduleTimeDetailVo convertToAppointmentScheduleTimeDetailVo(
            AppointmentScheduleDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToAppointmentScheduleTimeDetailVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把AppointmentScheduleDto转换成AppointmentScheduleTimeDetailVo */
    @AutoGenerated(locked = false, uuid = "b849faf0-bf2a-432e-b90a-27d9ffb09ba2-converter-Map")
    public Map<AppointmentScheduleDto, AppointmentScheduleTimeDetailVo>
            convertToAppointmentScheduleTimeDetailVoMap(List<AppointmentScheduleDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<WaitTimeConfigBaseDto, ClinicWaitRegisterTimeVo> waitTimeConfigListMap =
                clinicWaitRegisterTimeVoConverter.convertToClinicWaitRegisterTimeVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getWaitTimeConfigList()))
                                .flatMap(dto -> dto.getWaitTimeConfigList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<SchedulingSlotDetailBaseDto, SchedulingNumberDetailVo> schedulingSlotDetailListMap =
                schedulingNumberDetailVoConverter.convertToSchedulingNumberDetailVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getSchedulingSlotDetailList()))
                                .flatMap(dto -> dto.getSchedulingSlotDetailList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<AppointmentScheduleDto, AppointmentScheduleTimeDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            AppointmentScheduleTimeDetailVo vo =
                                                    new AppointmentScheduleTimeDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setDayOfWeek(dto.getDayOfWeek());
                                            vo.setBranchInstitutionId(dto.getBranchInstitutionId());
                                            vo.setDepartmentId(dto.getDepartmentId());
                                            vo.setTimeDescription(dto.getTimeDescription());
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            vo.setDoctorId(dto.getDoctorId());
                                            vo.setRoomId(dto.getRoomId());
                                            vo.setRegisterTime(dto.getRegisterTime());
                                            vo.setLimitNumber(dto.getLimitNumber());
                                            vo.setAddNumber(dto.getAddNumber());
                                            vo.setRemark(dto.getRemark());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setSchedulePlanId(dto.getSchedulePlanId());
                                            vo.setStatus(dto.getStatus());
                                            vo.setAppId(dto.getAppId());
                                            vo.setSchedulingTemplateId(
                                                    dto.getSchedulingTemplateId());
                                            vo.setSchedulingBusinessType(
                                                    dto.getSchedulingBusinessType());
                                            vo.setReviewFlag(dto.getReviewFlag());
                                            vo.setWaitTimeConfigList(
                                                    dto.getWaitTimeConfigList() == null
                                                            ? null
                                                            : dto.getWaitTimeConfigList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    waitTimeConfigListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            vo.setSchedulingSlotDetailList(
                                                    dto.getSchedulingSlotDetailList() == null
                                                            ? null
                                                            : dto
                                                                    .getSchedulingSlotDetailList()
                                                                    .stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    schedulingSlotDetailListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把AppointmentScheduleDto转换成AppointmentScheduleTimeDetailVo */
    @AutoGenerated(locked = true, uuid = "b849faf0-bf2a-432e-b90a-27d9ffb09ba2-converter-list")
    public List<AppointmentScheduleTimeDetailVo> convertToAppointmentScheduleTimeDetailVoList(
            List<AppointmentScheduleDto> dtoList) {
        return new ArrayList<>(convertToAppointmentScheduleTimeDetailVoMap(dtoList).values());
    }
}
