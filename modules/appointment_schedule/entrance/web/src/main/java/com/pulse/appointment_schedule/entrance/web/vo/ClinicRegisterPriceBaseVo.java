package com.pulse.appointment_schedule.entrance.web.vo;

import com.pulse.appointment_schedule.common.enums.ClinicCostTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "8dc0df49-1ecd-470d-8add-6bbf63fe3316|VO|DEFINITION")
public class ClinicRegisterPriceBaseVo {
    /** 号类编码 */
    @AutoGenerated(locked = true, uuid = "e47575a7-4e46-42de-a645-97f3856906fd")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "3246ba88-9d58-4382-8913-c6054f998136")
    private Date createdAt;

    /** 费用类型 */
    @AutoGenerated(locked = true, uuid = "b164cc8b-c074-4a35-9586-b5fa0d45daf9")
    private ClinicCostTypeEnum feeType;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "8e875e69-a95d-40fb-8a10-7c5f624a3124")
    private String id;

    /** 项目名称 */
    @AutoGenerated(locked = true, uuid = "7796710c-b6d8-4799-a4e5-fd284eccc2bb")
    private String itemName;

    /** 价格 */
    @AutoGenerated(locked = true, uuid = "*************-471c-a34c-1fa9d0935a43")
    private BigDecimal price;

    /** 价格项目ID */
    @AutoGenerated(locked = true, uuid = "6dd90a4d-28fc-463d-ad7c-4d8c86f1427d")
    private String priceItemId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "131bbb5b-3a14-4401-8b0b-3b89dc3e291e")
    private Date updatedAt;
}
