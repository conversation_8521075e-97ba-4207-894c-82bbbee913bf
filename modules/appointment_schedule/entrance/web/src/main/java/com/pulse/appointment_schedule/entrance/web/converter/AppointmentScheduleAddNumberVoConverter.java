package com.pulse.appointment_schedule.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.entrance.web.query.assembler.AppointmentScheduleAddNumberVoDataAssembler;
import com.pulse.appointment_schedule.entrance.web.query.assembler.AppointmentScheduleAddNumberVoDataAssembler.AppointmentScheduleAddNumberVoDataHolder;
import com.pulse.appointment_schedule.entrance.web.query.collector.AppointmentScheduleAddNumberVoDataCollector;
import com.pulse.appointment_schedule.entrance.web.vo.AppointmentScheduleAddNumberVo;
import com.pulse.appointment_schedule.entrance.web.vo.AppointmentScheduleAddNumberVo.OrganizationBaseVo;
import com.pulse.appointment_schedule.entrance.web.vo.AppointmentScheduleAddNumberVo.StaffBaseVo;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到AppointmentScheduleAddNumberVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "14d636d1-045f-4f69-833c-bb82501541df|VO|CONVERTER")
public class AppointmentScheduleAddNumberVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleAddNumberVoDataAssembler appointmentScheduleAddNumberVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleAddNumberVoDataCollector appointmentScheduleAddNumberVoDataCollector;

    /** 使用默认方式组装AppointmentScheduleAddNumberVo数据 */
    @AutoGenerated(locked = true, uuid = "0fc1dccb-8294-39c9-a19c-5671ce135f51")
    public AppointmentScheduleAddNumberVo convertAndAssembleData(AppointmentScheduleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把AppointmentScheduleBaseDto转换成AppointmentScheduleAddNumberVo */
    @AutoGenerated(locked = false, uuid = "14d636d1-045f-4f69-833c-bb82501541df-converter-Map")
    public Map<AppointmentScheduleBaseDto, AppointmentScheduleAddNumberVo>
            convertToAppointmentScheduleAddNumberVoMap(List<AppointmentScheduleBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<AppointmentScheduleBaseDto, AppointmentScheduleAddNumberVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            AppointmentScheduleAddNumberVo vo =
                                                    new AppointmentScheduleAddNumberVo();
                                            vo.setId(dto.getId());
                                            vo.setSchedulingBusinessType(
                                                    dto.getSchedulingBusinessType());
                                            vo.setRegisterTime(dto.getRegisterTime());
                                            vo.setLimitNumber(dto.getLimitNumber());
                                            vo.setAddNumber(dto.getAddNumber());
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把AppointmentScheduleBaseDto转换成AppointmentScheduleAddNumberVo */
    @AutoGenerated(locked = true, uuid = "14d636d1-045f-4f69-833c-bb82501541df-converter-list")
    public List<AppointmentScheduleAddNumberVo> convertToAppointmentScheduleAddNumberVoList(
            List<AppointmentScheduleBaseDto> dtoList) {
        return new ArrayList<>(convertToAppointmentScheduleAddNumberVoMap(dtoList).values());
    }

    /** 把OrganizationBaseDto转换成OrganizationBaseVo */
    @AutoGenerated(locked = false, uuid = "2d160971-bf60-4e9d-8af9-3151ca26a40e-converter-Map")
    public Map<OrganizationBaseDto, AppointmentScheduleAddNumberVo.OrganizationBaseVo>
            convertToOrganizationBaseVoMap(List<OrganizationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<OrganizationBaseDto, OrganizationBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            OrganizationBaseVo vo = new OrganizationBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把OrganizationBaseDto转换成OrganizationBaseVo */
    @AutoGenerated(locked = true, uuid = "2d160971-bf60-4e9d-8af9-3151ca26a40e-converter-list")
    public List<AppointmentScheduleAddNumberVo.OrganizationBaseVo> convertToOrganizationBaseVoList(
            List<OrganizationBaseDto> dtoList) {
        return new ArrayList<>(convertToOrganizationBaseVoMap(dtoList).values());
    }

    /** 把AppointmentScheduleBaseDto转换成AppointmentScheduleAddNumberVo */
    @AutoGenerated(locked = true, uuid = "50fbba49-0e74-3705-a8d4-5261eb70beab")
    public AppointmentScheduleAddNumberVo convertToAppointmentScheduleAddNumberVo(
            AppointmentScheduleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToAppointmentScheduleAddNumberVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "97e16769-4af3-3fee-b2e8-0d9b5cab86d1")
    public AppointmentScheduleAddNumberVo.StaffBaseVo convertToStaffBaseVo(StaffBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把OrganizationBaseDto转换成OrganizationBaseVo */
    @AutoGenerated(locked = true, uuid = "9ea0ead9-30e7-3017-88d4-e601ae0163d7")
    public AppointmentScheduleAddNumberVo.OrganizationBaseVo convertToOrganizationBaseVo(
            OrganizationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToOrganizationBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = false, uuid = "c040f682-a8ca-4ec0-870a-c4706cfc8b92-converter-Map")
    public Map<StaffBaseDto, AppointmentScheduleAddNumberVo.StaffBaseVo> convertToStaffBaseVoMap(
            List<StaffBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffBaseDto, StaffBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffBaseVo vo = new StaffBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setName(dto.getName());
                                            vo.setCampusOrganizationId(
                                                    dto.getCampusOrganizationId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "c040f682-a8ca-4ec0-870a-c4706cfc8b92-converter-list")
    public List<AppointmentScheduleAddNumberVo.StaffBaseVo> convertToStaffBaseVoList(
            List<StaffBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装AppointmentScheduleAddNumberVo列表数据 */
    @AutoGenerated(locked = true, uuid = "fa982a62-0f3d-39e6-a1eb-4cb3c51cf9c3")
    public List<AppointmentScheduleAddNumberVo> convertAndAssembleDataList(
            List<AppointmentScheduleBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        AppointmentScheduleAddNumberVoDataHolder dataHolder =
                new AppointmentScheduleAddNumberVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, AppointmentScheduleAddNumberVo> voMap =
                convertToAppointmentScheduleAddNumberVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        appointmentScheduleAddNumberVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        appointmentScheduleAddNumberVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
