package com.pulse.appointment_schedule.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.pulse.appointment_schedule.common.enums.ClinicCostTypeEnum;
import com.pulse.appointment_schedule.entrance.web.query.assembler.SchedulePlanPriceVoDataAssembler;
import com.pulse.appointment_schedule.entrance.web.query.assembler.SchedulePlanPriceVoDataAssembler.SchedulePlanPriceVoDataHolder;
import com.pulse.appointment_schedule.entrance.web.query.collector.SchedulePlanPriceVoDataCollector;
import com.pulse.appointment_schedule.entrance.web.vo.ClinicRegisterTypeItemVo;
import com.pulse.appointment_schedule.entrance.web.vo.OrganizationDepartmentsVo;
import com.pulse.appointment_schedule.entrance.web.vo.SchedulePlanPriceVo;
import com.pulse.appointment_schedule.manager.dto.ClinicRegisterPriceItemDto;
import com.pulse.appointment_schedule.manager.dto.ClinicRegisterTypeItemDto;
import com.pulse.appointment_schedule.manager.dto.SchedulePlanPriceDto;
import com.pulse.appointment_schedule.service.SchedulePlanBaseDtoService;
import com.pulse.organization.manager.dto.OrganizationDepartmentDto;
import com.vs.code.AutoGenerated;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/** 完成Dto到SchedulePlanPriceVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "6ae803f4-5e18-4ced-a037-70c2b4dc16f8|VO|CONVERTER")
public class SchedulePlanPriceVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private OrganizationDepartmentsVoConverter organizationDepartmentsVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanBaseDtoService schedulePlanBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanPriceVoDataAssembler schedulePlanPriceVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private SchedulePlanPriceVoDataCollector schedulePlanPriceVoDataCollector;

    /** 使用默认方式组装SchedulePlanPriceVo数据 */
    @AutoGenerated(locked = true, uuid = "449b5e1c-5a4d-30d3-8dd9-4463f498d59b")
    public SchedulePlanPriceVo convertAndAssembleData(SchedulePlanPriceDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装SchedulePlanPriceVo列表数据 */
    @AutoGenerated(locked = true, uuid = "478981d1-3cfc-3db0-9561-ad5e36cc0456")
    public List<SchedulePlanPriceVo> convertAndAssembleDataList(
            List<SchedulePlanPriceDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        SchedulePlanPriceVoDataHolder dataHolder = new SchedulePlanPriceVoDataHolder();
        dataHolder.setRootBaseDtoList(
                schedulePlanBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(SchedulePlanPriceDto::getId)
                                .collect(Collectors.toList())));
        Map<String, SchedulePlanPriceVo> voMap =
                convertToSchedulePlanPriceVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        schedulePlanPriceVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        schedulePlanPriceVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把SchedulePlanPriceDto转换成SchedulePlanPriceVo */
    @AutoGenerated(locked = true, uuid = "4acf4b68-cdff-37b8-9b9c-731952749f67")
    public SchedulePlanPriceVo convertToSchedulePlanPriceVo(SchedulePlanPriceDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToSchedulePlanPriceVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把SchedulePlanPriceDto转换成SchedulePlanPriceVo */
    @AutoGenerated(locked = false, uuid = "6ae803f4-5e18-4ced-a037-70c2b4dc16f8-converter-Map")
    public Map<SchedulePlanPriceDto, SchedulePlanPriceVo> convertToSchedulePlanPriceVoMap(
            List<SchedulePlanPriceDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<OrganizationDepartmentDto, OrganizationDepartmentsVo> departmentMap =
                organizationDepartmentsVoConverter.convertToOrganizationDepartmentsVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(SchedulePlanPriceDto::getDepartment)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<SchedulePlanPriceDto, SchedulePlanPriceVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            SchedulePlanPriceVo vo = new SchedulePlanPriceVo();
                                            vo.setId(dto.getId());
                                            vo.setDayOfWeek(dto.getDayOfWeek());
                                            vo.setBranchInstitutionId(dto.getBranchInstitutionId());
                                            vo.setTimeDescription(dto.getTimeDescription());
                                            vo.setDoctorId(dto.getDoctorId());
                                            vo.setRoomId(dto.getRoomId());
                                            vo.setActivationDate(dto.getActivationDate());
                                            vo.setLimitNumber(dto.getLimitNumber());
                                            vo.setAddNumber(dto.getAddNumber());
                                            vo.setVisitEveryFewWeek(dto.getVisitEveryFewWeek());
                                            vo.setRemark(dto.getRemark());
                                            vo.setCancelFlag(dto.getCancelFlag());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setStatus(dto.getStatus());
                                            vo.setSchedulingTemplateId(
                                                    dto.getSchedulingTemplateId());
                                            vo.setPlanBusinessType(dto.getPlanBusinessType());
                                            vo.setDepartment(
                                                    dto.getDepartment() == null
                                                            ? null
                                                            : departmentMap.get(
                                                                    dto.getDepartment()));
                                            vo.setClinicRegisterTypeId(
                                                    dto.getClinicRegisterTypeId());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把SchedulePlanPriceDto转换成SchedulePlanPriceVo */
    @AutoGenerated(locked = true, uuid = "6ae803f4-5e18-4ced-a037-70c2b4dc16f8-converter-list")
    public List<SchedulePlanPriceVo> convertToSchedulePlanPriceVoList(
            List<SchedulePlanPriceDto> dtoList) {
        return new ArrayList<>(convertToSchedulePlanPriceVoMap(dtoList).values());
    }
}
