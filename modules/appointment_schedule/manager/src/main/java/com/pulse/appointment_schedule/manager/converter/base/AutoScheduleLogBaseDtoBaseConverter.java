package com.pulse.appointment_schedule.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.dto.AutoScheduleLogBaseDto;
import com.pulse.appointment_schedule.persist.dos.AutoScheduleLog;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "5bf9270c-c635-4fa5-99ae-5f8cd212c9d5|DTO|BASE_CONVERTER")
public class AutoScheduleLogBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public AutoScheduleLogBaseDto convertFromAutoScheduleLogToAutoScheduleLogBaseDto(
            AutoScheduleLog autoScheduleLog) {
        return convertFromAutoScheduleLogToAutoScheduleLogBaseDto(List.of(autoScheduleLog)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<AutoScheduleLogBaseDto> convertFromAutoScheduleLogToAutoScheduleLogBaseDto(
            List<AutoScheduleLog> autoScheduleLogList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(autoScheduleLogList)) {
            return new ArrayList<>();
        }
        List<AutoScheduleLogBaseDto> autoScheduleLogBaseDtoList = new ArrayList<>();
        for (AutoScheduleLog autoScheduleLog : autoScheduleLogList) {
            if (autoScheduleLog == null) {
                continue;
            }
            AutoScheduleLogBaseDto autoScheduleLogBaseDto = new AutoScheduleLogBaseDto();
            autoScheduleLogBaseDto.setId(autoScheduleLog.getId());
            autoScheduleLogBaseDto.setLockVersion(autoScheduleLog.getLockVersion());
            autoScheduleLogBaseDto.setScheduleDay(autoScheduleLog.getScheduleDay());
            autoScheduleLogBaseDto.setBranchInstitutionId(autoScheduleLog.getBranchInstitutionId());
            autoScheduleLogBaseDto.setCreatedAt(autoScheduleLog.getCreatedAt());
            autoScheduleLogBaseDto.setUpdatedAt(autoScheduleLog.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            autoScheduleLogBaseDtoList.add(autoScheduleLogBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return autoScheduleLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
