package com.pulse.appointment_schedule.manager.dto;

import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "ee91ad37-aa01-4d43-aa9e-d72295d99082|DTO|DEFINITION")
public class AppointmentDaysPlatformDto {
    /** 预约渠道ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "e59c3d5a-28f8-4842-bf50-74d1d61f559a")
    private AppointmentChannelBaseDto appointmentChannel;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "ad67e566-e09c-4c7f-9507-1ae953218831")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "6ae39497-336c-408b-a6f8-a30b6771d0b3")
    private String id;

    /** 预约开放提前天数 */
    @AutoGenerated(locked = true, uuid = "2ee94b98-6c49-4bf5-9870-20322ae5bd8b")
    private Long openDays;

    /** 开放时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f510adac-828c-498e-ba70-8628dc532214")
    private TimeEo openTime;

    /** 平台ID */
    @AutoGenerated(locked = true, uuid = "eb8172cf-cb8e-4a01-aabf-b1a75fbe87e9")
    private String platformId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "74d3914a-4106-421d-8592-a7237a72250b")
    private Date updatedAt;
}
