package com.pulse.appointment_schedule.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.AutoScheduleLogBaseDtoManager;
import com.pulse.appointment_schedule.manager.dto.AutoScheduleLogBaseDto;
import com.pulse.appointment_schedule.manager.dto.AutoScheduleLogDto;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@AutoGenerated(locked = false, uuid = "0cd5bda3-bffd-43e3-a520-7b225e0b3f23|DTO|BASE_CONVERTER")
public class AutoScheduleLogDtoBaseConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AutoScheduleLogBaseDtoManager autoScheduleLogBaseDtoManager;

    @AutoGenerated(locked = true)
    public AutoScheduleLogDto convertFromAutoScheduleLogBaseDtoToAutoScheduleLogDto(
            AutoScheduleLogBaseDto autoScheduleLogBaseDto) {
        return convertFromAutoScheduleLogBaseDtoToAutoScheduleLogDto(
                        List.of(autoScheduleLogBaseDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = false)
    public List<AutoScheduleLogDto> convertFromAutoScheduleLogBaseDtoToAutoScheduleLogDto(
            List<AutoScheduleLogBaseDto> autoScheduleLogBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(autoScheduleLogBaseDtoList)) {
            return new ArrayList<>();
        }
        List<AutoScheduleLogDto> autoScheduleLogDtoList = new ArrayList<>();
        for (AutoScheduleLogBaseDto autoScheduleLogBaseDto : autoScheduleLogBaseDtoList) {
            if (autoScheduleLogBaseDto == null) {
                continue;
            }
            AutoScheduleLogDto autoScheduleLogDto = new AutoScheduleLogDto();
            autoScheduleLogDto.setId(autoScheduleLogBaseDto.getId());
            autoScheduleLogDto.setScheduleDay(autoScheduleLogBaseDto.getScheduleDay());
            autoScheduleLogDto.setBranchInstitutionId(
                    autoScheduleLogBaseDto.getBranchInstitutionId());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            autoScheduleLogDtoList.add(autoScheduleLogDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return autoScheduleLogDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    @AutoGenerated(locked = true)
    public AutoScheduleLogBaseDto convertFromAutoScheduleLogDtoToAutoScheduleLogBaseDto(
            AutoScheduleLogDto autoScheduleLogDto) {
        return convertFromAutoScheduleLogDtoToAutoScheduleLogBaseDto(List.of(autoScheduleLogDto))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<AutoScheduleLogBaseDto> convertFromAutoScheduleLogDtoToAutoScheduleLogBaseDto(
            List<AutoScheduleLogDto> autoScheduleLogDtoList) {
        if (CollectionUtil.isEmpty(autoScheduleLogDtoList)) {
            return new ArrayList<>();
        }
        return autoScheduleLogBaseDtoManager.getByIds(
                autoScheduleLogDtoList.stream()
                        .map(AutoScheduleLogDto::getId)
                        .collect(Collectors.toList()));
    }
}
