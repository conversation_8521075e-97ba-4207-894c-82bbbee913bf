package com.pulse.appointment_schedule.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.AppointmentScheduleBaseDtoManager;
import com.pulse.appointment_schedule.manager.AppointmentScheduleDetailDtoManager;
import com.pulse.appointment_schedule.manager.converter.AppointmentScheduleBaseDtoConverter;
import com.pulse.appointment_schedule.manager.converter.AppointmentScheduleDetailDtoConverter;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleDetailDto;
import com.pulse.appointment_schedule.manager.facade.organization.OrganizationBaseDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.manager.facade.organization.StaffBaseDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.persist.dos.AppointmentSchedule;
import com.pulse.appointment_schedule.persist.mapper.AppointmentScheduleDao;
import com.pulse.organization.manager.dto.OrganizationBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "3f301744-6d94-477b-a24e-825a57cd5447|DTO|BASE_MANAGER_IMPL")
public abstract class AppointmentScheduleDetailDtoManagerBaseImpl
        implements AppointmentScheduleDetailDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleBaseDtoConverter appointmentScheduleBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleBaseDtoManager appointmentScheduleBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleDao appointmentScheduleDao;

    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleDetailDtoConverter appointmentScheduleDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private OrganizationBaseDtoServiceInAppointmentScheduleRpcAdapter
            organizationBaseDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private StaffBaseDtoServiceInAppointmentScheduleRpcAdapter
            staffBaseDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true, uuid = "0504ee67-168d-3fdb-96b2-e56386a28707")
    @Override
    public AppointmentScheduleDetailDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        AppointmentScheduleDetailDto appointmentScheduleDetailDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return appointmentScheduleDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "109f72f2-3587-3be0-9cb6-8b5b996212a0")
    @Override
    public List<AppointmentScheduleDetailDto> getByClinicRegisterTypeIds(
            List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicRegisterTypeId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1177fc32-37e4-3ef6-87e1-dc259706a5f0")
    @Override
    public List<AppointmentScheduleDetailDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList = appointmentScheduleDao.getByIds(id);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, AppointmentSchedule> appointmentScheduleMap =
                appointmentScheduleList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        appointmentScheduleList =
                id.stream()
                        .map(i -> appointmentScheduleMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2c213ab6-a61a-30c5-bec2-7b99cb7b15c7")
    @Override
    public List<AppointmentScheduleDetailDto> getBySchedulePlanId(String schedulePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleDetailDto> appointmentScheduleDetailDtoList =
                getBySchedulePlanIds(Arrays.asList(schedulePlanId));
        return appointmentScheduleDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3578760f-189a-3f4e-b15c-78704f9b63ca")
    @Override
    public List<AppointmentScheduleDetailDto> getBySchedulingTemplateIds(
            List<String> schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(schedulingTemplateId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getBySchedulingTemplateIds(schedulingTemplateId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "365ffe75-6414-39d0-9b22-f6fa500cda42")
    @Override
    public List<AppointmentScheduleDetailDto> getByDepartmentId(String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleDetailDto> appointmentScheduleDetailDtoList =
                getByDepartmentIds(Arrays.asList(departmentId));
        return appointmentScheduleDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "37b11e4b-e766-3004-b2b3-25945945f24a")
    @Override
    public List<AppointmentScheduleDetailDto> getByDoctorId(String doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleDetailDto> appointmentScheduleDetailDtoList =
                getByDoctorIds(Arrays.asList(doctorId));
        return appointmentScheduleDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "55948d4d-c9b4-3515-b559-9f4e95916abf")
    @Override
    public List<AppointmentScheduleDetailDto> getByDoctorIds(List<String> doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(doctorId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByDoctorIds(doctorId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "68e1d13c-b3a8-315b-ae56-441ac1669d49")
    @Override
    public List<AppointmentScheduleDetailDto> getByRoomId(String roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleDetailDto> appointmentScheduleDetailDtoList =
                getByRoomIds(Arrays.asList(roomId));
        return appointmentScheduleDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8f9418c7-5a6f-3609-9350-1c7b4ca29154")
    @Override
    public List<AppointmentScheduleDetailDto> getBySchedulePlanIds(List<String> schedulePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(schedulePlanId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getBySchedulePlanIds(schedulePlanId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a40507bc-136a-3b62-a452-902b74623174")
    @Override
    public List<AppointmentScheduleDetailDto> getByClinicRegisterTypeId(
            String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleDetailDto> appointmentScheduleDetailDtoList =
                getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        return appointmentScheduleDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b109df04-1e4a-39d6-9fd8-285c9e54fd5b")
    @Override
    public List<AppointmentScheduleDetailDto> getByDepartmentIds(List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(departmentId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByDepartmentIds(departmentId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c4fe54e5-8e16-311f-b43d-a5b6c4c990cb")
    @Override
    public List<AppointmentScheduleDetailDto> getByRoomIds(List<String> roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(roomId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByRoomIds(roomId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e5d8279c-220d-3d43-900b-e9f9453a02d9")
    @Override
    public List<AppointmentScheduleDetailDto> getBySchedulingTemplateId(
            String schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleDetailDto> appointmentScheduleDetailDtoList =
                getBySchedulingTemplateIds(Arrays.asList(schedulingTemplateId));
        return appointmentScheduleDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "fd162292-9d35-383b-8df7-0494d0898ecb")
    public List<AppointmentScheduleDetailDto>
            doConvertFromAppointmentScheduleToAppointmentScheduleDetailDto(
                    List<AppointmentSchedule> appointmentScheduleList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        Map<String, String> departmentIdMap =
                appointmentScheduleList.stream()
                        .filter(i -> i.getDepartmentId() != null)
                        .collect(
                                Collectors.toMap(
                                        AppointmentSchedule::getId,
                                        AppointmentSchedule::getDepartmentId));
        List<OrganizationBaseDto> departmentIdOrganizationBaseDtoList =
                organizationBaseDtoServiceInAppointmentScheduleRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(departmentIdMap.values())));
        Map<String, OrganizationBaseDto> departmentIdOrganizationBaseDtoMapRaw =
                departmentIdOrganizationBaseDtoList.stream()
                        .collect(Collectors.toMap(OrganizationBaseDto::getId, i -> i));
        Map<String, OrganizationBaseDto> departmentIdOrganizationBaseDtoMap =
                departmentIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        departmentIdOrganizationBaseDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                departmentIdOrganizationBaseDtoMapRaw.get(
                                                        i.getValue())));
        Map<String, String> doctorIdMap =
                appointmentScheduleList.stream()
                        .filter(i -> i.getDoctorId() != null)
                        .collect(
                                Collectors.toMap(
                                        AppointmentSchedule::getId,
                                        AppointmentSchedule::getDoctorId));
        List<StaffBaseDto> doctorIdStaffBaseDtoList =
                staffBaseDtoServiceInAppointmentScheduleRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(doctorIdMap.values())));
        Map<String, StaffBaseDto> doctorIdStaffBaseDtoMapRaw =
                doctorIdStaffBaseDtoList.stream()
                        .collect(Collectors.toMap(StaffBaseDto::getId, i -> i));
        Map<String, StaffBaseDto> doctorIdStaffBaseDtoMap =
                doctorIdMap.entrySet().stream()
                        .filter(i -> doctorIdStaffBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> doctorIdStaffBaseDtoMapRaw.get(i.getValue())));

        List<AppointmentScheduleBaseDto> baseDtoList =
                appointmentScheduleBaseDtoConverter
                        .convertFromAppointmentScheduleToAppointmentScheduleBaseDto(
                                appointmentScheduleList);
        Map<String, AppointmentScheduleDetailDto> dtoMap =
                appointmentScheduleDetailDtoConverter
                        .convertFromAppointmentScheduleBaseDtoToAppointmentScheduleDetailDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        AppointmentScheduleDetailDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<AppointmentScheduleDetailDto> appointmentScheduleDetailDtoList = new ArrayList<>();
        for (AppointmentSchedule i : appointmentScheduleList) {
            AppointmentScheduleDetailDto appointmentScheduleDetailDto = dtoMap.get(i.getId());
            if (appointmentScheduleDetailDto == null) {
                continue;
            }

            if (null != i.getDepartmentId()) {
                appointmentScheduleDetailDto.setDepartment(
                        departmentIdOrganizationBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getDoctorId()) {
                appointmentScheduleDetailDto.setDoctor(
                        doctorIdStaffBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            appointmentScheduleDetailDtoList.add(appointmentScheduleDetailDto);
        }
        return appointmentScheduleDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
