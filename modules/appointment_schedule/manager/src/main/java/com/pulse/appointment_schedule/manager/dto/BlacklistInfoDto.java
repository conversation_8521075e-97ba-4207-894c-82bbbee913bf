package com.pulse.appointment_schedule.manager.dto;

import com.pulse.appointment_schedule.common.enums.BlacklistSourceEnum;
import com.pulse.patient_information.manager.dto.PatientBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "ff8a36bb-0fb3-4246-8bc2-de98294594b8|DTO|DEFINITION")
public class BlacklistInfoDto {
    /** 黑名单来源 */
    @AutoGenerated(locked = true, uuid = "fb4887f7-8d2a-405a-bf21-0374f8506f79")
    private BlacklistSourceEnum blacklistSource;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "896bc711-4a70-4c49-beeb-56e3ff42c79c")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f59442b1-ee22-4244-a4b2-df204d671093")
    private String id;

    /** 限制原因 */
    @AutoGenerated(locked = true, uuid = "27f633c0-163f-43cc-b321-6a4fad19c5e9")
    private String limitReason;

    /** 限制时间 */
    @AutoGenerated(locked = true, uuid = "50d6955e-daed-46b9-8306-b712e59fb5dc")
    private Long limitTime;

    /** 患者信息 */
    @Valid
    @AutoGenerated(locked = true, uuid = "3b27ee4b-755f-4c4e-8491-e131670de6ad")
    private PatientBaseDto patient;

    /** 黑名单类型 */
    @AutoGenerated(locked = true, uuid = "b2efbef7-447d-4b91-8b9c-97ccb64b1a17")
    private String personType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "b10df83d-32f0-4a9f-90cc-58392db7acf6")
    private Date updatedAt;
}
