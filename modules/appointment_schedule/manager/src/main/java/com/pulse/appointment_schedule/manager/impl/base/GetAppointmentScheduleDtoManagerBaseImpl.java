package com.pulse.appointment_schedule.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.appointment_schedule.manager.AppointmentScheduleBaseDtoManager;
import com.pulse.appointment_schedule.manager.GetAppointmentScheduleDtoManager;
import com.pulse.appointment_schedule.manager.SchedulingSlotDetailDtoManager;
import com.pulse.appointment_schedule.manager.WaitTimeConfigDtoManager;
import com.pulse.appointment_schedule.manager.converter.AppointmentScheduleBaseDtoConverter;
import com.pulse.appointment_schedule.manager.converter.GetAppointmentScheduleDtoConverter;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.pulse.appointment_schedule.manager.dto.GetAppointmentScheduleDto;
import com.pulse.appointment_schedule.manager.dto.SchedulingSlotDetailDto;
import com.pulse.appointment_schedule.manager.dto.WaitTimeConfigDto;
import com.pulse.appointment_schedule.manager.facade.consulting_room.ConsultingRoomBaseDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.manager.facade.organization.StaffBaseDtoServiceInAppointmentScheduleRpcAdapter;
import com.pulse.appointment_schedule.persist.dos.AppointmentSchedule;
import com.pulse.appointment_schedule.persist.mapper.AppointmentScheduleDao;
import com.pulse.consulting_room.manager.dto.ConsultingRoomBaseDto;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "b738323e-2579-4630-a3e2-e8b6e26416f6|DTO|BASE_MANAGER_IMPL")
public abstract class GetAppointmentScheduleDtoManagerBaseImpl
        implements GetAppointmentScheduleDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleBaseDtoConverter appointmentScheduleBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleBaseDtoManager appointmentScheduleBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleDao appointmentScheduleDao;

    @AutoGenerated(locked = true)
    @Autowired
    private ConsultingRoomBaseDtoServiceInAppointmentScheduleRpcAdapter
            consultingRoomBaseDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private GetAppointmentScheduleDtoConverter getAppointmentScheduleDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private SchedulingSlotDetailDtoManager schedulingSlotDetailDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private StaffBaseDtoServiceInAppointmentScheduleRpcAdapter
            staffBaseDtoServiceInAppointmentScheduleRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private WaitTimeConfigDtoManager waitTimeConfigDtoManager;

    @AutoGenerated(locked = true, uuid = "0504ee67-168d-3fdb-96b2-e56386a28707")
    @Override
    public GetAppointmentScheduleDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<GetAppointmentScheduleDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        GetAppointmentScheduleDto getAppointmentScheduleDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return getAppointmentScheduleDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "075527b1-b8c8-35be-a67f-524cbeec151b")
    public List<GetAppointmentScheduleDto>
            doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(
                    List<AppointmentSchedule> appointmentScheduleList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        Map<String, String> roomIdMap =
                appointmentScheduleList.stream()
                        .filter(i -> i.getRoomId() != null)
                        .collect(
                                Collectors.toMap(
                                        AppointmentSchedule::getId,
                                        AppointmentSchedule::getRoomId));
        List<ConsultingRoomBaseDto> roomIdConsultingRoomBaseDtoList =
                consultingRoomBaseDtoServiceInAppointmentScheduleRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(roomIdMap.values())));
        Map<String, ConsultingRoomBaseDto> roomIdConsultingRoomBaseDtoMapRaw =
                roomIdConsultingRoomBaseDtoList.stream()
                        .collect(Collectors.toMap(ConsultingRoomBaseDto::getId, i -> i));
        Map<String, ConsultingRoomBaseDto> roomIdConsultingRoomBaseDtoMap =
                roomIdMap.entrySet().stream()
                        .filter(i -> roomIdConsultingRoomBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> roomIdConsultingRoomBaseDtoMapRaw.get(i.getValue())));
        Map<String, String> doctorIdMap =
                appointmentScheduleList.stream()
                        .filter(i -> i.getDoctorId() != null)
                        .collect(
                                Collectors.toMap(
                                        AppointmentSchedule::getId,
                                        AppointmentSchedule::getDoctorId));
        List<StaffBaseDto> doctorIdStaffBaseDtoList =
                staffBaseDtoServiceInAppointmentScheduleRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(doctorIdMap.values())));
        Map<String, StaffBaseDto> doctorIdStaffBaseDtoMapRaw =
                doctorIdStaffBaseDtoList.stream()
                        .collect(Collectors.toMap(StaffBaseDto::getId, i -> i));
        Map<String, StaffBaseDto> doctorIdStaffBaseDtoMap =
                doctorIdMap.entrySet().stream()
                        .filter(i -> doctorIdStaffBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> doctorIdStaffBaseDtoMapRaw.get(i.getValue())));

        List<SchedulingSlotDetailDto> schedulingSlotDetailDtoList =
                schedulingSlotDetailDtoManager.getByAppointmentScheduleIds(
                        appointmentScheduleList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<SchedulingSlotDetailDto>> idSchedulingSlotDetailDtoListMap =
                schedulingSlotDetailDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getAppointmentScheduleId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<WaitTimeConfigDto> waitTimeConfigDtoList =
                waitTimeConfigDtoManager.getByAppointmentScheduleIds(
                        appointmentScheduleList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<WaitTimeConfigDto>> idWaitTimeConfigDtoListMap =
                waitTimeConfigDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getAppointmentScheduleId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<AppointmentScheduleBaseDto> baseDtoList =
                appointmentScheduleBaseDtoConverter
                        .convertFromAppointmentScheduleToAppointmentScheduleBaseDto(
                                appointmentScheduleList);
        Map<String, GetAppointmentScheduleDto> dtoMap =
                getAppointmentScheduleDtoConverter
                        .convertFromAppointmentScheduleBaseDtoToGetAppointmentScheduleDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        GetAppointmentScheduleDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<GetAppointmentScheduleDto> getAppointmentScheduleDtoList = new ArrayList<>();
        for (AppointmentSchedule i : appointmentScheduleList) {
            GetAppointmentScheduleDto getAppointmentScheduleDto = dtoMap.get(i.getId());
            if (getAppointmentScheduleDto == null) {
                continue;
            }

            if (null != i.getRoomId()) {
                getAppointmentScheduleDto.setRoom(
                        roomIdConsultingRoomBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getDoctorId()) {
                getAppointmentScheduleDto.setDoctor(
                        doctorIdStaffBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getId()) {
                getAppointmentScheduleDto.setSchedulingSlotDetailList(
                        idSchedulingSlotDetailDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            if (null != i.getId()) {
                getAppointmentScheduleDto.setWaitTimeConfigList(
                        idWaitTimeConfigDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            getAppointmentScheduleDtoList.add(getAppointmentScheduleDto);
        }
        return getAppointmentScheduleDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "109f72f2-3587-3be0-9cb6-8b5b996212a0")
    @Override
    public List<GetAppointmentScheduleDto> getByClinicRegisterTypeIds(
            List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(clinicRegisterTypeId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1177fc32-37e4-3ef6-87e1-dc259706a5f0")
    @Override
    public List<GetAppointmentScheduleDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList = appointmentScheduleDao.getByIds(id);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, AppointmentSchedule> appointmentScheduleMap =
                appointmentScheduleList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        appointmentScheduleList =
                id.stream()
                        .map(i -> appointmentScheduleMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2c213ab6-a61a-30c5-bec2-7b99cb7b15c7")
    @Override
    public List<GetAppointmentScheduleDto> getBySchedulePlanId(String schedulePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<GetAppointmentScheduleDto> getAppointmentScheduleDtoList =
                getBySchedulePlanIds(Arrays.asList(schedulePlanId));
        return getAppointmentScheduleDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3578760f-189a-3f4e-b15c-78704f9b63ca")
    @Override
    public List<GetAppointmentScheduleDto> getBySchedulingTemplateIds(
            List<String> schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(schedulingTemplateId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getBySchedulingTemplateIds(schedulingTemplateId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "365ffe75-6414-39d0-9b22-f6fa500cda42")
    @Override
    public List<GetAppointmentScheduleDto> getByDepartmentId(String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<GetAppointmentScheduleDto> getAppointmentScheduleDtoList =
                getByDepartmentIds(Arrays.asList(departmentId));
        return getAppointmentScheduleDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "37b11e4b-e766-3004-b2b3-25945945f24a")
    @Override
    public List<GetAppointmentScheduleDto> getByDoctorId(String doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<GetAppointmentScheduleDto> getAppointmentScheduleDtoList =
                getByDoctorIds(Arrays.asList(doctorId));
        return getAppointmentScheduleDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "55948d4d-c9b4-3515-b559-9f4e95916abf")
    @Override
    public List<GetAppointmentScheduleDto> getByDoctorIds(List<String> doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(doctorId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByDoctorIds(doctorId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "68e1d13c-b3a8-315b-ae56-441ac1669d49")
    @Override
    public List<GetAppointmentScheduleDto> getByRoomId(String roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<GetAppointmentScheduleDto> getAppointmentScheduleDtoList =
                getByRoomIds(Arrays.asList(roomId));
        return getAppointmentScheduleDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8f9418c7-5a6f-3609-9350-1c7b4ca29154")
    @Override
    public List<GetAppointmentScheduleDto> getBySchedulePlanIds(List<String> schedulePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(schedulePlanId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getBySchedulePlanIds(schedulePlanId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a40507bc-136a-3b62-a452-902b74623174")
    @Override
    public List<GetAppointmentScheduleDto> getByClinicRegisterTypeId(String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<GetAppointmentScheduleDto> getAppointmentScheduleDtoList =
                getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        return getAppointmentScheduleDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b109df04-1e4a-39d6-9fd8-285c9e54fd5b")
    @Override
    public List<GetAppointmentScheduleDto> getByDepartmentIds(List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(departmentId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByDepartmentIds(departmentId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c4fe54e5-8e16-311f-b43d-a5b6c4c990cb")
    @Override
    public List<GetAppointmentScheduleDto> getByRoomIds(List<String> roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(roomId)) {
            return Collections.emptyList();
        }

        List<AppointmentSchedule> appointmentScheduleList =
                appointmentScheduleDao.getByRoomIds(roomId);
        if (CollectionUtil.isEmpty(appointmentScheduleList)) {
            return Collections.emptyList();
        }

        return doConvertFromAppointmentScheduleToGetAppointmentScheduleDto(appointmentScheduleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e5d8279c-220d-3d43-900b-e9f9453a02d9")
    @Override
    public List<GetAppointmentScheduleDto> getBySchedulingTemplateId(String schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<GetAppointmentScheduleDto> getAppointmentScheduleDtoList =
                getBySchedulingTemplateIds(Arrays.asList(schedulingTemplateId));
        return getAppointmentScheduleDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
