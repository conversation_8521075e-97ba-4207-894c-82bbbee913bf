package com.pulse.appointment_schedule.manager;

import com.pulse.appointment_schedule.manager.dto.SchedulePlanRoomDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "733f5a35-48ff-421a-84ed-65568b493bd0|DTO|MANAGER")
public interface SchedulePlanRoomDtoManager {

    @AutoGenerated(locked = true, uuid = "058dc447-b56d-3c3e-b4c3-80687fec42b7")
    List<SchedulePlanRoomDto> getByRoomIds(List<String> roomId);

    @AutoGenerated(locked = true, uuid = "0b052c3a-d73b-3c22-8210-ba168b748546")
    List<SchedulePlanRoomDto> getByDepartmentId(String departmentId);

    @AutoGenerated(locked = true, uuid = "15ec1b2f-4c75-366a-9ae3-35a6300ad446")
    List<SchedulePlanRoomDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "1941e7c0-7e3c-3b5b-8d03-080f175b3609")
    List<SchedulePlanRoomDto> getByClinicRegisterTypeIds(List<String> clinicRegisterTypeId);

    @AutoGenerated(locked = true, uuid = "6ebcca06-7c7e-3618-b202-24e9b581a0f1")
    List<SchedulePlanRoomDto> getByDoctorIds(List<String> doctorId);

    @AutoGenerated(locked = true, uuid = "864c1833-8a83-3bed-b26a-cf6ded03ff03")
    List<SchedulePlanRoomDto> getByRoomId(String roomId);

    @AutoGenerated(locked = true, uuid = "a02d0eaf-ed35-36ae-8360-d99839bdde6b")
    List<SchedulePlanRoomDto> getBySchedulingTemplateId(String schedulingTemplateId);

    @AutoGenerated(locked = true, uuid = "ac4a89ed-bccb-3b92-b782-6acb54fd9820")
    List<SchedulePlanRoomDto> getByClinicRegisterTypeId(String clinicRegisterTypeId);

    @AutoGenerated(locked = true, uuid = "b5bad184-3b4c-3872-aa48-734f32595715")
    List<SchedulePlanRoomDto> getBySchedulingTemplateIds(List<String> schedulingTemplateId);

    @AutoGenerated(locked = true, uuid = "bd6a79c3-79d2-3806-8bb9-7c6b56c4aade")
    SchedulePlanRoomDto getById(String id);

    @AutoGenerated(locked = true, uuid = "c3bf730d-a367-36e5-8207-436f9f30a50e")
    List<SchedulePlanRoomDto> getByDoctorId(String doctorId);

    @AutoGenerated(locked = true, uuid = "e4cf2795-1f6a-3fa0-860f-5f9510853a3e")
    List<SchedulePlanRoomDto> getByDepartmentIds(List<String> departmentId);
}
