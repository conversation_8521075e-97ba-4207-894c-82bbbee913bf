package com.pulse.appointment_schedule.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.appointment_schedule.manager.bo.*;
import com.pulse.appointment_schedule.manager.bo.NumberSourceConfigBO;
import com.pulse.appointment_schedule.persist.dos.NumberSourceConfig;
import com.pulse.appointment_schedule.service.base.BaseNumberSourceConfigBOService.MergeNumberSourceConfigBoResult;
import com.pulse.appointment_schedule.service.bto.MergeNumberSourceConfigBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "22356dc2-aaea-385e-b9ab-2dd2c7751f77")
public class BaseNumberSourceConfigBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行 */
    @AutoGenerated(locked = true)
    private NumberSourceConfigBO createMergeNumberSourceConfigOnDuplicateUpdate(
            BaseNumberSourceConfigBOService.MergeNumberSourceConfigBoResult boResult,
            MergeNumberSourceConfigBto mergeNumberSourceConfigBto) {
        NumberSourceConfigBO numberSourceConfigBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeNumberSourceConfigBto.getId() == null);
        if (!allNull && !found) {
            numberSourceConfigBO = NumberSourceConfigBO.getById(mergeNumberSourceConfigBto.getId());
            if (numberSourceConfigBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (numberSourceConfigBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(numberSourceConfigBO.convertToNumberSourceConfig());
                updatedBto.setBto(mergeNumberSourceConfigBto);
                updatedBto.setBo(numberSourceConfigBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "restrictionType")) {
                    numberSourceConfigBO.setRestrictionType(
                            mergeNumberSourceConfigBto.getRestrictionType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "scheduleEntrance")) {
                    numberSourceConfigBO.setScheduleEntrance(
                            mergeNumberSourceConfigBto.getScheduleEntrance());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "appointmentTypeId")) {
                    numberSourceConfigBO.setAppointmentTypeId(
                            mergeNumberSourceConfigBto.getAppointmentTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "openTime")) {
                    numberSourceConfigBO.setOpenTime(mergeNumberSourceConfigBto.getOpenTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "openDay")) {
                    numberSourceConfigBO.setOpenDay(mergeNumberSourceConfigBto.getOpenDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "updatedBy")) {
                    numberSourceConfigBO.setUpdatedBy(mergeNumberSourceConfigBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "updatedTime")) {
                    numberSourceConfigBO.setUpdatedTime(
                            mergeNumberSourceConfigBto.getUpdatedTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "remark")) {
                    numberSourceConfigBO.setRemark(mergeNumberSourceConfigBto.getRemark());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(numberSourceConfigBO.convertToNumberSourceConfig());
                updatedBto.setBto(mergeNumberSourceConfigBto);
                updatedBto.setBo(numberSourceConfigBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "restrictionType")) {
                    numberSourceConfigBO.setRestrictionType(
                            mergeNumberSourceConfigBto.getRestrictionType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "scheduleEntrance")) {
                    numberSourceConfigBO.setScheduleEntrance(
                            mergeNumberSourceConfigBto.getScheduleEntrance());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "appointmentTypeId")) {
                    numberSourceConfigBO.setAppointmentTypeId(
                            mergeNumberSourceConfigBto.getAppointmentTypeId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "openTime")) {
                    numberSourceConfigBO.setOpenTime(mergeNumberSourceConfigBto.getOpenTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "openDay")) {
                    numberSourceConfigBO.setOpenDay(mergeNumberSourceConfigBto.getOpenDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "updatedBy")) {
                    numberSourceConfigBO.setUpdatedBy(mergeNumberSourceConfigBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "updatedTime")) {
                    numberSourceConfigBO.setUpdatedTime(
                            mergeNumberSourceConfigBto.getUpdatedTime());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeNumberSourceConfigBto, "__$validPropertySet"),
                        "remark")) {
                    numberSourceConfigBO.setRemark(mergeNumberSourceConfigBto.getRemark());
                }
            }
        } else {
            numberSourceConfigBO = new NumberSourceConfigBO();
            if (pkExist) {
                numberSourceConfigBO.setId(mergeNumberSourceConfigBto.getId());
            } else {
                numberSourceConfigBO.setId(this.idGenerator.allocateId("number_source_config"));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "restrictionType")) {
                numberSourceConfigBO.setRestrictionType(
                        mergeNumberSourceConfigBto.getRestrictionType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "scheduleEntrance")) {
                numberSourceConfigBO.setScheduleEntrance(
                        mergeNumberSourceConfigBto.getScheduleEntrance());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "appointmentTypeId")) {
                numberSourceConfigBO.setAppointmentTypeId(
                        mergeNumberSourceConfigBto.getAppointmentTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "openTime")) {
                numberSourceConfigBO.setOpenTime(mergeNumberSourceConfigBto.getOpenTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "openDay")) {
                numberSourceConfigBO.setOpenDay(mergeNumberSourceConfigBto.getOpenDay());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "updatedBy")) {
                numberSourceConfigBO.setUpdatedBy(mergeNumberSourceConfigBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "updatedTime")) {
                numberSourceConfigBO.setUpdatedTime(mergeNumberSourceConfigBto.getUpdatedTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeNumberSourceConfigBto, "__$validPropertySet"),
                    "remark")) {
                numberSourceConfigBO.setRemark(mergeNumberSourceConfigBto.getRemark());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeNumberSourceConfigBto);
            addedBto.setBo(numberSourceConfigBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return numberSourceConfigBO;
    }

    @AutoGenerated(locked = true)
    protected MergeNumberSourceConfigBoResult mergeNumberSourceConfigBase(
            MergeNumberSourceConfigBto mergeNumberSourceConfigBto) {
        if (mergeNumberSourceConfigBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeNumberSourceConfigBoResult boResult = new MergeNumberSourceConfigBoResult();
        NumberSourceConfigBO numberSourceConfigBO =
                createMergeNumberSourceConfigOnDuplicateUpdate(
                        boResult, mergeNumberSourceConfigBto);
        boResult.setRootBo(numberSourceConfigBO);
        return boResult;
    }

    public static class MergeNumberSourceConfigBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public NumberSourceConfigBO getRootBo() {
            return (NumberSourceConfigBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeNumberSourceConfigBto, NumberSourceConfigBO> getCreatedBto(
                MergeNumberSourceConfigBto mergeNumberSourceConfigBto) {
            return this.getAddedResult(mergeNumberSourceConfigBto);
        }

        @AutoGenerated(locked = true)
        public NumberSourceConfig getDeleted_NumberSourceConfig() {
            return (NumberSourceConfig)
                    CollectionUtil.getFirst(this.getDeletedEntityList(NumberSourceConfig.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeNumberSourceConfigBto, NumberSourceConfig, NumberSourceConfigBO>
                getUpdatedBto(MergeNumberSourceConfigBto mergeNumberSourceConfigBto) {
            return super.getUpdatedResult(mergeNumberSourceConfigBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeNumberSourceConfigBto, NumberSourceConfigBO> getUnmodifiedBto(
                MergeNumberSourceConfigBto mergeNumberSourceConfigBto) {
            return super.getUnmodifiedResult(mergeNumberSourceConfigBto);
        }
    }
}
