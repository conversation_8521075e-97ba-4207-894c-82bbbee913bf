package com.pulse.appointment_schedule.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.AppointmentScheduleRoomDtoManager;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleRoomDto;
import com.pulse.appointment_schedule.service.converter.AppointmentScheduleRoomDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "bbe304f4-961e-4b63-bbf1-ccfcc443db5d|DTO|SERVICE")
public class AppointmentScheduleRoomDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleRoomDtoManager appointmentScheduleRoomDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private AppointmentScheduleRoomDtoServiceConverter appointmentScheduleRoomDtoServiceConverter;

    @PublicInterface(id = "a2116559-3653-4505-8efb-2f2b3f01c385", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "02638c96-8356-3e89-9aab-54235697f5d9")
    public List<AppointmentScheduleRoomDto> getBySchedulePlanId(
            @NotNull(message = "排班计划ID不能为空") String schedulePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySchedulePlanIds(Arrays.asList(schedulePlanId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "0081211e-4643-49bb-826a-ca2d49da917b", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "0a609141-9f15-3eaf-9b26-e41b822c6e99")
    public List<AppointmentScheduleRoomDto> getByRoomIds(
            @Valid @NotNull(message = "诊室id不能为空") List<String> roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        roomId = new ArrayList<>(new HashSet<>(roomId));
        List<AppointmentScheduleRoomDto> appointmentScheduleRoomDtoList =
                appointmentScheduleRoomDtoManager.getByRoomIds(roomId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return appointmentScheduleRoomDtoServiceConverter.AppointmentScheduleRoomDtoConverter(
                appointmentScheduleRoomDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "8e8c868c-a8f0-4bbc-9152-81500b776d2f", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "2c55f12b-6b79-394f-9091-f143bf6de023")
    public List<AppointmentScheduleRoomDto> getByDepartmentId(
            @NotNull(message = "科室id不能为空") String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDepartmentIds(Arrays.asList(departmentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "39fd3b60-5ef9-4453-982d-35570591a590", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "34741b37-7572-351e-a7d5-c4a2c1780b8e")
    public AppointmentScheduleRoomDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AppointmentScheduleRoomDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "dcb95ecc-fed1-4c6d-b2a4-52ac018dcdcd", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "4e2f435c-8111-34a6-962f-127056dcf8bd")
    public List<AppointmentScheduleRoomDto> getBySchedulingTemplateIds(
            @Valid @NotNull(message = "排班模板ID不能为空") List<String> schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        schedulingTemplateId = new ArrayList<>(new HashSet<>(schedulingTemplateId));
        List<AppointmentScheduleRoomDto> appointmentScheduleRoomDtoList =
                appointmentScheduleRoomDtoManager.getBySchedulingTemplateIds(schedulingTemplateId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return appointmentScheduleRoomDtoServiceConverter.AppointmentScheduleRoomDtoConverter(
                appointmentScheduleRoomDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "ebbdcaf7-4faf-44a0-80fa-ae656b4e888a", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "4e7cb986-8db6-392a-9f71-0e03c857dc5a")
    public List<AppointmentScheduleRoomDto> getByDoctorId(
            @NotNull(message = "医生id不能为空") String doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDoctorIds(Arrays.asList(doctorId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "5eb53ad0-f2c8-4736-b375-bcf8d49087df", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "5fe53c3a-2f70-3d80-9365-25d27ed59e56")
    public List<AppointmentScheduleRoomDto> getByDepartmentIds(
            @Valid @NotNull(message = "科室id不能为空") List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        departmentId = new ArrayList<>(new HashSet<>(departmentId));
        List<AppointmentScheduleRoomDto> appointmentScheduleRoomDtoList =
                appointmentScheduleRoomDtoManager.getByDepartmentIds(departmentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return appointmentScheduleRoomDtoServiceConverter.AppointmentScheduleRoomDtoConverter(
                appointmentScheduleRoomDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "a8152d9f-df76-4631-b8a9-3dccd377eaab", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "7fa75e17-ac14-341d-8eab-9db9526bd0a1")
    public List<AppointmentScheduleRoomDto> getByClinicRegisterTypeId(
            @NotNull(message = "挂号类别id不能为空") String clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByClinicRegisterTypeIds(Arrays.asList(clinicRegisterTypeId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "0dd73b99-6f17-438c-86f5-7073bb3e6275", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "95a436fd-c693-3d66-b7ef-319e13d38057")
    public List<AppointmentScheduleRoomDto> getBySchedulePlanIds(
            @Valid @NotNull(message = "排班计划ID不能为空") List<String> schedulePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        schedulePlanId = new ArrayList<>(new HashSet<>(schedulePlanId));
        List<AppointmentScheduleRoomDto> appointmentScheduleRoomDtoList =
                appointmentScheduleRoomDtoManager.getBySchedulePlanIds(schedulePlanId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return appointmentScheduleRoomDtoServiceConverter.AppointmentScheduleRoomDtoConverter(
                appointmentScheduleRoomDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "2a4d27cc-508b-4d82-938e-a6a9cea15cc3", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "b67e5c18-0065-3ca3-9861-342e9127f12c")
    public List<AppointmentScheduleRoomDto> getByRoomId(
            @NotNull(message = "诊室id不能为空") String roomId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRoomIds(Arrays.asList(roomId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "9f54b568-b163-42cf-a87e-954622c482aa", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "bd725000-fe90-37f6-a9af-05de29b65971")
    public List<AppointmentScheduleRoomDto> getBySchedulingTemplateId(
            @NotNull(message = "排班模板ID不能为空") String schedulingTemplateId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySchedulingTemplateIds(Arrays.asList(schedulingTemplateId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "188ba5f9-b0e6-4de2-a29b-411c6539f63c", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "c7b4fe26-df09-3e88-b1bf-87ed241a030e")
    public List<AppointmentScheduleRoomDto> getByDoctorIds(
            @Valid @NotNull(message = "医生id不能为空") List<String> doctorId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        doctorId = new ArrayList<>(new HashSet<>(doctorId));
        List<AppointmentScheduleRoomDto> appointmentScheduleRoomDtoList =
                appointmentScheduleRoomDtoManager.getByDoctorIds(doctorId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return appointmentScheduleRoomDtoServiceConverter.AppointmentScheduleRoomDtoConverter(
                appointmentScheduleRoomDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "8534bfa2-dad3-49a8-a7e4-8732f10e6d50", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "ced8d64a-5da0-3cca-a6eb-44f105f95a6f")
    public List<AppointmentScheduleRoomDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<AppointmentScheduleRoomDto> appointmentScheduleRoomDtoList =
                appointmentScheduleRoomDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return appointmentScheduleRoomDtoServiceConverter.AppointmentScheduleRoomDtoConverter(
                appointmentScheduleRoomDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "53a3168f-db20-45f0-9aa8-922953dbb9f6", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "fe6dbeea-62bc-37b7-beb7-5fc5e917f3f9")
    public List<AppointmentScheduleRoomDto> getByClinicRegisterTypeIds(
            @Valid @NotNull(message = "挂号类别id不能为空") List<String> clinicRegisterTypeId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        clinicRegisterTypeId = new ArrayList<>(new HashSet<>(clinicRegisterTypeId));
        List<AppointmentScheduleRoomDto> appointmentScheduleRoomDtoList =
                appointmentScheduleRoomDtoManager.getByClinicRegisterTypeIds(clinicRegisterTypeId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return appointmentScheduleRoomDtoServiceConverter.AppointmentScheduleRoomDtoConverter(
                appointmentScheduleRoomDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
