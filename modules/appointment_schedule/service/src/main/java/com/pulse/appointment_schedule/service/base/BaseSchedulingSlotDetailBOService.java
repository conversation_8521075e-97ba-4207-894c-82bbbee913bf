package com.pulse.appointment_schedule.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.appointment_schedule.manager.bo.*;
import com.pulse.appointment_schedule.manager.bo.SchedulingSlotDetailBO;
import com.pulse.appointment_schedule.persist.dos.SchedulingSlotDetail;
import com.pulse.appointment_schedule.service.base.BaseSchedulingSlotDetailBOService.DeleteScheduleSlotDetailBoResult;
import com.pulse.appointment_schedule.service.base.BaseSchedulingSlotDetailBOService.MergeScheduleSlotDetailBoResult;
import com.pulse.appointment_schedule.service.base.BaseSchedulingSlotDetailBOService.UpdateScheduleSlotDetailBoResult;
import com.pulse.appointment_schedule.service.base.BaseSchedulingSlotDetailBOService.UpdateSchedulingSlotDetailStatusBoResult;
import com.pulse.appointment_schedule.service.bto.DeleteScheduleSlotDetailBto;
import com.pulse.appointment_schedule.service.bto.MergeScheduleSlotDetailBto;
import com.pulse.appointment_schedule.service.bto.UpdateScheduleSlotDetailBto;
import com.pulse.appointment_schedule.service.bto.UpdateSchedulingSlotDetailStatusBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "f9f13238-346f-39ff-bbd1-026ce34dde20")
public class BaseSchedulingSlotDetailBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行 */
    @AutoGenerated(locked = true)
    private SchedulingSlotDetailBO createMergeScheduleSlotDetailOnDuplicateUpdate(
            BaseSchedulingSlotDetailBOService.MergeScheduleSlotDetailBoResult boResult,
            MergeScheduleSlotDetailBto mergeScheduleSlotDetailBto) {
        SchedulingSlotDetailBO schedulingSlotDetailBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeScheduleSlotDetailBto.getId() == null);
        if (!allNull && !found) {
            schedulingSlotDetailBO =
                    SchedulingSlotDetailBO.getById(mergeScheduleSlotDetailBto.getId());
            if (schedulingSlotDetailBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (schedulingSlotDetailBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(schedulingSlotDetailBO.convertToSchedulingSlotDetail());
                updatedBto.setBto(mergeScheduleSlotDetailBto);
                updatedBto.setBo(schedulingSlotDetailBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "appointmentScheduleId")) {
                    schedulingSlotDetailBO.setAppointmentScheduleId(
                            mergeScheduleSlotDetailBto.getAppointmentScheduleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "schedulePlanId")) {
                    schedulingSlotDetailBO.setSchedulePlanId(
                            mergeScheduleSlotDetailBto.getSchedulePlanId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "appointmentCategoryId")) {
                    schedulingSlotDetailBO.setAppointmentCategoryId(
                            mergeScheduleSlotDetailBto.getAppointmentCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "scheduleSlotNumber")) {
                    schedulingSlotDetailBO.setScheduleSlotNumber(
                            mergeScheduleSlotDetailBto.getScheduleSlotNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "appointmentStatus")) {
                    schedulingSlotDetailBO.setAppointmentStatus(
                            mergeScheduleSlotDetailBto.getAppointmentStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "planFlag")) {
                    schedulingSlotDetailBO.setPlanFlag(mergeScheduleSlotDetailBto.getPlanFlag());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(schedulingSlotDetailBO.convertToSchedulingSlotDetail());
                updatedBto.setBto(mergeScheduleSlotDetailBto);
                updatedBto.setBo(schedulingSlotDetailBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "appointmentScheduleId")) {
                    schedulingSlotDetailBO.setAppointmentScheduleId(
                            mergeScheduleSlotDetailBto.getAppointmentScheduleId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "schedulePlanId")) {
                    schedulingSlotDetailBO.setSchedulePlanId(
                            mergeScheduleSlotDetailBto.getSchedulePlanId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "appointmentCategoryId")) {
                    schedulingSlotDetailBO.setAppointmentCategoryId(
                            mergeScheduleSlotDetailBto.getAppointmentCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "scheduleSlotNumber")) {
                    schedulingSlotDetailBO.setScheduleSlotNumber(
                            mergeScheduleSlotDetailBto.getScheduleSlotNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "appointmentStatus")) {
                    schedulingSlotDetailBO.setAppointmentStatus(
                            mergeScheduleSlotDetailBto.getAppointmentStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeScheduleSlotDetailBto, "__$validPropertySet"),
                        "planFlag")) {
                    schedulingSlotDetailBO.setPlanFlag(mergeScheduleSlotDetailBto.getPlanFlag());
                }
            }
        } else {
            schedulingSlotDetailBO = new SchedulingSlotDetailBO();
            if (pkExist) {
                schedulingSlotDetailBO.setId(mergeScheduleSlotDetailBto.getId());
            } else {
                schedulingSlotDetailBO.setId(
                        String.valueOf(this.idGenerator.allocateId("scheduling_slot_detail")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeScheduleSlotDetailBto, "__$validPropertySet"),
                    "appointmentScheduleId")) {
                schedulingSlotDetailBO.setAppointmentScheduleId(
                        mergeScheduleSlotDetailBto.getAppointmentScheduleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeScheduleSlotDetailBto, "__$validPropertySet"),
                    "schedulePlanId")) {
                schedulingSlotDetailBO.setSchedulePlanId(
                        mergeScheduleSlotDetailBto.getSchedulePlanId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeScheduleSlotDetailBto, "__$validPropertySet"),
                    "appointmentCategoryId")) {
                schedulingSlotDetailBO.setAppointmentCategoryId(
                        mergeScheduleSlotDetailBto.getAppointmentCategoryId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeScheduleSlotDetailBto, "__$validPropertySet"),
                    "scheduleSlotNumber")) {
                schedulingSlotDetailBO.setScheduleSlotNumber(
                        mergeScheduleSlotDetailBto.getScheduleSlotNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeScheduleSlotDetailBto, "__$validPropertySet"),
                    "appointmentStatus")) {
                schedulingSlotDetailBO.setAppointmentStatus(
                        mergeScheduleSlotDetailBto.getAppointmentStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    mergeScheduleSlotDetailBto, "__$validPropertySet"),
                    "planFlag")) {
                schedulingSlotDetailBO.setPlanFlag(mergeScheduleSlotDetailBto.getPlanFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeScheduleSlotDetailBto);
            addedBto.setBo(schedulingSlotDetailBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return schedulingSlotDetailBO;
    }

    /** 删除对象:deleteScheduleSlotDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private SchedulingSlotDetailBO deleteDeleteScheduleSlotDetailOnMissThrowEx(
            BaseSchedulingSlotDetailBOService.DeleteScheduleSlotDetailBoResult boResult,
            DeleteScheduleSlotDetailBto deleteScheduleSlotDetailBto) {
        SchedulingSlotDetailBO schedulingSlotDetailBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteScheduleSlotDetailBto.getId() == null);
        if (!allNull && !found) {
            schedulingSlotDetailBO =
                    SchedulingSlotDetailBO.getById(deleteScheduleSlotDetailBto.getId());
            found = true;
        }
        if (schedulingSlotDetailBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(schedulingSlotDetailBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteScheduleSlotDetailBto);
            deletedBto.setEntity(schedulingSlotDetailBO.convertToSchedulingSlotDetail());
            boResult.getDeletedBtoList().add(deletedBto);
            return schedulingSlotDetailBO;
        }
    }

    /** 删除号源明细 */
    @AutoGenerated(locked = true)
    protected DeleteScheduleSlotDetailBoResult deleteScheduleSlotDetailBase(
            DeleteScheduleSlotDetailBto deleteScheduleSlotDetailBto) {
        if (deleteScheduleSlotDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteScheduleSlotDetailBoResult boResult = new DeleteScheduleSlotDetailBoResult();
        SchedulingSlotDetailBO schedulingSlotDetailBO =
                deleteDeleteScheduleSlotDetailOnMissThrowEx(boResult, deleteScheduleSlotDetailBto);
        boResult.setRootBo(schedulingSlotDetailBO);
        return boResult;
    }

    /** 保存号源明细 */
    @AutoGenerated(locked = true)
    protected MergeScheduleSlotDetailBoResult mergeScheduleSlotDetailBase(
            MergeScheduleSlotDetailBto mergeScheduleSlotDetailBto) {
        if (mergeScheduleSlotDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeScheduleSlotDetailBoResult boResult = new MergeScheduleSlotDetailBoResult();
        SchedulingSlotDetailBO schedulingSlotDetailBO =
                createMergeScheduleSlotDetailOnDuplicateUpdate(
                        boResult, mergeScheduleSlotDetailBto);
        boResult.setRootBo(schedulingSlotDetailBO);
        return boResult;
    }

    /** 更新号源明细 */
    @AutoGenerated(locked = true)
    protected UpdateScheduleSlotDetailBoResult updateScheduleSlotDetailBase(
            UpdateScheduleSlotDetailBto updateScheduleSlotDetailBto) {
        if (updateScheduleSlotDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateScheduleSlotDetailBoResult boResult = new UpdateScheduleSlotDetailBoResult();
        SchedulingSlotDetailBO schedulingSlotDetailBO =
                updateUpdateScheduleSlotDetailOnMissThrowEx(boResult, updateScheduleSlotDetailBto);
        boResult.setRootBo(schedulingSlotDetailBO);
        return boResult;
    }

    /** 根据排班ID和挂号序号更新排班号源明细的appointment_status字段。 */
    @AutoGenerated(locked = true)
    protected UpdateSchedulingSlotDetailStatusBoResult updateSchedulingSlotDetailStatusBase(
            UpdateSchedulingSlotDetailStatusBto updateSchedulingSlotDetailStatusBto) {
        if (updateSchedulingSlotDetailStatusBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateSchedulingSlotDetailStatusBoResult boResult =
                new UpdateSchedulingSlotDetailStatusBoResult();
        SchedulingSlotDetailBO schedulingSlotDetailBO =
                updateUpdateSchedulingSlotDetailStatusOnMissThrowEx(
                        boResult, updateSchedulingSlotDetailStatusBto);
        boResult.setRootBo(schedulingSlotDetailBO);
        return boResult;
    }

    /** 更新对象:updateScheduleSlotDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private SchedulingSlotDetailBO updateUpdateScheduleSlotDetailOnMissThrowEx(
            BaseSchedulingSlotDetailBOService.UpdateScheduleSlotDetailBoResult boResult,
            UpdateScheduleSlotDetailBto updateScheduleSlotDetailBto) {
        SchedulingSlotDetailBO schedulingSlotDetailBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateScheduleSlotDetailBto.getId() == null);
        if (!allNull && !found) {
            schedulingSlotDetailBO =
                    SchedulingSlotDetailBO.getById(updateScheduleSlotDetailBto.getId());
            found = true;
        }
        if (schedulingSlotDetailBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(schedulingSlotDetailBO.convertToSchedulingSlotDetail());
            updatedBto.setBto(updateScheduleSlotDetailBto);
            updatedBto.setBo(schedulingSlotDetailBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateScheduleSlotDetailBto, "__$validPropertySet"),
                    "appointmentStatus")) {
                schedulingSlotDetailBO.setAppointmentStatus(
                        updateScheduleSlotDetailBto.getAppointmentStatus());
            }
            return schedulingSlotDetailBO;
        }
    }

    /** 更新对象:updateSchedulingSlotDetailStatus,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private SchedulingSlotDetailBO updateUpdateSchedulingSlotDetailStatusOnMissThrowEx(
            BaseSchedulingSlotDetailBOService.UpdateSchedulingSlotDetailStatusBoResult boResult,
            UpdateSchedulingSlotDetailStatusBto updateSchedulingSlotDetailStatusBto) {
        SchedulingSlotDetailBO schedulingSlotDetailBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateSchedulingSlotDetailStatusBto.getId() == null);
        if (!allNull && !found) {
            schedulingSlotDetailBO =
                    SchedulingSlotDetailBO.getById(updateSchedulingSlotDetailStatusBto.getId());
            found = true;
        }
        if (schedulingSlotDetailBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(schedulingSlotDetailBO.convertToSchedulingSlotDetail());
            updatedBto.setBto(updateSchedulingSlotDetailStatusBto);
            updatedBto.setBo(schedulingSlotDetailBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateSchedulingSlotDetailStatusBto, "__$validPropertySet"),
                    "appointmentStatus")) {
                schedulingSlotDetailBO.setAppointmentStatus(
                        updateSchedulingSlotDetailStatusBto.getAppointmentStatus());
            }
            return schedulingSlotDetailBO;
        }
    }

    public static class MergeScheduleSlotDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public SchedulingSlotDetailBO getRootBo() {
            return (SchedulingSlotDetailBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeScheduleSlotDetailBto, SchedulingSlotDetailBO> getCreatedBto(
                MergeScheduleSlotDetailBto mergeScheduleSlotDetailBto) {
            return this.getAddedResult(mergeScheduleSlotDetailBto);
        }

        @AutoGenerated(locked = true)
        public SchedulingSlotDetail getDeleted_SchedulingSlotDetail() {
            return (SchedulingSlotDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(SchedulingSlotDetail.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeScheduleSlotDetailBto, SchedulingSlotDetail, SchedulingSlotDetailBO>
                getUpdatedBto(MergeScheduleSlotDetailBto mergeScheduleSlotDetailBto) {
            return super.getUpdatedResult(mergeScheduleSlotDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeScheduleSlotDetailBto, SchedulingSlotDetailBO> getUnmodifiedBto(
                MergeScheduleSlotDetailBto mergeScheduleSlotDetailBto) {
            return super.getUnmodifiedResult(mergeScheduleSlotDetailBto);
        }
    }

    public static class UpdateScheduleSlotDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public SchedulingSlotDetailBO getRootBo() {
            return (SchedulingSlotDetailBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateScheduleSlotDetailBto, SchedulingSlotDetailBO> getCreatedBto(
                UpdateScheduleSlotDetailBto updateScheduleSlotDetailBto) {
            return this.getAddedResult(updateScheduleSlotDetailBto);
        }

        @AutoGenerated(locked = true)
        public SchedulingSlotDetail getDeleted_SchedulingSlotDetail() {
            return (SchedulingSlotDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(SchedulingSlotDetail.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateScheduleSlotDetailBto, SchedulingSlotDetail, SchedulingSlotDetailBO>
                getUpdatedBto(UpdateScheduleSlotDetailBto updateScheduleSlotDetailBto) {
            return super.getUpdatedResult(updateScheduleSlotDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateScheduleSlotDetailBto, SchedulingSlotDetailBO> getUnmodifiedBto(
                UpdateScheduleSlotDetailBto updateScheduleSlotDetailBto) {
            return super.getUnmodifiedResult(updateScheduleSlotDetailBto);
        }
    }

    public static class DeleteScheduleSlotDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public SchedulingSlotDetailBO getRootBo() {
            return (SchedulingSlotDetailBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteScheduleSlotDetailBto, SchedulingSlotDetailBO> getCreatedBto(
                DeleteScheduleSlotDetailBto deleteScheduleSlotDetailBto) {
            return this.getAddedResult(deleteScheduleSlotDetailBto);
        }

        @AutoGenerated(locked = true)
        public SchedulingSlotDetail getDeleted_SchedulingSlotDetail() {
            return (SchedulingSlotDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(SchedulingSlotDetail.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteScheduleSlotDetailBto, SchedulingSlotDetail, SchedulingSlotDetailBO>
                getUpdatedBto(DeleteScheduleSlotDetailBto deleteScheduleSlotDetailBto) {
            return super.getUpdatedResult(deleteScheduleSlotDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteScheduleSlotDetailBto, SchedulingSlotDetailBO> getUnmodifiedBto(
                DeleteScheduleSlotDetailBto deleteScheduleSlotDetailBto) {
            return super.getUnmodifiedResult(deleteScheduleSlotDetailBto);
        }
    }

    public static class UpdateSchedulingSlotDetailStatusBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public SchedulingSlotDetailBO getRootBo() {
            return (SchedulingSlotDetailBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateSchedulingSlotDetailStatusBto, SchedulingSlotDetailBO> getCreatedBto(
                UpdateSchedulingSlotDetailStatusBto updateSchedulingSlotDetailStatusBto) {
            return this.getAddedResult(updateSchedulingSlotDetailStatusBto);
        }

        @AutoGenerated(locked = true)
        public SchedulingSlotDetail getDeleted_SchedulingSlotDetail() {
            return (SchedulingSlotDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(SchedulingSlotDetail.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateSchedulingSlotDetailStatusBto,
                        SchedulingSlotDetail,
                        SchedulingSlotDetailBO>
                getUpdatedBto(
                        UpdateSchedulingSlotDetailStatusBto updateSchedulingSlotDetailStatusBto) {
            return super.getUpdatedResult(updateSchedulingSlotDetailStatusBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateSchedulingSlotDetailStatusBto, SchedulingSlotDetailBO>
                getUnmodifiedBto(
                        UpdateSchedulingSlotDetailStatusBto updateSchedulingSlotDetailStatusBto) {
            return super.getUnmodifiedResult(updateSchedulingSlotDetailStatusBto);
        }
    }
}
