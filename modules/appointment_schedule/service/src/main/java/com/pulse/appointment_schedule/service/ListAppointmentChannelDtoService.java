package com.pulse.appointment_schedule.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.ListAppointmentChannelDtoManager;
import com.pulse.appointment_schedule.manager.dto.ListAppointmentChannelDto;
import com.pulse.appointment_schedule.service.converter.ListAppointmentChannelDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "f1388cc1-bb1c-4488-b921-96726d91fc26|DTO|SERVICE")
public class ListAppointmentChannelDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ListAppointmentChannelDtoManager listAppointmentChannelDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ListAppointmentChannelDtoServiceConverter listAppointmentChannelDtoServiceConverter;

    @PublicInterface(id = "0e323801-58e8-43ec-9431-08b91df6e8f1", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "1803015b-080a-3124-b728-efd4a2887507")
    public List<ListAppointmentChannelDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ListAppointmentChannelDto> listAppointmentChannelDtoList =
                listAppointmentChannelDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return listAppointmentChannelDtoServiceConverter.ListAppointmentChannelDtoConverter(
                listAppointmentChannelDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "44118a3b-48b4-4878-a7ae-e77ac51951d9", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "86ccd762-df80-3e5e-9baa-8d254114f142")
    public List<ListAppointmentChannelDto> getByAppointmentCategoryId(
            @NotNull(message = "号源分类ID不能为空") String appointmentCategoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAppointmentCategoryIds(Arrays.asList(appointmentCategoryId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "44521607-8b0a-417a-9bf4-8d04e77d3d68", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "9516fb80-23d7-375c-aa52-96fab695f9da")
    public List<ListAppointmentChannelDto> getByAppointmentCategoryIds(
            @Valid @NotNull(message = "号源分类ID不能为空") List<String> appointmentCategoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        appointmentCategoryId = new ArrayList<>(new HashSet<>(appointmentCategoryId));
        List<ListAppointmentChannelDto> listAppointmentChannelDtoList =
                listAppointmentChannelDtoManager.getByAppointmentCategoryIds(appointmentCategoryId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return listAppointmentChannelDtoServiceConverter.ListAppointmentChannelDtoConverter(
                listAppointmentChannelDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "1dc649dc-8e31-4230-8179-f98466d4cf65", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "ab2a65c1-3fb7-3fe3-afde-cdac3de36ff2")
    public ListAppointmentChannelDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ListAppointmentChannelDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
