package com.pulse.appointment_schedule.service.converter;

import com.pulse.appointment_schedule.manager.dto.WaitTimeConfigBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "11085515-92b7-3015-9fc7-780c13a9757a")
public class WaitTimeConfigBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<WaitTimeConfigBaseDto> WaitTimeConfigBaseDtoConverter(
            List<WaitTimeConfigBaseDto> waitTimeConfigBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return waitTimeConfigBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
