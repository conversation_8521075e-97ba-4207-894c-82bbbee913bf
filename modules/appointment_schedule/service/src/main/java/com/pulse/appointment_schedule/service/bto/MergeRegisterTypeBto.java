package com.pulse.appointment_schedule.service.bto;

import com.pulse.appointment_schedule.common.enums.ClinicCostTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ClinicRegisterType
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "67f3a105-dd74-4990-88fc-dc3e51e82422|BTO|DEFINITION")
public class MergeRegisterTypeBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "067ae775-986e-4d5c-bfd1-b8c8e88eff38")
    private Boolean cancelFlag;

    /** 常用类别标识 */
    @AutoGenerated(locked = true, uuid = "1dac559b-d455-40fc-9071-41af04317b08")
    private Boolean categoryMarker;

    @Valid
    @AutoGenerated(locked = true, uuid = "622df12d-1a85-4aef-a873-076983a099be")
    private List<MergeRegisterTypeBto.ClinicRegisterPriceBto> clinicRegisterPriceBtoList;

    /** 挂号类别 */
    @AutoGenerated(locked = true, uuid = "38c0a5f2-f7d5-4c49-b5f8-ea6b8dc1f1a9")
    private String clinicRegisterTypeId;

    /** 挂号类别名称 */
    @AutoGenerated(locked = true, uuid = "db8ab2dc-8552-439a-bff8-12df5ad25a77")
    private String clinicRegisterTypeName;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "0621a159-40fd-4ee9-9b5c-c3ba768ca823")
    private String createdBy;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f79b6260-d4af-4432-9b12-e642f57f7f44")
    private String id;

    /** 挂号类别大类ID */
    @AutoGenerated(locked = true, uuid = "9b66d093-721f-42aa-887b-254d62e1b1c7")
    private String majorCategorieId;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "342e0c46-18f7-48c6-8290-9c1a42b1e228")
    private InputCodeEo otherCode;

    /** 修改人 */
    @AutoGenerated(locked = true, uuid = "4a3e8c95-42a0-4a72-af63-58a4b288ac3a")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setCancelFlag(Boolean cancelFlag) {
        this.__$validPropertySet.add("cancelFlag");
        this.cancelFlag = cancelFlag;
    }

    @AutoGenerated(locked = true)
    public void setCategoryMarker(Boolean categoryMarker) {
        this.__$validPropertySet.add("categoryMarker");
        this.categoryMarker = categoryMarker;
    }

    @AutoGenerated(locked = true)
    public void setClinicRegisterPriceBtoList(
            List<MergeRegisterTypeBto.ClinicRegisterPriceBto> clinicRegisterPriceBtoList) {
        this.__$validPropertySet.add("clinicRegisterPriceBtoList");
        this.clinicRegisterPriceBtoList = clinicRegisterPriceBtoList;
    }

    @AutoGenerated(locked = true)
    public void setClinicRegisterTypeId(String clinicRegisterTypeId) {
        this.__$validPropertySet.add("clinicRegisterTypeId");
        this.clinicRegisterTypeId = clinicRegisterTypeId;
    }

    @AutoGenerated(locked = true)
    public void setClinicRegisterTypeName(String clinicRegisterTypeName) {
        this.__$validPropertySet.add("clinicRegisterTypeName");
        this.clinicRegisterTypeName = clinicRegisterTypeName;
    }

    @AutoGenerated(locked = true)
    public void setCreatedBy(String createdBy) {
        this.__$validPropertySet.add("createdBy");
        this.createdBy = createdBy;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setMajorCategorieId(String majorCategorieId) {
        this.__$validPropertySet.add("majorCategorieId");
        this.majorCategorieId = majorCategorieId;
    }

    @AutoGenerated(locked = true)
    public void setOtherCode(InputCodeEo otherCode) {
        this.__$validPropertySet.add("otherCode");
        this.otherCode = otherCode;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }

    /**
     * <b>[源自]</b> ClinicRegisterPrice
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ClinicRegisterPriceBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "f6c7973f-d07b-4fc0-8631-dcfc707819a4")
        private String id;

        /** 价格项目ID */
        @AutoGenerated(locked = true, uuid = "2c841963-425c-40af-b1c0-8b56380994a3")
        private String priceItemId;

        /** 费用类型 */
        @AutoGenerated(locked = true, uuid = "acb19261-c41b-4114-a661-e58616a67420")
        private ClinicCostTypeEnum feeType;

        /** 价格 */
        @AutoGenerated(locked = true, uuid = "6da380bd-0f68-462d-8448-23b0d1fd6b71")
        private BigDecimal price;

        /** 项目名称 */
        @AutoGenerated(locked = true, uuid = "98fcc979-674f-4417-a531-db3b810f6b21")
        private String itemName;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setPriceItemId(String priceItemId) {
            this.__$validPropertySet.add("priceItemId");
            this.priceItemId = priceItemId;
        }

        @AutoGenerated(locked = true)
        public void setFeeType(ClinicCostTypeEnum feeType) {
            this.__$validPropertySet.add("feeType");
            this.feeType = feeType;
        }

        @AutoGenerated(locked = true)
        public void setPrice(BigDecimal price) {
            this.__$validPropertySet.add("price");
            this.price = price;
        }

        @AutoGenerated(locked = true)
        public void setItemName(String itemName) {
            this.__$validPropertySet.add("itemName");
            this.itemName = itemName;
        }
    }
}
