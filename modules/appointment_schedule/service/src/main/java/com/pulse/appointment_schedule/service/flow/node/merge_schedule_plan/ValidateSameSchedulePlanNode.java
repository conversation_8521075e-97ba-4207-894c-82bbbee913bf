package com.pulse.appointment_schedule.service.flow.node.merge_schedule_plan;

import com.pulse.appointment_schedule.manager.dto.SchedulePlanDto;
import com.pulse.appointment_schedule.persist.qto.ListSchedulePlanQto;
import com.pulse.appointment_schedule.service.bto.MergeSchedulePlanBto;
import com.pulse.appointment_schedule.service.flow.context.MergeSchedulePlanContext;
import com.pulse.appointment_schedule.service.query.SchedulePlanDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.es.query.VSQueryResult;
import com.vs.flow.node.NodeIfComponent;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component("appointmentSchedule-mergeSchedulePlan-validateSameSchedulePlan")
@AutoGenerated(locked = false, uuid = "cd218ac2-e1af-4da5-8465-1414fb484aad|FLOW_NODE|DEFINITION")
public class ValidateSameSchedulePlanNode extends NodeIfComponent {

    @Resource private SchedulePlanDtoQueryService schedulePlanDtoQueryService;

    /**
     * 实现流程判断逻辑 节点之间传参都必须通过Context传递 如果要去取Context，调用参数 getFirstContextBean() 如果要终止流程，调用
     * super.setEnd();
     */
    @AutoGenerated(locked = false, uuid = "cd218ac2-e1af-4da5-8465-1414fb484aad")
    public boolean processIf() {
        /** This block is generated by vs, do not modify, start anchor 1 */
        /** 获取宿主流程的context */
        MergeSchedulePlanContext context = getFirstContextBean();
        MergeSchedulePlanBto schedulePlanBto = context.getSchedulePlanBto();
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 以下开始处理业务逻辑
        if (schedulePlanBto.getId() != null && !schedulePlanBto.getId().isEmpty()) {
            return false;
        }
        ListSchedulePlanQto listSchedulePlanQto = new ListSchedulePlanQto();
        listSchedulePlanQto.setBranchInstitutionIdIs(
                schedulePlanBto.getBranchInstitutionId()); // 院区
        listSchedulePlanQto.setDayOfWeekIs(schedulePlanBto.getDayOfWeek()); // 星期
        listSchedulePlanQto.setTimeDescriptionIs(schedulePlanBto.getTimeDescription()); // 午别
        listSchedulePlanQto.setClinicRegisterTypeIdIs(
                schedulePlanBto.getClinicRegisterTypeId()); // 挂号类别
        listSchedulePlanQto.setDepartmentIdIs(schedulePlanBto.getDepartmentId()); // 科室
        if (schedulePlanBto.getDoctorId().isEmpty()) {
            listSchedulePlanQto.setDoctorIdIs(schedulePlanBto.getDoctorId());
        }
        listSchedulePlanQto.setSize(1);
        listSchedulePlanQto.setFrom(0);
        VSQueryResult<SchedulePlanDto> schedulePlanDtoVSQueryResult =
                schedulePlanDtoQueryService.listSchedulePlanWaterfall(listSchedulePlanQto);

        if (schedulePlanDtoVSQueryResult.getCount() > 0) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "排班不能重复!");
        }

        System.out.println("validate_same_schedule_plan");
        return false;
    }
}
