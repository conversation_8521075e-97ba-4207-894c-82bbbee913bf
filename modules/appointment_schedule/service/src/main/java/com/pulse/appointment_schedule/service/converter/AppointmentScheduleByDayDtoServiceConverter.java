package com.pulse.appointment_schedule.service.converter;

import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleByDayDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "cb70b911-4204-3bc9-bcfb-1080828c3dab")
public class AppointmentScheduleByDayDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<AppointmentScheduleByDayDto> AppointmentScheduleByDayDtoConverter(
            List<AppointmentScheduleByDayDto> appointmentScheduleByDayDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return appointmentScheduleByDayDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
