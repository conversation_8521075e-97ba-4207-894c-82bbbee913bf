package com.pulse.appointment_schedule.service;

import com.pulse.appointment_schedule.manager.bo.*;
import com.pulse.appointment_schedule.manager.dto.*;
import com.pulse.appointment_schedule.manager.dto.AppointmentScheduleBaseDto;
import com.pulse.appointment_schedule.persist.dos.AppointmentSchedule;
import com.pulse.appointment_schedule.persist.dos.CancellationRecord;
import com.pulse.appointment_schedule.service.base.BaseAppointmentScheduleBOService;
import com.pulse.appointment_schedule.service.bto.*;
import com.pulse.appointment_schedule.service.bto.MergeAppointmentScheduleBto;
import com.pulse.appointment_schedule.service.bto.MergeCancellationRecordBto;
import com.pulse.appointment_schedule.service.bto.UpdateAppointmentScheduleStatusBto;
import com.pulse.appointment_schedule.service.bto.UpdateScheduleAppointmentDoctorDepartmentBto;
import com.pulse.appointment_schedule.service.flow.node.merge_schedule_plan.MergeWaitTimeConfigNode;
import com.pulse.appointment_schedule.service.query.AppointmentScheduleDtoQueryService;
import com.pulse.appointment_schedule.service.query.AutoScheduleLogDtoQueryService;
import com.pulse.appointment_schedule.service.query.SchedulePlanDtoQueryService;
import com.vs.bo.AddedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "a6624154-e4e2-4a74-bb10-5fe29e95e960|BO|SERVICE")
public class AppointmentScheduleBOService extends BaseAppointmentScheduleBOService {
    @AutoGenerated(locked = true)
    @Resource
    private AppointmentScheduleBaseDtoService appointmentScheduleBaseDtoService;

    @Autowired @Resource private AutoScheduleLogBOService autoScheduleLogBOService;

    @Autowired @Resource private MergeWaitTimeConfigNode mergeWaitTimeConfig;

    @Resource private AutoScheduleLogDtoQueryService autoScheduleLogDtoQueryService;

    @Resource private SchedulePlanDtoQueryService schedulePlanDtoQueryService;

    @Resource private AppointmentScheduleDtoQueryService appointmentScheduleDtoQueryService;

    /** 保存排班 */
    @PublicInterface(id = "615500d7-c569-4a94-b5f7-2653a9e317ca", module = "appointment_schedule")
    @Transactional
    @AutoGenerated(locked = false, uuid = "53285886-9c78-4164-919b-f5b34e3481d9")
    public String mergeAppointmentSchedule(
            @Valid @NotNull MergeAppointmentScheduleBto mergeAppointmentScheduleBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        AppointmentScheduleBaseDto appointmentScheduleBaseDto = null;
        if (mergeAppointmentScheduleBto.getId() != null) {
            appointmentScheduleBaseDto =
                    appointmentScheduleBaseDtoService.getById(mergeAppointmentScheduleBto.getId());
        }
        MergeAppointmentScheduleBoResult boResult =
                super.mergeAppointmentScheduleBase(mergeAppointmentScheduleBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeAppointmentScheduleBto */
        {
            MergeAppointmentScheduleBto bto =
                    boResult
                            .<MergeAppointmentScheduleBto>getBtoOfType(
                                    MergeAppointmentScheduleBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeAppointmentScheduleBto, AppointmentSchedule, AppointmentScheduleBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeAppointmentScheduleBto, AppointmentScheduleBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                AppointmentScheduleBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                AppointmentSchedule entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                AppointmentScheduleBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新排班状态 */
    @PublicInterface(id = "e376d604-7d93-47b7-a668-46e39920c940", module = "appointment_schedule")
    @Transactional
    @AutoGenerated(locked = false, uuid = "84b6f92c-7fa0-4e0a-8ca7-8a6b3a294b25")
    public String updateAppointmentScheduleStatus(
            @Valid @NotNull UpdateAppointmentScheduleStatusBto updateAppointmentScheduleStatusBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        AppointmentScheduleBaseDto appointmentScheduleBaseDto =
                appointmentScheduleBaseDtoService.getById(
                        updateAppointmentScheduleStatusBto.getId());
        UpdateAppointmentScheduleStatusBoResult boResult =
                super.updateAppointmentScheduleStatusBase(updateAppointmentScheduleStatusBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateAppointmentScheduleStatusBto */
        {
            UpdateAppointmentScheduleStatusBto bto =
                    boResult
                            .<UpdateAppointmentScheduleStatusBto>getBtoOfType(
                                    UpdateAppointmentScheduleStatusBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            UpdateAppointmentScheduleStatusBto,
                            AppointmentSchedule,
                            AppointmentScheduleBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                AppointmentScheduleBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                AppointmentSchedule entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 更新排班医生科室 */
    @PublicInterface(id = "e15722ca-719d-45ee-a498-cfcb68e7e4be", module = "appointment_schedule")
    @Transactional
    @AutoGenerated(locked = false, uuid = "a5c5ed5b-6613-4424-a407-b700ea073714")
    public String updateScheduleAppointmentDoctorDepartment(
            @Valid @NotNull
                    UpdateScheduleAppointmentDoctorDepartmentBto
                            updateScheduleAppointmentDoctorDepartmentBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        AppointmentScheduleBaseDto appointmentScheduleBaseDto =
                appointmentScheduleBaseDtoService.getById(
                        updateScheduleAppointmentDoctorDepartmentBto.getId());
        UpdateScheduleAppointmentDoctorDepartmentBoResult boResult =
                super.updateScheduleAppointmentDoctorDepartmentBase(
                        updateScheduleAppointmentDoctorDepartmentBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateScheduleAppointmentDoctorDepartmentBto */
        {
            UpdateScheduleAppointmentDoctorDepartmentBto bto =
                    boResult
                            .<UpdateScheduleAppointmentDoctorDepartmentBto>getBtoOfType(
                                    UpdateScheduleAppointmentDoctorDepartmentBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<
                            UpdateScheduleAppointmentDoctorDepartmentBto,
                            AppointmentSchedule,
                            AppointmentScheduleBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                AppointmentScheduleBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                AppointmentSchedule entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 停诊记录 */
    @PublicInterface(id = "280f78a4-77db-4b02-b80b-a97c2127c9ea", module = "appointment_schedule")
    @Transactional
    @AutoGenerated(locked = false, uuid = "b746d644-fe87-42c7-829b-15d415bc8088")
    public String mergeCancellationRecord(
            @Valid @NotNull MergeCancellationRecordBto mergeCancellationRecordBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        AppointmentScheduleBaseDto appointmentScheduleBaseDto = null;
        if (mergeCancellationRecordBto.getId() != null) {
            appointmentScheduleBaseDto =
                    appointmentScheduleBaseDtoService.getById(mergeCancellationRecordBto.getId());
        }
        MergeCancellationRecordBoResult boResult =
                super.mergeCancellationRecordBase(mergeCancellationRecordBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeCancellationRecordBto.CancellationRecordBto */
        {
            for (MergeCancellationRecordBto.CancellationRecordBto bto :
                    boResult.<MergeCancellationRecordBto.CancellationRecordBto>getBtoOfType(
                            MergeCancellationRecordBto.CancellationRecordBto.class)) {
                UpdatedBto<
                                MergeCancellationRecordBto.CancellationRecordBto,
                                CancellationRecord,
                                CancellationRecordBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<MergeCancellationRecordBto.CancellationRecordBto, CancellationRecordBO>
                        addedBto = boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    CancellationRecordBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    CancellationRecord entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    CancellationRecordBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<CancellationRecord> deletedEntityList =
                    boResult.getDeletedEntityList(CancellationRecord.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** 处理 MergeCancellationRecordBto */
        {
            MergeCancellationRecordBto bto =
                    boResult
                            .<MergeCancellationRecordBto>getBtoOfType(
                                    MergeCancellationRecordBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeCancellationRecordBto, AppointmentSchedule, AppointmentScheduleBO>
                    updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeCancellationRecordBto, AppointmentScheduleBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                AppointmentScheduleBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                AppointmentSchedule entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                AppointmentScheduleBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
