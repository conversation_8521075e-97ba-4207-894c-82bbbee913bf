package com.pulse.appointment_schedule.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.WaitTimeConfigDtoManager;
import com.pulse.appointment_schedule.manager.dto.WaitTimeConfigDto;
import com.pulse.appointment_schedule.service.converter.WaitTimeConfigDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "6149d7d3-b5e8-4ae1-b5f2-6c6c45c9effa|DTO|SERVICE")
public class WaitTimeConfigDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private WaitTimeConfigDtoManager waitTimeConfigDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private WaitTimeConfigDtoServiceConverter waitTimeConfigDtoServiceConverter;

    @PublicInterface(id = "1bba38e9-21c9-42ca-80fb-218ba2f07ee2", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "58c5a1ca-c7e3-3972-9748-3fa68e8082c1")
    public List<WaitTimeConfigDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<WaitTimeConfigDto> waitTimeConfigDtoList = waitTimeConfigDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return waitTimeConfigDtoServiceConverter.WaitTimeConfigDtoConverter(waitTimeConfigDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "275176b3-8d45-4e7a-980b-5ff68fc825e4", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "69591149-737c-35d9-adcf-6b2dc1bb1f27")
    public List<WaitTimeConfigDto> getBySchedlePlanId(
            @NotNull(message = "排班计划ID不能为空") String schedlePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySchedlePlanIds(Arrays.asList(schedlePlanId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "50320288-aff8-4e54-b95e-aaf8086854bc", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "988dec66-5d51-3261-bb67-9b9e6c6a4d1e")
    public List<WaitTimeConfigDto> getByAppointmentScheduleIds(
            @Valid @NotNull(message = "排班ID不能为空") List<String> appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        appointmentScheduleId = new ArrayList<>(new HashSet<>(appointmentScheduleId));
        List<WaitTimeConfigDto> waitTimeConfigDtoList =
                waitTimeConfigDtoManager.getByAppointmentScheduleIds(appointmentScheduleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return waitTimeConfigDtoServiceConverter.WaitTimeConfigDtoConverter(waitTimeConfigDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f804a0d0-b3af-4543-af14-68716737f6a7", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "bbccf8ed-6d2e-3822-8e31-01f3553dbe4c")
    public WaitTimeConfigDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<WaitTimeConfigDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "6017430d-97b9-4fb5-9659-1df901dac22d",
            module = "appointment_schedule",
            moduleId = "2ac579b1-8101-4d8a-bd07-52d7c4f5421a",
            pubRpc = true,
            version = "1748252169132")
    @AutoGenerated(locked = false, uuid = "d4bc97ca-5a60-3400-a60d-4976d3c275b5")
    public List<WaitTimeConfigDto> getByAppointmentScheduleId(
            @NotNull(message = "排班ID不能为空") String appointmentScheduleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAppointmentScheduleIds(Arrays.asList(appointmentScheduleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "6d5984f1-5c9f-4e78-bbce-4348c1b742fa", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "d5b5ba21-ac09-32d6-a94e-1217556ef2d8")
    public List<WaitTimeConfigDto> getBySchedlePlanIds(
            @Valid @NotNull(message = "排班计划ID不能为空") List<String> schedlePlanId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        schedlePlanId = new ArrayList<>(new HashSet<>(schedlePlanId));
        List<WaitTimeConfigDto> waitTimeConfigDtoList =
                waitTimeConfigDtoManager.getBySchedlePlanIds(schedlePlanId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return waitTimeConfigDtoServiceConverter.WaitTimeConfigDtoConverter(waitTimeConfigDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
