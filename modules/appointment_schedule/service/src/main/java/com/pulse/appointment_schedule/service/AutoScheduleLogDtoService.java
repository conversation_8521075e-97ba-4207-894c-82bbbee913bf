package com.pulse.appointment_schedule.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.manager.AutoScheduleLogDtoManager;
import com.pulse.appointment_schedule.manager.dto.AutoScheduleLogDto;
import com.pulse.appointment_schedule.service.converter.AutoScheduleLogDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "0cd5bda3-bffd-43e3-a520-7b225e0b3f23|DTO|SERVICE")
public class AutoScheduleLogDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private AutoScheduleLogDtoManager autoScheduleLogDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private AutoScheduleLogDtoServiceConverter autoScheduleLogDtoServiceConverter;

    @PublicInterface(id = "df74517e-fe7c-49c1-aadd-51be6d8a507b", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "54b899f7-d1ed-3791-ba56-dacc0d1b3834")
    public List<AutoScheduleLogDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<Long> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<AutoScheduleLogDto> autoScheduleLogDtoList = autoScheduleLogDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return autoScheduleLogDtoServiceConverter.AutoScheduleLogDtoConverter(
                autoScheduleLogDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "55d79362-9dd9-4e67-81bc-0a0fb8219e6d", module = "appointment_schedule")
    @AutoGenerated(locked = false, uuid = "665124ef-d0dd-39d6-b4b3-2d3c9748684f")
    public AutoScheduleLogDto getById(@NotNull(message = "主键不能为空") Long id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AutoScheduleLogDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
