package com.pulse.appointment_schedule.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.appointment_schedule.persist.qto.ListSchedulePlanPriceQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "216640d6-467f-40f5-916c-94d410de5a7e|QTO|DAO")
public class ListSchedulePlanPriceQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 排班模板价表 */
    @AutoGenerated(locked = false, uuid = "216640d6-467f-40f5-916c-94d410de5a7e-count")
    public Integer count(ListSchedulePlanPriceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(schedule_plan.id) FROM schedule_plan WHERE"
                    + " schedule_plan.branch_institution_id = #branchInstitutionIdIs AND"
                    + " schedule_plan.department_id = #departmentIdIs AND schedule_plan.day_of_week"
                    + " in #dayOfWeekIn AND schedule_plan.doctor_id = #doctorIdIs AND"
                    + " schedule_plan.cancel_flag = #cancelFlagIs AND"
                    + " schedule_plan.clinic_register_type_id in #clinicRegisterTypeIdIn AND"
                    + " schedule_plan.time_description in #timeDescriptionIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())) {
            conditionToRemove.add("#clinicRegisterTypeIdIn");
        }
        if (qto.getBranchInstitutionIdIs() == null) {
            conditionToRemove.add("#branchInstitutionIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getTimeDescriptionIn())) {
            conditionToRemove.add("#timeDescriptionIn");
        }
        if (qto.getDepartmentIdIs() == null) {
            conditionToRemove.add("#departmentIdIs");
        }
        if (qto.getCancelFlagIs() == null) {
            conditionToRemove.add("#cancelFlagIs");
        }
        if (CollectionUtil.isEmpty(qto.getDayOfWeekIn())) {
            conditionToRemove.add("#dayOfWeekIn");
        }
        if (qto.getDoctorIdIs() == null) {
            conditionToRemove.add("#doctorIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace(
                                "#clinicRegisterTypeIdIn",
                                CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getClinicRegisterTypeIdIn().size()))
                        .replace("#branchInstitutionIdIs", "?")
                        .replace(
                                "#timeDescriptionIn",
                                CollectionUtil.isEmpty(qto.getTimeDescriptionIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getTimeDescriptionIn().size()))
                        .replace("#departmentIdIs", "?")
                        .replace("#cancelFlagIs", "?")
                        .replace(
                                "#dayOfWeekIn",
                                CollectionUtil.isEmpty(qto.getDayOfWeekIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getDayOfWeekIn().size()))
                        .replace("#doctorIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIn")) {
                sqlParams.addAll(qto.getClinicRegisterTypeIdIn());
            } else if (paramName.equalsIgnoreCase("#branchInstitutionIdIs")) {
                sqlParams.add(qto.getBranchInstitutionIdIs());
            } else if (paramName.equalsIgnoreCase("#timeDescriptionIn")) {
                sqlParams.addAll(
                        qto.getTimeDescriptionIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#departmentIdIs")) {
                sqlParams.add(qto.getDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#cancelFlagIs")) {
                sqlParams.add(qto.getCancelFlagIs());
            } else if (paramName.equalsIgnoreCase("#dayOfWeekIn")) {
                sqlParams.addAll(qto.getDayOfWeekIn());
            } else if (paramName.equalsIgnoreCase("#doctorIdIs")) {
                sqlParams.add(qto.getDoctorIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 排班模板价表 */
    @AutoGenerated(locked = false, uuid = "216640d6-467f-40f5-916c-94d410de5a7e-query-all")
    public List<String> query(ListSchedulePlanPriceQto qto) {
        qto.setSize(500);
        qto.setFrom(0);
        return this.queryPaged(qto);
    }

    /** 排班模板价表 */
    @AutoGenerated(locked = false, uuid = "216640d6-467f-40f5-916c-94d410de5a7e-query-paginate")
    public List<String> queryPaged(ListSchedulePlanPriceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT schedule_plan.id FROM schedule_plan WHERE"
                    + " schedule_plan.branch_institution_id = #branchInstitutionIdIs AND"
                    + " schedule_plan.department_id = #departmentIdIs AND schedule_plan.day_of_week"
                    + " in #dayOfWeekIn AND schedule_plan.doctor_id = #doctorIdIs AND"
                    + " schedule_plan.cancel_flag = #cancelFlagIs AND"
                    + " schedule_plan.clinic_register_type_id in #clinicRegisterTypeIdIn AND"
                    + " schedule_plan.time_description in #timeDescriptionIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())) {
            conditionToRemove.add("#clinicRegisterTypeIdIn");
        }
        if (qto.getBranchInstitutionIdIs() == null) {
            conditionToRemove.add("#branchInstitutionIdIs");
        }
        if (CollectionUtil.isEmpty(qto.getTimeDescriptionIn())) {
            conditionToRemove.add("#timeDescriptionIn");
        }
        if (qto.getDepartmentIdIs() == null) {
            conditionToRemove.add("#departmentIdIs");
        }
        if (qto.getCancelFlagIs() == null) {
            conditionToRemove.add("#cancelFlagIs");
        }
        if (CollectionUtil.isEmpty(qto.getDayOfWeekIn())) {
            conditionToRemove.add("#dayOfWeekIn");
        }
        if (qto.getDoctorIdIs() == null) {
            conditionToRemove.add("#doctorIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace(
                                "#clinicRegisterTypeIdIn",
                                CollectionUtil.isEmpty(qto.getClinicRegisterTypeIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(
                                                qto.getClinicRegisterTypeIdIn().size()))
                        .replace("#branchInstitutionIdIs", "?")
                        .replace(
                                "#timeDescriptionIn",
                                CollectionUtil.isEmpty(qto.getTimeDescriptionIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getTimeDescriptionIn().size()))
                        .replace("#departmentIdIs", "?")
                        .replace("#cancelFlagIs", "?")
                        .replace(
                                "#dayOfWeekIn",
                                CollectionUtil.isEmpty(qto.getDayOfWeekIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getDayOfWeekIn().size()))
                        .replace("#doctorIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#clinicRegisterTypeIdIn")) {
                sqlParams.addAll(qto.getClinicRegisterTypeIdIn());
            } else if (paramName.equalsIgnoreCase("#branchInstitutionIdIs")) {
                sqlParams.add(qto.getBranchInstitutionIdIs());
            } else if (paramName.equalsIgnoreCase("#timeDescriptionIn")) {
                sqlParams.addAll(
                        qto.getTimeDescriptionIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#departmentIdIs")) {
                sqlParams.add(qto.getDepartmentIdIs());
            } else if (paramName.equalsIgnoreCase("#cancelFlagIs")) {
                sqlParams.add(qto.getCancelFlagIs());
            } else if (paramName.equalsIgnoreCase("#dayOfWeekIn")) {
                sqlParams.addAll(qto.getDayOfWeekIn());
            } else if (paramName.equalsIgnoreCase("#doctorIdIs")) {
                sqlParams.add(qto.getDoctorIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  schedule_plan.id asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
