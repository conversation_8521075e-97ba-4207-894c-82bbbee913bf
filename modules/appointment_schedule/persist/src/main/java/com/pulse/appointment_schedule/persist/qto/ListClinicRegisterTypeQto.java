package com.pulse.appointment_schedule.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "9f0c9aa7-8b53-4929-90f3-135269f25843|QTO|DEFINITION")
public class ListClinicRegisterTypeQto {
    @AutoGenerated(locked = true, uuid = "ab02d1c8-6ef6-4053-9544-bf5e2c2167af")
    private Integer from;

    @AutoGenerated(locked = true, uuid = "a0afc435-52b7-453c-8210-4a0c2ded7b6c")
    private String scrollId;

    @AutoGenerated(locked = true, uuid = "0593b714-8445-4af5-8f3e-347a8ac7ad88")
    private Integer size;
}
