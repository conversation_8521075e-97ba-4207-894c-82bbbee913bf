package com.pulse.appointment_schedule.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "appointment_channel", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "acb330db-0249-48f1-84e0-6668f7f1e8ec|ENTITY|DEFINITION")
public class AppointmentChannel {
    @AutoGenerated(locked = true, uuid = "308c6eeb-0ee8-43f6-9a88-268dac454b2f")
    @TableField(value = "appointment_category_id")
    private String appointmentCategoryId;

    @AutoGenerated(locked = true, uuid = "f4cfba8b-cf38-45e6-9e13-5b451836e557")
    @TableField(value = "appointment_classification_id")
    private String appointmentClassificationId;

    @AutoGenerated(locked = true, uuid = "ad3558ba-bc08-4e1b-87c0-0e34653c3727")
    @TableField(value = "cancelled_flag")
    private Boolean cancelledFlag;

    @AutoGenerated(locked = true, uuid = "b9e1e5b1-425b-51c2-8ff4-5844fc1f1dc8")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "764088e8-6daa-4292-b7bc-8f932bbba42c")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "828d47eb-d08a-49c3-96ad-ef1bfd1637de")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "b9317f02-5930-4bc2-85d2-88f489272ea4")
    @TableField(value = "instruction")
    private String instruction;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "f85c4d89-7333-4129-9c3d-a6b9018ca3a7")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "187a247c-2cdb-4cbf-b7ef-4143e80d54f9")
    @TableField(value = "platform_type_id")
    private String platformTypeId;

    @AutoGenerated(locked = true, uuid = "2b87a490-dec2-5c9b-822b-7f740432caac")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "c4509e58-aa7e-465c-8505-f14cc75df9bc")
    @TableField(value = "updated_by")
    private String updatedBy;
}
