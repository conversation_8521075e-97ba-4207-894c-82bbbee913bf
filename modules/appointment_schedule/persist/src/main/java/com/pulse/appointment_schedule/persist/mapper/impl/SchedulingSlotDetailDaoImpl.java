package com.pulse.appointment_schedule.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.appointment_schedule.persist.dos.SchedulingSlotDetail;
import com.pulse.appointment_schedule.persist.mapper.SchedulingSlotDetailDao;
import com.pulse.appointment_schedule.persist.mapper.mybatis.SchedulingSlotDetailMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "464c831f-7620-490e-b858-4f4a9dc2f2bc|ENTITY|DAO")
public class SchedulingSlotDetailDaoImpl implements SchedulingSlotDetailDao {
    @AutoGenerated(locked = true)
    @Resource
    private SchedulingSlotDetailMapper schedulingSlotDetailMapper;

    @AutoGenerated(locked = true, uuid = "240896e2-06c9-3cbd-a550-4723c75ff30c")
    @Override
    public List<SchedulingSlotDetail> getByAppointmentScheduleId(String appointmentScheduleId) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("appointment_schedule_id", appointmentScheduleId)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return schedulingSlotDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "9d2b175d-9cc4-3ffa-8240-e5dba70eb593")
    @Override
    public List<SchedulingSlotDetail> getByAppointmentCategoryId(String appointmentCategoryId) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("appointment_category_id", appointmentCategoryId)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return schedulingSlotDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a5009701-f08b-39a4-a195-99e0939c6c20")
    @Override
    public List<SchedulingSlotDetail> getByIds(List<String> id) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return schedulingSlotDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b15391d6-3655-39b5-bae5-52dd4db57f7c")
    @Override
    public List<SchedulingSlotDetail> getByAppointmentCategoryIds(
            List<String> appointmentCategoryId) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("appointment_category_id", appointmentCategoryId)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return schedulingSlotDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "be83ff5c-6ef7-3de9-825b-eef434fbe735")
    @Override
    public List<SchedulingSlotDetail> getBySchedulePlanIds(List<String> schedulePlanId) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("schedule_plan_id", schedulePlanId).eq("deleted_at", 0L).orderByAsc("id");
        return schedulingSlotDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "cc08536d-1e6c-3a80-9295-3b4f7abfdfa0")
    @Override
    public SchedulingSlotDetail getById(String id) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return schedulingSlotDetailMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "db56b34b-c59d-3a99-9120-50e036a68892")
    @Override
    public List<SchedulingSlotDetail> getBySchedulePlanId(String schedulePlanId) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("schedule_plan_id", schedulePlanId).eq("deleted_at", 0L).orderByAsc("id");
        return schedulingSlotDetailMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f9abbdb8-af01-3036-b172-d2f19a08b9fb")
    @Override
    public List<SchedulingSlotDetail> getByAppointmentScheduleIds(
            List<String> appointmentScheduleId) {
        QueryWrapper<SchedulingSlotDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("appointment_schedule_id", appointmentScheduleId)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return schedulingSlotDetailMapper.selectList(queryWrapper);
    }
}
