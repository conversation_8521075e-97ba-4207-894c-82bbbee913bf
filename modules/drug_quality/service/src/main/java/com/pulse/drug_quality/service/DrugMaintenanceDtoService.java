package com.pulse.drug_quality.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_quality.manager.DrugMaintenanceDtoManager;
import com.pulse.drug_quality.manager.dto.DrugMaintenanceDto;
import com.pulse.drug_quality.service.converter.DrugMaintenanceDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "2baa17bf-3741-43fb-9315-a0d70b3a397a|DTO|SERVICE")
public class DrugMaintenanceDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugMaintenanceDtoManager drugMaintenanceDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugMaintenanceDtoServiceConverter drugMaintenanceDtoServiceConverter;

    @PublicInterface(id = "afc71860-e0a5-45ba-8de5-06605e4ab3d6", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "17a1cbbc-b80b-3e78-bca2-f6f9129dfb4d")
    public List<DrugMaintenanceDto> getByReviewers(
            @Valid @NotNull(message = "审核人不能为空") List<String> reviewer) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        reviewer = new ArrayList<>(new HashSet<>(reviewer));
        List<DrugMaintenanceDto> drugMaintenanceDtoList =
                drugMaintenanceDtoManager.getByReviewers(reviewer);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugMaintenanceDtoServiceConverter.DrugMaintenanceDtoConverter(
                drugMaintenanceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "a4a088ab-a951-4969-b944-528bfb05ede9", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "599d3cce-42d0-31d8-9e13-f66ac722d22b")
    public List<DrugMaintenanceDto> getByDrugOriginCode(
            @NotNull(message = "药物产地不能为空") String drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugOriginCodes(Arrays.asList(drugOriginCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "53b37a68-e3eb-4112-973a-04d6f6ca8cce", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "6632f421-4275-311b-9dc2-254ba81b2b34")
    public List<DrugMaintenanceDto> getByDrugProductSpecificationIds(
            @Valid @NotNull(message = "药品规格ID不能为空") List<String> drugProductSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugProductSpecificationId = new ArrayList<>(new HashSet<>(drugProductSpecificationId));
        List<DrugMaintenanceDto> drugMaintenanceDtoList =
                drugMaintenanceDtoManager.getByDrugProductSpecificationIds(
                        drugProductSpecificationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugMaintenanceDtoServiceConverter.DrugMaintenanceDtoConverter(
                drugMaintenanceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f87c4243-a1e7-4a4d-8555-ac6a5b95e5b7", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "76d8ac1b-3526-3c49-bae0-8551f18762d7")
    public List<DrugMaintenanceDto> getByDrugStockIds(
            @Valid @NotNull(message = "批次id不能为空") List<String> drugStockId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugStockId = new ArrayList<>(new HashSet<>(drugStockId));
        List<DrugMaintenanceDto> drugMaintenanceDtoList =
                drugMaintenanceDtoManager.getByDrugStockIds(drugStockId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugMaintenanceDtoServiceConverter.DrugMaintenanceDtoConverter(
                drugMaintenanceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "7c8f2af7-eacb-4f43-abeb-b48f965b8b94", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "87a40e51-4e0d-3bf2-bf58-f7039e6a2a3a")
    public List<DrugMaintenanceDto> getByReviewer(@NotNull(message = "审核人不能为空") String reviewer) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByReviewers(Arrays.asList(reviewer));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1d103c7f-24d4-44d4-81dc-94d624fbaf0b", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "8d05cbf2-d159-31e9-9904-a553643acf70")
    public List<DrugMaintenanceDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugMaintenanceDto> drugMaintenanceDtoList = drugMaintenanceDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugMaintenanceDtoServiceConverter.DrugMaintenanceDtoConverter(
                drugMaintenanceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "157926d1-7daa-4af0-807a-7485b3ba9614", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "af84ea55-b62e-3b6e-a87f-ddecca72ee8d")
    public List<DrugMaintenanceDto> getByStaffs(
            @Valid @NotNull(message = "养护人不能为空") List<String> staff) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        staff = new ArrayList<>(new HashSet<>(staff));
        List<DrugMaintenanceDto> drugMaintenanceDtoList =
                drugMaintenanceDtoManager.getByStaffs(staff);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugMaintenanceDtoServiceConverter.DrugMaintenanceDtoConverter(
                drugMaintenanceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "7afe935b-4d8a-4c08-a00d-b5fcbdc74bc0", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "b313e03b-2691-3865-8a44-f30f91905d7f")
    public List<DrugMaintenanceDto> getByDrugOriginCodes(
            @Valid @NotNull(message = "药物产地不能为空") List<String> drugOriginCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        drugOriginCode = new ArrayList<>(new HashSet<>(drugOriginCode));
        List<DrugMaintenanceDto> drugMaintenanceDtoList =
                drugMaintenanceDtoManager.getByDrugOriginCodes(drugOriginCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugMaintenanceDtoServiceConverter.DrugMaintenanceDtoConverter(
                drugMaintenanceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "b1b69527-7231-477c-98e8-03c28496c366", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "bb45efbd-45a0-364a-a8fb-f8aab84e95dd")
    public List<DrugMaintenanceDto> getByDrugStockId(
            @NotNull(message = "批次id不能为空") String drugStockId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugStockIds(Arrays.asList(drugStockId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "5d318740-4855-4df5-877a-cc45b183c723", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "ca600af3-7dbb-3547-8659-6ab77e9747b1")
    public List<DrugMaintenanceDto> getByDrugProductSpecificationId(
            @NotNull(message = "药品规格ID不能为空") String drugProductSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDrugProductSpecificationIds(Arrays.asList(drugProductSpecificationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "753c228d-b767-4079-a7bb-4839347a95cd", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "cb187dee-2374-36e4-95f2-b564434e9b7d")
    public List<DrugMaintenanceDto> getByDepartmentId(
            @NotNull(message = "库房科室id不能为空") String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDepartmentIds(Arrays.asList(departmentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "732491b1-1d18-4955-acb4-24d5689e22c5", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "d914f39d-1772-318b-9aa0-3f1c160c989f")
    public DrugMaintenanceDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugMaintenanceDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "a943920f-5c40-4572-97c1-fadc90f2c9b4", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "eb0e1208-adbc-3674-9c8a-0058f33fd0bc")
    public List<DrugMaintenanceDto> getByDepartmentIds(
            @Valid @NotNull(message = "库房科室id不能为空") List<String> departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        departmentId = new ArrayList<>(new HashSet<>(departmentId));
        List<DrugMaintenanceDto> drugMaintenanceDtoList =
                drugMaintenanceDtoManager.getByDepartmentIds(departmentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugMaintenanceDtoServiceConverter.DrugMaintenanceDtoConverter(
                drugMaintenanceDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "a0a80b9f-bbfc-412e-99bf-8414cabdb5ae", module = "drug_quality")
    @AutoGenerated(locked = false, uuid = "fa475437-7e2a-3053-a2ff-5fda3c629e0b")
    public List<DrugMaintenanceDto> getByStaff(@NotNull(message = "养护人不能为空") String staff) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStaffs(Arrays.asList(staff));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
