package com.pulse.drug_quality.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_quality.manager.bo.*;
import com.pulse.drug_quality.manager.bo.QualityAcceptReportBO;
import com.pulse.drug_quality.persist.dos.QualityAcceptReport;
import com.pulse.drug_quality.persist.dos.QualityAcceptReportDetail;
import com.pulse.drug_quality.service.base.BaseQualityAcceptReportBOService.CreateQualityAcceptReportBoResult;
import com.pulse.drug_quality.service.base.BaseQualityAcceptReportBOService.DeleteQualityAcceptReportBoResult;
import com.pulse.drug_quality.service.base.BaseQualityAcceptReportBOService.DeleteQualityAcceptReportDetailBoResult;
import com.pulse.drug_quality.service.base.BaseQualityAcceptReportBOService.UpdateQualityAcceptReportBoResult;
import com.pulse.drug_quality.service.bto.CreateQualityAcceptReportBto;
import com.pulse.drug_quality.service.bto.DeleteQualityAcceptReportBto;
import com.pulse.drug_quality.service.bto.DeleteQualityAcceptReportDetailBto;
import com.pulse.drug_quality.service.bto.UpdateQualityAcceptReportBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "051a0919-561a-3183-9953-24e7889658c7")
public class BaseQualityAcceptReportBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private QualityAcceptReportBO createCreateQualityAcceptReportOnDuplicateThrowEx(
            CreateQualityAcceptReportBoResult boResult,
            CreateQualityAcceptReportBto createQualityAcceptReportBto) {
        QualityAcceptReportBO qualityAcceptReportBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createQualityAcceptReportBto.getId() == null);
        if (!allNull && !found) {
            qualityAcceptReportBO =
                    QualityAcceptReportBO.getById(createQualityAcceptReportBto.getId());
            if (qualityAcceptReportBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (qualityAcceptReportBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!",
                        qualityAcceptReportBO.getId(),
                        "quality_accept_report");
                throw new IgnoredException(400, "质量验收单已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "quality_accept_report",
                        qualityAcceptReportBO.getId(),
                        "quality_accept_report");
                throw new IgnoredException(400, "质量验收单已存在");
            }
        } else {
            qualityAcceptReportBO = new QualityAcceptReportBO();
            if (pkExist) {
                qualityAcceptReportBO.setId(createQualityAcceptReportBto.getId());
            } else {
                qualityAcceptReportBO.setId(
                        String.valueOf(this.idGenerator.allocateId("quality_accept_report")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "storageCode")) {
                qualityAcceptReportBO.setStorageCode(createQualityAcceptReportBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "exportImportCode")) {
                qualityAcceptReportBO.setExportImportCode(
                        createQualityAcceptReportBto.getExportImportCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptNumber")) {
                qualityAcceptReportBO.setAcceptNumber(
                        createQualityAcceptReportBto.getAcceptNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "drugImportId")) {
                qualityAcceptReportBO.setDrugImportId(
                        createQualityAcceptReportBto.getDrugImportId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "importDateTime")) {
                qualityAcceptReportBO.setImportDateTime(
                        createQualityAcceptReportBto.getImportDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "accountantFlag")) {
                qualityAcceptReportBO.setAccountantFlag(
                        createQualityAcceptReportBto.getAccountantFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptType")) {
                qualityAcceptReportBO.setAcceptType(createQualityAcceptReportBto.getAcceptType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "supplierId")) {
                qualityAcceptReportBO.setSupplierId(createQualityAcceptReportBto.getSupplierId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptDateTime")) {
                qualityAcceptReportBO.setAcceptDateTime(
                        createQualityAcceptReportBto.getAcceptDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "remark")) {
                qualityAcceptReportBO.setRemark(createQualityAcceptReportBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "createdBy")) {
                qualityAcceptReportBO.setCreatedBy(createQualityAcceptReportBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "auditStaffId")) {
                qualityAcceptReportBO.setAuditStaffId(
                        createQualityAcceptReportBto.getAuditStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptStaffId")) {
                qualityAcceptReportBO.setAcceptStaffId(
                        createQualityAcceptReportBto.getAcceptStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "checkStaffId")) {
                qualityAcceptReportBO.setCheckStaffId(
                        createQualityAcceptReportBto.getCheckStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "stockCost")) {
                qualityAcceptReportBO.setStockCost(createQualityAcceptReportBto.getStockCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "retailCost")) {
                qualityAcceptReportBO.setRetailCost(createQualityAcceptReportBto.getRetailCost());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createQualityAcceptReportBto);
            addedBto.setBo(qualityAcceptReportBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return qualityAcceptReportBO;
    }

    /** 创建质量验收单 */
    @AutoGenerated(locked = true)
    protected CreateQualityAcceptReportBoResult createQualityAcceptReportBase(
            CreateQualityAcceptReportBto createQualityAcceptReportBto) {
        if (createQualityAcceptReportBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateQualityAcceptReportBoResult boResult = new CreateQualityAcceptReportBoResult();
        QualityAcceptReportBO qualityAcceptReportBO =
                createCreateQualityAcceptReportOnDuplicateThrowEx(
                        boResult, createQualityAcceptReportBto);
        if (qualityAcceptReportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createQualityAcceptReportBto, "__$validPropertySet"),
                    "qualityAcceptReportDetailBtoList")) {
                createQualityAcceptReportDetailBtoOnDuplicateThrowEx(
                        boResult, createQualityAcceptReportBto, qualityAcceptReportBO);
            }
        }
        boResult.setRootBo(qualityAcceptReportBO);
        return boResult;
    }

    /** 创建对象:QualityAcceptReportDetailBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createQualityAcceptReportDetailBtoOnDuplicateThrowEx(
            BaseQualityAcceptReportBOService.CreateQualityAcceptReportBoResult boResult,
            CreateQualityAcceptReportBto createQualityAcceptReportBto,
            QualityAcceptReportBO qualityAcceptReportBO) {
        if (CollectionUtil.isNotEmpty(
                createQualityAcceptReportBto.getQualityAcceptReportDetailBtoList())) {
            for (CreateQualityAcceptReportBto.QualityAcceptReportDetailBto item :
                    createQualityAcceptReportBto.getQualityAcceptReportDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<QualityAcceptReportDetailBO> any =
                        qualityAcceptReportBO.getQualityAcceptReportDetailBOSet().stream()
                                .filter(
                                        qualityAcceptReportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                qualityAcceptReportDetailBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "quality_accept_report_detail");
                        throw new IgnoredException(400, "质量验收明细已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "quality_accept_report_detail",
                                any.get().getId());
                        throw new IgnoredException(400, "质量验收明细已存在");
                    }
                } else {
                    QualityAcceptReportDetailBO subBo = new QualityAcceptReportDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugProductSpecificationId")) {
                        subBo.setDrugProductSpecificationId(item.getDrugProductSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugProductName")) {
                        subBo.setDrugProductName(item.getDrugProductName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugImportDetailId")) {
                        subBo.setDrugImportDetailId(item.getDrugImportDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugBatchInventoryId")) {
                        subBo.setDrugBatchInventoryId(item.getDrugBatchInventoryId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "arrivalAmount")) {
                        subBo.setArrivalAmount(item.getArrivalAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptAmount")) {
                        subBo.setAcceptAmount(item.getAcceptAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "damageAmount")) {
                        subBo.setDamageAmount(item.getDamageAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "approvalNumber")) {
                        subBo.setApprovalNumber(item.getApprovalNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "manufactureDateTime")) {
                        subBo.setManufactureDateTime(item.getManufactureDateTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "firmId")) {
                        subBo.setFirmId(item.getFirmId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "supplierId")) {
                        subBo.setSupplierId(item.getSupplierId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "importedNumber")) {
                        subBo.setImportedNumber(item.getImportedNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "registeredTrademark")) {
                        subBo.setRegisteredTrademark(item.getRegisteredTrademark());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "factoryInspectionCertificate")) {
                        subBo.setFactoryInspectionCertificate(
                                item.getFactoryInspectionCertificate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "packageCondition")) {
                        subBo.setPackageCondition(item.getPackageCondition());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "appearanceQuality")) {
                        subBo.setAppearanceQuality(item.getAppearanceQuality());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptConclusions")) {
                        subBo.setAcceptConclusions(item.getAcceptConclusions());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "invoiceCode")) {
                        subBo.setInvoiceCode(item.getInvoiceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "invoiceDateTime")) {
                        subBo.setInvoiceDateTime(item.getInvoiceDateTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptReportNumber")) {
                        subBo.setAcceptReportNumber(item.getAcceptReportNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "registrationNumber")) {
                        subBo.setRegistrationNumber(item.getRegistrationNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "portInspectionReport")) {
                        subBo.setPortInspectionReport(item.getPortInspectionReport());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clarityCheckResults")) {
                        subBo.setClarityCheckResults(item.getClarityCheckResults());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "disposeCondition")) {
                        subBo.setDisposeCondition(item.getDisposeCondition());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchaseStaffId")) {
                        subBo.setPurchaseStaffId(item.getPurchaseStaffId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "cultivationPlace")) {
                        subBo.setCultivationPlace(item.getCultivationPlace());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugShape")) {
                        subBo.setDrugShape(item.getDrugShape());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "originProvince")) {
                        subBo.setOriginProvince(item.getOriginProvince());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "arrivalTemperature")) {
                        subBo.setArrivalTemperature(item.getArrivalTemperature());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchIssuanceCertificateNumber")) {
                        subBo.setBatchIssuanceCertificateNumber(
                                item.getBatchIssuanceCertificateNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stockPrice")) {
                        subBo.setStockPrice(item.getStockPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stockCost")) {
                        subBo.setStockCost(item.getStockCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailCost")) {
                        subBo.setRetailCost(item.getRetailCost());
                    }
                    subBo.setQualityAcceptReportBO(qualityAcceptReportBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "quality_accept_report_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    qualityAcceptReportBO.getQualityAcceptReportDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:QualityAcceptReportDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createQualityAcceptReportDetailBtoOnDuplicateUpdate(
            UpdateQualityAcceptReportBoResult boResult,
            UpdateQualityAcceptReportBto updateQualityAcceptReportBto,
            QualityAcceptReportBO qualityAcceptReportBO) {
        if (CollectionUtil.isEmpty(
                updateQualityAcceptReportBto.getQualityAcceptReportDetailBtoList())) {
            updateQualityAcceptReportBto.setQualityAcceptReportDetailBtoList(List.of());
        }
        qualityAcceptReportBO
                .getQualityAcceptReportDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    updateQualityAcceptReportBto
                                            .getQualityAcceptReportDetailBtoList()
                                            .stream()
                                            .filter(
                                                    qualityAcceptReportDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (qualityAcceptReportDetailBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            qualityAcceptReportDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToQualityAcceptReportDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                updateQualityAcceptReportBto.getQualityAcceptReportDetailBtoList())) {
            for (UpdateQualityAcceptReportBto.QualityAcceptReportDetailBto item :
                    updateQualityAcceptReportBto.getQualityAcceptReportDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<QualityAcceptReportDetailBO> any =
                        qualityAcceptReportBO.getQualityAcceptReportDetailBOSet().stream()
                                .filter(
                                        qualityAcceptReportDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                qualityAcceptReportDetailBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        QualityAcceptReportDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToQualityAcceptReportDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugProductSpecificationId")) {
                            bo.setDrugProductSpecificationId(item.getDrugProductSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugProductName")) {
                            bo.setDrugProductName(item.getDrugProductName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugImportDetailId")) {
                            bo.setDrugImportDetailId(item.getDrugImportDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugBatchInventoryId")) {
                            bo.setDrugBatchInventoryId(item.getDrugBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "arrivalAmount")) {
                            bo.setArrivalAmount(item.getArrivalAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptAmount")) {
                            bo.setAcceptAmount(item.getAcceptAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "damageAmount")) {
                            bo.setDamageAmount(item.getDamageAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "approvalNumber")) {
                            bo.setApprovalNumber(item.getApprovalNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "manufactureDateTime")) {
                            bo.setManufactureDateTime(item.getManufactureDateTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firmId")) {
                            bo.setFirmId(item.getFirmId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplierId")) {
                            bo.setSupplierId(item.getSupplierId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importedNumber")) {
                            bo.setImportedNumber(item.getImportedNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "registeredTrademark")) {
                            bo.setRegisteredTrademark(item.getRegisteredTrademark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "factoryInspectionCertificate")) {
                            bo.setFactoryInspectionCertificate(
                                    item.getFactoryInspectionCertificate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageCondition")) {
                            bo.setPackageCondition(item.getPackageCondition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "appearanceQuality")) {
                            bo.setAppearanceQuality(item.getAppearanceQuality());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptConclusions")) {
                            bo.setAcceptConclusions(item.getAcceptConclusions());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceCode")) {
                            bo.setInvoiceCode(item.getInvoiceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceDateTime")) {
                            bo.setInvoiceDateTime(item.getInvoiceDateTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptReportNumber")) {
                            bo.setAcceptReportNumber(item.getAcceptReportNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "registrationNumber")) {
                            bo.setRegistrationNumber(item.getRegistrationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "portInspectionReport")) {
                            bo.setPortInspectionReport(item.getPortInspectionReport());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clarityCheckResults")) {
                            bo.setClarityCheckResults(item.getClarityCheckResults());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disposeCondition")) {
                            bo.setDisposeCondition(item.getDisposeCondition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchaseStaffId")) {
                            bo.setPurchaseStaffId(item.getPurchaseStaffId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "cultivationPlace")) {
                            bo.setCultivationPlace(item.getCultivationPlace());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugShape")) {
                            bo.setDrugShape(item.getDrugShape());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "originProvince")) {
                            bo.setOriginProvince(item.getOriginProvince());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "arrivalTemperature")) {
                            bo.setArrivalTemperature(item.getArrivalTemperature());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchIssuanceCertificateNumber")) {
                            bo.setBatchIssuanceCertificateNumber(
                                    item.getBatchIssuanceCertificateNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockPrice")) {
                            bo.setStockPrice(item.getStockPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockCost")) {
                            bo.setStockCost(item.getStockCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailCost")) {
                            bo.setRetailCost(item.getRetailCost());
                        }
                    } else {
                        QualityAcceptReportDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToQualityAcceptReportDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sortNumber")) {
                            bo.setSortNumber(item.getSortNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugProductSpecificationId")) {
                            bo.setDrugProductSpecificationId(item.getDrugProductSpecificationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugProductName")) {
                            bo.setDrugProductName(item.getDrugProductName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugImportDetailId")) {
                            bo.setDrugImportDetailId(item.getDrugImportDetailId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugBatchInventoryId")) {
                            bo.setDrugBatchInventoryId(item.getDrugBatchInventoryId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "arrivalAmount")) {
                            bo.setArrivalAmount(item.getArrivalAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptAmount")) {
                            bo.setAcceptAmount(item.getAcceptAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "damageAmount")) {
                            bo.setDamageAmount(item.getDamageAmount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchNumber")) {
                            bo.setBatchNumber(item.getBatchNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "expirationDate")) {
                            bo.setExpirationDate(item.getExpirationDate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "approvalNumber")) {
                            bo.setApprovalNumber(item.getApprovalNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "manufactureDateTime")) {
                            bo.setManufactureDateTime(item.getManufactureDateTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "firmId")) {
                            bo.setFirmId(item.getFirmId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "supplierId")) {
                            bo.setSupplierId(item.getSupplierId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "importedNumber")) {
                            bo.setImportedNumber(item.getImportedNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "registeredTrademark")) {
                            bo.setRegisteredTrademark(item.getRegisteredTrademark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "factoryInspectionCertificate")) {
                            bo.setFactoryInspectionCertificate(
                                    item.getFactoryInspectionCertificate());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "packageCondition")) {
                            bo.setPackageCondition(item.getPackageCondition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "appearanceQuality")) {
                            bo.setAppearanceQuality(item.getAppearanceQuality());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptConclusions")) {
                            bo.setAcceptConclusions(item.getAcceptConclusions());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceCode")) {
                            bo.setInvoiceCode(item.getInvoiceCode());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "invoiceDateTime")) {
                            bo.setInvoiceDateTime(item.getInvoiceDateTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "acceptReportNumber")) {
                            bo.setAcceptReportNumber(item.getAcceptReportNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "registrationNumber")) {
                            bo.setRegistrationNumber(item.getRegistrationNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "portInspectionReport")) {
                            bo.setPortInspectionReport(item.getPortInspectionReport());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "clarityCheckResults")) {
                            bo.setClarityCheckResults(item.getClarityCheckResults());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "disposeCondition")) {
                            bo.setDisposeCondition(item.getDisposeCondition());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "purchaseStaffId")) {
                            bo.setPurchaseStaffId(item.getPurchaseStaffId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "cultivationPlace")) {
                            bo.setCultivationPlace(item.getCultivationPlace());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drugShape")) {
                            bo.setDrugShape(item.getDrugShape());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "originProvince")) {
                            bo.setOriginProvince(item.getOriginProvince());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "arrivalTemperature")) {
                            bo.setArrivalTemperature(item.getArrivalTemperature());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "batchIssuanceCertificateNumber")) {
                            bo.setBatchIssuanceCertificateNumber(
                                    item.getBatchIssuanceCertificateNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockPrice")) {
                            bo.setStockPrice(item.getStockPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stockCost")) {
                            bo.setStockCost(item.getStockCost());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailPrice")) {
                            bo.setRetailPrice(item.getRetailPrice());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "retailCost")) {
                            bo.setRetailCost(item.getRetailCost());
                        }
                    }
                } else {
                    QualityAcceptReportDetailBO subBo = new QualityAcceptReportDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sortNumber")) {
                        subBo.setSortNumber(item.getSortNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugProductSpecificationId")) {
                        subBo.setDrugProductSpecificationId(item.getDrugProductSpecificationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugProductName")) {
                        subBo.setDrugProductName(item.getDrugProductName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugImportDetailId")) {
                        subBo.setDrugImportDetailId(item.getDrugImportDetailId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugBatchInventoryId")) {
                        subBo.setDrugBatchInventoryId(item.getDrugBatchInventoryId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "arrivalAmount")) {
                        subBo.setArrivalAmount(item.getArrivalAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptAmount")) {
                        subBo.setAcceptAmount(item.getAcceptAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "damageAmount")) {
                        subBo.setDamageAmount(item.getDamageAmount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchNumber")) {
                        subBo.setBatchNumber(item.getBatchNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "expirationDate")) {
                        subBo.setExpirationDate(item.getExpirationDate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "approvalNumber")) {
                        subBo.setApprovalNumber(item.getApprovalNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "manufactureDateTime")) {
                        subBo.setManufactureDateTime(item.getManufactureDateTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "firmId")) {
                        subBo.setFirmId(item.getFirmId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "supplierId")) {
                        subBo.setSupplierId(item.getSupplierId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "importedNumber")) {
                        subBo.setImportedNumber(item.getImportedNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "registeredTrademark")) {
                        subBo.setRegisteredTrademark(item.getRegisteredTrademark());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "factoryInspectionCertificate")) {
                        subBo.setFactoryInspectionCertificate(
                                item.getFactoryInspectionCertificate());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "packageCondition")) {
                        subBo.setPackageCondition(item.getPackageCondition());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "appearanceQuality")) {
                        subBo.setAppearanceQuality(item.getAppearanceQuality());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptConclusions")) {
                        subBo.setAcceptConclusions(item.getAcceptConclusions());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "invoiceCode")) {
                        subBo.setInvoiceCode(item.getInvoiceCode());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "invoiceDateTime")) {
                        subBo.setInvoiceDateTime(item.getInvoiceDateTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "acceptReportNumber")) {
                        subBo.setAcceptReportNumber(item.getAcceptReportNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "registrationNumber")) {
                        subBo.setRegistrationNumber(item.getRegistrationNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "portInspectionReport")) {
                        subBo.setPortInspectionReport(item.getPortInspectionReport());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "clarityCheckResults")) {
                        subBo.setClarityCheckResults(item.getClarityCheckResults());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "disposeCondition")) {
                        subBo.setDisposeCondition(item.getDisposeCondition());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "purchaseStaffId")) {
                        subBo.setPurchaseStaffId(item.getPurchaseStaffId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "cultivationPlace")) {
                        subBo.setCultivationPlace(item.getCultivationPlace());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drugShape")) {
                        subBo.setDrugShape(item.getDrugShape());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "originProvince")) {
                        subBo.setOriginProvince(item.getOriginProvince());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "arrivalTemperature")) {
                        subBo.setArrivalTemperature(item.getArrivalTemperature());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "batchIssuanceCertificateNumber")) {
                        subBo.setBatchIssuanceCertificateNumber(
                                item.getBatchIssuanceCertificateNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stockPrice")) {
                        subBo.setStockPrice(item.getStockPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stockCost")) {
                        subBo.setStockCost(item.getStockCost());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailPrice")) {
                        subBo.setRetailPrice(item.getRetailPrice());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "retailCost")) {
                        subBo.setRetailCost(item.getRetailCost());
                    }
                    subBo.setQualityAcceptReportBO(qualityAcceptReportBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "quality_accept_report_detail")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    qualityAcceptReportBO.getQualityAcceptReportDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 删除对象:deleteQualityAcceptReport,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private QualityAcceptReportBO deleteDeleteQualityAcceptReportOnMissThrowEx(
            BaseQualityAcceptReportBOService.DeleteQualityAcceptReportBoResult boResult,
            DeleteQualityAcceptReportBto deleteQualityAcceptReportBto) {
        QualityAcceptReportBO qualityAcceptReportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteQualityAcceptReportBto.getId() == null);
        if (!allNull && !found) {
            qualityAcceptReportBO =
                    QualityAcceptReportBO.getById(deleteQualityAcceptReportBto.getId());
            found = true;
        }
        if (qualityAcceptReportBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(qualityAcceptReportBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteQualityAcceptReportBto);
            deletedBto.setEntity(qualityAcceptReportBO.convertToQualityAcceptReport());
            boResult.getDeletedBtoList().add(deletedBto);
            return qualityAcceptReportBO;
        }
    }

    /** 删除质量验收单 */
    @AutoGenerated(locked = true)
    protected DeleteQualityAcceptReportBoResult deleteQualityAcceptReportBase(
            DeleteQualityAcceptReportBto deleteQualityAcceptReportBto) {
        if (deleteQualityAcceptReportBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteQualityAcceptReportBoResult boResult = new DeleteQualityAcceptReportBoResult();
        QualityAcceptReportBO qualityAcceptReportBO =
                deleteDeleteQualityAcceptReportOnMissThrowEx(
                        boResult, deleteQualityAcceptReportBto);
        boResult.setRootBo(qualityAcceptReportBO);
        return boResult;
    }

    /** 删除质量验收单明细 */
    @AutoGenerated(locked = true)
    protected DeleteQualityAcceptReportDetailBoResult deleteQualityAcceptReportDetailBase(
            DeleteQualityAcceptReportDetailBto deleteQualityAcceptReportDetailBto) {
        if (deleteQualityAcceptReportDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteQualityAcceptReportDetailBoResult boResult =
                new DeleteQualityAcceptReportDetailBoResult();
        QualityAcceptReportBO qualityAcceptReportBO =
                updateDeleteQualityAcceptReportDetailOnMissThrowEx(
                        boResult, deleteQualityAcceptReportDetailBto);
        if (qualityAcceptReportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    deleteQualityAcceptReportDetailBto, "__$validPropertySet"),
                    "qualityAcceptReportDetailBtoList")) {
                deleteQualityAcceptReportDetailBtoOnMissThrowEx(
                        boResult, deleteQualityAcceptReportDetailBto, qualityAcceptReportBO);
            }
        }
        boResult.setRootBo(qualityAcceptReportBO);
        return boResult;
    }

    /** 删除对象:qualityAcceptReportDetailBto,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void deleteQualityAcceptReportDetailBtoOnMissThrowEx(
            DeleteQualityAcceptReportDetailBoResult boResult,
            DeleteQualityAcceptReportDetailBto deleteQualityAcceptReportDetailBto,
            QualityAcceptReportBO qualityAcceptReportBO) {
        if (CollectionUtil.isNotEmpty(
                deleteQualityAcceptReportDetailBto.getQualityAcceptReportDetailBtoList())) {
            for (DeleteQualityAcceptReportDetailBto.QualityAcceptReportDetailBto item :
                    deleteQualityAcceptReportDetailBto.getQualityAcceptReportDetailBtoList()) {
                Optional<QualityAcceptReportDetailBO> any =
                        qualityAcceptReportBO.getQualityAcceptReportDetailBOSet().stream()
                                .filter(
                                        qualityAcceptReportDetailBOSet -> {
                                            boolean found = false;
                                            boolean allNull = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                qualityAcceptReportDetailBOSet
                                                                        .getId(),
                                                                item.getId());
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    qualityAcceptReportBO.getQualityAcceptReportDetailBOSet().remove(any.get());
                    DeletedBto deletedBto = new DeletedBto();
                    deletedBto.setBto(item);
                    deletedBto.setEntity(any.get().convertToQualityAcceptReportDetail());
                    boResult.getDeletedBtoList().add(deletedBto);
                } else {
                    throw new IgnoredException(400, "删除失败，无法找到原对象！");
                }
            }
        }
    }

    /** 更新对象:deleteQualityAcceptReportDetail,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private QualityAcceptReportBO updateDeleteQualityAcceptReportDetailOnMissThrowEx(
            BaseQualityAcceptReportBOService.DeleteQualityAcceptReportDetailBoResult boResult,
            DeleteQualityAcceptReportDetailBto deleteQualityAcceptReportDetailBto) {
        QualityAcceptReportBO qualityAcceptReportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteQualityAcceptReportDetailBto.getId() == null);
        if (!allNull && !found) {
            qualityAcceptReportBO =
                    QualityAcceptReportBO.getById(deleteQualityAcceptReportDetailBto.getId());
            found = true;
        }
        if (qualityAcceptReportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(qualityAcceptReportBO.convertToQualityAcceptReport());
            updatedBto.setBto(deleteQualityAcceptReportDetailBto);
            updatedBto.setBo(qualityAcceptReportBO);
            boResult.getUpdatedList().add(updatedBto);
            return qualityAcceptReportBO;
        }
    }

    /** 修改质量验收单 */
    @AutoGenerated(locked = true)
    protected UpdateQualityAcceptReportBoResult updateQualityAcceptReportBase(
            UpdateQualityAcceptReportBto updateQualityAcceptReportBto) {
        if (updateQualityAcceptReportBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateQualityAcceptReportBoResult boResult = new UpdateQualityAcceptReportBoResult();
        QualityAcceptReportBO qualityAcceptReportBO =
                updateUpdateQualityAcceptReportOnMissThrowEx(
                        boResult, updateQualityAcceptReportBto);
        if (qualityAcceptReportBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "qualityAcceptReportDetailBtoList")) {
                createQualityAcceptReportDetailBtoOnDuplicateUpdate(
                        boResult, updateQualityAcceptReportBto, qualityAcceptReportBO);
            }
        }
        boResult.setRootBo(qualityAcceptReportBO);
        return boResult;
    }

    /** 更新对象:updateQualityAcceptReport,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private QualityAcceptReportBO updateUpdateQualityAcceptReportOnMissThrowEx(
            BaseQualityAcceptReportBOService.UpdateQualityAcceptReportBoResult boResult,
            UpdateQualityAcceptReportBto updateQualityAcceptReportBto) {
        QualityAcceptReportBO qualityAcceptReportBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateQualityAcceptReportBto.getId() == null);
        if (!allNull && !found) {
            qualityAcceptReportBO =
                    QualityAcceptReportBO.getById(updateQualityAcceptReportBto.getId());
            found = true;
        }
        if (qualityAcceptReportBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(qualityAcceptReportBO.convertToQualityAcceptReport());
            updatedBto.setBto(updateQualityAcceptReportBto);
            updatedBto.setBo(qualityAcceptReportBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "storageCode")) {
                qualityAcceptReportBO.setStorageCode(updateQualityAcceptReportBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "exportImportCode")) {
                qualityAcceptReportBO.setExportImportCode(
                        updateQualityAcceptReportBto.getExportImportCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptNumber")) {
                qualityAcceptReportBO.setAcceptNumber(
                        updateQualityAcceptReportBto.getAcceptNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "drugImportId")) {
                qualityAcceptReportBO.setDrugImportId(
                        updateQualityAcceptReportBto.getDrugImportId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "importDateTime")) {
                qualityAcceptReportBO.setImportDateTime(
                        updateQualityAcceptReportBto.getImportDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "accountantFlag")) {
                qualityAcceptReportBO.setAccountantFlag(
                        updateQualityAcceptReportBto.getAccountantFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptType")) {
                qualityAcceptReportBO.setAcceptType(updateQualityAcceptReportBto.getAcceptType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "supplierId")) {
                qualityAcceptReportBO.setSupplierId(updateQualityAcceptReportBto.getSupplierId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "arrivalDateTime")) {
                qualityAcceptReportBO.setArrivalDateTime(
                        updateQualityAcceptReportBto.getArrivalDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptDateTime")) {
                qualityAcceptReportBO.setAcceptDateTime(
                        updateQualityAcceptReportBto.getAcceptDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "importCount")) {
                qualityAcceptReportBO.setImportCount(updateQualityAcceptReportBto.getImportCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "updatedBy")) {
                qualityAcceptReportBO.setUpdatedBy(updateQualityAcceptReportBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "remark")) {
                qualityAcceptReportBO.setRemark(updateQualityAcceptReportBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "auditDateTime")) {
                qualityAcceptReportBO.setAuditDateTime(
                        updateQualityAcceptReportBto.getAuditDateTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "createdBy")) {
                qualityAcceptReportBO.setCreatedBy(updateQualityAcceptReportBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "auditStaffId")) {
                qualityAcceptReportBO.setAuditStaffId(
                        updateQualityAcceptReportBto.getAuditStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "acceptStaffId")) {
                qualityAcceptReportBO.setAcceptStaffId(
                        updateQualityAcceptReportBto.getAcceptStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "payFlag")) {
                qualityAcceptReportBO.setPayFlag(updateQualityAcceptReportBto.getPayFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "settleId")) {
                qualityAcceptReportBO.setSettleId(updateQualityAcceptReportBto.getSettleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "storekeeperId")) {
                qualityAcceptReportBO.setStorekeeperId(
                        updateQualityAcceptReportBto.getStorekeeperId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "checkStaffId")) {
                qualityAcceptReportBO.setCheckStaffId(
                        updateQualityAcceptReportBto.getCheckStaffId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "stockCost")) {
                qualityAcceptReportBO.setStockCost(updateQualityAcceptReportBto.getStockCost());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    updateQualityAcceptReportBto, "__$validPropertySet"),
                    "retailCost")) {
                qualityAcceptReportBO.setRetailCost(updateQualityAcceptReportBto.getRetailCost());
            }
            return qualityAcceptReportBO;
        }
    }

    public static class CreateQualityAcceptReportBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public QualityAcceptReportBO getRootBo() {
            return (QualityAcceptReportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        CreateQualityAcceptReportBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetailBO>
                getCreatedBto(
                        CreateQualityAcceptReportBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return this.getAddedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateQualityAcceptReportBto, QualityAcceptReportBO> getCreatedBto(
                CreateQualityAcceptReportBto createQualityAcceptReportBto) {
            return this.getAddedResult(createQualityAcceptReportBto);
        }

        @AutoGenerated(locked = true)
        public QualityAcceptReportDetail getDeleted_QualityAcceptReportDetail() {
            return (QualityAcceptReportDetail)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(QualityAcceptReportDetail.class));
        }

        @AutoGenerated(locked = true)
        public QualityAcceptReport getDeleted_QualityAcceptReport() {
            return (QualityAcceptReport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(QualityAcceptReport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateQualityAcceptReportBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetail,
                        QualityAcceptReportDetailBO>
                getUpdatedBto(
                        CreateQualityAcceptReportBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return super.getUpdatedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateQualityAcceptReportBto, QualityAcceptReport, QualityAcceptReportBO>
                getUpdatedBto(CreateQualityAcceptReportBto createQualityAcceptReportBto) {
            return super.getUpdatedResult(createQualityAcceptReportBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateQualityAcceptReportBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetailBO>
                getUnmodifiedBto(
                        CreateQualityAcceptReportBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return super.getUnmodifiedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateQualityAcceptReportBto, QualityAcceptReportBO> getUnmodifiedBto(
                CreateQualityAcceptReportBto createQualityAcceptReportBto) {
            return super.getUnmodifiedResult(createQualityAcceptReportBto);
        }
    }

    public static class UpdateQualityAcceptReportBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public QualityAcceptReportBO getRootBo() {
            return (QualityAcceptReportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        UpdateQualityAcceptReportBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetailBO>
                getCreatedBto(
                        UpdateQualityAcceptReportBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return this.getAddedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateQualityAcceptReportBto, QualityAcceptReportBO> getCreatedBto(
                UpdateQualityAcceptReportBto updateQualityAcceptReportBto) {
            return this.getAddedResult(updateQualityAcceptReportBto);
        }

        @AutoGenerated(locked = true)
        public QualityAcceptReportDetail getDeleted_QualityAcceptReportDetail() {
            return (QualityAcceptReportDetail)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(QualityAcceptReportDetail.class));
        }

        @AutoGenerated(locked = true)
        public QualityAcceptReport getDeleted_QualityAcceptReport() {
            return (QualityAcceptReport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(QualityAcceptReport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        UpdateQualityAcceptReportBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetail,
                        QualityAcceptReportDetailBO>
                getUpdatedBto(
                        UpdateQualityAcceptReportBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return super.getUpdatedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateQualityAcceptReportBto, QualityAcceptReport, QualityAcceptReportBO>
                getUpdatedBto(UpdateQualityAcceptReportBto updateQualityAcceptReportBto) {
            return super.getUpdatedResult(updateQualityAcceptReportBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        UpdateQualityAcceptReportBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetailBO>
                getUnmodifiedBto(
                        UpdateQualityAcceptReportBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return super.getUnmodifiedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateQualityAcceptReportBto, QualityAcceptReportBO> getUnmodifiedBto(
                UpdateQualityAcceptReportBto updateQualityAcceptReportBto) {
            return super.getUnmodifiedResult(updateQualityAcceptReportBto);
        }
    }

    public static class DeleteQualityAcceptReportBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public QualityAcceptReportBO getRootBo() {
            return (QualityAcceptReportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteQualityAcceptReportBto, QualityAcceptReportBO> getCreatedBto(
                DeleteQualityAcceptReportBto deleteQualityAcceptReportBto) {
            return this.getAddedResult(deleteQualityAcceptReportBto);
        }

        @AutoGenerated(locked = true)
        public QualityAcceptReport getDeleted_QualityAcceptReport() {
            return (QualityAcceptReport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(QualityAcceptReport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteQualityAcceptReportBto, QualityAcceptReport, QualityAcceptReportBO>
                getUpdatedBto(DeleteQualityAcceptReportBto deleteQualityAcceptReportBto) {
            return super.getUpdatedResult(deleteQualityAcceptReportBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteQualityAcceptReportBto, QualityAcceptReportBO> getUnmodifiedBto(
                DeleteQualityAcceptReportBto deleteQualityAcceptReportBto) {
            return super.getUnmodifiedResult(deleteQualityAcceptReportBto);
        }
    }

    public static class DeleteQualityAcceptReportDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public QualityAcceptReportBO getRootBo() {
            return (QualityAcceptReportBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        DeleteQualityAcceptReportDetailBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetailBO>
                getCreatedBto(
                        DeleteQualityAcceptReportDetailBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return this.getAddedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteQualityAcceptReportDetailBto, QualityAcceptReportBO> getCreatedBto(
                DeleteQualityAcceptReportDetailBto deleteQualityAcceptReportDetailBto) {
            return this.getAddedResult(deleteQualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public QualityAcceptReportDetail getDeleted_QualityAcceptReportDetail() {
            return (QualityAcceptReportDetail)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(QualityAcceptReportDetail.class));
        }

        @AutoGenerated(locked = true)
        public QualityAcceptReport getDeleted_QualityAcceptReport() {
            return (QualityAcceptReport)
                    CollectionUtil.getFirst(this.getDeletedEntityList(QualityAcceptReport.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteQualityAcceptReportDetailBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetail,
                        QualityAcceptReportDetailBO>
                getUpdatedBto(
                        DeleteQualityAcceptReportDetailBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return super.getUpdatedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteQualityAcceptReportDetailBto,
                        QualityAcceptReport,
                        QualityAcceptReportBO>
                getUpdatedBto(
                        DeleteQualityAcceptReportDetailBto deleteQualityAcceptReportDetailBto) {
            return super.getUpdatedResult(deleteQualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        DeleteQualityAcceptReportDetailBto.QualityAcceptReportDetailBto,
                        QualityAcceptReportDetailBO>
                getUnmodifiedBto(
                        DeleteQualityAcceptReportDetailBto.QualityAcceptReportDetailBto
                                qualityAcceptReportDetailBto) {
            return super.getUnmodifiedResult(qualityAcceptReportDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteQualityAcceptReportDetailBto, QualityAcceptReportBO>
                getUnmodifiedBto(
                        DeleteQualityAcceptReportDetailBto deleteQualityAcceptReportDetailBto) {
            return super.getUnmodifiedResult(deleteQualityAcceptReportDetailBto);
        }
    }
}
