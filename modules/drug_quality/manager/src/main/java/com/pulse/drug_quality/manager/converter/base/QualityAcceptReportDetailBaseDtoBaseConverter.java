package com.pulse.drug_quality.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_quality.manager.dto.QualityAcceptReportDetailBaseDto;
import com.pulse.drug_quality.persist.dos.QualityAcceptReportDetail;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "36d69378-a78d-4639-83a0-3939d3595f13|DTO|BASE_CONVERTER")
public class QualityAcceptReportDetailBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public QualityAcceptReportDetailBaseDto
            convertFromQualityAcceptReportDetailToQualityAcceptReportDetailBaseDto(
                    QualityAcceptReportDetail qualityAcceptReportDetail) {
        return convertFromQualityAcceptReportDetailToQualityAcceptReportDetailBaseDto(
                        List.of(qualityAcceptReportDetail))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<QualityAcceptReportDetailBaseDto>
            convertFromQualityAcceptReportDetailToQualityAcceptReportDetailBaseDto(
                    List<QualityAcceptReportDetail> qualityAcceptReportDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(qualityAcceptReportDetailList)) {
            return new ArrayList<>();
        }
        List<QualityAcceptReportDetailBaseDto> qualityAcceptReportDetailBaseDtoList =
                new ArrayList<>();
        for (QualityAcceptReportDetail qualityAcceptReportDetail : qualityAcceptReportDetailList) {
            if (qualityAcceptReportDetail == null) {
                continue;
            }
            QualityAcceptReportDetailBaseDto qualityAcceptReportDetailBaseDto =
                    new QualityAcceptReportDetailBaseDto();
            qualityAcceptReportDetailBaseDto.setId(qualityAcceptReportDetail.getId());
            qualityAcceptReportDetailBaseDto.setReportId(qualityAcceptReportDetail.getReportId());
            qualityAcceptReportDetailBaseDto.setSortNumber(
                    qualityAcceptReportDetail.getSortNumber());
            qualityAcceptReportDetailBaseDto.setDrugProductSpecificationId(
                    qualityAcceptReportDetail.getDrugProductSpecificationId());
            qualityAcceptReportDetailBaseDto.setDrugProductName(
                    qualityAcceptReportDetail.getDrugProductName());
            qualityAcceptReportDetailBaseDto.setDrugImportDetailId(
                    qualityAcceptReportDetail.getDrugImportDetailId());
            qualityAcceptReportDetailBaseDto.setDrugBatchInventoryId(
                    qualityAcceptReportDetail.getDrugBatchInventoryId());
            qualityAcceptReportDetailBaseDto.setArrivalAmount(
                    qualityAcceptReportDetail.getArrivalAmount());
            qualityAcceptReportDetailBaseDto.setAcceptAmount(
                    qualityAcceptReportDetail.getAcceptAmount());
            qualityAcceptReportDetailBaseDto.setDamageAmount(
                    qualityAcceptReportDetail.getDamageAmount());
            qualityAcceptReportDetailBaseDto.setBatchNumber(
                    qualityAcceptReportDetail.getBatchNumber());
            qualityAcceptReportDetailBaseDto.setExpirationDate(
                    qualityAcceptReportDetail.getExpirationDate());
            qualityAcceptReportDetailBaseDto.setApprovalNumber(
                    qualityAcceptReportDetail.getApprovalNumber());
            qualityAcceptReportDetailBaseDto.setManufactureDateTime(
                    qualityAcceptReportDetail.getManufactureDateTime());
            qualityAcceptReportDetailBaseDto.setFirmId(qualityAcceptReportDetail.getFirmId());
            qualityAcceptReportDetailBaseDto.setSupplierId(
                    qualityAcceptReportDetail.getSupplierId());
            qualityAcceptReportDetailBaseDto.setImportedNumber(
                    qualityAcceptReportDetail.getImportedNumber());
            qualityAcceptReportDetailBaseDto.setRegisteredTrademark(
                    qualityAcceptReportDetail.getRegisteredTrademark());
            qualityAcceptReportDetailBaseDto.setFactoryInspectionCertificate(
                    qualityAcceptReportDetail.getFactoryInspectionCertificate());
            qualityAcceptReportDetailBaseDto.setPackageCondition(
                    qualityAcceptReportDetail.getPackageCondition());
            qualityAcceptReportDetailBaseDto.setAppearanceQuality(
                    qualityAcceptReportDetail.getAppearanceQuality());
            qualityAcceptReportDetailBaseDto.setAcceptConclusions(
                    qualityAcceptReportDetail.getAcceptConclusions());
            qualityAcceptReportDetailBaseDto.setInvoiceCode(
                    qualityAcceptReportDetail.getInvoiceCode());
            qualityAcceptReportDetailBaseDto.setInvoiceDateTime(
                    qualityAcceptReportDetail.getInvoiceDateTime());
            qualityAcceptReportDetailBaseDto.setAcceptReportNumber(
                    qualityAcceptReportDetail.getAcceptReportNumber());
            qualityAcceptReportDetailBaseDto.setRegistrationNumber(
                    qualityAcceptReportDetail.getRegistrationNumber());
            qualityAcceptReportDetailBaseDto.setPortInspectionReport(
                    qualityAcceptReportDetail.getPortInspectionReport());
            qualityAcceptReportDetailBaseDto.setClarityCheckResults(
                    qualityAcceptReportDetail.getClarityCheckResults());
            qualityAcceptReportDetailBaseDto.setDisposeCondition(
                    qualityAcceptReportDetail.getDisposeCondition());
            qualityAcceptReportDetailBaseDto.setPurchaseStaffId(
                    qualityAcceptReportDetail.getPurchaseStaffId());
            qualityAcceptReportDetailBaseDto.setCultivationPlace(
                    qualityAcceptReportDetail.getCultivationPlace());
            qualityAcceptReportDetailBaseDto.setDrugShape(qualityAcceptReportDetail.getDrugShape());
            qualityAcceptReportDetailBaseDto.setOriginProvince(
                    qualityAcceptReportDetail.getOriginProvince());
            qualityAcceptReportDetailBaseDto.setArrivalTemperature(
                    qualityAcceptReportDetail.getArrivalTemperature());
            qualityAcceptReportDetailBaseDto.setBatchIssuanceCertificateNumber(
                    qualityAcceptReportDetail.getBatchIssuanceCertificateNumber());
            qualityAcceptReportDetailBaseDto.setStockPrice(
                    qualityAcceptReportDetail.getStockPrice());
            qualityAcceptReportDetailBaseDto.setStockCost(qualityAcceptReportDetail.getStockCost());
            qualityAcceptReportDetailBaseDto.setRetailPrice(
                    qualityAcceptReportDetail.getRetailPrice());
            qualityAcceptReportDetailBaseDto.setRetailCost(
                    qualityAcceptReportDetail.getRetailCost());
            qualityAcceptReportDetailBaseDto.setCreatedAt(qualityAcceptReportDetail.getCreatedAt());
            qualityAcceptReportDetailBaseDto.setUpdatedAt(qualityAcceptReportDetail.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            qualityAcceptReportDetailBaseDtoList.add(qualityAcceptReportDetailBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return qualityAcceptReportDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
