package com.pulse.drug_dictionary.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.ManageModelEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "99a960df-21c5-4fa5-86c4-db795ab4b566|DTO|DEFINITION")
public class DrugOriginBaseDto {
    /** 帐簿类别 */
    @AutoGenerated(locked = true, uuid = "6806d863-107b-4948-91af-094dd90c1fab")
    private String accountType;

    /** 包装量 冗余存 */
    @AutoGenerated(locked = true, uuid = "458fd268-c5af-4c50-854f-f319a7f74b2e")
    private Long amountPerPackage;

    /** 集采未中标住院限用药品 住院限制用药标识（需要配置白名单才能开医嘱） */
    @AutoGenerated(locked = true, uuid = "6d2ee15b-a322-4518-bad8-eb185cdd83d8")
    private Boolean bidLostInpLimitFlag;

    /** 集采标识 集采标识，通过任务控制竞品开单限制 */
    @AutoGenerated(locked = true, uuid = "4aee4656-504f-46b1-9187-b6c9f38b4355")
    private Boolean centralPurchaseFlag;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "eea225a1-ae74-47d2-b04c-31c7ac83a16a")
    private Date createdAt;

    /** 创建人id */
    @AutoGenerated(locked = true, uuid = "7be49dc7-d05d-47b1-8587-ec86eb4a48b2")
    private String createdBy;

    /** 日最高剂量 */
    @AutoGenerated(locked = true, uuid = "6f264879-66c4-45ae-9e76-4206669fe5ee")
    private BigDecimal dayMaxDosage;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "7f902fcc-cfba-4703-a786-6b943ef9b67f")
    private Long deletedAt;

    /** 捐赠标志 赠品药品控制不能调价，药品方案检索会显示赠品标志,可能还有其他业务场景 */
    @AutoGenerated(locked = true, uuid = "bf8cb4e8-9b15-411a-8f97-558e8facdc7f")
    private Boolean donationFlag;

    /** 药品编码 */
    @AutoGenerated(locked = true, uuid = "c4f1b2a6-75f2-42d9-828c-54b1461337d8")
    private String drugCode;

    /** 药品产地 */
    @AutoGenerated(locked = true, uuid = "122b8cd4-b3bf-47be-a87b-eead8116401c")
    private String drugOriginCode;

    /** 药品产地名称 */
    @AutoGenerated(locked = true, uuid = "b2f51bc7-ee6f-4d90-9867-b75e17152dc2")
    private String drugOriginName;

    /** 药品生产商ID */
    @AutoGenerated(locked = true, uuid = "79fee31b-2ba8-4be5-a11a-ad4ff6e1aee4")
    private String drugProducerId;

    /** 药品规格id */
    @AutoGenerated(locked = true, uuid = "c23b4b25-1ee0-41b0-9a6d-d4fd02fa5830")
    private String drugSpecificationId;

    /** 药品类型 */
    @AutoGenerated(locked = true, uuid = "0f63fbf2-5dd2-4805-b1e9-370b3345680d")
    private DrugTypeEnum drugType;

    /** 启用标志 控制商品是否可用 */
    @AutoGenerated(locked = true, uuid = "eab4e74e-491e-49a3-8962-6f0ae113dbd3")
    private Boolean enableFlag;

    /** 免费药品标志 免费药品控制不能调价，可能还有其他业务场景 */
    @AutoGenerated(locked = true, uuid = "20f19ea2-28fb-4b3e-beda-f0d86a59e4f0")
    private Boolean freeFlag;

    /** gcp药品标识 */
    @AutoGenerated(locked = true, uuid = "12bd40c8-4b59-45bc-a87e-8c46c48c7620")
    private Boolean gcpFlag;

    /** GMP标志 良好生产规范 */
    @AutoGenerated(locked = true, uuid = "34f9da5a-d017-48f2-a351-222bf111b0cc")
    private Boolean gmpFlag;

    /** 住院使用标志 */
    @AutoGenerated(locked = true, uuid = "3051527a-f9f2-4454-b836-a42661a91ac9")
    private Boolean inpUsageFlag;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "28590a6e-169d-46a5-8342-be2bb2c869e7")
    private InputCodeEo inputCode;

    /** 机构id */
    @AutoGenerated(locked = true, uuid = "b01f0bfd-fbe1-4f11-8923-2766fed8ad5d")
    private String institutionId;

    /** 互联网标志 */
    @AutoGenerated(locked = true, uuid = "b78829f7-c8a8-44f6-a80e-b6b6a114a947")
    private Boolean internetFlage;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "9a1386d2-a59c-4d89-8460-3fcbe84d2ac5")
    private Boolean invalidFlag;

    /** 管理模式 */
    @AutoGenerated(locked = true, uuid = "66c23b73-d49f-45d4-adda-9cc58c0132eb")
    private ManageModelEnum manageMode;

    /** 最大开药天数 */
    @AutoGenerated(locked = true, uuid = "65b0a077-0226-4706-aff4-88ad15c6fb0e")
    private Long maxDay;

    /** 最高零售价 */
    @AutoGenerated(locked = true, uuid = "60b9543e-e656-4b93-95d3-caa15b8fc2aa")
    private BigDecimal maxRetailPrice;

    /** 最高进价 */
    @AutoGenerated(locked = true, uuid = "787b0c1f-1d20-45e2-baf4-a6963dd65a7c")
    private BigDecimal maxStockPrice;

    /** 对应的病案首页费用分类 */
    @AutoGenerated(locked = true, uuid = "75f45760-4289-4bc7-904d-00e8c9456361")
    private String medicalRecordType;

    /** OTC标志 */
    @AutoGenerated(locked = true, uuid = "024e914e-194f-4824-9bdd-402e1a815e00")
    private Boolean otcFlag;

    /** 门诊使用标志 */
    @AutoGenerated(locked = true, uuid = "b1084d03-f598-400c-a0c2-4f98fe1b5229")
    private Boolean outpUsageFlag;

    /** 包装规格 冗余存 */
    @AutoGenerated(locked = true, uuid = "aa4f64f6-e02d-450a-bec4-6b09b801de69")
    private String packageSpecification;

    /** 包装单位 冗余存 */
    @AutoGenerated(locked = true, uuid = "eaba4cd0-f533-460b-a200-47897882f23c")
    private String packageUnit;

    /** 静配排批优先级 数字越小，优先级越高，空值优先级最低 */
    @AutoGenerated(locked = true, uuid = "fd995cc3-4707-4fe4-bc02-33277d4f3c9b")
    private Long pivasBatchPriority;

    /** 静配标识 */
    @AutoGenerated(locked = true, uuid = "21cb8b93-c8b2-4810-9252-107b431ea7e2")
    private Boolean pivasFlag;

    /** 开单系数 药品开单系数，医嘱开立时只能以开单系数的倍数开立 */
    @AutoGenerated(locked = true, uuid = "5b908947-6224-4102-b726-834cafcec149")
    private BigDecimal prescriptionRatio;

    /** 注册商标 */
    @AutoGenerated(locked = true, uuid = "3955a5b7-8ac7-4cb0-9c2a-7982739bc2c7")
    private String registerBrand;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "f8fdb6ac-0efe-4d12-b735-fe06da81eb32")
    private String remark;

    /** 自备标志HR3-49598(415955) 自备药品处方、医嘱需要发药生成领药单，但是不记账、不扣库存 */
    @AutoGenerated(locked = true, uuid = "a268eb1a-59fe-4b52-bc6f-223afcd8a1a8")
    private Boolean selfProvideFlag;

    /** 贮藏条件 （常温库、阴凉库、冷库）医嘱发药、静配发药同病区冷藏药品与普通药品区分开单独发药、单独打印 */
    @AutoGenerated(locked = true, uuid = "edf494af-aee5-448f-85f9-e351db6d8e03")
    private String storeCondition;

    /** 毒理分类 */
    @AutoGenerated(locked = true, uuid = "073d2cac-d05b-48f9-a50f-2d788d904b96")
    private String toxicType;

    /** 追溯码 */
    @AutoGenerated(locked = true, uuid = "8711c500-8bd4-43d6-83c9-7fb7a9da7421")
    private String traceabilityCode;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "f9806700-1c51-4d45-983a-342f939a96e1")
    private Date updatedAt;

    /** 修改人id */
    @AutoGenerated(locked = true, uuid = "3340f634-fa24-41e3-9cdc-eb700478ff22")
    private String updatedBy;

    /** 中标号 */
    @AutoGenerated(locked = true, uuid = "7580b2fe-5e11-434b-9ebe-7bf38c9c3605")
    private String winningNumber;
}
