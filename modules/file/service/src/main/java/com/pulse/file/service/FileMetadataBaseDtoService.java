package com.pulse.file.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.file.manager.FileMetadataBaseDtoManager;
import com.pulse.file.manager.dto.FileMetadataBaseDto;
import com.pulse.file.persist.dos.FileMetadata.BusinessIdAndBusinessType;
import com.pulse.file.persist.eo.IdxBusinessEo;
import com.pulse.file.service.converter.FileMetadataBaseDtoServiceConverter;
import com.pulse.file.service.converter.voConverter.FileMetadataIdxBusinessConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "87e1d9db-50c7-4cd0-bfde-af06237c3fd6|DTO|SERVICE")
public class FileMetadataBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private FileMetadataBaseDtoManager fileMetadataBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private FileMetadataBaseDtoServiceConverter fileMetadataBaseDtoServiceConverter;

    @PublicInterface(id = "ea773aa8-ef9a-4eb1-ba0a-5ffc49bc1fd7", module = "file")
    @AutoGenerated(locked = false, uuid = "28bd2f34-ad0e-39fb-8731-c4f1113f6bc1")
    public FileMetadataBaseDto getById(@NotNull(message = "Primary Key不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<FileMetadataBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "26140424-b8e7-42ab-b70c-667def59a790", module = "file")
    @AutoGenerated(locked = false, uuid = "2fe5b077-6e54-385c-b20d-eaab85fc6dbb")
    public List<FileMetadataBaseDto> getByBusinessIdAndBusinessType(
            @Valid @NotNull IdxBusinessEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByBusinessIdsAndBusinessTypes(Arrays.asList(var));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "c878285a-3796-40db-8a95-5de21002ff07", module = "file")
    @AutoGenerated(locked = false, uuid = "5b18346c-0113-3b73-9b25-c297a524a9d4")
    public List<FileMetadataBaseDto> getByIds(
            @Valid @NotNull(message = "Primary Key不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<FileMetadataBaseDto> fileMetadataBaseDtoList = fileMetadataBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return fileMetadataBaseDtoServiceConverter.FileMetadataBaseDtoConverter(
                fileMetadataBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "2888cf42-2b42-4a95-a598-687785d54cda", module = "file")
    @AutoGenerated(locked = false, uuid = "6077d402-e94b-3bcb-acd9-d356ba63f70d")
    public List<FileMetadataBaseDto> getByUploadBy(
            @NotNull(message = "上传者ID不能为空") String uploadBy) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByUploadBys(Arrays.asList(uploadBy));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "11986b76-3600-4d93-99d8-763e96fc79e5", module = "file")
    @AutoGenerated(locked = false, uuid = "ac77a5e4-a4e9-3121-863c-e27c40cd033a")
    public List<FileMetadataBaseDto> getByBusinessIdsAndBusinessTypes(
            @Valid @NotNull List<IdxBusinessEo> idxBusinessEo) {
        List<BusinessIdAndBusinessType> businessIdAndBusinessType =
                idxBusinessEo.stream()
                        .map(FileMetadataIdxBusinessConverter::convertFromIdxBusinessToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<FileMetadataBaseDto> fileMetadataBaseDtoList =
                fileMetadataBaseDtoManager.getByBusinessIdsAndBusinessTypes(
                        businessIdAndBusinessType);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return fileMetadataBaseDtoServiceConverter.FileMetadataBaseDtoConverter(
                fileMetadataBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "32b5f829-6760-43e5-9231-b591a8ff4c40", module = "file")
    @AutoGenerated(locked = false, uuid = "d8100782-8ff2-35d4-b20d-303b080e142b")
    public List<FileMetadataBaseDto> getByUploadBys(
            @Valid @NotNull(message = "上传者ID不能为空") List<String> uploadBy) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        uploadBy = new ArrayList<>(new HashSet<>(uploadBy));
        List<FileMetadataBaseDto> fileMetadataBaseDtoList =
                fileMetadataBaseDtoManager.getByUploadBys(uploadBy);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return fileMetadataBaseDtoServiceConverter.FileMetadataBaseDtoConverter(
                fileMetadataBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
