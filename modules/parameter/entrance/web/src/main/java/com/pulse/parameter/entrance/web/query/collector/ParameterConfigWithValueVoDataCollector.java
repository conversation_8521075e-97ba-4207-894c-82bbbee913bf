package com.pulse.parameter.entrance.web.query.collector;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.parameter.entrance.web.converter.ParameterConfigWithValueVoConverter;
import com.pulse.parameter.entrance.web.converter.ParameterValueVoConverter;
import com.pulse.parameter.entrance.web.query.assembler.ParameterConfigWithValueVoDataAssembler.ParameterConfigWithValueVoDataHolder;
import com.pulse.parameter.entrance.web.vo.ParameterValueVo;
import com.pulse.parameter.manager.dto.ParameterConfigBaseDto;
import com.pulse.parameter.manager.dto.ParameterConfigWithValueDto;
import com.pulse.parameter.manager.dto.ParameterValueBaseDto;
import com.pulse.parameter.service.ParameterConfigBaseDtoService;
import com.pulse.parameter.service.ParameterValueBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ParameterConfigWithValueVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "37cedeac-662f-3d45-9b13-6c24578d7d17")
public class ParameterConfigWithValueVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigBaseDtoService parameterConfigBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueVoConverter parameterConfigWithValueVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterConfigWithValueVoDataCollector parameterConfigWithValueVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueBaseDtoService parameterValueBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ParameterValueVoConverter parameterValueVoConverter;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "3407dcb7-9ed4-338e-8793-4cd1f1c05ab4")
    private void fillDataWhenNecessary(ParameterConfigWithValueVoDataHolder dataHolder) {
        List<ParameterConfigBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.parameterValueList == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ParameterConfigBaseDto::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ParameterValueBaseDto> baseDtoList =
                    parameterValueBaseDtoService.getByConfigIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ParameterValueBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, List<ParameterValueBaseDto>> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.groupingBy(ParameterValueBaseDto::getConfigId));
            Map<ParameterValueBaseDto, ParameterValueVo> dtoVoMap =
                    parameterValueVoConverter.convertToParameterValueVoMap(baseDtoList);
            Map<ParameterValueBaseDto, ParameterValueVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.parameterValueList =
                    rootDtoList.stream()
                            .map(ParameterConfigBaseDto::getId)
                            .flatMap(
                                    tmpId ->
                                            Optional.ofNullable(baseDtoMap.get(tmpId))
                                                    .orElse(new ArrayList<>())
                                                    .stream())
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取ParameterConfigWithValueDto数据填充ParameterConfigWithValueVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "b60d9ee5-8eac-363f-8aca-3c69885e4b97")
    public void collectDataWithDtoData(
            List<ParameterConfigWithValueDto> dtoList,
            ParameterConfigWithValueVoDataHolder dataHolder) {
        List<ParameterValueBaseDto> parameterValueListList = new ArrayList<>();

        for (ParameterConfigWithValueDto rootDto : dtoList) {
            if (CollectionUtil.isNotEmpty(rootDto.getParameterValueList())) {
                for (ParameterValueBaseDto parameterValueListDto :
                        rootDto.getParameterValueList()) {
                    parameterValueListList.add(parameterValueListDto);
                }
            }
        }

        // access parameterValueList
        Map<ParameterValueBaseDto, ParameterValueVo> parameterValueListVoMap =
                parameterValueVoConverter.convertToParameterValueVoMap(parameterValueListList);
        dataHolder.parameterValueList =
                parameterValueListList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> parameterValueListVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "d655aa20-de6b-3d81-855a-70835601293b")
    public void collectDataDefault(ParameterConfigWithValueVoDataHolder dataHolder) {
        parameterConfigWithValueVoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
