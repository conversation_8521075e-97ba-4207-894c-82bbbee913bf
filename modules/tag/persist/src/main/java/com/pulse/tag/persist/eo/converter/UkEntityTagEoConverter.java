package com.pulse.tag.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.tag.persist.eo.UkEntityTagEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for UkEntityTagEo */
@Converter
@AutoGenerated(locked = true, uuid = "84efd073-99cf-36f2-ba1a-c985914e79bf")
public class UkEntityTagEoConverter implements AttributeConverter<UkEntityTagEo, String> {

    /** convert DB column to UkEntityTagEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(UkEntityTagEo ukEntityTagEo) {
        if (ukEntityTagEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(ukEntityTagEo);
        }
    }

    /** convert DB column to UkEntityTagEo */
    @AutoGenerated(locked = true)
    public UkEntityTagEo convertToEntityAttribute(String ukEntityTagEoJson) {
        if (StrUtil.isEmpty(ukEntityTagEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(ukEntityTagEoJson, new TypeReference<UkEntityTagEo>() {});
        }
    }
}
