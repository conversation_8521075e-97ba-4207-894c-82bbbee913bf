package com.pulse.tag.service;

import com.pulse.tag.manager.bo.*;
import com.pulse.tag.manager.dto.TagBaseDto;
import com.pulse.tag.persist.dos.Tag;
import com.pulse.tag.service.base.BaseTagBOService;
import com.pulse.tag.service.bto.CreateTagBto;
import com.pulse.tag.service.bto.DeleteTagBto;
import com.pulse.tag.service.bto.UpdateTagBto;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "4c91eac9-6eb6-45dc-8a9f-ffb47fbe5023|BO|SERVICE")
public class TagBOService extends BaseTagBOService {
    @AutoGenerated(locked = true)
    @Resource
    private TagBaseDtoService tagBaseDtoService;

    /** 更新标签 */
    @PublicInterface(id = "640244cf-98ea-433c-9eeb-70165264e049", module = "tag")
    @Transactional
    @AutoGenerated(locked = false, uuid = "7490a22f-a6da-46ff-b2bf-02ae57052311")
    public String updateTag(@Valid @NotNull UpdateTagBto updateTagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        TagBaseDto tagBaseDto = tagBaseDtoService.getById(updateTagBto.getId());
        UpdateTagBoResult boResult = super.updateTagBase(updateTagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateTagBto */
        {
            UpdateTagBto bto =
                    boResult.<UpdateTagBto>getBtoOfType(UpdateTagBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateTagBto, Tag, TagBO> updatedBto = boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                TagBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                Tag entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建标签 */
    @PublicInterface(id = "9d6ba1a8-1193-4141-99f4-bf88c76c787f", module = "tag")
    @Transactional
    @AutoGenerated(locked = false, uuid = "bd942c7b-9b35-405c-9390-e0a10c1b63c5")
    public String createTag(@Valid @NotNull CreateTagBto createTagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateTagBoResult boResult = super.createTagBase(createTagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateTagBto */
        {
            CreateTagBto bto =
                    boResult.<CreateTagBto>getBtoOfType(CreateTagBto.class).stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateTagBto, TagBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                TagBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 删除标签 */
    @PublicInterface(id = "91f28ec9-4804-403d-8b0f-90519a909c97", module = "tag")
    @Transactional
    @AutoGenerated(locked = false, uuid = "facbc006-d64f-493e-91f3-b3b36f4c785c")
    public String deleteTag(@Valid @NotNull DeleteTagBto deleteTagBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        TagBaseDto tagBaseDto = tagBaseDtoService.getById(deleteTagBto.getId());
        DeleteTagBoResult boResult = super.deleteTagBase(deleteTagBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteTagBto */
        {
            DeleteTagBto bto =
                    boResult.<DeleteTagBto>getBtoOfType(DeleteTagBto.class).stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteTagBto, Tag> deletedBto = boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
