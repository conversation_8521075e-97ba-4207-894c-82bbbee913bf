package com.pulse.tag.service.converter.voConverter;

import com.pulse.tag.persist.dos.EntityTag;
import com.pulse.tag.persist.dos.EntityTag.ValidFromAndValidTo;
import com.pulse.tag.persist.eo.IdxValidTimeEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "264c52be-220b-3ff2-9e62-e98e2a23dc8c")
public class EntityTagIdxValidTimeConverter {

    @AutoGenerated(locked = true)
    public static EntityTag.ValidFromAndValidTo convertFromIdxValidTimeToInner(
            IdxValidTimeEo idxValidTime) {
        if (null == idxValidTime) {
            return null;
        }

        ValidFromAndValidTo validFromAndValidTo = new ValidFromAndValidTo();
        validFromAndValidTo.setValidFrom(idxValidTime.getValidFrom());
        validFromAndValidTo.setValidTo(idxValidTime.getValidTo());
        return validFromAndValidTo;
    }
}
