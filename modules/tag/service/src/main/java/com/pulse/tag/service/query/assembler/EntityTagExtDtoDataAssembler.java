package com.pulse.tag.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.tag.manager.dto.EntityTagBaseDto;
import com.pulse.tag.manager.dto.EntityTagExtDto;
import com.pulse.tag.manager.dto.TagBaseDto;
import com.pulse.tag.service.EntityTagBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** EntityTagExtDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "8bff6db4-a035-399d-b7d5-93bda8ca9bcc")
public class EntityTagExtDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private EntityTagBaseDtoService entityTagBaseDtoService;

    /** 组装EntityTagExtDto数据 */
    @AutoGenerated(locked = true, uuid = "466eaf48-19f4-352e-8dbb-ddc47841b4e7")
    public void assembleData(
            List<EntityTagExtDto> dtoList,
            EntityTagExtDtoDataAssembler.EntityTagExtDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, EntityTagBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(EntityTagBaseDto::getId, Function.identity()));

        Map<String, TagBaseDto> tag =
                dataHolder.tag.stream()
                        .collect(Collectors.toMap(TagBaseDto::getId, Function.identity()));

        for (EntityTagExtDto dto : dtoList) {
            dto.setTag(
                    Optional.ofNullable(tag.get(baseDtoMap.get(dto.getId()).getTagId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装EntityTagExtDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "fec80deb-f617-347f-a9c9-99e0b9cb24cd")
    public void assembleDataCustomized(List<EntityTagExtDto> dataList) {
        // 自定义数据组装

    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class EntityTagExtDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<EntityTagBaseDto> rootBaseDtoList;

        /** 持有dto字段tag的Dto数据 */
        @AutoGenerated(locked = true)
        public List<TagBaseDto> tag;
    }
}
