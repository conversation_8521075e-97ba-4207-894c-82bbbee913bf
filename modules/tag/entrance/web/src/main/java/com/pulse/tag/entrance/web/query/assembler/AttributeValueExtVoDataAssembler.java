package com.pulse.tag.entrance.web.query.assembler;

import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.pulse.dictionary_basic.manager.dto.AttributeValueBaseDto;
import com.pulse.tag.entrance.web.vo.AttributeValueExtVo;
import com.pulse.tag.entrance.web.vo.TagRefAttributeDefinitionBaseVo;
import com.pulse.tag.manager.facade.dictionary_basic.AttributeValueBaseDtoServiceInTagRpcAdapter;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** AttributeValueExtVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "69345137-3a70-38e6-96c8-1e22ea87d2f9")
public class AttributeValueExtVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueBaseDtoServiceInTagRpcAdapter attributeValueBaseDtoServiceInTagRpcAdapter;

    /** 批量自定义组装AttributeValueExtVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "518b9c33-f0dc-324b-85f9-614af8084669")
    public void assembleDataCustomized(List<AttributeValueExtVo> dataList) {
        // 自定义数据组装

    }

    /** 组装AttributeValueExtVo数据 */
    @AutoGenerated(locked = true, uuid = "da5bd6e3-ba5a-33b2-bbd7-627237c65265")
    public void assembleData(
            Map<String, AttributeValueExtVo> voMap,
            AttributeValueExtVoDataAssembler.AttributeValueExtVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<AttributeValueBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, Pair<AttributeDefinitionBaseDto, TagRefAttributeDefinitionBaseVo>> attribute =
                dataHolder.attribute.keySet().stream()
                        .collect(
                                Collectors.toMap(
                                        dto -> dto.getId(),
                                        dto -> Pair.of(dto, dataHolder.attribute.get(dto)),
                                        (o1, o2) -> o1));

        for (AttributeValueBaseDto baseDto : baseDtoList) {
            AttributeValueExtVo vo = voMap.get(baseDto.getId());
            vo.setAttribute(
                    Optional.ofNullable(attribute.get(baseDto.getAttributeId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class AttributeValueExtVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<AttributeValueBaseDto> rootBaseDtoList;

        /** 持有字段attribute的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<AttributeDefinitionBaseDto, TagRefAttributeDefinitionBaseVo> attribute;
    }
}
