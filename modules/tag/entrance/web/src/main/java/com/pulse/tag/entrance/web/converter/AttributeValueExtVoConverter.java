package com.pulse.tag.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.pulse.dictionary_basic.manager.dto.AttributeValueExtDto;
import com.pulse.tag.entrance.web.query.assembler.AttributeValueExtVoDataAssembler;
import com.pulse.tag.entrance.web.query.assembler.AttributeValueExtVoDataAssembler.AttributeValueExtVoDataHolder;
import com.pulse.tag.entrance.web.query.collector.AttributeValueExtVoDataCollector;
import com.pulse.tag.entrance.web.vo.AttributeValueExtVo;
import com.pulse.tag.entrance.web.vo.TagRefAttributeDefinitionBaseVo;
import com.pulse.tag.manager.facade.dictionary_basic.AttributeValueBaseDtoServiceInTagRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到AttributeValueExtVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "9061c21f-9a43-4007-ad8c-abd39035431e|VO|CONVERTER")
public class AttributeValueExtVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueBaseDtoServiceInTagRpcAdapter attributeValueBaseDtoServiceInTagRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueExtVoDataAssembler attributeValueExtVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueExtVoDataCollector attributeValueExtVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TagRefAttributeDefinitionBaseVoConverter tagRefAttributeDefinitionBaseVoConverter;

    /** 使用默认方式组装AttributeValueExtVo列表数据 */
    @AutoGenerated(locked = true, uuid = "044e5393-64ac-31bb-86f2-cd666338577f")
    public List<AttributeValueExtVo> convertAndAssembleDataList(
            List<AttributeValueExtDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        AttributeValueExtVoDataHolder dataHolder = new AttributeValueExtVoDataHolder();
        dataHolder.setRootBaseDtoList(
                attributeValueBaseDtoServiceInTagRpcAdapter.getByIds(
                        dtoList.stream()
                                .map(AttributeValueExtDto::getId)
                                .collect(Collectors.toList())));
        Map<String, AttributeValueExtVo> voMap =
                convertToAttributeValueExtVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        attributeValueExtVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        attributeValueExtVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装AttributeValueExtVo数据 */
    @AutoGenerated(locked = true, uuid = "721cc665-b0fd-312b-8d21-8b8d75614f4e")
    public AttributeValueExtVo convertAndAssembleData(AttributeValueExtDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把AttributeValueExtDto转换成AttributeValueExtVo */
    @AutoGenerated(locked = false, uuid = "9061c21f-9a43-4007-ad8c-abd39035431e-converter-Map")
    public Map<AttributeValueExtDto, AttributeValueExtVo> convertToAttributeValueExtVoMap(
            List<AttributeValueExtDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<AttributeDefinitionBaseDto, TagRefAttributeDefinitionBaseVo> attributeMap =
                tagRefAttributeDefinitionBaseVoConverter
                        .convertToTagRefAttributeDefinitionBaseVoMap(
                                dtoList.stream()
                                        .filter(Objects::nonNull)
                                        .map(AttributeValueExtDto::getAttribute)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()));
        Map<AttributeValueExtDto, AttributeValueExtVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            AttributeValueExtVo vo = new AttributeValueExtVo();
                                            vo.setId(dto.getId());
                                            vo.setEntityType(dto.getEntityType());
                                            vo.setEntityId(dto.getEntityId());
                                            vo.setAttribute(
                                                    dto.getAttribute() == null
                                                            ? null
                                                            : attributeMap.get(dto.getAttribute()));
                                            vo.setValue(dto.getValue());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把AttributeValueExtDto转换成AttributeValueExtVo */
    @AutoGenerated(locked = true, uuid = "9061c21f-9a43-4007-ad8c-abd39035431e-converter-list")
    public List<AttributeValueExtVo> convertToAttributeValueExtVoList(
            List<AttributeValueExtDto> dtoList) {
        return new ArrayList<>(convertToAttributeValueExtVoMap(dtoList).values());
    }

    /** 把AttributeValueExtDto转换成AttributeValueExtVo */
    @AutoGenerated(locked = true, uuid = "b050798f-b129-3d28-b535-e44527e2ee7d")
    public AttributeValueExtVo convertToAttributeValueExtVo(AttributeValueExtDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToAttributeValueExtVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
