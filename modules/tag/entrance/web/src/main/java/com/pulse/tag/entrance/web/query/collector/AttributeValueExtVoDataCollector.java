package com.pulse.tag.entrance.web.query.collector;

import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.pulse.dictionary_basic.manager.dto.AttributeValueBaseDto;
import com.pulse.dictionary_basic.manager.dto.AttributeValueExtDto;
import com.pulse.tag.entrance.web.converter.AttributeValueExtVoConverter;
import com.pulse.tag.entrance.web.converter.TagRefAttributeDefinitionBaseVoConverter;
import com.pulse.tag.entrance.web.query.assembler.AttributeValueExtVoDataAssembler.AttributeValueExtVoDataHolder;
import com.pulse.tag.entrance.web.vo.TagRefAttributeDefinitionBaseVo;
import com.pulse.tag.manager.facade.dictionary_basic.AttributeDefinitionBaseDtoServiceInTagRpcAdapter;
import com.pulse.tag.manager.facade.dictionary_basic.AttributeValueBaseDtoServiceInTagRpcAdapter;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装AttributeValueExtVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "608481c3-f52b-3404-9d02-fc24fec4ab8a")
public class AttributeValueExtVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private AttributeDefinitionBaseDtoServiceInTagRpcAdapter
            attributeDefinitionBaseDtoServiceInTagRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueBaseDtoServiceInTagRpcAdapter attributeValueBaseDtoServiceInTagRpcAdapter;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueExtVoConverter attributeValueExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeValueExtVoDataCollector attributeValueExtVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private TagRefAttributeDefinitionBaseVoConverter tagRefAttributeDefinitionBaseVoConverter;

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "24a9b5f9-b631-34ed-a541-1266431c4427")
    public void collectDataDefault(AttributeValueExtVoDataHolder dataHolder) {
        attributeValueExtVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "aa843ca5-b903-3f62-b5e5-e90dd5fbc4dc")
    private void fillDataWhenNecessary(AttributeValueExtVoDataHolder dataHolder) {
        List<AttributeValueBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.attribute == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(AttributeValueBaseDto::getAttributeId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<AttributeDefinitionBaseDto> baseDtoList =
                    attributeDefinitionBaseDtoServiceInTagRpcAdapter
                            .getByIds(new ArrayList<>(ids))
                            .stream()
                            .sorted(Comparator.comparing(AttributeDefinitionBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, AttributeDefinitionBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            AttributeDefinitionBaseDto::getId,
                                            Function.identity()));
            Map<AttributeDefinitionBaseDto, TagRefAttributeDefinitionBaseVo> dtoVoMap =
                    tagRefAttributeDefinitionBaseVoConverter
                            .convertToTagRefAttributeDefinitionBaseVoMap(baseDtoList);
            Map<AttributeDefinitionBaseDto, TagRefAttributeDefinitionBaseVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.attribute =
                    rootDtoList.stream()
                            .map(AttributeValueBaseDto::getAttributeId)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** 获取AttributeValueExtDto数据填充AttributeValueExtVo，并根据扩展关系填充剩余数据 */
    @AutoGenerated(locked = true, uuid = "ebd6e814-7741-37a3-b4e9-1db96e8f0cfd")
    public void collectDataWithDtoData(
            List<AttributeValueExtDto> dtoList, AttributeValueExtVoDataHolder dataHolder) {
        List<AttributeDefinitionBaseDto> attributeList = new ArrayList<>();

        for (AttributeValueExtDto rootDto : dtoList) {
            AttributeDefinitionBaseDto attributeDto = rootDto.getAttribute();
            if (attributeDto != null) {
                attributeList.add(attributeDto);
            }
        }

        // access attribute
        Map<AttributeDefinitionBaseDto, TagRefAttributeDefinitionBaseVo> attributeVoMap =
                tagRefAttributeDefinitionBaseVoConverter
                        .convertToTagRefAttributeDefinitionBaseVoMap(attributeList);
        dataHolder.attribute =
                attributeList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> attributeVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
