package com.pulse.tag.manager.bo;

import com.pulse.tag.manager.bo.base.BaseEntityTagBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE entity_tag  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ? and lock_version = ?")
@Table(name = "entity_tag")
@Entity
@AutoGenerated(locked = false, uuid = "574b604f-15ba-4023-81f3-40ba82f6da02|BO|DEFINITION")
public class EntityTagBO extends BaseEntityTagBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "574b604f-15ba-4023-81f3-40ba82f6da02|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
