package com.pulse.drug_permission.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.drug_permission.manager.bo.*;
import com.pulse.drug_permission.manager.bo.DrugSpecificationDetailUsageBO;
import com.pulse.drug_permission.persist.dos.DrugSpecificationDetailUsage;
import com.pulse.drug_permission.service.base.BaseDrugSpecificationDetailUsageBOService.DeleteUsageBoResult;
import com.pulse.drug_permission.service.base.BaseDrugSpecificationDetailUsageBOService.SaveSpecificationDetailDefaultUsageBoResult;
import com.pulse.drug_permission.service.bto.DeleteUsageBto;
import com.pulse.drug_permission.service.bto.SaveSpecificationDetailDefaultUsageBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "05173397-1600-32d3-8547-641abf0afc58")
public class BaseDrugSpecificationDetailUsageBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugSpecificationDetailUsageBO
            createSaveSpecificationDetailDefaultUsageOnDuplicateUpdate(
                    BaseDrugSpecificationDetailUsageBOService
                                    .SaveSpecificationDetailDefaultUsageBoResult
                            boResult,
                    SaveSpecificationDetailDefaultUsageBto saveSpecificationDetailDefaultUsageBto) {
        DrugSpecificationDetailUsageBO drugSpecificationDetailUsageBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveSpecificationDetailDefaultUsageBto.getId() == null);
        if (!allNull && !found) {
            drugSpecificationDetailUsageBO =
                    DrugSpecificationDetailUsageBO.getById(
                            saveSpecificationDetailDefaultUsageBto.getId());
            if (drugSpecificationDetailUsageBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugSpecificationDetailUsageBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        drugSpecificationDetailUsageBO.convertToDrugSpecificationDetailUsage());
                updatedBto.setBto(saveSpecificationDetailDefaultUsageBto);
                updatedBto.setBo(drugSpecificationDetailUsageBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "usePriority")) {
                    drugSpecificationDetailUsageBO.setUsePriority(
                            saveSpecificationDetailDefaultUsageBto.getUsePriority());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "dosage")) {
                    drugSpecificationDetailUsageBO.setDosage(
                            saveSpecificationDetailDefaultUsageBto.getDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "dosageUnit")) {
                    drugSpecificationDetailUsageBO.setDosageUnit(
                            saveSpecificationDetailDefaultUsageBto.getDosageUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "usageDescription")) {
                    drugSpecificationDetailUsageBO.setUsageDescription(
                            saveSpecificationDetailDefaultUsageBto.getUsageDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "administration")) {
                    drugSpecificationDetailUsageBO.setAdministration(
                            saveSpecificationDetailDefaultUsageBto.getAdministration());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "frequency")) {
                    drugSpecificationDetailUsageBO.setFrequency(
                            saveSpecificationDetailDefaultUsageBto.getFrequency());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "outpFlag")) {
                    drugSpecificationDetailUsageBO.setOutpFlag(
                            saveSpecificationDetailDefaultUsageBto.getOutpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "erpFlag")) {
                    drugSpecificationDetailUsageBO.setErpFlag(
                            saveSpecificationDetailDefaultUsageBto.getErpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "inpFlag")) {
                    drugSpecificationDetailUsageBO.setInpFlag(
                            saveSpecificationDetailDefaultUsageBto.getInpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "remark")) {
                    drugSpecificationDetailUsageBO.setRemark(
                            saveSpecificationDetailDefaultUsageBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "outpAmount")) {
                    drugSpecificationDetailUsageBO.setOutpAmount(
                            saveSpecificationDetailDefaultUsageBto.getOutpAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "unit")) {
                    drugSpecificationDetailUsageBO.setUnit(
                            saveSpecificationDetailDefaultUsageBto.getUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "outpDay")) {
                    drugSpecificationDetailUsageBO.setOutpDay(
                            saveSpecificationDetailDefaultUsageBto.getOutpDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "erpAmount")) {
                    drugSpecificationDetailUsageBO.setErpAmount(
                            saveSpecificationDetailDefaultUsageBto.getErpAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "erpDay")) {
                    drugSpecificationDetailUsageBO.setErpDay(
                            saveSpecificationDetailDefaultUsageBto.getErpDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "dischargeMedicationDay")) {
                    drugSpecificationDetailUsageBO.setDischargeMedicationDay(
                            saveSpecificationDetailDefaultUsageBto.getDischargeMedicationDay());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(
                        drugSpecificationDetailUsageBO.convertToDrugSpecificationDetailUsage());
                updatedBto.setBto(saveSpecificationDetailDefaultUsageBto);
                updatedBto.setBo(drugSpecificationDetailUsageBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "usePriority")) {
                    drugSpecificationDetailUsageBO.setUsePriority(
                            saveSpecificationDetailDefaultUsageBto.getUsePriority());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "dosage")) {
                    drugSpecificationDetailUsageBO.setDosage(
                            saveSpecificationDetailDefaultUsageBto.getDosage());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "dosageUnit")) {
                    drugSpecificationDetailUsageBO.setDosageUnit(
                            saveSpecificationDetailDefaultUsageBto.getDosageUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "usageDescription")) {
                    drugSpecificationDetailUsageBO.setUsageDescription(
                            saveSpecificationDetailDefaultUsageBto.getUsageDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "administration")) {
                    drugSpecificationDetailUsageBO.setAdministration(
                            saveSpecificationDetailDefaultUsageBto.getAdministration());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "frequency")) {
                    drugSpecificationDetailUsageBO.setFrequency(
                            saveSpecificationDetailDefaultUsageBto.getFrequency());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "outpFlag")) {
                    drugSpecificationDetailUsageBO.setOutpFlag(
                            saveSpecificationDetailDefaultUsageBto.getOutpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "erpFlag")) {
                    drugSpecificationDetailUsageBO.setErpFlag(
                            saveSpecificationDetailDefaultUsageBto.getErpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "inpFlag")) {
                    drugSpecificationDetailUsageBO.setInpFlag(
                            saveSpecificationDetailDefaultUsageBto.getInpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "remark")) {
                    drugSpecificationDetailUsageBO.setRemark(
                            saveSpecificationDetailDefaultUsageBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "outpAmount")) {
                    drugSpecificationDetailUsageBO.setOutpAmount(
                            saveSpecificationDetailDefaultUsageBto.getOutpAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "unit")) {
                    drugSpecificationDetailUsageBO.setUnit(
                            saveSpecificationDetailDefaultUsageBto.getUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "outpDay")) {
                    drugSpecificationDetailUsageBO.setOutpDay(
                            saveSpecificationDetailDefaultUsageBto.getOutpDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "erpAmount")) {
                    drugSpecificationDetailUsageBO.setErpAmount(
                            saveSpecificationDetailDefaultUsageBto.getErpAmount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "erpDay")) {
                    drugSpecificationDetailUsageBO.setErpDay(
                            saveSpecificationDetailDefaultUsageBto.getErpDay());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveSpecificationDetailDefaultUsageBto,
                                        "__$validPropertySet"),
                        "dischargeMedicationDay")) {
                    drugSpecificationDetailUsageBO.setDischargeMedicationDay(
                            saveSpecificationDetailDefaultUsageBto.getDischargeMedicationDay());
                }
            }
        } else {
            drugSpecificationDetailUsageBO = new DrugSpecificationDetailUsageBO();
            if (pkExist) {
                drugSpecificationDetailUsageBO.setId(
                        saveSpecificationDetailDefaultUsageBto.getId());
            } else {
                drugSpecificationDetailUsageBO.setId(
                        String.valueOf(
                                this.idGenerator.allocateId("drug_specification_detail_usage")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "usePriority")) {
                drugSpecificationDetailUsageBO.setUsePriority(
                        saveSpecificationDetailDefaultUsageBto.getUsePriority());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "dosage")) {
                drugSpecificationDetailUsageBO.setDosage(
                        saveSpecificationDetailDefaultUsageBto.getDosage());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "dosageUnit")) {
                drugSpecificationDetailUsageBO.setDosageUnit(
                        saveSpecificationDetailDefaultUsageBto.getDosageUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "usageDescription")) {
                drugSpecificationDetailUsageBO.setUsageDescription(
                        saveSpecificationDetailDefaultUsageBto.getUsageDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "administration")) {
                drugSpecificationDetailUsageBO.setAdministration(
                        saveSpecificationDetailDefaultUsageBto.getAdministration());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "frequency")) {
                drugSpecificationDetailUsageBO.setFrequency(
                        saveSpecificationDetailDefaultUsageBto.getFrequency());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "outpFlag")) {
                drugSpecificationDetailUsageBO.setOutpFlag(
                        saveSpecificationDetailDefaultUsageBto.getOutpFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "erpFlag")) {
                drugSpecificationDetailUsageBO.setErpFlag(
                        saveSpecificationDetailDefaultUsageBto.getErpFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "inpFlag")) {
                drugSpecificationDetailUsageBO.setInpFlag(
                        saveSpecificationDetailDefaultUsageBto.getInpFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "remark")) {
                drugSpecificationDetailUsageBO.setRemark(
                        saveSpecificationDetailDefaultUsageBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "outpAmount")) {
                drugSpecificationDetailUsageBO.setOutpAmount(
                        saveSpecificationDetailDefaultUsageBto.getOutpAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "unit")) {
                drugSpecificationDetailUsageBO.setUnit(
                        saveSpecificationDetailDefaultUsageBto.getUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "outpDay")) {
                drugSpecificationDetailUsageBO.setOutpDay(
                        saveSpecificationDetailDefaultUsageBto.getOutpDay());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "erpAmount")) {
                drugSpecificationDetailUsageBO.setErpAmount(
                        saveSpecificationDetailDefaultUsageBto.getErpAmount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "erpDay")) {
                drugSpecificationDetailUsageBO.setErpDay(
                        saveSpecificationDetailDefaultUsageBto.getErpDay());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveSpecificationDetailDefaultUsageBto, "__$validPropertySet"),
                    "dischargeMedicationDay")) {
                drugSpecificationDetailUsageBO.setDischargeMedicationDay(
                        saveSpecificationDetailDefaultUsageBto.getDischargeMedicationDay());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveSpecificationDetailDefaultUsageBto);
            addedBto.setBo(drugSpecificationDetailUsageBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugSpecificationDetailUsageBO;
    }

    /** 删除对象:deleteUsage,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugSpecificationDetailUsageBO deleteDeleteUsageOnMissThrowEx(
            BaseDrugSpecificationDetailUsageBOService.DeleteUsageBoResult boResult,
            DeleteUsageBto deleteUsageBto) {
        DrugSpecificationDetailUsageBO drugSpecificationDetailUsageBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteUsageBto.getId() == null);
        if (!allNull && !found) {
            drugSpecificationDetailUsageBO =
                    DrugSpecificationDetailUsageBO.getById(deleteUsageBto.getId());
            found = true;
        }
        if (drugSpecificationDetailUsageBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugSpecificationDetailUsageBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteUsageBto);
            deletedBto.setEntity(
                    drugSpecificationDetailUsageBO.convertToDrugSpecificationDetailUsage());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugSpecificationDetailUsageBO;
        }
    }

    /** 删除常用方式 */
    @AutoGenerated(locked = true)
    protected DeleteUsageBoResult deleteUsageBase(DeleteUsageBto deleteUsageBto) {
        if (deleteUsageBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteUsageBoResult boResult = new DeleteUsageBoResult();
        DrugSpecificationDetailUsageBO drugSpecificationDetailUsageBO =
                deleteDeleteUsageOnMissThrowEx(boResult, deleteUsageBto);
        boResult.setRootBo(drugSpecificationDetailUsageBO);
        return boResult;
    }

    /** 功能：药品常用方法维护 */
    @AutoGenerated(locked = true)
    protected SaveSpecificationDetailDefaultUsageBoResult saveSpecificationDetailDefaultUsageBase(
            SaveSpecificationDetailDefaultUsageBto saveSpecificationDetailDefaultUsageBto) {
        if (saveSpecificationDetailDefaultUsageBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveSpecificationDetailDefaultUsageBoResult boResult =
                new SaveSpecificationDetailDefaultUsageBoResult();
        DrugSpecificationDetailUsageBO drugSpecificationDetailUsageBO =
                createSaveSpecificationDetailDefaultUsageOnDuplicateUpdate(
                        boResult, saveSpecificationDetailDefaultUsageBto);
        boResult.setRootBo(drugSpecificationDetailUsageBO);
        return boResult;
    }

    public static class SaveSpecificationDetailDefaultUsageBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailUsageBO getRootBo() {
            return (DrugSpecificationDetailUsageBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveSpecificationDetailDefaultUsageBto, DrugSpecificationDetailUsageBO>
                getCreatedBto(
                        SaveSpecificationDetailDefaultUsageBto
                                saveSpecificationDetailDefaultUsageBto) {
            return this.getAddedResult(saveSpecificationDetailDefaultUsageBto);
        }

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailUsage getDeleted_DrugSpecificationDetailUsage() {
            return (DrugSpecificationDetailUsage)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugSpecificationDetailUsage.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveSpecificationDetailDefaultUsageBto,
                        DrugSpecificationDetailUsage,
                        DrugSpecificationDetailUsageBO>
                getUpdatedBto(
                        SaveSpecificationDetailDefaultUsageBto
                                saveSpecificationDetailDefaultUsageBto) {
            return super.getUpdatedResult(saveSpecificationDetailDefaultUsageBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveSpecificationDetailDefaultUsageBto, DrugSpecificationDetailUsageBO>
                getUnmodifiedBto(
                        SaveSpecificationDetailDefaultUsageBto
                                saveSpecificationDetailDefaultUsageBto) {
            return super.getUnmodifiedResult(saveSpecificationDetailDefaultUsageBto);
        }
    }

    public static class DeleteUsageBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailUsageBO getRootBo() {
            return (DrugSpecificationDetailUsageBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteUsageBto, DrugSpecificationDetailUsageBO> getCreatedBto(
                DeleteUsageBto deleteUsageBto) {
            return this.getAddedResult(deleteUsageBto);
        }

        @AutoGenerated(locked = true)
        public DrugSpecificationDetailUsage getDeleted_DrugSpecificationDetailUsage() {
            return (DrugSpecificationDetailUsage)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(DrugSpecificationDetailUsage.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        DeleteUsageBto,
                        DrugSpecificationDetailUsage,
                        DrugSpecificationDetailUsageBO>
                getUpdatedBto(DeleteUsageBto deleteUsageBto) {
            return super.getUpdatedResult(deleteUsageBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteUsageBto, DrugSpecificationDetailUsageBO> getUnmodifiedBto(
                DeleteUsageBto deleteUsageBto) {
            return super.getUnmodifiedResult(deleteUsageBto);
        }
    }
}
