package com.pulse.drug_permission.entrance.web.query.executor;

import com.pulse.drug_permission.entrance.web.converter.DrugCentralPurchaseDepartmentTaskWithTaskVoConverter;
import com.pulse.drug_permission.entrance.web.query.assembler.DrugCentralPurchaseDepartmentTaskWithTaskVoDataAssembler;
import com.pulse.drug_permission.entrance.web.query.assembler.DrugCentralPurchaseDepartmentTaskWithTaskVoDataAssembler.DrugCentralPurchaseDepartmentTaskWithTaskVoDataHolder;
import com.pulse.drug_permission.entrance.web.query.collector.DrugCentralPurchaseDepartmentTaskWithTaskVoDataCollector;
import com.pulse.drug_permission.entrance.web.vo.DrugCentralPurchaseDepartmentTaskWithTaskVo;
import com.pulse.drug_permission.manager.dto.DrugCentralPurchaseDepartmentTaskBaseDto;
import com.pulse.drug_permission.persist.qto.ListDrugCentralPurchaseDepartmentTaskWithTaskQto;
import com.pulse.drug_permission.service.DrugCentralPurchaseDepartmentTaskBaseDtoService;
import com.pulse.drug_permission.service.index.entity.ListDrugCentralPurchaseDepartmentTaskWithTaskQtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/** DrugCentralPurchaseDepartmentTaskWithTaskVo查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "3367edbd-38cc-3e2c-bdf4-9ab655dd7671")
public class DrugCentralPurchaseDepartmentTaskWithTaskVoQueryExecutor {
    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseDepartmentTaskBaseDtoService
            drugCentralPurchaseDepartmentTaskBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseDepartmentTaskWithTaskVoConverter
            drugCentralPurchaseDepartmentTaskWithTaskVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseDepartmentTaskWithTaskVoDataAssembler
            drugCentralPurchaseDepartmentTaskWithTaskVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugCentralPurchaseDepartmentTaskWithTaskVoDataCollector
            drugCentralPurchaseDepartmentTaskWithTaskVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private ListDrugCentralPurchaseDepartmentTaskWithTaskQtoService
            listDrugCentralPurchaseDepartmentTaskWithTaskQtoService;

    /** 将ID列表转换为Map<ID, VO> */
    @AutoGenerated(locked = true, uuid = "306e81a1-15b6-380f-8f40-5da4c87e95bb")
    private Map<String, DrugCentralPurchaseDepartmentTaskWithTaskVo> toIdVoMap(
            List<String> ids, DrugCentralPurchaseDepartmentTaskWithTaskVoDataHolder dataHolder) {
        List<DrugCentralPurchaseDepartmentTaskBaseDto> rootBaseDtoList =
                drugCentralPurchaseDepartmentTaskBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(rootBaseDtoList);
        Map<String, DrugCentralPurchaseDepartmentTaskBaseDto> baseDtoMap =
                rootBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        DrugCentralPurchaseDepartmentTaskBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        Map<DrugCentralPurchaseDepartmentTaskBaseDto, DrugCentralPurchaseDepartmentTaskWithTaskVo>
                voMap =
                        drugCentralPurchaseDepartmentTaskWithTaskVoConverter
                                .convertToDrugCentralPurchaseDepartmentTaskWithTaskVoMap(
                                        new ArrayList<>(baseDtoMap.values()));
        Map<String, DrugCentralPurchaseDepartmentTaskWithTaskVo> idVoMap =
                baseDtoMap.values().stream()
                        .collect(
                                Collectors.toMap(
                                        DrugCentralPurchaseDepartmentTaskBaseDto::getId,
                                        baseDto -> voMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        return ids.stream()
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                id -> idVoMap.get(id),
                                (o1, o2) -> o1,
                                LinkedHashMap::new));
    }

    /**
     * 根据ListDrugCentralPurchaseDepartmentTaskWithTaskQto查询DrugCentralPurchaseDepartmentTaskWithTaskVo列表,上限500
     */
    @AutoGenerated(locked = false, uuid = "ec2fb5be-70b2-3f88-87ec-15d66bb3a127")
    public List<DrugCentralPurchaseDepartmentTaskWithTaskVo>
            queryByListDrugCentralPurchaseDepartmentTaskWithTask(
                    @NotNull ListDrugCentralPurchaseDepartmentTaskWithTaskQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listDrugCentralPurchaseDepartmentTaskWithTaskQtoService.query(qto);
        DrugCentralPurchaseDepartmentTaskWithTaskVoDataHolder dataHolder =
                new DrugCentralPurchaseDepartmentTaskWithTaskVoDataHolder();
        Map<String, DrugCentralPurchaseDepartmentTaskWithTaskVo> idVoMap =
                toIdVoMap(ids, dataHolder);
        drugCentralPurchaseDepartmentTaskWithTaskVoDataCollector.collectDataDefault(dataHolder);
        drugCentralPurchaseDepartmentTaskWithTaskVoDataAssembler.assembleData(idVoMap, dataHolder);
        List<DrugCentralPurchaseDepartmentTaskWithTaskVo> result =
                ids.stream()
                        .map(id -> idVoMap.get(id))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
