package com.pulse.log.service.query;

import com.pulse.log.manager.dto.OperationLogBaseDto;
import com.pulse.log.persist.qto.ListOperationLogQto;
import com.pulse.log.service.OperationLogBaseDtoService;
import com.pulse.log.service.index.entity.ListOperationLogQtoService;
import com.pulse.log.service.query.assembler.OperationLogBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** OperationLogBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "0925e47a-8389-3983-81b5-549079a8a2d9")
public class OperationLogBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListOperationLogQtoService listOperationLogQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private OperationLogBaseDtoDataAssembler operationLogBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private OperationLogBaseDtoService operationLogBaseDtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "56797e92-dc1b-3ed7-8cd6-b58ddab2d4b0")
    private List<OperationLogBaseDto> toDtoList(List<String> ids) {
        List<OperationLogBaseDto> baseDtoList = operationLogBaseDtoService.getByIds(ids);
        Map<String, OperationLogBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(Collectors.toMap(OperationLogBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据ListOperationLogQto查询OperationLogBaseDto列表,分页 */
    @PublicInterface(id = "a2408d06-01e0-4711-8ab7-8cbfa89b7ed3", module = "log")
    @AutoGenerated(locked = false, uuid = "e8e6d9df-745d-31fc-9b7b-047b1b02626e")
    public VSQueryResult<OperationLogBaseDto> listOperationLogPaged(
            @Valid @NotNull ListOperationLogQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listOperationLogQtoService.queryPaged(qto);
        List<OperationLogBaseDto> dtoList = toDtoList(ids);
        operationLogBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listOperationLogQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
