package com.pulse.log.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.log.common.enums.LogOperationTypeEnum;
import com.pulse.log.manager.OperationLogBaseDtoManager;
import com.pulse.log.manager.dto.OperationLogBaseDto;
import com.pulse.log.service.converter.OperationLogBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "e48dbacd-4aac-4dad-b3f5-940e8f18db4e|DTO|SERVICE")
public class OperationLogBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OperationLogBaseDtoManager operationLogBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OperationLogBaseDtoServiceConverter operationLogBaseDtoServiceConverter;

    @PublicInterface(id = "6c20261d-4e7c-40f4-807b-268824be9562", module = "log")
    @AutoGenerated(locked = false, uuid = "02091926-ab8d-3cb9-a93a-509c2dcaa12d")
    public List<OperationLogBaseDto> getBySourceBusinessIds(
            @Valid @NotNull(message = "来源业务ID不能为空") List<String> sourceBusinessId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        sourceBusinessId = new ArrayList<>(new HashSet<>(sourceBusinessId));
        List<OperationLogBaseDto> operationLogBaseDtoList =
                operationLogBaseDtoManager.getBySourceBusinessIds(sourceBusinessId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "c6d840c9-face-414c-83a9-a3bc37ff9019", module = "log")
    @AutoGenerated(locked = false, uuid = "09d6d343-34f7-3dd6-b6a4-cdc8c405685f")
    public List<OperationLogBaseDto> getByOperatorStaffIds(
            @Valid @NotNull(message = "操作人员ID不能为空") List<String> operatorStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        operatorStaffId = new ArrayList<>(new HashSet<>(operatorStaffId));
        List<OperationLogBaseDto> operationLogBaseDtoList =
                operationLogBaseDtoManager.getByOperatorStaffIds(operatorStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "80ce755d-e6b8-4257-992c-3236a3f2bf7d", module = "log")
    @AutoGenerated(locked = false, uuid = "16f56794-f553-396c-9fee-1a9bb2527624")
    public List<OperationLogBaseDto> getByBusinessScenes(
            @Valid @NotNull(message = "业务场景不能为空") List<String> businessScene) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        businessScene = new ArrayList<>(new HashSet<>(businessScene));
        List<OperationLogBaseDto> operationLogBaseDtoList =
                operationLogBaseDtoManager.getByBusinessScenes(businessScene);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "161d6939-fbb3-466d-b9f7-1759f2d2b09c", module = "log")
    @AutoGenerated(locked = false, uuid = "2fa095e8-ccd8-3f88-9033-5ab3da659938")
    public List<OperationLogBaseDto> getByBatchIds(
            @Valid @NotNull(message = "批次ID不能为空") List<String> batchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        batchId = new ArrayList<>(new HashSet<>(batchId));
        List<OperationLogBaseDto> operationLogBaseDtoList =
                operationLogBaseDtoManager.getByBatchIds(batchId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "41953b9f-de4a-413f-8833-0647c763ed2c", module = "log")
    @AutoGenerated(locked = false, uuid = "42f83af6-4611-3591-b73a-138885ece8be")
    public List<OperationLogBaseDto> getByOperatorUserIds(
            @Valid @NotNull(message = "操作用户ID不能为空") List<String> operatorUserId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        operatorUserId = new ArrayList<>(new HashSet<>(operatorUserId));
        List<OperationLogBaseDto> operationLogBaseDtoList =
                operationLogBaseDtoManager.getByOperatorUserIds(operatorUserId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "56fcb498-e7ef-4aaf-ad73-759211047bca", module = "log")
    @AutoGenerated(locked = false, uuid = "4cd8962b-86ea-3ead-b5e2-02c0428279ba")
    public List<OperationLogBaseDto> getByBatchId(@NotNull(message = "批次ID不能为空") String batchId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByBatchIds(Arrays.asList(batchId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "0e78ca0b-b6ba-4ecd-a348-6ec4bb39086f", module = "log")
    @AutoGenerated(locked = false, uuid = "4e310ef9-3bab-3c6a-a7a2-26ec967e72d9")
    public List<OperationLogBaseDto> getByOperationType(
            @NotNull LogOperationTypeEnum operationType) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOperationTypes(Arrays.asList(operationType));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "e1fcd674-6212-46f0-9562-34c1afeec417", module = "log")
    @AutoGenerated(locked = false, uuid = "5d303155-a43e-3333-835d-22f7a15d17d6")
    public List<OperationLogBaseDto> getByBusinessScene(
            @NotNull(message = "业务场景不能为空") String businessScene) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByBusinessScenes(Arrays.asList(businessScene));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "ad069ba9-43eb-472e-a2c5-0bb91bfbbbe2", module = "log")
    @AutoGenerated(locked = false, uuid = "7fa72bfe-357a-371e-a1f7-2a95aaa52df4")
    public List<OperationLogBaseDto> getByOperationTypes(
            @Valid @NotNull List<LogOperationTypeEnum> operationType) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        operationType = new ArrayList<>(new HashSet<>(operationType));
        List<OperationLogBaseDto> operationLogBaseDtoList =
                operationLogBaseDtoManager.getByOperationTypes(operationType);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "13bbf65e-a063-4e56-9b66-ef96a3262bcd", module = "log")
    @AutoGenerated(locked = false, uuid = "881340be-f5d8-336a-908a-1f81564c2c6e")
    public List<OperationLogBaseDto> getByOperatorStaffId(
            @NotNull(message = "操作人员ID不能为空") String operatorStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOperatorStaffIds(Arrays.asList(operatorStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "7ce19363-ebb4-4497-9137-997b79d9544f", module = "log")
    @AutoGenerated(locked = false, uuid = "88f7c630-5a24-3137-88b2-1289e58df0de")
    public List<OperationLogBaseDto> getByOperationTime(@NotNull Date operationTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOperationTimes(Arrays.asList(operationTime));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "9db33b49-69e8-45d5-a879-12c9ff2321bd", module = "log")
    @AutoGenerated(locked = false, uuid = "ad49133a-6e68-3f7d-a87a-6c6c24eacf11")
    public List<OperationLogBaseDto> getByOperationTimes(@Valid @NotNull List<Date> operationTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        operationTime = new ArrayList<>(new HashSet<>(operationTime));
        List<OperationLogBaseDto> operationLogBaseDtoList =
                operationLogBaseDtoManager.getByOperationTimes(operationTime);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f6d31fd0-a3d4-4f46-b7b3-c3e291834ac4", module = "log")
    @AutoGenerated(locked = false, uuid = "adfca71b-da60-3a5d-a47a-1dd408592277")
    public List<OperationLogBaseDto> getBySourceBusinessId(
            @NotNull(message = "来源业务ID不能为空") String sourceBusinessId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySourceBusinessIds(Arrays.asList(sourceBusinessId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1d8162b2-549d-48c2-8bd7-9ff5f74819e8", module = "log")
    @AutoGenerated(locked = false, uuid = "b724367d-1abe-32ce-897e-8d765044a41e")
    public OperationLogBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OperationLogBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "7a0ee702-593c-4183-aa8e-e6fc6ec633b8", module = "log")
    @AutoGenerated(locked = false, uuid = "d74be541-e63d-3d87-b4fa-2dc01ba9792d")
    public List<OperationLogBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OperationLogBaseDto> operationLogBaseDtoList = operationLogBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return operationLogBaseDtoServiceConverter.OperationLogBaseDtoConverter(
                operationLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "9724af39-9348-4b89-9976-f76fca9db077", module = "log")
    @AutoGenerated(locked = false, uuid = "fb8b374f-7acd-39ff-9804-9532ba225f3b")
    public List<OperationLogBaseDto> getByOperatorUserId(
            @NotNull(message = "操作用户ID不能为空") String operatorUserId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOperatorUserIds(Arrays.asList(operatorUserId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
