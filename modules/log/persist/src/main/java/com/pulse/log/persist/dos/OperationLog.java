package com.pulse.log.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.log.common.enums.LogOperationResultEnum;
import com.pulse.log.common.enums.LogOperationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "operation_log", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "4a346ea0-af40-40ce-a651-de2eec0bbc39|ENTITY|DEFINITION")
public class OperationLog {
    /** 批次ID，用于关联同一批次的多条操作记录，例如批量更新药品库存 */
    @AutoGenerated(locked = true, uuid = "a37ddc82-c8be-4bfb-a068-002c8d9792fe")
    @TableField(value = "batch_id")
    private String batchId;

    /** 业务场景编码，例如“患者入院登记”、“药品库存更新”、“财务结算”等 */
    @AutoGenerated(locked = true, uuid = "58804697-60e5-4143-ad2f-3332a3338436")
    @TableField(value = "business_scene")
    private String businessScene;

    @AutoGenerated(locked = true, uuid = "d6822708-7156-5d66-ac19-191dfc9cdd1c")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "8599f673-f6cd-5498-af2e-c53d4cc60b17")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    /**
     * 数字签名，使用国密SM2算法对关键字段（如`id`、`operation_type`、`source_business_id`、`operation_time`）进行签名，确保数据完整性和不可抵赖性
     */
    @AutoGenerated(locked = true, uuid = "98f3129c-f7e4-4b9b-9a5b-544e42ac1a7c")
    @TableField(value = "digital_signature")
    private String digitalSignature;

    @AutoGenerated(locked = true, uuid = "1973d721-aeb9-4369-bc6d-520001e95e6f")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "6226f453-f36c-48ff-9cb1-a4a2f622ee87")
    @TableField(value = "lock_version")
    private Long lockVersion;

    /** 操作结果状态，例如“SUCCESS”（成功）或“FAIL”（失败） */
    @AutoGenerated(locked = true, uuid = "102c08b9-ea00-441a-ad55-ac91cc9052f2")
    @TableField(value = "operation_result")
    private LogOperationResultEnum operationResult;

    /** 操作时间，精确记录操作发生的时间点 */
    @AutoGenerated(locked = true, uuid = "b2d7e010-289c-41bb-b7d6-24b5bc150bd3")
    @TableField(value = "operation_time")
    private Date operationTime;

    /** 操作类型，例如“CREATE”（新增）、“UPDATE”（更新）、“DELETE”（删除） */
    @AutoGenerated(locked = true, uuid = "74dbd678-3a8e-43f5-bfa3-864365fb698d")
    @TableField(value = "operation_type")
    private LogOperationTypeEnum operationType;

    /** 操作终端IP，支持IPv6，记录操作来源的网络地址 */
    @AutoGenerated(locked = true, uuid = "bb62594a-791c-4bea-a87e-05e4ede99979")
    @TableField(value = "operator_ip")
    private String operatorIp;

    /** 操作组织ID，关联`organization`表的`id`，标识操作所属的组织（如科室） */
    @AutoGenerated(locked = true, uuid = "36c4d4d4-c3f3-4fd2-ac1b-532c4d3ed245")
    @TableField(value = "operator_organization_id")
    private String operatorOrganizationId;

    /** 操作人角色ID，关联`role`表的`id`，标识用户的角色权限 */
    @AutoGenerated(locked = true, uuid = "b37f6b4f-7a9c-4a0c-ac81-456f6b410510")
    @TableField(value = "operator_role_id")
    private String operatorRoleId;

    /** 操作人员ID，关联staff表 */
    @AutoGenerated(locked = true, uuid = "e0853b08-d46f-46e5-b673-161aa06b7476")
    @TableField(value = "operator_staff_id")
    private String operatorStaffId;

    /** 操作用户ID，关联`user`表的`id`，标识执行操作的用户 */
    @AutoGenerated(locked = true, uuid = "050f137e-9aa7-47d3-b37d-24e0cc08965f")
    @TableField(value = "operator_user_id")
    private String operatorUserId;

    /** 操作后信息，JSON格式，记录操作后的关键数据，用于对比变化 */
    @AutoGenerated(locked = true, uuid = "63eff631-3484-4f5c-9ac7-fb588f06acae")
    @TableField(value = "post_operation_info")
    private String postOperationInfo;

    /** 操作前信息，JSON格式，记录操作前的关键数据，如患者信息或药品数量 */
    @AutoGenerated(locked = true, uuid = "68b099ae-1b1f-4696-bd15-7cf754462189")
    @TableField(value = "pre_operation_info")
    private String preOperationInfo;

    /** 备注，记录额外信息或操作说明，例如“紧急入院”或“库存不足提醒” */
    @AutoGenerated(locked = true, uuid = "13894417-3383-4bd1-ad0b-137690a08945")
    @TableField(value = "remark")
    private String remark;

    /** 来源业务ID，关联具体业务记录，例如患者ID或门诊就诊ID */
    @AutoGenerated(locked = true, uuid = "649e958a-3878-43a0-aa0f-3ecfd721e4fe")
    @TableField(value = "source_business_id")
    private String sourceBusinessId;

    @AutoGenerated(locked = true, uuid = "6a06ea63-9f08-5a9a-b085-6f4ce4cd1062")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
