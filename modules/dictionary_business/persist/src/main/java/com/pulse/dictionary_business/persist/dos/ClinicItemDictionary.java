package com.pulse.dictionary_business.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.pulse.dictionary_business.common.enums.DefaultPerformDepartmentTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@TableName(value = "clinic_item_dictionary", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "*************-471c-9121-d56eb3a39ac2|ENTITY|DEFINITION")
public class ClinicItemDictionary {
    @AutoGenerated(locked = true, uuid = "1eb4dab3-6fd6-423b-91ef-ed8e930e49f4")
    @TableField(value = "age_max_limit")
    private Long ageMaxLimit;

    @AutoGenerated(locked = true, uuid = "0489704a-8ce1-4645-8e13-63b59f0a74c4")
    @TableField(value = "age_min_limit")
    private Long ageMinLimit;

    @AutoGenerated(locked = true, uuid = "51fc317b-a1bb-4774-a204-0b1d052d3ffe")
    @TableField(value = "alias")
    private String alias;

    @AutoGenerated(locked = true, uuid = "d2f093b1-b193-46ac-9acb-3e9a34f71d1b")
    @TableField(value = "audit_date")
    private Date auditDate;

    @AutoGenerated(locked = true, uuid = "22a123f0-053b-44fa-b342-6a520d91a27d")
    @TableField(value = "audit_flag")
    private Boolean auditFlag;

    @AutoGenerated(locked = true, uuid = "3aa4da6e-79bf-40fa-9117-d550fc37e147")
    @TableField(value = "audit_operator_id")
    private String auditOperatorId;

    @AutoGenerated(locked = true, uuid = "86336fb3-fe25-4ce8-8727-0b64faeadac5")
    @TableField(value = "billing_attribute")
    private String billingAttribute;

    /** 时间间隔分钟 */
    @AutoGenerated(locked = true, uuid = "4b2791f3-deb0-41bc-a51f-6c8635095958")
    @TableField(value = "billing_interval")
    private Long billingInterval;

    @Valid
    @AutoGenerated(locked = true, uuid = "6a5879e2-3dec-470d-aa94-3059f3c51308")
    @TableField(value = "campus_id_list", typeHandler = JacksonTypeHandler.class)
    private List<String> campusIdList;

    /** 字典：卡片打印类型 */
    @Valid
    @AutoGenerated(locked = true, uuid = "34c90508-458e-48be-a003-39747f5b4921")
    @TableField(value = "card_print_type", typeHandler = JacksonTypeHandler.class)
    private List<String> cardPrintTypeList;

    @AutoGenerated(locked = true, uuid = "fe1f1ab9-7161-406c-92dd-7d19598f8b74")
    @TableField(value = "clinic_item_catalog_id")
    private String clinicItemCatalogId;

    @AutoGenerated(locked = true, uuid = "a52c6071-c878-4de5-8e4b-5e32228e51b6")
    @TableId(value = "clinic_item_id")
    private String clinicItemId;

    @AutoGenerated(locked = true, uuid = "aa492d0f-8d78-475b-bee7-3165c9acaddb")
    @TableField(value = "clinic_item_name")
    private String clinicItemName;

    @AutoGenerated(locked = true, uuid = "d73c3367-f97d-5913-b4ec-b6244f925743")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "8277eabd-9d1a-4c4a-84e8-d0b6b667e24d")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "c1f07e8a-2f49-4bec-80cd-c9ec21457caf")
    @TableField(value = "delay_days")
    private Long delayDays;

    @AutoGenerated(locked = true, uuid = "1bf1d051-ac7b-409e-b26f-f49ac02608f4")
    @TableField(value = "description")
    private String description;

    @AutoGenerated(locked = true, uuid = "6e86b4fd-5d55-43d5-80a1-6fc0b8e1d8bf")
    @TableField(value = "disabled_reason")
    private String disabledReason;

    @AutoGenerated(locked = true, uuid = "c56d2320-4ff2-41c4-a8a8-9479e58877cb")
    @TableField(value = "double_signature_flag")
    private Boolean doubleSignatureFlag;

    @AutoGenerated(locked = true, uuid = "9863b171-f6a2-4396-b1ec-9b41cbbc1205")
    @TableField(value = "enable_flag")
    private Boolean enableFlag;

    @Valid
    @AutoGenerated(locked = true, uuid = "399be94a-d957-418f-ab6b-d3dce8e731f5")
    @TableField(value = "exclusion_time", typeHandler = JacksonTypeHandler.class)
    private TimeEo exclusionTime;

    @AutoGenerated(locked = true, uuid = "174d3244-5586-4a8f-b6b0-1499205b7964")
    @TableField(value = "exclusion_type")
    private String exclusionType;

    @AutoGenerated(locked = true, uuid = "aa6ea7dd-d5a5-421d-bcc2-a034dc3b6d1d")
    @TableField(value = "frequency")
    private String frequency;

    @AutoGenerated(locked = true, uuid = "51680446-3c97-40ce-a280-72e0076495ca")
    @TableField(value = "frequency_not_allowed_modify_flag")
    private Boolean frequencyNotAllowedModifyFlag;

    @AutoGenerated(locked = true, uuid = "3c63891b-6ba3-4e33-96b9-903860e09451")
    @TableField(value = "include_current_department_flag")
    private Boolean includeCurrentDepartmentFlag;

    @AutoGenerated(locked = true, uuid = "b5978d7f-d94a-4b4d-859b-badd5194e53d")
    @TableField(value = "inp_default_perform_department_type")
    private DefaultPerformDepartmentTypeEnum inpDefaultPerformDepartmentType;

    @Valid
    @AutoGenerated(locked = true, uuid = "85402b7d-3955-467d-819b-8a50286a4e48")
    @TableField(value = "input_code", typeHandler = JacksonTypeHandler.class)
    private InputCodeEo inputCode;

    @AutoGenerated(locked = true, uuid = "02e87bcd-2105-405f-8dc0-cdc8e6828d0a")
    @TableField(value = "institution_id")
    private String institutionId;

    @AutoGenerated(locked = true, uuid = "f1e4f421-44dc-4548-8286-b947fe9e3c00")
    @TableField(value = "item_specification")
    private String itemSpecification;

    @AutoGenerated(locked = true, uuid = "1ae02ec9-06f1-41e9-aa23-b3a18aafca7f")
    @TableField(value = "item_type")
    private String itemType;

    @AutoGenerated(locked = true, uuid = "5a1b6fa0-0b48-4e55-af02-8a767abf3f5c")
    @TableField(value = "limit_gender")
    private String limitGender;

    @Valid
    @AutoGenerated(locked = true, uuid = "720c33d7-ba5f-404e-9e77-45fc4dda3868")
    @TableField(value = "limit_ward_id_list", typeHandler = JacksonTypeHandler.class)
    private List<String> limitWardIdList;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "31359a17-864b-4691-b225-1065da36b7f5")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "8b98df81-0dd8-4630-8505-6d7d3a88971b")
    @TableField(value = "only_select_setting_department_flag")
    private Boolean onlySelectSettingDepartmentFlag;

    @AutoGenerated(locked = true, uuid = "50122c06-7226-4f7d-9dbb-833db997a4a4")
    @TableField(value = "operation_code")
    private String operationCode;

    @AutoGenerated(locked = true, uuid = "8cffaa43-c1d6-4e6a-96fd-94b11163c573")
    @TableField(value = "order_frequency_billing_type")
    private String orderFrequencyBillingType;

    @AutoGenerated(locked = true, uuid = "569a0bef-986f-41b4-bbc2-38bbd41061da")
    @TableField(value = "outp_default_perform_department_id")
    private String outpDefaultPerformDepartmentId;

    @AutoGenerated(locked = true, uuid = "976eba22-1f53-4a82-a2a7-8d3eb0b280ba")
    @TableField(value = "pda_perform_flag")
    private Boolean pdaPerformFlag;

    @AutoGenerated(locked = true, uuid = "930eefeb-0cbf-4e4c-a73e-928d787121b8")
    @TableField(value = "print_flag")
    private Boolean printFlag;

    @AutoGenerated(locked = true, uuid = "0472b831-e6ca-4f30-9d30-fd5765d627c9")
    @TableField(value = "remark")
    private String remark;

    @AutoGenerated(locked = true, uuid = "7a67faa6-532b-4908-a837-b97a1c599bbd")
    @TableField(value = "rescue_flag")
    private Boolean rescueFlag;

    @AutoGenerated(locked = true, uuid = "fc33240e-eb60-4341-815a-e2f49614e214")
    @TableField(value = "sort_number")
    private Long sortNumber;

    @AutoGenerated(locked = true, uuid = "6aaed298-373d-497d-b520-db8b614fb59c")
    @TableField(value = "special_need_flag")
    private Boolean specialNeedFlag;

    @AutoGenerated(locked = true, uuid = "32945c3c-93f0-40b3-a151-ec1ecb495f69")
    @TableField(value = "standard_code")
    private String standardCode;

    @AutoGenerated(locked = true, uuid = "65c079fb-ad67-4f5e-82cc-b2d13be54cad")
    @TableField(value = "unit")
    private String unit;

    @AutoGenerated(locked = true, uuid = "7bbd6e8d-77af-5363-98db-ef4de5492bdf")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "6208bad7-1d80-49bb-90dc-5fd01f30bd5e")
    @TableField(value = "updated_by")
    private String updatedBy;

    @Valid
    @AutoGenerated(locked = true, uuid = "d42b0606-af11-4371-975c-ef895da73001")
    @TableField(value = "use_scope_list", typeHandler = JacksonTypeHandler.class)
    private List<String> useScopeList;
}
