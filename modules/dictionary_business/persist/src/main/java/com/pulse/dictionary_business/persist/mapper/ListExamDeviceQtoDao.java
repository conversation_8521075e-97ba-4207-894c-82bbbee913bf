package com.pulse.dictionary_business.persist.mapper;

import com.pulse.dictionary_business.persist.qto.ListExamDeviceQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "6e933bd9-586a-48c8-86d4-0ac90638781b|QTO|DAO")
public class ListExamDeviceQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询检查设备列表数据 */
    @AutoGenerated(locked = false, uuid = "6e933bd9-586a-48c8-86d4-0ac90638781b-count")
    public Integer count(ListExamDeviceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(exam_device.id) FROM exam_device LEFT JOIN exam_type_dictionary"
                    + " \"examType\" on exam_device.exam_type_id = \"examType\".id WHERE"
                    + " \"examType\".id = #examTypeIdIs AND exam_device.enable_flag = #enableFlagIs"
                    + " AND ( exam_device.device_name like #keyword OR exam_device.device_id like"
                    + " #keyword OR JSON_VALUE(exam_device.input_code, '$.pinyin') like"
                    + " #inputCodePinyinLike OR JSON_VALUE(exam_device.input_code, '$.wubi') like"
                    + " #inputCodeWubiLike OR JSON_VALUE(exam_device.input_code, '$.custom') like"
                    + " #inputCodeCustomLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (qto.getExamTypeIdIs() == null) {
            conditionToRemove.add("#examTypeIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getKeyword() == null) {
            conditionToRemove.add("#keyword");
        }
        if (qto.getKeyword() == null) {
            conditionToRemove.add("#keyword");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace("#examTypeIdIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#keyword", "?")
                        .replace("#keyword", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#examTypeIdIs")) {
                sqlParams.add(qto.getExamTypeIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#keyword")) {
                sqlParams.add("%" + qto.getKeyword() + "%");
            } else if (paramName.equalsIgnoreCase("#keyword")) {
                sqlParams.add("%" + qto.getKeyword() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询检查设备列表数据 */
    @AutoGenerated(locked = false, uuid = "6e933bd9-586a-48c8-86d4-0ac90638781b-query-all")
    public List<String> query(ListExamDeviceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT exam_device.id FROM exam_device LEFT JOIN exam_type_dictionary \"examType\""
                    + " on exam_device.exam_type_id = \"examType\".id WHERE \"examType\".id ="
                    + " #examTypeIdIs AND exam_device.enable_flag = #enableFlagIs AND ("
                    + " exam_device.device_name like #keyword OR exam_device.device_id like"
                    + " #keyword OR JSON_VALUE(exam_device.input_code, '$.pinyin') like"
                    + " #inputCodePinyinLike OR JSON_VALUE(exam_device.input_code, '$.wubi') like"
                    + " #inputCodeWubiLike OR JSON_VALUE(exam_device.input_code, '$.custom') like"
                    + " #inputCodeCustomLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (qto.getExamTypeIdIs() == null) {
            conditionToRemove.add("#examTypeIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getKeyword() == null) {
            conditionToRemove.add("#keyword");
        }
        if (qto.getKeyword() == null) {
            conditionToRemove.add("#keyword");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace("#examTypeIdIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#keyword", "?")
                        .replace("#keyword", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#examTypeIdIs")) {
                sqlParams.add(qto.getExamTypeIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#keyword")) {
                sqlParams.add("%" + qto.getKeyword() + "%");
            } else if (paramName.equalsIgnoreCase("#keyword")) {
                sqlParams.add("%" + qto.getKeyword() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  exam_device.sort_number asc , exam_device.device_name asc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询检查设备列表数据 */
    @AutoGenerated(locked = false, uuid = "6e933bd9-586a-48c8-86d4-0ac90638781b-query-paginate")
    public List<String> queryPaged(ListExamDeviceQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT exam_device.id FROM exam_device LEFT JOIN exam_type_dictionary \"examType\""
                    + " on exam_device.exam_type_id = \"examType\".id WHERE \"examType\".id ="
                    + " #examTypeIdIs AND exam_device.enable_flag = #enableFlagIs AND ("
                    + " exam_device.device_name like #keyword OR exam_device.device_id like"
                    + " #keyword OR JSON_VALUE(exam_device.input_code, '$.pinyin') like"
                    + " #inputCodePinyinLike OR JSON_VALUE(exam_device.input_code, '$.wubi') like"
                    + " #inputCodeWubiLike OR JSON_VALUE(exam_device.input_code, '$.custom') like"
                    + " #inputCodeCustomLike ) ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getInputCodePinyinLike() == null) {
            conditionToRemove.add("#inputCodePinyinLike");
        }
        if (qto.getInputCodeCustomLike() == null) {
            conditionToRemove.add("#inputCodeCustomLike");
        }
        if (qto.getInputCodeWubiLike() == null) {
            conditionToRemove.add("#inputCodeWubiLike");
        }
        if (qto.getExamTypeIdIs() == null) {
            conditionToRemove.add("#examTypeIdIs");
        }
        if (qto.getEnableFlagIs() == null) {
            conditionToRemove.add("#enableFlagIs");
        }
        if (qto.getKeyword() == null) {
            conditionToRemove.add("#keyword");
        }
        if (qto.getKeyword() == null) {
            conditionToRemove.add("#keyword");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        String parsedSql =
                sql.replace("#inputCodePinyinLike", "?")
                        .replace("#inputCodeCustomLike", "?")
                        .replace("#inputCodeWubiLike", "?")
                        .replace("#examTypeIdIs", "?")
                        .replace("#enableFlagIs", "?")
                        .replace("#keyword", "?")
                        .replace("#keyword", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#inputCodePinyinLike")) {
                sqlParams.add("%" + qto.getInputCodePinyinLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeCustomLike")) {
                sqlParams.add("%" + qto.getInputCodeCustomLike() + "%");
            } else if (paramName.equalsIgnoreCase("#inputCodeWubiLike")) {
                sqlParams.add("%" + qto.getInputCodeWubiLike() + "%");
            } else if (paramName.equalsIgnoreCase("#examTypeIdIs")) {
                sqlParams.add(qto.getExamTypeIdIs());
            } else if (paramName.equalsIgnoreCase("#enableFlagIs")) {
                sqlParams.add(qto.getEnableFlagIs());
            } else if (paramName.equalsIgnoreCase("#keyword")) {
                sqlParams.add("%" + qto.getKeyword() + "%");
            } else if (paramName.equalsIgnoreCase("#keyword")) {
                sqlParams.add("%" + qto.getKeyword() + "%");
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  exam_device.sort_number asc , exam_device.device_name asc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
