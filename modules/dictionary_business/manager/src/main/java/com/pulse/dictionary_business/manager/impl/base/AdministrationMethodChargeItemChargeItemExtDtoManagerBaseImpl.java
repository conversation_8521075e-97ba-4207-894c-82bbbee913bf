package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.billing_public_config.manager.dto.ChargeItemClinicItemDto;
import com.pulse.dictionary_business.manager.AdministrationMethodChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.AdministrationMethodChargeItemChargeItemExtDtoManager;
import com.pulse.dictionary_business.manager.converter.AdministrationMethodChargeItemBaseDtoConverter;
import com.pulse.dictionary_business.manager.converter.AdministrationMethodChargeItemChargeItemExtDtoConverter;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodChargeItemBaseDto;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodChargeItemChargeItemExtDto;
import com.pulse.dictionary_business.manager.facade.billing_public_config.ChargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter;
import com.pulse.dictionary_business.persist.dos.AdministrationMethodChargeItem;
import com.pulse.dictionary_business.persist.mapper.AdministrationMethodChargeItemDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "72b9ea27-5be5-4deb-8157-45aba49319dd|DTO|BASE_MANAGER_IMPL")
public abstract class AdministrationMethodChargeItemChargeItemExtDtoManagerBaseImpl
        implements AdministrationMethodChargeItemChargeItemExtDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodChargeItemBaseDtoConverter
            administrationMethodChargeItemBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodChargeItemBaseDtoManager
            administrationMethodChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodChargeItemChargeItemExtDtoConverter
            administrationMethodChargeItemChargeItemExtDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodChargeItemDao administrationMethodChargeItemDao;

    @AutoGenerated(locked = true)
    @Autowired
    private ChargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter
            chargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter;

    @AutoGenerated(locked = true, uuid = "03b87a94-9008-3160-94ca-e85710576ce0")
    @Override
    public AdministrationMethodChargeItemChargeItemExtDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AdministrationMethodChargeItemChargeItemExtDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        AdministrationMethodChargeItemChargeItemExtDto
                administrationMethodChargeItemChargeItemExtDto =
                        CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return administrationMethodChargeItemChargeItemExtDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "10eef539-5a40-3586-acf8-776cf117af1e")
    @Override
    public List<AdministrationMethodChargeItemChargeItemExtDto> getByChargeItemIds(
            List<String> chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(chargeItemId)) {
            return Collections.emptyList();
        }

        List<AdministrationMethodChargeItem> administrationMethodChargeItemList =
                administrationMethodChargeItemDao.getByChargeItemIds(chargeItemId);
        if (CollectionUtil.isEmpty(administrationMethodChargeItemList)) {
            return Collections.emptyList();
        }

        return doConvertFromAdministrationMethodChargeItemToAdministrationMethodChargeItemChargeItemExtDto(
                administrationMethodChargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "23e14b10-79d8-34f0-a57e-8c26bf25cc99")
    @Override
    public List<AdministrationMethodChargeItemChargeItemExtDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<AdministrationMethodChargeItem> administrationMethodChargeItemList =
                administrationMethodChargeItemDao.getByIds(id);
        if (CollectionUtil.isEmpty(administrationMethodChargeItemList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, AdministrationMethodChargeItem> administrationMethodChargeItemMap =
                administrationMethodChargeItemList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        administrationMethodChargeItemList =
                id.stream()
                        .map(i -> administrationMethodChargeItemMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromAdministrationMethodChargeItemToAdministrationMethodChargeItemChargeItemExtDto(
                administrationMethodChargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "56034e5e-ee52-342c-afa8-48f997b196bd")
    @Override
    public List<AdministrationMethodChargeItemChargeItemExtDto> getByAdministrationMethodIds(
            List<String> administrationMethodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(administrationMethodId)) {
            return Collections.emptyList();
        }

        List<AdministrationMethodChargeItem> administrationMethodChargeItemList =
                administrationMethodChargeItemDao.getByAdministrationMethodIds(
                        administrationMethodId);
        if (CollectionUtil.isEmpty(administrationMethodChargeItemList)) {
            return Collections.emptyList();
        }

        return doConvertFromAdministrationMethodChargeItemToAdministrationMethodChargeItemChargeItemExtDto(
                administrationMethodChargeItemList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "617a2fd8-1166-324d-a93c-55b668ab2371")
    @Override
    public List<AdministrationMethodChargeItemChargeItemExtDto> getByAdministrationMethodId(
            String administrationMethodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AdministrationMethodChargeItemChargeItemExtDto>
                administrationMethodChargeItemChargeItemExtDtoList =
                        getByAdministrationMethodIds(Arrays.asList(administrationMethodId));
        return administrationMethodChargeItemChargeItemExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9381d5a9-f9ab-3eb6-82d8-43f045f79c90")
    @Override
    public List<AdministrationMethodChargeItemChargeItemExtDto> getByChargeItemId(
            String chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AdministrationMethodChargeItemChargeItemExtDto>
                administrationMethodChargeItemChargeItemExtDtoList =
                        getByChargeItemIds(Arrays.asList(chargeItemId));
        return administrationMethodChargeItemChargeItemExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f421575c-9d5a-3363-ace4-57b15fd14af7")
    public List<AdministrationMethodChargeItemChargeItemExtDto>
            doConvertFromAdministrationMethodChargeItemToAdministrationMethodChargeItemChargeItemExtDto(
                    List<AdministrationMethodChargeItem> administrationMethodChargeItemList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(administrationMethodChargeItemList)) {
            return Collections.emptyList();
        }

        Map<String, String> chargeItemIdMap =
                administrationMethodChargeItemList.stream()
                        .filter(i -> i.getChargeItemId() != null)
                        .collect(
                                Collectors.toMap(
                                        AdministrationMethodChargeItem::getId,
                                        AdministrationMethodChargeItem::getChargeItemId));
        List<ChargeItemClinicItemDto> chargeItemIdChargeItemClinicItemDtoList =
                chargeItemClinicItemDtoServiceInDictionaryBusinessRpcAdapter.getByItemCodes(
                        new ArrayList<>(new HashSet<>(chargeItemIdMap.values())));
        Map<String, ChargeItemClinicItemDto> chargeItemIdChargeItemClinicItemDtoMapRaw =
                chargeItemIdChargeItemClinicItemDtoList.stream()
                        .collect(Collectors.toMap(ChargeItemClinicItemDto::getItemCode, i -> i));
        Map<String, ChargeItemClinicItemDto> chargeItemIdChargeItemClinicItemDtoMap =
                chargeItemIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        chargeItemIdChargeItemClinicItemDtoMapRaw.get(i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                chargeItemIdChargeItemClinicItemDtoMapRaw.get(
                                                        i.getValue())));

        List<AdministrationMethodChargeItemBaseDto> baseDtoList =
                administrationMethodChargeItemBaseDtoConverter
                        .convertFromAdministrationMethodChargeItemToAdministrationMethodChargeItemBaseDto(
                                administrationMethodChargeItemList);
        Map<String, AdministrationMethodChargeItemChargeItemExtDto> dtoMap =
                administrationMethodChargeItemChargeItemExtDtoConverter
                        .convertFromAdministrationMethodChargeItemBaseDtoToAdministrationMethodChargeItemChargeItemExtDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        AdministrationMethodChargeItemChargeItemExtDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<AdministrationMethodChargeItemChargeItemExtDto>
                administrationMethodChargeItemChargeItemExtDtoList = new ArrayList<>();
        for (AdministrationMethodChargeItem i : administrationMethodChargeItemList) {
            AdministrationMethodChargeItemChargeItemExtDto
                    administrationMethodChargeItemChargeItemExtDto = dtoMap.get(i.getId());
            if (administrationMethodChargeItemChargeItemExtDto == null) {
                continue;
            }

            if (null != i.getChargeItemId()) {
                administrationMethodChargeItemChargeItemExtDto.setChargeItem(
                        chargeItemIdChargeItemClinicItemDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            administrationMethodChargeItemChargeItemExtDtoList.add(
                    administrationMethodChargeItemChargeItemExtDto);
        }
        return administrationMethodChargeItemChargeItemExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
