package com.pulse.dictionary_business.manager.dto;

import com.pulse.dictionary_business.common.enums.AgeUnitEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "311afb23-d674-4989-abfb-fe9a9a598fef|DTO|DEFINITION")
public class LabItemRuleBaseDto {
    /** 适用开单科室列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "7a4c3223-4180-4c47-a9b2-5229ac2e96a1")
    private List<String> applicableOrderDepartmentIdList;

    /** 适用开单医生列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "d9a1d467-0880-4599-8c1b-9476f2f65efe")
    private List<String> applicableOrderDoctorIdList;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "8c09c096-eaac-48cf-900a-3d41d481ea84")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "b7c1c6cc-bb47-4f58-8656-e78e0fa1acc0")
    private String id;

    /** 检验项目ID */
    @AutoGenerated(locked = true, uuid = "62cbbf25-4e50-492c-87c3-be62b28d8d49")
    private String labItemId;

    /** 检验项目说明 */
    @AutoGenerated(locked = true, uuid = "139f877e-0789-406a-a8d3-e91561d78cb2")
    private String labItemInstruction;

    /** 限制年龄上限 */
    @AutoGenerated(locked = true, uuid = "97190eb9-2520-43e9-9902-8e312c88ee9b")
    private Long limitAgeMax;

    /** 限制年龄下限 */
    @AutoGenerated(locked = true, uuid = "0e225b79-e7e9-4933-bd3f-50e7fbe1444e")
    private Long limitAgeMin;

    /** 年龄限制单位 */
    @AutoGenerated(locked = true, uuid = "342b1798-7755-4f9b-a099-5edeb63adc06")
    private AgeUnitEnum limitAgeUnit;

    /** 性别限制 */
    @AutoGenerated(locked = true, uuid = "c845c51e-052d-4bc0-989a-f2ff5664921c")
    private String limitGender;

    /** 不允许的开单部门ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c75177aa-0e33-43ce-96d4-d254645d1fb6")
    private List<String> notAllowedOrderDepartmentIdList;

    /** 不允许的开单医生ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "18e314fa-ed44-4027-a93e-e60783a8c2dd")
    private List<String> notAllowedOrderDoctorIdList;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "54b88793-25b2-4ad5-b1f5-6e1ac774e5aa")
    private Date updatedAt;

    /** 加急类型 */
    @AutoGenerated(locked = true, uuid = "c2c55195-2b6f-4354-ad69-4cc8c9084dd8")
    private String urgentType;
}
