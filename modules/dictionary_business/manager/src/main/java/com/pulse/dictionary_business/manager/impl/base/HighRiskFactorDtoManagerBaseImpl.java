package com.pulse.dictionary_business.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.dictionary_business.manager.HighRiskDiagnosisBaseDtoManager;
import com.pulse.dictionary_business.manager.HighRiskDrugBaseDtoManager;
import com.pulse.dictionary_business.manager.HighRiskFactorDtoManager;
import com.pulse.dictionary_business.manager.HighRiskFactorRuleBaseDtoManager;
import com.pulse.dictionary_business.manager.HighRiskItemBaseDtoManager;
import com.pulse.dictionary_business.manager.converter.HighRiskFactorDtoConverter;
import com.pulse.dictionary_business.manager.converter.HighRiskFactorRuleBaseDtoConverter;
import com.pulse.dictionary_business.manager.dto.HighRiskDiagnosisBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskDrugBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskFactorDto;
import com.pulse.dictionary_business.manager.dto.HighRiskFactorRuleBaseDto;
import com.pulse.dictionary_business.manager.dto.HighRiskItemBaseDto;
import com.pulse.dictionary_business.persist.dos.HighRiskFactor;
import com.pulse.dictionary_business.persist.mapper.HighRiskFactorDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "4ab0afda-a819-4f6b-844e-74d56a46776e|DTO|BASE_MANAGER_IMPL")
public abstract class HighRiskFactorDtoManagerBaseImpl implements HighRiskFactorDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private HighRiskDiagnosisBaseDtoManager highRiskDiagnosisBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private HighRiskDrugBaseDtoManager highRiskDrugBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private HighRiskFactorDao highRiskFactorDao;

    @AutoGenerated(locked = true)
    @Autowired
    private HighRiskFactorDtoConverter highRiskFactorDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private HighRiskFactorRuleBaseDtoConverter highRiskFactorRuleBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private HighRiskFactorRuleBaseDtoManager highRiskFactorRuleBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private HighRiskItemBaseDtoManager highRiskItemBaseDtoManager;

    @AutoGenerated(locked = true, uuid = "8a86925f-5303-3269-8fd7-f25e6a0eab45")
    public List<HighRiskFactorDto> doConvertFromHighRiskFactorToHighRiskFactorDto(
            List<HighRiskFactor> highRiskFactorList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(highRiskFactorList)) {
            return Collections.emptyList();
        }

        List<HighRiskDiagnosisBaseDto> highRiskDiagnosisBaseDtoList =
                highRiskDiagnosisBaseDtoManager.getByHighRiskFactorIds(
                        highRiskFactorList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<HighRiskDiagnosisBaseDto>> idHighRiskDiagnosisBaseDtoListMap =
                highRiskDiagnosisBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getHighRiskFactorId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<HighRiskItemBaseDto> highRiskItemBaseDtoList =
                highRiskItemBaseDtoManager.getByHighRiskFactorIds(
                        highRiskFactorList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<HighRiskItemBaseDto>> idHighRiskItemBaseDtoListMap =
                highRiskItemBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getHighRiskFactorId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<HighRiskDrugBaseDto> highRiskDrugBaseDtoList =
                highRiskDrugBaseDtoManager.getByHighRiskFactorIds(
                        highRiskFactorList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<HighRiskDrugBaseDto>> idHighRiskDrugBaseDtoListMap =
                highRiskDrugBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getHighRiskFactorId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<HighRiskFactorRuleBaseDto> baseDtoList =
                highRiskFactorRuleBaseDtoConverter
                        .convertFromHighRiskFactorToHighRiskFactorRuleBaseDto(highRiskFactorList);
        Map<String, HighRiskFactorDto> dtoMap =
                highRiskFactorDtoConverter
                        .convertFromHighRiskFactorRuleBaseDtoToHighRiskFactorDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        HighRiskFactorDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<HighRiskFactorDto> highRiskFactorDtoList = new ArrayList<>();
        for (HighRiskFactor i : highRiskFactorList) {
            HighRiskFactorDto highRiskFactorDto = dtoMap.get(i.getId());
            if (highRiskFactorDto == null) {
                continue;
            }

            if (null != i.getId()) {
                highRiskFactorDto.setHighRiskDiagnosisList(
                        idHighRiskDiagnosisBaseDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            if (null != i.getId()) {
                highRiskFactorDto.setHighRiskItemList(
                        idHighRiskItemBaseDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            if (null != i.getId()) {
                highRiskFactorDto.setHighRiskDrugList(
                        idHighRiskDrugBaseDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            highRiskFactorDtoList.add(highRiskFactorDto);
        }
        return highRiskFactorDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "8dab0002-a6c4-3ee1-aad8-6bb41421c16a")
    @Override
    public List<HighRiskFactorDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<HighRiskFactor> highRiskFactorList = highRiskFactorDao.getByIds(id);
        if (CollectionUtil.isEmpty(highRiskFactorList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, HighRiskFactor> highRiskFactorMap =
                highRiskFactorList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        highRiskFactorList =
                id.stream()
                        .map(i -> highRiskFactorMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromHighRiskFactorToHighRiskFactorDto(highRiskFactorList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ceba602b-b740-329c-b1c9-dad2e2348234")
    @Override
    public HighRiskFactorDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<HighRiskFactorDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        HighRiskFactorDto highRiskFactorDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return highRiskFactorDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
