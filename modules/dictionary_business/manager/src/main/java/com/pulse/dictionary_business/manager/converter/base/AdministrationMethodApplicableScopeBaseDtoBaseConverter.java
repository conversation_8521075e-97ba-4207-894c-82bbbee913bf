package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.AdministrationMethodApplicableScopeBaseDto;
import com.pulse.dictionary_business.persist.dos.AdministrationMethodApplicableScope;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "6a26edf4-a48a-4f7e-86bb-59d3e5dc3f80|DTO|BASE_CONVERTER")
public class AdministrationMethodApplicableScopeBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public AdministrationMethodApplicableScopeBaseDto
            convertFromAdministrationMethodApplicableScopeToAdministrationMethodApplicableScopeBaseDto(
                    AdministrationMethodApplicableScope administrationMethodApplicableScope) {
        return convertFromAdministrationMethodApplicableScopeToAdministrationMethodApplicableScopeBaseDto(
                        List.of(administrationMethodApplicableScope))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<AdministrationMethodApplicableScopeBaseDto>
            convertFromAdministrationMethodApplicableScopeToAdministrationMethodApplicableScopeBaseDto(
                    List<AdministrationMethodApplicableScope>
                            administrationMethodApplicableScopeList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(administrationMethodApplicableScopeList)) {
            return new ArrayList<>();
        }
        List<AdministrationMethodApplicableScopeBaseDto>
                administrationMethodApplicableScopeBaseDtoList = new ArrayList<>();
        for (AdministrationMethodApplicableScope administrationMethodApplicableScope :
                administrationMethodApplicableScopeList) {
            if (administrationMethodApplicableScope == null) {
                continue;
            }
            AdministrationMethodApplicableScopeBaseDto administrationMethodApplicableScopeBaseDto =
                    new AdministrationMethodApplicableScopeBaseDto();
            administrationMethodApplicableScopeBaseDto.setId(
                    administrationMethodApplicableScope.getId());
            administrationMethodApplicableScopeBaseDto.setAdministrationMethodId(
                    administrationMethodApplicableScope.getAdministrationMethodId());
            administrationMethodApplicableScopeBaseDto.setApplicableObjectType(
                    administrationMethodApplicableScope.getApplicableObjectType());
            administrationMethodApplicableScopeBaseDto.setObjectName(
                    administrationMethodApplicableScope.getObjectName());
            administrationMethodApplicableScopeBaseDto.setObjectId(
                    administrationMethodApplicableScope.getObjectId());
            administrationMethodApplicableScopeBaseDto.setUpdatedBy(
                    administrationMethodApplicableScope.getUpdatedBy());
            administrationMethodApplicableScopeBaseDto.setCreatedBy(
                    administrationMethodApplicableScope.getCreatedBy());
            administrationMethodApplicableScopeBaseDto.setCreatedAt(
                    administrationMethodApplicableScope.getCreatedAt());
            administrationMethodApplicableScopeBaseDto.setUpdatedAt(
                    administrationMethodApplicableScope.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            administrationMethodApplicableScopeBaseDtoList.add(
                    administrationMethodApplicableScopeBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return administrationMethodApplicableScopeBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
