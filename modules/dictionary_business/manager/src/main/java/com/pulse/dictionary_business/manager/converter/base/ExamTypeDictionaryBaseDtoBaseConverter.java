package com.pulse.dictionary_business.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.dto.ExamTypeDictionaryBaseDto;
import com.pulse.dictionary_business.persist.dos.ExamTypeDictionary;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "6d7ad1c2-fcb5-46ae-bcc6-524c71517863|DTO|BASE_CONVERTER")
public class ExamTypeDictionaryBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public ExamTypeDictionaryBaseDto convertFromExamTypeDictionaryToExamTypeDictionaryBaseDto(
            ExamTypeDictionary examTypeDictionary) {
        return convertFromExamTypeDictionaryToExamTypeDictionaryBaseDto(List.of(examTypeDictionary))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<ExamTypeDictionaryBaseDto> convertFromExamTypeDictionaryToExamTypeDictionaryBaseDto(
            List<ExamTypeDictionary> examTypeDictionaryList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(examTypeDictionaryList)) {
            return new ArrayList<>();
        }
        List<ExamTypeDictionaryBaseDto> examTypeDictionaryBaseDtoList = new ArrayList<>();
        for (ExamTypeDictionary examTypeDictionary : examTypeDictionaryList) {
            if (examTypeDictionary == null) {
                continue;
            }
            ExamTypeDictionaryBaseDto examTypeDictionaryBaseDto = new ExamTypeDictionaryBaseDto();
            examTypeDictionaryBaseDto.setId(examTypeDictionary.getId());
            examTypeDictionaryBaseDto.setSortNumber(examTypeDictionary.getSortNumber());
            examTypeDictionaryBaseDto.setExamTypeCode(examTypeDictionary.getExamTypeCode());
            examTypeDictionaryBaseDto.setExamTypeName(examTypeDictionary.getExamTypeName());
            examTypeDictionaryBaseDto.setInputCode(examTypeDictionary.getInputCode());
            examTypeDictionaryBaseDto.setStandardExamTypeCode(
                    examTypeDictionary.getStandardExamTypeCode());
            examTypeDictionaryBaseDto.setStandardExamTypeName(
                    examTypeDictionary.getStandardExamTypeName());
            examTypeDictionaryBaseDto.setEnableFlag(examTypeDictionary.getEnableFlag());
            examTypeDictionaryBaseDto.setCampusIdList(examTypeDictionary.getCampusIdList());
            examTypeDictionaryBaseDto.setUseScopeList(examTypeDictionary.getUseScopeList());
            examTypeDictionaryBaseDto.setBillingMode(examTypeDictionary.getBillingMode());
            examTypeDictionaryBaseDto.setEveryDayMaxPartCount(
                    examTypeDictionary.getEveryDayMaxPartCount());
            examTypeDictionaryBaseDto.setEveryVisitMaxPartCount(
                    examTypeDictionary.getEveryVisitMaxPartCount());
            examTypeDictionaryBaseDto.setCreatedBy(examTypeDictionary.getCreatedBy());
            examTypeDictionaryBaseDto.setUpdatedBy(examTypeDictionary.getUpdatedBy());
            examTypeDictionaryBaseDto.setPartSplitFlag(examTypeDictionary.getPartSplitFlag());
            examTypeDictionaryBaseDto.setParentId(examTypeDictionary.getParentId());
            examTypeDictionaryBaseDto.setLockVersion(examTypeDictionary.getLockVersion());
            examTypeDictionaryBaseDto.setCreatedAt(examTypeDictionary.getCreatedAt());
            examTypeDictionaryBaseDto.setUpdatedAt(examTypeDictionary.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            examTypeDictionaryBaseDtoList.add(examTypeDictionaryBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return examTypeDictionaryBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
