package com.pulse.dictionary_business.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.bo.ExamItemBO;
import com.pulse.dictionary_business.persist.dos.ExamItem;
import com.pulse.dictionary_business.persist.dos.ExamItemBody;
import com.pulse.dictionary_business.persist.dos.ExamItemDevice;
import com.pulse.dictionary_business.persist.dos.ExamItemDocumentTemplate;
import com.pulse.dictionary_business.persist.dos.ExamItemDrug;
import com.pulse.dictionary_business.persist.dos.ExamItemExtension;
import com.pulse.dictionary_business.persist.dos.ExamItemMethod;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.CreateExamItemBoResult;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.SaveExamItemBodyListBoResult;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.SaveExamItemDeviceListBoResult;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.SaveExamItemDrugListBoResult;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.SaveExamItemExtensionBoResult;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.SaveExamItemMethodBoResult;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.SaveExamItemRuleBoResult;
import com.pulse.dictionary_business.service.base.BaseExamItemBOService.UpdateExamItemBoResult;
import com.pulse.dictionary_business.service.bto.CreateExamItemBto;
import com.pulse.dictionary_business.service.bto.SaveExamItemBodyListBto;
import com.pulse.dictionary_business.service.bto.SaveExamItemDeviceListBto;
import com.pulse.dictionary_business.service.bto.SaveExamItemDrugListBto;
import com.pulse.dictionary_business.service.bto.SaveExamItemExtensionBto;
import com.pulse.dictionary_business.service.bto.SaveExamItemMethodBto;
import com.pulse.dictionary_business.service.bto.SaveExamItemRuleBto;
import com.pulse.dictionary_business.service.bto.UpdateExamItemBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "9f6ba27f-fd40-32c3-a42f-1b6bce34cdc2")
public class BaseExamItemBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private ExamItemBO createCreateExamItemOnDuplicateThrowEx(
            CreateExamItemBoResult boResult, CreateExamItemBto createExamItemBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createExamItemBto.getClinicItemId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getByClinicItemId(createExamItemBto.getClinicItemId());
            if (examItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'clinic_item_id'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (examItemBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", examItemBO.getId(), "exam_item");
                throw new IgnoredException(400, "检查项目已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "exam_item",
                        examItemBO.getId(),
                        "exam_item");
                throw new IgnoredException(400, "检查项目已存在");
            }
        } else {
            examItemBO = new ExamItemBO();
            if (pkExist) {
                examItemBO.setId(String.valueOf(this.idGenerator.allocateId("exam_item")));
            } else {
                examItemBO.setId(String.valueOf(this.idGenerator.allocateId("exam_item")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examCatalogId")) {
                examItemBO.setExamCatalogId(createExamItemBto.getExamCatalogId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examTypeId")) {
                examItemBO.setExamTypeId(createExamItemBto.getExamTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "clinicItemId")) {
                examItemBO.setClinicItemId(createExamItemBto.getClinicItemId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "emptyStomachFlag")) {
                examItemBO.setEmptyStomachFlag(createExamItemBto.getEmptyStomachFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "bedsideFlag")) {
                examItemBO.setBedsideFlag(createExamItemBto.getBedsideFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "sortNumber")) {
                examItemBO.setSortNumber(createExamItemBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examDirectionList")) {
                examItemBO.setExamDirectionList(createExamItemBto.getExamDirectionList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "enhancedScanningFlag")) {
                examItemBO.setEnhancedScanningFlag(createExamItemBto.getEnhancedScanningFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "partNumber")) {
                examItemBO.setPartNumber(createExamItemBto.getPartNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "uclaFlag")) {
                examItemBO.setUclaFlag(createExamItemBto.getUclaFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "urgentType")) {
                examItemBO.setUrgentType(createExamItemBto.getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "unitWeight")) {
                examItemBO.setUnitWeight(createExamItemBto.getUnitWeight());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "limbPositionCodeList")) {
                examItemBO.setLimbPositionCodeList(createExamItemBto.getLimbPositionCodeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "appointType")) {
                examItemBO.setAppointType(createExamItemBto.getAppointType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "promptContent")) {
                examItemBO.setPromptContent(createExamItemBto.getPromptContent());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "pathologyType")) {
                examItemBO.setPathologyType(createExamItemBto.getPathologyType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examItemBO.setIodineContrastFlag(createExamItemBto.getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examItemBO.setGadoliniumContrastFlag(createExamItemBto.getGadoliniumContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "internetEnableFlag")) {
                examItemBO.setInternetEnableFlag(createExamItemBto.getInternetEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "selfServiceOrderFlag")) {
                examItemBO.setSelfServiceOrderFlag(createExamItemBto.getSelfServiceOrderFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "notAllowedDepartmentIdList")) {
                examItemBO.setNotAllowedDepartmentIdList(
                        createExamItemBto.getNotAllowedDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "allowedStaffIdList")) {
                examItemBO.setAllowedStaffIdList(createExamItemBto.getAllowedStaffIdList());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createExamItemBto);
            addedBto.setBo(examItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examItemBO;
    }

    /** 创建检查项目 */
    @AutoGenerated(locked = true)
    protected CreateExamItemBoResult createExamItemBase(CreateExamItemBto createExamItemBto) {
        if (createExamItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateExamItemBoResult boResult = new CreateExamItemBoResult();
        ExamItemBO examItemBO = createCreateExamItemOnDuplicateThrowEx(boResult, createExamItemBto);
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examItemExtensionBtoList")) {
                createExamItemExtensionBto(boResult, createExamItemBto, examItemBO);
            }
        }
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examItemMethodBtoList")) {
                createExamItemMethodBto(boResult, createExamItemBto, examItemBO);
            }
        }
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examItemDrugBtoList")) {
                createExamItemDrugBto(boResult, createExamItemBto, examItemBO);
            }
        }
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examItemDeviceBtoList")) {
                createExamItemDeviceBto(boResult, createExamItemBto, examItemBO);
            }
        }
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examItemBodyBtoList")) {
                createExamItemBodyBto(boResult, createExamItemBto, examItemBO);
            }
        }
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createExamItemBto, "__$validPropertySet"),
                    "examItemDocumentTemplateBto")) {
                createExamItemDocumentTemplateBtoOnDuplicateThrowEx(
                        boResult, createExamItemBto, examItemBO);
            }
        }
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 创建对象ExamItemBodyBto */
    @AutoGenerated(locked = true)
    private void createExamItemBodyBto(
            CreateExamItemBoResult boResult,
            CreateExamItemBto createExamItemBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isNotEmpty(createExamItemBto.getExamItemBodyBtoList())) {
            for (CreateExamItemBto.ExamItemBodyBto item :
                    createExamItemBto.getExamItemBodyBtoList()) {
                ExamItemBodyBO subBo = new ExamItemBodyBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "examBodyId")) {
                    subBo.setExamBodyId(item.getExamBodyId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                subBo.setExamItemBO(examItemBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_item_body")));
                examItemBO.getExamItemBodyBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamItemBodyBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamItemBodyBtoOnDuplicateUpdate(
            SaveExamItemBodyListBoResult boResult,
            SaveExamItemBodyListBto saveExamItemBodyListBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isEmpty(saveExamItemBodyListBto.getExamItemBodyBtoList())) {
            saveExamItemBodyListBto.setExamItemBodyBtoList(List.of());
        }
        examItemBO
                .getExamItemBodyBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamItemBodyListBto.getExamItemBodyBtoList().stream()
                                            .filter(
                                                    examItemBodyBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examItemBodyBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examItemBodyBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamItemBody());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveExamItemBodyListBto.getExamItemBodyBtoList())) {
            for (SaveExamItemBodyListBto.ExamItemBodyBto item :
                    saveExamItemBodyListBto.getExamItemBodyBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamItemBodyBO> any =
                        examItemBO.getExamItemBodyBOSet().stream()
                                .filter(
                                        examItemBodyBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examItemBodyBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamItemBodyBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemBody());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examBodyId")) {
                            bo.setExamBodyId(item.getExamBodyId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        ExamItemBodyBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemBody());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examBodyId")) {
                            bo.setExamBodyId(item.getExamBodyId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    ExamItemBodyBO subBo = new ExamItemBodyBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "examBodyId")) {
                        subBo.setExamBodyId(item.getExamBodyId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setExamItemBO(examItemBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_item_body")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examItemBO.getExamItemBodyBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象ExamItemDeviceBto */
    @AutoGenerated(locked = true)
    private void createExamItemDeviceBto(
            CreateExamItemBoResult boResult,
            CreateExamItemBto createExamItemBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isNotEmpty(createExamItemBto.getExamItemDeviceBtoList())) {
            for (CreateExamItemBto.ExamItemDeviceBto item :
                    createExamItemBto.getExamItemDeviceBtoList()) {
                ExamItemDeviceBO subBo = new ExamItemDeviceBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusId")) {
                    subBo.setCampusId(item.getCampusId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "examDeviceId")) {
                    subBo.setExamDeviceId(item.getExamDeviceId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "priority")) {
                    subBo.setPriority(item.getPriority());
                }
                subBo.setExamItemBO(examItemBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_item_device")));
                examItemBO.getExamItemDeviceBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamItemDeviceBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamItemDeviceBtoOnDuplicateUpdate(
            SaveExamItemDeviceListBoResult boResult,
            SaveExamItemDeviceListBto saveExamItemDeviceListBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isEmpty(saveExamItemDeviceListBto.getExamItemDeviceBtoList())) {
            saveExamItemDeviceListBto.setExamItemDeviceBtoList(List.of());
        }
        examItemBO
                .getExamItemDeviceBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamItemDeviceListBto.getExamItemDeviceBtoList().stream()
                                            .filter(
                                                    examItemDeviceBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examItemDeviceBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examItemDeviceBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamItemDevice());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveExamItemDeviceListBto.getExamItemDeviceBtoList())) {
            for (SaveExamItemDeviceListBto.ExamItemDeviceBto item :
                    saveExamItemDeviceListBto.getExamItemDeviceBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamItemDeviceBO> any =
                        examItemBO.getExamItemDeviceBOSet().stream()
                                .filter(
                                        examItemDeviceBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examItemDeviceBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamItemDeviceBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemDevice());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusId")) {
                            bo.setCampusId(item.getCampusId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examDeviceId")) {
                            bo.setExamDeviceId(item.getExamDeviceId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priority")) {
                            bo.setPriority(item.getPriority());
                        }
                    } else {
                        ExamItemDeviceBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemDevice());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusId")) {
                            bo.setCampusId(item.getCampusId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examDeviceId")) {
                            bo.setExamDeviceId(item.getExamDeviceId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priority")) {
                            bo.setPriority(item.getPriority());
                        }
                    }
                } else {
                    ExamItemDeviceBO subBo = new ExamItemDeviceBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusId")) {
                        subBo.setCampusId(item.getCampusId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "examDeviceId")) {
                        subBo.setExamDeviceId(item.getExamDeviceId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "priority")) {
                        subBo.setPriority(item.getPriority());
                    }
                    subBo.setExamItemBO(examItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("exam_item_device")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examItemBO.getExamItemDeviceBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:ExamItemDocumentTemplateBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createExamItemDocumentTemplateBtoOnDuplicateThrowEx(
            CreateExamItemBoResult boResult,
            CreateExamItemBto createExamItemBto,
            ExamItemBO examItemBO) {
        if (examItemBO.getExamItemDocumentTemplateBO() != null) {
            log.error(
                    "id:{}在数据库表:{}中已经存在！",
                    examItemBO.getExamItemDocumentTemplateBO().getId(),
                    "exam_item_document_template");
            throw new IgnoredException(400, "检查项目文书模板已存在");
        } else {
            if (createExamItemBto.getExamItemDocumentTemplateBto() == null) {
                return;
            }
            ExamItemDocumentTemplateBO examItemDocumentTemplateBO =
                    examItemBO.getOrCreateExamItemDocumentTemplateBO();
            CreateExamItemBto.ExamItemDocumentTemplateBto examItemDocumentTemplateBto =
                    createExamItemBto.getExamItemDocumentTemplateBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                examItemDocumentTemplateBO.setOutpatientInformedConsentForm(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                examItemDocumentTemplateBO.setInpatientInformedConsentForm(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                examItemDocumentTemplateBO.setOutpatientApplyTemplate(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                examItemDocumentTemplateBO.setInpatientApplyTemplate(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                examItemDocumentTemplateBO.setEmergencyObservationApplyTemplate(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                examItemDocumentTemplateBO.setPreHospitalApplyTemplate(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                examItemDocumentTemplateBO.setPhysicalExamApplyTemplate(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "createdBy")) {
                examItemDocumentTemplateBO.setCreatedBy(
                        createExamItemBto.getExamItemDocumentTemplateBto().getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examItemDocumentTemplateBO.setIodineContrastFlag(
                        createExamItemBto.getExamItemDocumentTemplateBto().getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examItemDocumentTemplateBO.setGadoliniumContrastFlag(
                        createExamItemBto
                                .getExamItemDocumentTemplateBto()
                                .getGadoliniumContrastFlag());
            }

            examItemDocumentTemplateBO.setId(
                    String.valueOf(this.idGenerator.allocateId("exam_item_document_template")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(examItemDocumentTemplateBto);
            addedBto.setBo(examItemDocumentTemplateBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:ExamItemDocumentTemplateBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamItemDocumentTemplateBtoOnDuplicateUpdate(
            BaseExamItemBOService.SaveExamItemRuleBoResult boResult,
            SaveExamItemRuleBto saveExamItemRuleBto,
            ExamItemBO examItemBO) {
        if (examItemBO.getExamItemDocumentTemplateBO() != null) {
            ExamItemDocumentTemplateBO bo = examItemBO.getOrCreateExamItemDocumentTemplateBO();
            SaveExamItemRuleBto.ExamItemDocumentTemplateBto bto =
                    saveExamItemRuleBto.getExamItemDocumentTemplateBto();
            if (bto == null) {
                ExamItemDocumentTemplate deletedItem =
                        examItemBO
                                .getExamItemDocumentTemplateBO()
                                .convertToExamItemDocumentTemplate();
                boResult.getDeletedList().add(deletedItem);
                examItemBO.setExamItemDocumentTemplateBO(null);
                return;
            }
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setBo(bo);
            updatedBto.setEntity(bo.convertToExamItemDocumentTemplate());
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                bo.setOutpatientInformedConsentForm(bto.getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                bo.setInpatientInformedConsentForm(bto.getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                bo.setOutpatientApplyTemplate(bto.getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                bo.setInpatientApplyTemplate(bto.getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                bo.setEmergencyObservationApplyTemplate(bto.getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                bo.setPreHospitalApplyTemplate(bto.getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                bo.setPhysicalExamApplyTemplate(bto.getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "createdBy")) {
                bo.setCreatedBy(bto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "updatedBy")) {
                bo.setUpdatedBy(bto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                bo.setIodineContrastFlag(bto.getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                bo.setGadoliniumContrastFlag(bto.getGadoliniumContrastFlag());
            }
            boResult.getUpdatedList().add(updatedBto);
        } else {
            if (saveExamItemRuleBto.getExamItemDocumentTemplateBto() == null) {
                return;
            }
            ExamItemDocumentTemplateBO examItemDocumentTemplateBO =
                    examItemBO.getOrCreateExamItemDocumentTemplateBO();
            SaveExamItemRuleBto.ExamItemDocumentTemplateBto examItemDocumentTemplateBto =
                    saveExamItemRuleBto.getExamItemDocumentTemplateBto();
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientInformedConsentForm")) {
                examItemDocumentTemplateBO.setOutpatientInformedConsentForm(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getOutpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientInformedConsentForm")) {
                examItemDocumentTemplateBO.setInpatientInformedConsentForm(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getInpatientInformedConsentForm());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "outpatientApplyTemplate")) {
                examItemDocumentTemplateBO.setOutpatientApplyTemplate(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getOutpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "inpatientApplyTemplate")) {
                examItemDocumentTemplateBO.setInpatientApplyTemplate(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getInpatientApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "emergencyObservationApplyTemplate")) {
                examItemDocumentTemplateBO.setEmergencyObservationApplyTemplate(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getEmergencyObservationApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "preHospitalApplyTemplate")) {
                examItemDocumentTemplateBO.setPreHospitalApplyTemplate(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getPreHospitalApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "physicalExamApplyTemplate")) {
                examItemDocumentTemplateBO.setPhysicalExamApplyTemplate(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getPhysicalExamApplyTemplate());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "createdBy")) {
                examItemDocumentTemplateBO.setCreatedBy(
                        saveExamItemRuleBto.getExamItemDocumentTemplateBto().getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "updatedBy")) {
                examItemDocumentTemplateBO.setUpdatedBy(
                        saveExamItemRuleBto.getExamItemDocumentTemplateBto().getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examItemDocumentTemplateBO.setIodineContrastFlag(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    examItemDocumentTemplateBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examItemDocumentTemplateBO.setGadoliniumContrastFlag(
                        saveExamItemRuleBto
                                .getExamItemDocumentTemplateBto()
                                .getGadoliniumContrastFlag());
            }

            examItemDocumentTemplateBO.setId(
                    String.valueOf(this.idGenerator.allocateId("exam_item_document_template")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(examItemDocumentTemplateBto);
            addedBto.setBo(examItemDocumentTemplateBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象ExamItemDrugBto */
    @AutoGenerated(locked = true)
    private void createExamItemDrugBto(
            CreateExamItemBoResult boResult,
            CreateExamItemBto createExamItemBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isNotEmpty(createExamItemBto.getExamItemDrugBtoList())) {
            for (CreateExamItemBto.ExamItemDrugBto item :
                    createExamItemBto.getExamItemDrugBtoList()) {
                ExamItemDrugBO subBo = new ExamItemDrugBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "priceItemId")) {
                    subBo.setPriceItemId(item.getPriceItemId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "priceItemType")) {
                    subBo.setPriceItemType(item.getPriceItemType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "count")) {
                    subBo.setCount(item.getCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "campusIdList")) {
                    subBo.setCampusIdList(item.getCampusIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "administration")) {
                    subBo.setAdministration(item.getAdministration());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "departmentDrugFlag")) {
                    subBo.setDepartmentDrugFlag(item.getDepartmentDrugFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "useScopeList")) {
                    subBo.setUseScopeList(item.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "contrastAgentType")) {
                    subBo.setContrastAgentType(item.getContrastAgentType());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "updatedBy")) {
                    subBo.setUpdatedBy(item.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "createdBy")) {
                    subBo.setCreatedBy(item.getCreatedBy());
                }
                subBo.setExamItemBO(examItemBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_item_drug")));
                examItemBO.getExamItemDrugBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamItemDrugBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamItemDrugBtoOnDuplicateUpdate(
            SaveExamItemDrugListBoResult boResult,
            SaveExamItemDrugListBto saveExamItemDrugListBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isEmpty(saveExamItemDrugListBto.getExamItemDrugBtoList())) {
            saveExamItemDrugListBto.setExamItemDrugBtoList(List.of());
        }
        examItemBO
                .getExamItemDrugBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamItemDrugListBto.getExamItemDrugBtoList().stream()
                                            .filter(
                                                    examItemDrugBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examItemDrugBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examItemDrugBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamItemDrug());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveExamItemDrugListBto.getExamItemDrugBtoList())) {
            for (SaveExamItemDrugListBto.ExamItemDrugBto item :
                    saveExamItemDrugListBto.getExamItemDrugBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamItemDrugBO> any =
                        examItemBO.getExamItemDrugBOSet().stream()
                                .filter(
                                        examItemDrugBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examItemDrugBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamItemDrugBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemDrug());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priceItemId")) {
                            bo.setPriceItemId(item.getPriceItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priceItemType")) {
                            bo.setPriceItemType(item.getPriceItemType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "administration")) {
                            bo.setAdministration(item.getAdministration());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentDrugFlag")) {
                            bo.setDepartmentDrugFlag(item.getDepartmentDrugFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contrastAgentType")) {
                            bo.setContrastAgentType(item.getContrastAgentType());
                        }
                    } else {
                        ExamItemDrugBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemDrug());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priceItemId")) {
                            bo.setPriceItemId(item.getPriceItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "priceItemType")) {
                            bo.setPriceItemType(item.getPriceItemType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "administration")) {
                            bo.setAdministration(item.getAdministration());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "departmentDrugFlag")) {
                            bo.setDepartmentDrugFlag(item.getDepartmentDrugFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "contrastAgentType")) {
                            bo.setContrastAgentType(item.getContrastAgentType());
                        }
                    }
                } else {
                    ExamItemDrugBO subBo = new ExamItemDrugBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "priceItemId")) {
                        subBo.setPriceItemId(item.getPriceItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "priceItemType")) {
                        subBo.setPriceItemType(item.getPriceItemType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "count")) {
                        subBo.setCount(item.getCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "administration")) {
                        subBo.setAdministration(item.getAdministration());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "departmentDrugFlag")) {
                        subBo.setDepartmentDrugFlag(item.getDepartmentDrugFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "contrastAgentType")) {
                        subBo.setContrastAgentType(item.getContrastAgentType());
                    }
                    subBo.setExamItemBO(examItemBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_item_drug")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examItemBO.getExamItemDrugBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象ExamItemExtensionBto */
    @AutoGenerated(locked = true)
    private void createExamItemExtensionBto(
            BaseExamItemBOService.CreateExamItemBoResult boResult,
            CreateExamItemBto createExamItemBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isNotEmpty(createExamItemBto.getExamItemExtensionBtoList())) {
            for (CreateExamItemBto.ExamItemExtensionBto item :
                    createExamItemBto.getExamItemExtensionBtoList()) {
                ExamItemExtensionBO subBo = new ExamItemExtensionBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "usageScope")) {
                    subBo.setUsageScope(item.getUsageScope());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "branchInstitutionId")) {
                    subBo.setBranchInstitutionId(item.getBranchInstitutionId());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "radiationFilmOption")) {
                    subBo.setRadiationFilmOption(item.getRadiationFilmOption());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "appointMethod")) {
                    subBo.setAppointMethod(item.getAppointMethod());
                }
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "appointType")) {
                    subBo.setAppointType(item.getAppointType());
                }
                subBo.setExamItemBO(examItemBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_item_extension")));
                examItemBO.getExamItemExtensionBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamItemExtensionBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamItemExtensionBtoOnDuplicateUpdate(
            SaveExamItemExtensionBoResult boResult,
            SaveExamItemExtensionBto saveExamItemExtensionBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isEmpty(saveExamItemExtensionBto.getExamItemExtensionBtoList())) {
            saveExamItemExtensionBto.setExamItemExtensionBtoList(List.of());
        }
        examItemBO
                .getExamItemExtensionBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamItemExtensionBto.getExamItemExtensionBtoList().stream()
                                            .filter(
                                                    examItemExtensionBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examItemExtensionBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examItemExtensionBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamItemExtension());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveExamItemExtensionBto.getExamItemExtensionBtoList())) {
            for (SaveExamItemExtensionBto.ExamItemExtensionBto item :
                    saveExamItemExtensionBto.getExamItemExtensionBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamItemExtensionBO> any =
                        examItemBO.getExamItemExtensionBOSet().stream()
                                .filter(
                                        examItemExtensionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examItemExtensionBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamItemExtensionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemExtension());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "usageScope")) {
                            bo.setUsageScope(item.getUsageScope());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "branchInstitutionId")) {
                            bo.setBranchInstitutionId(item.getBranchInstitutionId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "radiationFilmOption")) {
                            bo.setRadiationFilmOption(item.getRadiationFilmOption());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "appointMethod")) {
                            bo.setAppointMethod(item.getAppointMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "appointType")) {
                            bo.setAppointType(item.getAppointType());
                        }
                    } else {
                        ExamItemExtensionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemExtension());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "usageScope")) {
                            bo.setUsageScope(item.getUsageScope());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "branchInstitutionId")) {
                            bo.setBranchInstitutionId(item.getBranchInstitutionId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "radiationFilmOption")) {
                            bo.setRadiationFilmOption(item.getRadiationFilmOption());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "appointMethod")) {
                            bo.setAppointMethod(item.getAppointMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "appointType")) {
                            bo.setAppointType(item.getAppointType());
                        }
                    }
                } else {
                    ExamItemExtensionBO subBo = new ExamItemExtensionBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "usageScope")) {
                        subBo.setUsageScope(item.getUsageScope());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "branchInstitutionId")) {
                        subBo.setBranchInstitutionId(item.getBranchInstitutionId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "radiationFilmOption")) {
                        subBo.setRadiationFilmOption(item.getRadiationFilmOption());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "appointMethod")) {
                        subBo.setAppointMethod(item.getAppointMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "appointType")) {
                        subBo.setAppointType(item.getAppointType());
                    }
                    subBo.setExamItemBO(examItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("exam_item_extension")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examItemBO.getExamItemExtensionBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象ExamItemMethodBto */
    @AutoGenerated(locked = true)
    private void createExamItemMethodBto(
            CreateExamItemBoResult boResult,
            CreateExamItemBto createExamItemBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isNotEmpty(createExamItemBto.getExamItemMethodBtoList())) {
            for (CreateExamItemBto.ExamItemMethodBto item :
                    createExamItemBto.getExamItemMethodBtoList()) {
                ExamItemMethodBO subBo = new ExamItemMethodBO();
                if (CollectionUtil.contains(
                        (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                        "examMethodId")) {
                    subBo.setExamMethodId(item.getExamMethodId());
                }
                subBo.setExamItemBO(examItemBO);
                subBo.setId(String.valueOf(this.idGenerator.allocateId("exam_item_method")));
                examItemBO.getExamItemMethodBOSet().add(subBo);
                AddedBto addedBto = new AddedBto();
                addedBto.setBo(subBo);
                addedBto.setBto(item);
                boResult.getAddBtoList().add(addedBto);
            }
        }
    }

    /** 创建对象:ExamItemMethodBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createExamItemMethodBtoOnDuplicateUpdate(
            SaveExamItemMethodBoResult boResult,
            SaveExamItemMethodBto saveExamItemMethodBto,
            ExamItemBO examItemBO) {
        if (CollectionUtil.isEmpty(saveExamItemMethodBto.getExamItemMethodBtoList())) {
            saveExamItemMethodBto.setExamItemMethodBtoList(List.of());
        }
        examItemBO
                .getExamItemMethodBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveExamItemMethodBto.getExamItemMethodBtoList().stream()
                                            .filter(
                                                    examItemMethodBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (examItemMethodBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            examItemMethodBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToExamItemMethod());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveExamItemMethodBto.getExamItemMethodBtoList())) {
            for (SaveExamItemMethodBto.ExamItemMethodBto item :
                    saveExamItemMethodBto.getExamItemMethodBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<ExamItemMethodBO> any =
                        examItemBO.getExamItemMethodBOSet().stream()
                                .filter(
                                        examItemMethodBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                examItemMethodBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        ExamItemMethodBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemMethod());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examMethodId")) {
                            bo.setExamMethodId(item.getExamMethodId());
                        }
                    } else {
                        ExamItemMethodBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToExamItemMethod());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "examMethodId")) {
                            bo.setExamMethodId(item.getExamMethodId());
                        }
                    }
                } else {
                    ExamItemMethodBO subBo = new ExamItemMethodBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "examMethodId")) {
                        subBo.setExamMethodId(item.getExamMethodId());
                    }
                    subBo.setExamItemBO(examItemBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("exam_item_method")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    examItemBO.getExamItemMethodBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private ExamItemBO createSaveExamItemRuleOnDuplicateUpdate(
            SaveExamItemRuleBoResult boResult, SaveExamItemRuleBto saveExamItemRuleBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveExamItemRuleBto.getId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getById(saveExamItemRuleBto.getId());
            if (examItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (examItemBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examItemBO.convertToExamItem());
                updatedBto.setBto(saveExamItemRuleBto);
                updatedBto.setBo(examItemBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "emptyStomachFlag")) {
                    examItemBO.setEmptyStomachFlag(saveExamItemRuleBto.getEmptyStomachFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "suffocateUrineFlag")) {
                    examItemBO.setSuffocateUrineFlag(saveExamItemRuleBto.getSuffocateUrineFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "bedsideFlag")) {
                    examItemBO.setBedsideFlag(saveExamItemRuleBto.getBedsideFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "examDirectionList")) {
                    examItemBO.setExamDirectionList(saveExamItemRuleBto.getExamDirectionList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "applyFormRequiredFlag")) {
                    examItemBO.setApplyFormRequiredFlag(
                            saveExamItemRuleBto.getApplyFormRequiredFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "uclaFlag")) {
                    examItemBO.setUclaFlag(saveExamItemRuleBto.getUclaFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "urgentType")) {
                    examItemBO.setUrgentType(saveExamItemRuleBto.getUrgentType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "unitWeight")) {
                    examItemBO.setUnitWeight(saveExamItemRuleBto.getUnitWeight());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "limbPositionCodeList")) {
                    examItemBO.setLimbPositionCodeList(
                            saveExamItemRuleBto.getLimbPositionCodeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "appointType")) {
                    examItemBO.setAppointType(saveExamItemRuleBto.getAppointType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "promptContent")) {
                    examItemBO.setPromptContent(saveExamItemRuleBto.getPromptContent());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "iodineContrastFlag")) {
                    examItemBO.setIodineContrastFlag(saveExamItemRuleBto.getIodineContrastFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "gadoliniumContrastFlag")) {
                    examItemBO.setGadoliniumContrastFlag(
                            saveExamItemRuleBto.getGadoliniumContrastFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "internetEnableFlag")) {
                    examItemBO.setInternetEnableFlag(saveExamItemRuleBto.getInternetEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "selfServiceOrderFlag")) {
                    examItemBO.setSelfServiceOrderFlag(
                            saveExamItemRuleBto.getSelfServiceOrderFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "notAllowedDepartmentIdList")) {
                    examItemBO.setNotAllowedDepartmentIdList(
                            saveExamItemRuleBto.getNotAllowedDepartmentIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "allowedStaffIdList")) {
                    examItemBO.setAllowedStaffIdList(saveExamItemRuleBto.getAllowedStaffIdList());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(examItemBO.convertToExamItem());
                updatedBto.setBto(saveExamItemRuleBto);
                updatedBto.setBo(examItemBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "emptyStomachFlag")) {
                    examItemBO.setEmptyStomachFlag(saveExamItemRuleBto.getEmptyStomachFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "suffocateUrineFlag")) {
                    examItemBO.setSuffocateUrineFlag(saveExamItemRuleBto.getSuffocateUrineFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "bedsideFlag")) {
                    examItemBO.setBedsideFlag(saveExamItemRuleBto.getBedsideFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "examDirectionList")) {
                    examItemBO.setExamDirectionList(saveExamItemRuleBto.getExamDirectionList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "applyFormRequiredFlag")) {
                    examItemBO.setApplyFormRequiredFlag(
                            saveExamItemRuleBto.getApplyFormRequiredFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "uclaFlag")) {
                    examItemBO.setUclaFlag(saveExamItemRuleBto.getUclaFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "urgentType")) {
                    examItemBO.setUrgentType(saveExamItemRuleBto.getUrgentType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "unitWeight")) {
                    examItemBO.setUnitWeight(saveExamItemRuleBto.getUnitWeight());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "limbPositionCodeList")) {
                    examItemBO.setLimbPositionCodeList(
                            saveExamItemRuleBto.getLimbPositionCodeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "appointType")) {
                    examItemBO.setAppointType(saveExamItemRuleBto.getAppointType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "promptContent")) {
                    examItemBO.setPromptContent(saveExamItemRuleBto.getPromptContent());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "iodineContrastFlag")) {
                    examItemBO.setIodineContrastFlag(saveExamItemRuleBto.getIodineContrastFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "gadoliniumContrastFlag")) {
                    examItemBO.setGadoliniumContrastFlag(
                            saveExamItemRuleBto.getGadoliniumContrastFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "internetEnableFlag")) {
                    examItemBO.setInternetEnableFlag(saveExamItemRuleBto.getInternetEnableFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "selfServiceOrderFlag")) {
                    examItemBO.setSelfServiceOrderFlag(
                            saveExamItemRuleBto.getSelfServiceOrderFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "notAllowedDepartmentIdList")) {
                    examItemBO.setNotAllowedDepartmentIdList(
                            saveExamItemRuleBto.getNotAllowedDepartmentIdList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveExamItemRuleBto, "__$validPropertySet"),
                        "allowedStaffIdList")) {
                    examItemBO.setAllowedStaffIdList(saveExamItemRuleBto.getAllowedStaffIdList());
                }
            }
        } else {
            examItemBO = new ExamItemBO();
            if (pkExist) {
                examItemBO.setId(saveExamItemRuleBto.getId());
            } else {
                examItemBO.setId(String.valueOf(this.idGenerator.allocateId("exam_item")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "emptyStomachFlag")) {
                examItemBO.setEmptyStomachFlag(saveExamItemRuleBto.getEmptyStomachFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "suffocateUrineFlag")) {
                examItemBO.setSuffocateUrineFlag(saveExamItemRuleBto.getSuffocateUrineFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "bedsideFlag")) {
                examItemBO.setBedsideFlag(saveExamItemRuleBto.getBedsideFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "examDirectionList")) {
                examItemBO.setExamDirectionList(saveExamItemRuleBto.getExamDirectionList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "applyFormRequiredFlag")) {
                examItemBO.setApplyFormRequiredFlag(saveExamItemRuleBto.getApplyFormRequiredFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "uclaFlag")) {
                examItemBO.setUclaFlag(saveExamItemRuleBto.getUclaFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "urgentType")) {
                examItemBO.setUrgentType(saveExamItemRuleBto.getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "unitWeight")) {
                examItemBO.setUnitWeight(saveExamItemRuleBto.getUnitWeight());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "limbPositionCodeList")) {
                examItemBO.setLimbPositionCodeList(saveExamItemRuleBto.getLimbPositionCodeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "appointType")) {
                examItemBO.setAppointType(saveExamItemRuleBto.getAppointType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "promptContent")) {
                examItemBO.setPromptContent(saveExamItemRuleBto.getPromptContent());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "iodineContrastFlag")) {
                examItemBO.setIodineContrastFlag(saveExamItemRuleBto.getIodineContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "gadoliniumContrastFlag")) {
                examItemBO.setGadoliniumContrastFlag(
                        saveExamItemRuleBto.getGadoliniumContrastFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "internetEnableFlag")) {
                examItemBO.setInternetEnableFlag(saveExamItemRuleBto.getInternetEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "selfServiceOrderFlag")) {
                examItemBO.setSelfServiceOrderFlag(saveExamItemRuleBto.getSelfServiceOrderFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "notAllowedDepartmentIdList")) {
                examItemBO.setNotAllowedDepartmentIdList(
                        saveExamItemRuleBto.getNotAllowedDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "allowedStaffIdList")) {
                examItemBO.setAllowedStaffIdList(saveExamItemRuleBto.getAllowedStaffIdList());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveExamItemRuleBto);
            addedBto.setBo(examItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return examItemBO;
    }

    /** 保存检查项目部位列表 */
    @AutoGenerated(locked = true)
    protected SaveExamItemBodyListBoResult saveExamItemBodyListBase(
            SaveExamItemBodyListBto saveExamItemBodyListBto) {
        if (saveExamItemBodyListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamItemBodyListBoResult boResult = new SaveExamItemBodyListBoResult();
        ExamItemBO examItemBO =
                updateSaveExamItemBodyListOnMissThrowEx(boResult, saveExamItemBodyListBto);
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamItemBodyListBto, "__$validPropertySet"),
                    "examItemBodyBtoList")) {
                createExamItemBodyBtoOnDuplicateUpdate(
                        boResult, saveExamItemBodyListBto, examItemBO);
            }
        }
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 保存检查项目对应设备 */
    @AutoGenerated(locked = true)
    protected SaveExamItemDeviceListBoResult saveExamItemDeviceListBase(
            SaveExamItemDeviceListBto saveExamItemDeviceListBto) {
        if (saveExamItemDeviceListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamItemDeviceListBoResult boResult = new SaveExamItemDeviceListBoResult();
        ExamItemBO examItemBO =
                updateSaveExamItemDeviceListOnMissThrowEx(boResult, saveExamItemDeviceListBto);
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamItemDeviceListBto, "__$validPropertySet"),
                    "examItemDeviceBtoList")) {
                createExamItemDeviceBtoOnDuplicateUpdate(
                        boResult, saveExamItemDeviceListBto, examItemBO);
            }
        }
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 保存检查项目药品信息 */
    @AutoGenerated(locked = true)
    protected SaveExamItemDrugListBoResult saveExamItemDrugListBase(
            SaveExamItemDrugListBto saveExamItemDrugListBto) {
        if (saveExamItemDrugListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamItemDrugListBoResult boResult = new SaveExamItemDrugListBoResult();
        ExamItemBO examItemBO =
                updateSaveExamItemDrugListOnMissThrowEx(boResult, saveExamItemDrugListBto);
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamItemDrugListBto, "__$validPropertySet"),
                    "examItemDrugBtoList")) {
                createExamItemDrugBtoOnDuplicateUpdate(
                        boResult, saveExamItemDrugListBto, examItemBO);
            }
        }
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 保存检查项目扩展信息 */
    @AutoGenerated(locked = true)
    protected SaveExamItemExtensionBoResult saveExamItemExtensionBase(
            SaveExamItemExtensionBto saveExamItemExtensionBto) {
        if (saveExamItemExtensionBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamItemExtensionBoResult boResult = new SaveExamItemExtensionBoResult();
        ExamItemBO examItemBO =
                updateSaveExamItemExtensionOnMissThrowEx(boResult, saveExamItemExtensionBto);
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveExamItemExtensionBto, "__$validPropertySet"),
                    "examItemExtensionBtoList")) {
                createExamItemExtensionBtoOnDuplicateUpdate(
                        boResult, saveExamItemExtensionBto, examItemBO);
            }
        }
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 保存检查项目方法 */
    @AutoGenerated(locked = true)
    protected SaveExamItemMethodBoResult saveExamItemMethodBase(
            SaveExamItemMethodBto saveExamItemMethodBto) {
        if (saveExamItemMethodBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamItemMethodBoResult boResult = new SaveExamItemMethodBoResult();
        ExamItemBO examItemBO =
                updateSaveExamItemMethodOnMissThrowEx(boResult, saveExamItemMethodBto);
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "examItemMethodBtoList")) {
                createExamItemMethodBtoOnDuplicateUpdate(
                        boResult, saveExamItemMethodBto, examItemBO);
            }
        }
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 保存检查项目规则 */
    @AutoGenerated(locked = true)
    protected SaveExamItemRuleBoResult saveExamItemRuleBase(
            SaveExamItemRuleBto saveExamItemRuleBto) {
        if (saveExamItemRuleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveExamItemRuleBoResult boResult = new SaveExamItemRuleBoResult();
        ExamItemBO examItemBO =
                createSaveExamItemRuleOnDuplicateUpdate(boResult, saveExamItemRuleBto);
        if (examItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemRuleBto, "__$validPropertySet"),
                    "examItemDocumentTemplateBto")) {
                createExamItemDocumentTemplateBtoOnDuplicateUpdate(
                        boResult, saveExamItemRuleBto, examItemBO);
            }
        }
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 更新检查项目 */
    @AutoGenerated(locked = true)
    protected UpdateExamItemBoResult updateExamItemBase(UpdateExamItemBto updateExamItemBto) {
        if (updateExamItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateExamItemBoResult boResult = new UpdateExamItemBoResult();
        ExamItemBO examItemBO = updateUpdateExamItemOnMissThrowEx(boResult, updateExamItemBto);
        boResult.setRootBo(examItemBO);
        return boResult;
    }

    /** 更新对象:saveExamItemBodyList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamItemBO updateSaveExamItemBodyListOnMissThrowEx(
            BaseExamItemBOService.SaveExamItemBodyListBoResult boResult,
            SaveExamItemBodyListBto saveExamItemBodyListBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveExamItemBodyListBto.getId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getById(saveExamItemBodyListBto.getId());
            found = true;
        }
        if (examItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examItemBO.convertToExamItem());
            updatedBto.setBto(saveExamItemBodyListBto);
            updatedBto.setBo(examItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return examItemBO;
        }
    }

    /** 更新对象:saveExamItemDeviceList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamItemBO updateSaveExamItemDeviceListOnMissThrowEx(
            BaseExamItemBOService.SaveExamItemDeviceListBoResult boResult,
            SaveExamItemDeviceListBto saveExamItemDeviceListBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveExamItemDeviceListBto.getId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getById(saveExamItemDeviceListBto.getId());
            found = true;
        }
        if (examItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examItemBO.convertToExamItem());
            updatedBto.setBto(saveExamItemDeviceListBto);
            updatedBto.setBo(examItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return examItemBO;
        }
    }

    /** 更新对象:saveExamItemDrugList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamItemBO updateSaveExamItemDrugListOnMissThrowEx(
            BaseExamItemBOService.SaveExamItemDrugListBoResult boResult,
            SaveExamItemDrugListBto saveExamItemDrugListBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveExamItemDrugListBto.getId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getById(saveExamItemDrugListBto.getId());
            found = true;
        }
        if (examItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examItemBO.convertToExamItem());
            updatedBto.setBto(saveExamItemDrugListBto);
            updatedBto.setBo(examItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return examItemBO;
        }
    }

    /** 更新对象:saveExamItemExtension,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamItemBO updateSaveExamItemExtensionOnMissThrowEx(
            BaseExamItemBOService.SaveExamItemExtensionBoResult boResult,
            SaveExamItemExtensionBto saveExamItemExtensionBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveExamItemExtensionBto.getId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getById(saveExamItemExtensionBto.getId());
            found = true;
        }
        if (examItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examItemBO.convertToExamItem());
            updatedBto.setBto(saveExamItemExtensionBto);
            updatedBto.setBo(examItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return examItemBO;
        }
    }

    /** 更新对象:saveExamItemMethod,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamItemBO updateSaveExamItemMethodOnMissThrowEx(
            BaseExamItemBOService.SaveExamItemMethodBoResult boResult,
            SaveExamItemMethodBto saveExamItemMethodBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveExamItemMethodBto.getClinicItemId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getByClinicItemId(saveExamItemMethodBto.getClinicItemId());
            if (examItemBO != null) {
                found = true;
            }
        }
        if (examItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examItemBO.convertToExamItem());
            updatedBto.setBto(saveExamItemMethodBto);
            updatedBto.setBo(examItemBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "clinicItemId")) {
                examItemBO.setClinicItemId(saveExamItemMethodBto.getClinicItemId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "urgentType")) {
                examItemBO.setUrgentType(saveExamItemMethodBto.getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "unitWeight")) {
                examItemBO.setUnitWeight(saveExamItemMethodBto.getUnitWeight());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "promptContent")) {
                examItemBO.setPromptContent(saveExamItemMethodBto.getPromptContent());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "notAllowedDepartmentIdList")) {
                examItemBO.setNotAllowedDepartmentIdList(
                        saveExamItemMethodBto.getNotAllowedDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "allowedStaffIdList")) {
                examItemBO.setAllowedStaffIdList(saveExamItemMethodBto.getAllowedStaffIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveExamItemMethodBto, "__$validPropertySet"),
                    "pathologyType")) {
                examItemBO.setPathologyType(saveExamItemMethodBto.getPathologyType());
            }
            return examItemBO;
        }
    }

    /** 更新对象:updateExamItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private ExamItemBO updateUpdateExamItemOnMissThrowEx(
            BaseExamItemBOService.UpdateExamItemBoResult boResult,
            UpdateExamItemBto updateExamItemBto) {
        ExamItemBO examItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateExamItemBto.getId() == null);
        if (!allNull && !found) {
            examItemBO = ExamItemBO.getById(updateExamItemBto.getId());
            found = true;
        }
        if (examItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(examItemBO.convertToExamItem());
            updatedBto.setBto(updateExamItemBto);
            updatedBto.setBo(examItemBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamItemBto, "__$validPropertySet"),
                    "examCatalogId")) {
                examItemBO.setExamCatalogId(updateExamItemBto.getExamCatalogId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamItemBto, "__$validPropertySet"),
                    "examTypeId")) {
                examItemBO.setExamTypeId(updateExamItemBto.getExamTypeId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamItemBto, "__$validPropertySet"),
                    "sortNumber")) {
                examItemBO.setSortNumber(updateExamItemBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamItemBto, "__$validPropertySet"),
                    "enhancedScanningFlag")) {
                examItemBO.setEnhancedScanningFlag(updateExamItemBto.getEnhancedScanningFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamItemBto, "__$validPropertySet"),
                    "partNumber")) {
                examItemBO.setPartNumber(updateExamItemBto.getPartNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateExamItemBto, "__$validPropertySet"),
                    "pathologyType")) {
                examItemBO.setPathologyType(updateExamItemBto.getPathologyType());
            }
            return examItemBO;
        }
    }

    public static class CreateExamItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamItemBto.ExamItemExtensionBto, ExamItemExtensionBO> getCreatedBto(
                CreateExamItemBto.ExamItemExtensionBto examItemExtensionBto) {
            return this.getAddedResult(examItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamItemBto, ExamItemBO> getCreatedBto(
                CreateExamItemBto createExamItemBto) {
            return this.getAddedResult(createExamItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamItemBto.ExamItemMethodBto, ExamItemMethodBO> getCreatedBto(
                CreateExamItemBto.ExamItemMethodBto examItemMethodBto) {
            return this.getAddedResult(examItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamItemBto.ExamItemDrugBto, ExamItemDrugBO> getCreatedBto(
                CreateExamItemBto.ExamItemDrugBto examItemDrugBto) {
            return this.getAddedResult(examItemDrugBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamItemBto.ExamItemDeviceBto, ExamItemDeviceBO> getCreatedBto(
                CreateExamItemBto.ExamItemDeviceBto examItemDeviceBto) {
            return this.getAddedResult(examItemDeviceBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamItemBto.ExamItemBodyBto, ExamItemBodyBO> getCreatedBto(
                CreateExamItemBto.ExamItemBodyBto examItemBodyBto) {
            return this.getAddedResult(examItemBodyBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateExamItemBto.ExamItemDocumentTemplateBto, ExamItemDocumentTemplateBO>
                getCreatedBto(
                        CreateExamItemBto.ExamItemDocumentTemplateBto examItemDocumentTemplateBto) {
            return this.getAddedResult(examItemDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public ExamItemExtension getDeleted_ExamItemExtension() {
            return (ExamItemExtension)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemExtension.class));
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamItemMethod getDeleted_ExamItemMethod() {
            return (ExamItemMethod)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemMethod.class));
        }

        @AutoGenerated(locked = true)
        public ExamItemDrug getDeleted_ExamItemDrug() {
            return (ExamItemDrug)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemDrug.class));
        }

        @AutoGenerated(locked = true)
        public ExamItemDevice getDeleted_ExamItemDevice() {
            return (ExamItemDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemDevice.class));
        }

        @AutoGenerated(locked = true)
        public ExamItemBody getDeleted_ExamItemBody() {
            return (ExamItemBody)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemBody.class));
        }

        @AutoGenerated(locked = true)
        public ExamItemDocumentTemplate getDeleted_ExamItemDocumentTemplate() {
            return (ExamItemDocumentTemplate)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamItemDocumentTemplate.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamItemBto.ExamItemExtensionBto,
                        ExamItemExtension,
                        ExamItemExtensionBO>
                getUpdatedBto(CreateExamItemBto.ExamItemExtensionBto examItemExtensionBto) {
            return super.getUpdatedResult(examItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamItemBto, ExamItem, ExamItemBO> getUpdatedBto(
                CreateExamItemBto createExamItemBto) {
            return super.getUpdatedResult(createExamItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamItemBto.ExamItemMethodBto, ExamItemMethod, ExamItemMethodBO>
                getUpdatedBto(CreateExamItemBto.ExamItemMethodBto examItemMethodBto) {
            return super.getUpdatedResult(examItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamItemBto.ExamItemDrugBto, ExamItemDrug, ExamItemDrugBO>
                getUpdatedBto(CreateExamItemBto.ExamItemDrugBto examItemDrugBto) {
            return super.getUpdatedResult(examItemDrugBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamItemBto.ExamItemDeviceBto, ExamItemDevice, ExamItemDeviceBO>
                getUpdatedBto(CreateExamItemBto.ExamItemDeviceBto examItemDeviceBto) {
            return super.getUpdatedResult(examItemDeviceBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateExamItemBto.ExamItemBodyBto, ExamItemBody, ExamItemBodyBO>
                getUpdatedBto(CreateExamItemBto.ExamItemBodyBto examItemBodyBto) {
            return super.getUpdatedResult(examItemBodyBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateExamItemBto.ExamItemDocumentTemplateBto,
                        ExamItemDocumentTemplate,
                        ExamItemDocumentTemplateBO>
                getUpdatedBto(
                        CreateExamItemBto.ExamItemDocumentTemplateBto examItemDocumentTemplateBto) {
            return super.getUpdatedResult(examItemDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamItemBto.ExamItemExtensionBto, ExamItemExtensionBO>
                getUnmodifiedBto(CreateExamItemBto.ExamItemExtensionBto examItemExtensionBto) {
            return super.getUnmodifiedResult(examItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamItemBto, ExamItemBO> getUnmodifiedBto(
                CreateExamItemBto createExamItemBto) {
            return super.getUnmodifiedResult(createExamItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamItemBto.ExamItemMethodBto, ExamItemMethodBO>
                getUnmodifiedBto(CreateExamItemBto.ExamItemMethodBto examItemMethodBto) {
            return super.getUnmodifiedResult(examItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamItemBto.ExamItemDrugBto, ExamItemDrugBO> getUnmodifiedBto(
                CreateExamItemBto.ExamItemDrugBto examItemDrugBto) {
            return super.getUnmodifiedResult(examItemDrugBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamItemBto.ExamItemDeviceBto, ExamItemDeviceBO>
                getUnmodifiedBto(CreateExamItemBto.ExamItemDeviceBto examItemDeviceBto) {
            return super.getUnmodifiedResult(examItemDeviceBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateExamItemBto.ExamItemBodyBto, ExamItemBodyBO> getUnmodifiedBto(
                CreateExamItemBto.ExamItemBodyBto examItemBodyBto) {
            return super.getUnmodifiedResult(examItemBodyBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        CreateExamItemBto.ExamItemDocumentTemplateBto, ExamItemDocumentTemplateBO>
                getUnmodifiedBto(
                        CreateExamItemBto.ExamItemDocumentTemplateBto examItemDocumentTemplateBto) {
            return super.getUnmodifiedResult(examItemDocumentTemplateBto);
        }
    }

    public static class SaveExamItemExtensionBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemExtensionBto.ExamItemExtensionBto, ExamItemExtensionBO>
                getCreatedBto(SaveExamItemExtensionBto.ExamItemExtensionBto examItemExtensionBto) {
            return this.getAddedResult(examItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemExtensionBto, ExamItemBO> getCreatedBto(
                SaveExamItemExtensionBto saveExamItemExtensionBto) {
            return this.getAddedResult(saveExamItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public ExamItemExtension getDeleted_ExamItemExtension() {
            return (ExamItemExtension)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemExtension.class));
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamItemExtensionBto.ExamItemExtensionBto,
                        ExamItemExtension,
                        ExamItemExtensionBO>
                getUpdatedBto(SaveExamItemExtensionBto.ExamItemExtensionBto examItemExtensionBto) {
            return super.getUpdatedResult(examItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemExtensionBto, ExamItem, ExamItemBO> getUpdatedBto(
                SaveExamItemExtensionBto saveExamItemExtensionBto) {
            return super.getUpdatedResult(saveExamItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemExtensionBto.ExamItemExtensionBto, ExamItemExtensionBO>
                getUnmodifiedBto(
                        SaveExamItemExtensionBto.ExamItemExtensionBto examItemExtensionBto) {
            return super.getUnmodifiedResult(examItemExtensionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemExtensionBto, ExamItemBO> getUnmodifiedBto(
                SaveExamItemExtensionBto saveExamItemExtensionBto) {
            return super.getUnmodifiedResult(saveExamItemExtensionBto);
        }
    }

    public static class SaveExamItemMethodBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemMethodBto.ExamItemMethodBto, ExamItemMethodBO> getCreatedBto(
                SaveExamItemMethodBto.ExamItemMethodBto examItemMethodBto) {
            return this.getAddedResult(examItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemMethodBto, ExamItemBO> getCreatedBto(
                SaveExamItemMethodBto saveExamItemMethodBto) {
            return this.getAddedResult(saveExamItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public ExamItemMethod getDeleted_ExamItemMethod() {
            return (ExamItemMethod)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemMethod.class));
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemMethodBto.ExamItemMethodBto, ExamItemMethod, ExamItemMethodBO>
                getUpdatedBto(SaveExamItemMethodBto.ExamItemMethodBto examItemMethodBto) {
            return super.getUpdatedResult(examItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemMethodBto, ExamItem, ExamItemBO> getUpdatedBto(
                SaveExamItemMethodBto saveExamItemMethodBto) {
            return super.getUpdatedResult(saveExamItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemMethodBto.ExamItemMethodBto, ExamItemMethodBO>
                getUnmodifiedBto(SaveExamItemMethodBto.ExamItemMethodBto examItemMethodBto) {
            return super.getUnmodifiedResult(examItemMethodBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemMethodBto, ExamItemBO> getUnmodifiedBto(
                SaveExamItemMethodBto saveExamItemMethodBto) {
            return super.getUnmodifiedResult(saveExamItemMethodBto);
        }
    }

    public static class SaveExamItemDrugListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemDrugListBto.ExamItemDrugBto, ExamItemDrugBO> getCreatedBto(
                SaveExamItemDrugListBto.ExamItemDrugBto examItemDrugBto) {
            return this.getAddedResult(examItemDrugBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemDrugListBto, ExamItemBO> getCreatedBto(
                SaveExamItemDrugListBto saveExamItemDrugListBto) {
            return this.getAddedResult(saveExamItemDrugListBto);
        }

        @AutoGenerated(locked = true)
        public ExamItemDrug getDeleted_ExamItemDrug() {
            return (ExamItemDrug)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemDrug.class));
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemDrugListBto.ExamItemDrugBto, ExamItemDrug, ExamItemDrugBO>
                getUpdatedBto(SaveExamItemDrugListBto.ExamItemDrugBto examItemDrugBto) {
            return super.getUpdatedResult(examItemDrugBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemDrugListBto, ExamItem, ExamItemBO> getUpdatedBto(
                SaveExamItemDrugListBto saveExamItemDrugListBto) {
            return super.getUpdatedResult(saveExamItemDrugListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemDrugListBto.ExamItemDrugBto, ExamItemDrugBO>
                getUnmodifiedBto(SaveExamItemDrugListBto.ExamItemDrugBto examItemDrugBto) {
            return super.getUnmodifiedResult(examItemDrugBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemDrugListBto, ExamItemBO> getUnmodifiedBto(
                SaveExamItemDrugListBto saveExamItemDrugListBto) {
            return super.getUnmodifiedResult(saveExamItemDrugListBto);
        }
    }

    public static class UpdateExamItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateExamItemBto, ExamItemBO> getCreatedBto(
                UpdateExamItemBto updateExamItemBto) {
            return this.getAddedResult(updateExamItemBto);
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateExamItemBto, ExamItem, ExamItemBO> getUpdatedBto(
                UpdateExamItemBto updateExamItemBto) {
            return super.getUpdatedResult(updateExamItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateExamItemBto, ExamItemBO> getUnmodifiedBto(
                UpdateExamItemBto updateExamItemBto) {
            return super.getUnmodifiedResult(updateExamItemBto);
        }
    }

    public static class SaveExamItemDeviceListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemDeviceListBto, ExamItemBO> getCreatedBto(
                SaveExamItemDeviceListBto saveExamItemDeviceListBto) {
            return this.getAddedResult(saveExamItemDeviceListBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemDeviceListBto.ExamItemDeviceBto, ExamItemDeviceBO>
                getCreatedBto(SaveExamItemDeviceListBto.ExamItemDeviceBto examItemDeviceBto) {
            return this.getAddedResult(examItemDeviceBto);
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public ExamItemDevice getDeleted_ExamItemDevice() {
            return (ExamItemDevice)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemDevice.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemDeviceListBto, ExamItem, ExamItemBO> getUpdatedBto(
                SaveExamItemDeviceListBto saveExamItemDeviceListBto) {
            return super.getUpdatedResult(saveExamItemDeviceListBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamItemDeviceListBto.ExamItemDeviceBto,
                        ExamItemDevice,
                        ExamItemDeviceBO>
                getUpdatedBto(SaveExamItemDeviceListBto.ExamItemDeviceBto examItemDeviceBto) {
            return super.getUpdatedResult(examItemDeviceBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemDeviceListBto, ExamItemBO> getUnmodifiedBto(
                SaveExamItemDeviceListBto saveExamItemDeviceListBto) {
            return super.getUnmodifiedResult(saveExamItemDeviceListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemDeviceListBto.ExamItemDeviceBto, ExamItemDeviceBO>
                getUnmodifiedBto(SaveExamItemDeviceListBto.ExamItemDeviceBto examItemDeviceBto) {
            return super.getUnmodifiedResult(examItemDeviceBto);
        }
    }

    public static class SaveExamItemBodyListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemBodyListBto.ExamItemBodyBto, ExamItemBodyBO> getCreatedBto(
                SaveExamItemBodyListBto.ExamItemBodyBto examItemBodyBto) {
            return this.getAddedResult(examItemBodyBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemBodyListBto, ExamItemBO> getCreatedBto(
                SaveExamItemBodyListBto saveExamItemBodyListBto) {
            return this.getAddedResult(saveExamItemBodyListBto);
        }

        @AutoGenerated(locked = true)
        public ExamItemBody getDeleted_ExamItemBody() {
            return (ExamItemBody)
                    CollectionUtil.getFirst(this.getDeletedEntityList(ExamItemBody.class));
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemBodyListBto.ExamItemBodyBto, ExamItemBody, ExamItemBodyBO>
                getUpdatedBto(SaveExamItemBodyListBto.ExamItemBodyBto examItemBodyBto) {
            return super.getUpdatedResult(examItemBodyBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemBodyListBto, ExamItem, ExamItemBO> getUpdatedBto(
                SaveExamItemBodyListBto saveExamItemBodyListBto) {
            return super.getUpdatedResult(saveExamItemBodyListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemBodyListBto.ExamItemBodyBto, ExamItemBodyBO>
                getUnmodifiedBto(SaveExamItemBodyListBto.ExamItemBodyBto examItemBodyBto) {
            return super.getUnmodifiedResult(examItemBodyBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemBodyListBto, ExamItemBO> getUnmodifiedBto(
                SaveExamItemBodyListBto saveExamItemBodyListBto) {
            return super.getUnmodifiedResult(saveExamItemBodyListBto);
        }
    }

    public static class SaveExamItemRuleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public ExamItemBO getRootBo() {
            return (ExamItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemRuleBto.ExamItemDocumentTemplateBto, ExamItemDocumentTemplateBO>
                getCreatedBto(
                        SaveExamItemRuleBto.ExamItemDocumentTemplateBto
                                examItemDocumentTemplateBto) {
            return this.getAddedResult(examItemDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveExamItemRuleBto, ExamItemBO> getCreatedBto(
                SaveExamItemRuleBto saveExamItemRuleBto) {
            return this.getAddedResult(saveExamItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public ExamItemDocumentTemplate getDeleted_ExamItemDocumentTemplate() {
            return (ExamItemDocumentTemplate)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(ExamItemDocumentTemplate.class));
        }

        @AutoGenerated(locked = true)
        public ExamItem getDeleted_ExamItem() {
            return (ExamItem) CollectionUtil.getFirst(this.getDeletedEntityList(ExamItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveExamItemRuleBto.ExamItemDocumentTemplateBto,
                        ExamItemDocumentTemplate,
                        ExamItemDocumentTemplateBO>
                getUpdatedBto(
                        SaveExamItemRuleBto.ExamItemDocumentTemplateBto
                                examItemDocumentTemplateBto) {
            return super.getUpdatedResult(examItemDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveExamItemRuleBto, ExamItem, ExamItemBO> getUpdatedBto(
                SaveExamItemRuleBto saveExamItemRuleBto) {
            return super.getUpdatedResult(saveExamItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveExamItemRuleBto.ExamItemDocumentTemplateBto, ExamItemDocumentTemplateBO>
                getUnmodifiedBto(
                        SaveExamItemRuleBto.ExamItemDocumentTemplateBto
                                examItemDocumentTemplateBto) {
            return super.getUnmodifiedResult(examItemDocumentTemplateBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveExamItemRuleBto, ExamItemBO> getUnmodifiedBto(
                SaveExamItemRuleBto saveExamItemRuleBto) {
            return super.getUnmodifiedResult(saveExamItemRuleBto);
        }
    }
}
