package com.pulse.dictionary_business.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.bo.AdministrationMethodBO;
import com.pulse.dictionary_business.persist.dos.AdministrationMethod;
import com.pulse.dictionary_business.persist.dos.AdministrationMethodApplicableScope;
import com.pulse.dictionary_business.persist.dos.AdministrationMethodChargeItem;
import com.pulse.dictionary_business.persist.dos.AdministrationMethodDefaultPerformDepartment;
import com.pulse.dictionary_business.service.base.BaseAdministrationMethodBOService.ChangeAdministrationMethodEnableFlagBoResult;
import com.pulse.dictionary_business.service.base.BaseAdministrationMethodBOService.SaveAdministrationMethodApplicableScopeListBoResult;
import com.pulse.dictionary_business.service.base.BaseAdministrationMethodBOService.SaveAdministrationMethodBoResult;
import com.pulse.dictionary_business.service.base.BaseAdministrationMethodBOService.SaveAdministrationMethodChargeItemListBoResult;
import com.pulse.dictionary_business.service.base.BaseAdministrationMethodBOService.SaveAdministrationMethodDefaultPerformDepartmentListBoResult;
import com.pulse.dictionary_business.service.bto.ChangeAdministrationMethodEnableFlagBto;
import com.pulse.dictionary_business.service.bto.SaveAdministrationMethodApplicableScopeListBto;
import com.pulse.dictionary_business.service.bto.SaveAdministrationMethodBto;
import com.pulse.dictionary_business.service.bto.SaveAdministrationMethodChargeItemListBto;
import com.pulse.dictionary_business.service.bto.SaveAdministrationMethodDefaultPerformDepartmentListBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "111be9f1-**************-5ea07a4e5b7e")
public class BaseAdministrationMethodBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 修改给药方式启用标识 */
    @AutoGenerated(locked = true)
    protected ChangeAdministrationMethodEnableFlagBoResult changeAdministrationMethodEnableFlagBase(
            ChangeAdministrationMethodEnableFlagBto changeAdministrationMethodEnableFlagBto) {
        if (changeAdministrationMethodEnableFlagBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        ChangeAdministrationMethodEnableFlagBoResult boResult =
                new ChangeAdministrationMethodEnableFlagBoResult();
        AdministrationMethodBO administrationMethodBO =
                updateChangeAdministrationMethodEnableFlagOnMissThrowEx(
                        boResult, changeAdministrationMethodEnableFlagBto);
        boResult.setRootBo(administrationMethodBO);
        return boResult;
    }

    /** 创建对象:AdministrationMethodApplicableScopeBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createAdministrationMethodApplicableScopeBtoOnDuplicateUpdate(
            BaseAdministrationMethodBOService.SaveAdministrationMethodBoResult boResult,
            SaveAdministrationMethodBto saveAdministrationMethodBto,
            AdministrationMethodBO administrationMethodBO) {
        if (CollectionUtil.isEmpty(
                saveAdministrationMethodBto.getAdministrationMethodApplicableScopeBtoList())) {
            saveAdministrationMethodBto.setAdministrationMethodApplicableScopeBtoList(List.of());
        }
        administrationMethodBO
                .getAdministrationMethodApplicableScopeBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveAdministrationMethodBto
                                            .getAdministrationMethodApplicableScopeBtoList()
                                            .stream()
                                            .filter(
                                                    administrationMethodApplicableScopeBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (administrationMethodApplicableScopeBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            administrationMethodApplicableScopeBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToAdministrationMethodApplicableScope());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveAdministrationMethodBto.getAdministrationMethodApplicableScopeBtoList())) {
            for (SaveAdministrationMethodBto.AdministrationMethodApplicableScopeBto item :
                    saveAdministrationMethodBto.getAdministrationMethodApplicableScopeBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<AdministrationMethodApplicableScopeBO> any =
                        administrationMethodBO
                                .getAdministrationMethodApplicableScopeBOSet()
                                .stream()
                                .filter(
                                        administrationMethodApplicableScopeBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                administrationMethodApplicableScopeBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        AdministrationMethodApplicableScopeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodApplicableScope());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectName")) {
                            bo.setObjectName(item.getObjectName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectId")) {
                            bo.setObjectId(item.getObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        AdministrationMethodApplicableScopeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodApplicableScope());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectName")) {
                            bo.setObjectName(item.getObjectName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectId")) {
                            bo.setObjectId(item.getObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    AdministrationMethodApplicableScopeBO subBo =
                            new AdministrationMethodApplicableScopeBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicableObjectType")) {
                        subBo.setApplicableObjectType(item.getApplicableObjectType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "objectName")) {
                        subBo.setObjectName(item.getObjectName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "objectId")) {
                        subBo.setObjectId(item.getObjectId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setAdministrationMethodBO(administrationMethodBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "administration_method_applicable_scope")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    administrationMethodBO.getAdministrationMethodApplicableScopeBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:AdministrationMethodApplicableScopeBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createAdministrationMethodApplicableScopeBtoOnDuplicateUpdate(
            SaveAdministrationMethodApplicableScopeListBoResult boResult,
            SaveAdministrationMethodApplicableScopeListBto
                    saveAdministrationMethodApplicableScopeListBto,
            AdministrationMethodBO administrationMethodBO) {
        if (CollectionUtil.isEmpty(
                saveAdministrationMethodApplicableScopeListBto
                        .getAdministrationMethodApplicableScopeBtoList())) {
            saveAdministrationMethodApplicableScopeListBto
                    .setAdministrationMethodApplicableScopeBtoList(List.of());
        }
        administrationMethodBO
                .getAdministrationMethodApplicableScopeBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveAdministrationMethodApplicableScopeListBto
                                            .getAdministrationMethodApplicableScopeBtoList()
                                            .stream()
                                            .filter(
                                                    administrationMethodApplicableScopeBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (administrationMethodApplicableScopeBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            administrationMethodApplicableScopeBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToAdministrationMethodApplicableScope());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveAdministrationMethodApplicableScopeListBto
                        .getAdministrationMethodApplicableScopeBtoList())) {
            for (SaveAdministrationMethodApplicableScopeListBto
                            .AdministrationMethodApplicableScopeBto
                    item :
                            saveAdministrationMethodApplicableScopeListBto
                                    .getAdministrationMethodApplicableScopeBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<AdministrationMethodApplicableScopeBO> any =
                        administrationMethodBO
                                .getAdministrationMethodApplicableScopeBOSet()
                                .stream()
                                .filter(
                                        administrationMethodApplicableScopeBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                administrationMethodApplicableScopeBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        AdministrationMethodApplicableScopeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodApplicableScope());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectName")) {
                            bo.setObjectName(item.getObjectName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectId")) {
                            bo.setObjectId(item.getObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        AdministrationMethodApplicableScopeBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodApplicableScope());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectName")) {
                            bo.setObjectName(item.getObjectName());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "objectId")) {
                            bo.setObjectId(item.getObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    AdministrationMethodApplicableScopeBO subBo =
                            new AdministrationMethodApplicableScopeBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicableObjectType")) {
                        subBo.setApplicableObjectType(item.getApplicableObjectType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "objectName")) {
                        subBo.setObjectName(item.getObjectName());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "objectId")) {
                        subBo.setObjectId(item.getObjectId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setAdministrationMethodBO(administrationMethodBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "administration_method_applicable_scope")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    administrationMethodBO.getAdministrationMethodApplicableScopeBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:AdministrationMethodChargeItemBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createAdministrationMethodChargeItemBtoOnDuplicateUpdate(
            SaveAdministrationMethodBoResult boResult,
            SaveAdministrationMethodBto saveAdministrationMethodBto,
            AdministrationMethodBO administrationMethodBO) {
        if (CollectionUtil.isEmpty(
                saveAdministrationMethodBto.getAdministrationMethodChargeItemBtoList())) {
            saveAdministrationMethodBto.setAdministrationMethodChargeItemBtoList(List.of());
        }
        administrationMethodBO
                .getAdministrationMethodChargeItemBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveAdministrationMethodBto
                                            .getAdministrationMethodChargeItemBtoList()
                                            .stream()
                                            .filter(
                                                    administrationMethodChargeItemBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (administrationMethodChargeItemBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            administrationMethodChargeItemBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToAdministrationMethodChargeItem());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveAdministrationMethodBto.getAdministrationMethodChargeItemBtoList())) {
            for (SaveAdministrationMethodBto.AdministrationMethodChargeItemBto item :
                    saveAdministrationMethodBto.getAdministrationMethodChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<AdministrationMethodChargeItemBO> any =
                        administrationMethodBO.getAdministrationMethodChargeItemBOSet().stream()
                                .filter(
                                        administrationMethodChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                administrationMethodChargeItemBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        AdministrationMethodChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "continueFlag")) {
                            bo.setContinueFlag(item.getContinueFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpBillingMethod")) {
                            bo.setOutpBillingMethod(item.getOutpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpBillingMethod")) {
                            bo.setInpBillingMethod(item.getInpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stayRescueBillingMethod")) {
                            bo.setStayRescueBillingMethod(item.getStayRescueBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "erpBillingMethod")) {
                            bo.setErpBillingMethod(item.getErpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        AdministrationMethodChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "continueFlag")) {
                            bo.setContinueFlag(item.getContinueFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpBillingMethod")) {
                            bo.setOutpBillingMethod(item.getOutpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpBillingMethod")) {
                            bo.setInpBillingMethod(item.getInpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stayRescueBillingMethod")) {
                            bo.setStayRescueBillingMethod(item.getStayRescueBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "erpBillingMethod")) {
                            bo.setErpBillingMethod(item.getErpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    AdministrationMethodChargeItemBO subBo = new AdministrationMethodChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "count")) {
                        subBo.setCount(item.getCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "continueFlag")) {
                        subBo.setContinueFlag(item.getContinueFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "outpBillingMethod")) {
                        subBo.setOutpBillingMethod(item.getOutpBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inpBillingMethod")) {
                        subBo.setInpBillingMethod(item.getInpBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stayRescueBillingMethod")) {
                        subBo.setStayRescueBillingMethod(item.getStayRescueBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "erpBillingMethod")) {
                        subBo.setErpBillingMethod(item.getErpBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setAdministrationMethodBO(administrationMethodBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "administration_method_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    administrationMethodBO.getAdministrationMethodChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:AdministrationMethodChargeItemBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createAdministrationMethodChargeItemBtoOnDuplicateUpdate(
            SaveAdministrationMethodChargeItemListBoResult boResult,
            SaveAdministrationMethodChargeItemListBto saveAdministrationMethodChargeItemListBto,
            AdministrationMethodBO administrationMethodBO) {
        if (CollectionUtil.isEmpty(
                saveAdministrationMethodChargeItemListBto
                        .getAdministrationMethodChargeItemBtoList())) {
            saveAdministrationMethodChargeItemListBto.setAdministrationMethodChargeItemBtoList(
                    List.of());
        }
        administrationMethodBO
                .getAdministrationMethodChargeItemBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveAdministrationMethodChargeItemListBto
                                            .getAdministrationMethodChargeItemBtoList()
                                            .stream()
                                            .filter(
                                                    administrationMethodChargeItemBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (administrationMethodChargeItemBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            administrationMethodChargeItemBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToAdministrationMethodChargeItem());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveAdministrationMethodChargeItemListBto
                        .getAdministrationMethodChargeItemBtoList())) {
            for (SaveAdministrationMethodChargeItemListBto.AdministrationMethodChargeItemBto item :
                    saveAdministrationMethodChargeItemListBto
                            .getAdministrationMethodChargeItemBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<AdministrationMethodChargeItemBO> any =
                        administrationMethodBO.getAdministrationMethodChargeItemBOSet().stream()
                                .filter(
                                        administrationMethodChargeItemBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                administrationMethodChargeItemBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        AdministrationMethodChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "continueFlag")) {
                            bo.setContinueFlag(item.getContinueFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpBillingMethod")) {
                            bo.setOutpBillingMethod(item.getOutpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpBillingMethod")) {
                            bo.setInpBillingMethod(item.getInpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stayRescueBillingMethod")) {
                            bo.setStayRescueBillingMethod(item.getStayRescueBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "erpBillingMethod")) {
                            bo.setErpBillingMethod(item.getErpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        AdministrationMethodChargeItemBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToAdministrationMethodChargeItem());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "chargeItemId")) {
                            bo.setChargeItemId(item.getChargeItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "campusIdList")) {
                            bo.setCampusIdList(item.getCampusIdList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "continueFlag")) {
                            bo.setContinueFlag(item.getContinueFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "outpBillingMethod")) {
                            bo.setOutpBillingMethod(item.getOutpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpBillingMethod")) {
                            bo.setInpBillingMethod(item.getInpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "stayRescueBillingMethod")) {
                            bo.setStayRescueBillingMethod(item.getStayRescueBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "erpBillingMethod")) {
                            bo.setErpBillingMethod(item.getErpBillingMethod());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    AdministrationMethodChargeItemBO subBo = new AdministrationMethodChargeItemBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "chargeItemId")) {
                        subBo.setChargeItemId(item.getChargeItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "campusIdList")) {
                        subBo.setCampusIdList(item.getCampusIdList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "count")) {
                        subBo.setCount(item.getCount());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "continueFlag")) {
                        subBo.setContinueFlag(item.getContinueFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "outpBillingMethod")) {
                        subBo.setOutpBillingMethod(item.getOutpBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inpBillingMethod")) {
                        subBo.setInpBillingMethod(item.getInpBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "stayRescueBillingMethod")) {
                        subBo.setStayRescueBillingMethod(item.getStayRescueBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "erpBillingMethod")) {
                        subBo.setErpBillingMethod(item.getErpBillingMethod());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setAdministrationMethodBO(administrationMethodBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "administration_method_charge_item")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    administrationMethodBO.getAdministrationMethodChargeItemBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:AdministrationMethodDefaultPerformDepartmentBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createAdministrationMethodDefaultPerformDepartmentBtoOnDuplicateUpdate(
            SaveAdministrationMethodBoResult boResult,
            SaveAdministrationMethodBto saveAdministrationMethodBto,
            AdministrationMethodBO administrationMethodBO) {
        if (CollectionUtil.isEmpty(
                saveAdministrationMethodBto
                        .getAdministrationMethodDefaultPerformDepartmentBtoList())) {
            saveAdministrationMethodBto.setAdministrationMethodDefaultPerformDepartmentBtoList(
                    List.of());
        }
        administrationMethodBO
                .getAdministrationMethodDefaultPerformDepartmentBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveAdministrationMethodBto
                                            .getAdministrationMethodDefaultPerformDepartmentBtoList()
                                            .stream()
                                            .filter(
                                                    administrationMethodDefaultPerformDepartmentBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (administrationMethodDefaultPerformDepartmentBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            administrationMethodDefaultPerformDepartmentBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(
                                                item
                                                        .convertToAdministrationMethodDefaultPerformDepartment());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveAdministrationMethodBto
                        .getAdministrationMethodDefaultPerformDepartmentBtoList())) {
            for (SaveAdministrationMethodBto.AdministrationMethodDefaultPerformDepartmentBto item :
                    saveAdministrationMethodBto
                            .getAdministrationMethodDefaultPerformDepartmentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<AdministrationMethodDefaultPerformDepartmentBO> any =
                        administrationMethodBO
                                .getAdministrationMethodDefaultPerformDepartmentBOSet()
                                .stream()
                                .filter(
                                        administrationMethodDefaultPerformDepartmentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                administrationMethodDefaultPerformDepartmentBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        AdministrationMethodDefaultPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(
                                bo.convertToAdministrationMethodDefaultPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectId")) {
                            bo.setApplicableObjectId(item.getApplicableObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpDefaultPerformDepartmentType")) {
                            bo.setInpDefaultPerformDepartmentType(
                                    item.getInpDefaultPerformDepartmentType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        AdministrationMethodDefaultPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(
                                bo.convertToAdministrationMethodDefaultPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectId")) {
                            bo.setApplicableObjectId(item.getApplicableObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpDefaultPerformDepartmentType")) {
                            bo.setInpDefaultPerformDepartmentType(
                                    item.getInpDefaultPerformDepartmentType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    AdministrationMethodDefaultPerformDepartmentBO subBo =
                            new AdministrationMethodDefaultPerformDepartmentBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicableObjectType")) {
                        subBo.setApplicableObjectType(item.getApplicableObjectType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicableObjectId")) {
                        subBo.setApplicableObjectId(item.getApplicableObjectId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inpDefaultPerformDepartmentType")) {
                        subBo.setInpDefaultPerformDepartmentType(
                                item.getInpDefaultPerformDepartmentType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setAdministrationMethodBO(administrationMethodBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "administration_method_default_perform_department")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    administrationMethodBO
                            .getAdministrationMethodDefaultPerformDepartmentBOSet()
                            .add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:AdministrationMethodDefaultPerformDepartmentBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createAdministrationMethodDefaultPerformDepartmentBtoOnDuplicateUpdate(
            SaveAdministrationMethodDefaultPerformDepartmentListBoResult boResult,
            SaveAdministrationMethodDefaultPerformDepartmentListBto
                    saveAdministrationMethodDefaultPerformDepartmentListBto,
            AdministrationMethodBO administrationMethodBO) {
        if (CollectionUtil.isEmpty(
                saveAdministrationMethodDefaultPerformDepartmentListBto
                        .getAdministrationMethodDefaultPerformDepartmentBtoList())) {
            saveAdministrationMethodDefaultPerformDepartmentListBto
                    .setAdministrationMethodDefaultPerformDepartmentBtoList(List.of());
        }
        administrationMethodBO
                .getAdministrationMethodDefaultPerformDepartmentBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveAdministrationMethodDefaultPerformDepartmentListBto
                                            .getAdministrationMethodDefaultPerformDepartmentBtoList()
                                            .stream()
                                            .filter(
                                                    administrationMethodDefaultPerformDepartmentBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (administrationMethodDefaultPerformDepartmentBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            administrationMethodDefaultPerformDepartmentBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(
                                                item
                                                        .convertToAdministrationMethodDefaultPerformDepartment());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveAdministrationMethodDefaultPerformDepartmentListBto
                        .getAdministrationMethodDefaultPerformDepartmentBtoList())) {
            for (SaveAdministrationMethodDefaultPerformDepartmentListBto
                            .AdministrationMethodDefaultPerformDepartmentBto
                    item :
                            saveAdministrationMethodDefaultPerformDepartmentListBto
                                    .getAdministrationMethodDefaultPerformDepartmentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<AdministrationMethodDefaultPerformDepartmentBO> any =
                        administrationMethodBO
                                .getAdministrationMethodDefaultPerformDepartmentBOSet()
                                .stream()
                                .filter(
                                        administrationMethodDefaultPerformDepartmentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                administrationMethodDefaultPerformDepartmentBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        AdministrationMethodDefaultPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(
                                bo.convertToAdministrationMethodDefaultPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectId")) {
                            bo.setApplicableObjectId(item.getApplicableObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpDefaultPerformDepartmentType")) {
                            bo.setInpDefaultPerformDepartmentType(
                                    item.getInpDefaultPerformDepartmentType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    } else {
                        AdministrationMethodDefaultPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(
                                bo.convertToAdministrationMethodDefaultPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectType")) {
                            bo.setApplicableObjectType(item.getApplicableObjectType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "applicableObjectId")) {
                            bo.setApplicableObjectId(item.getApplicableObjectId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "useScopeList")) {
                            bo.setUseScopeList(item.getUseScopeList());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "inpDefaultPerformDepartmentType")) {
                            bo.setInpDefaultPerformDepartmentType(
                                    item.getInpDefaultPerformDepartmentType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                    }
                } else {
                    AdministrationMethodDefaultPerformDepartmentBO subBo =
                            new AdministrationMethodDefaultPerformDepartmentBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicableObjectType")) {
                        subBo.setApplicableObjectType(item.getApplicableObjectType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "applicableObjectId")) {
                        subBo.setApplicableObjectId(item.getApplicableObjectId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "useScopeList")) {
                        subBo.setUseScopeList(item.getUseScopeList());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "inpDefaultPerformDepartmentType")) {
                        subBo.setInpDefaultPerformDepartmentType(
                                item.getInpDefaultPerformDepartmentType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    subBo.setAdministrationMethodBO(administrationMethodBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(
                                        this.idGenerator.allocateId(
                                                "administration_method_default_perform_department")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    administrationMethodBO
                            .getAdministrationMethodDefaultPerformDepartmentBOSet()
                            .add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private AdministrationMethodBO createSaveAdministrationMethodOnDuplicateUpdate(
            SaveAdministrationMethodBoResult boResult,
            SaveAdministrationMethodBto saveAdministrationMethodBto) {
        AdministrationMethodBO administrationMethodBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveAdministrationMethodBto.getCode() == null);
        if (!allNull && !found) {
            administrationMethodBO =
                    AdministrationMethodBO.getByCode(saveAdministrationMethodBto.getCode());
            if (administrationMethodBO != null) {
                matchedUkName += "(";
                matchedUkName += "'code'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (administrationMethodBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(administrationMethodBO.convertToAdministrationMethod());
                updatedBto.setBto(saveAdministrationMethodBto);
                updatedBto.setBo(administrationMethodBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "name")) {
                    administrationMethodBO.setName(saveAdministrationMethodBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "inputCode")) {
                    administrationMethodBO.setInputCode(saveAdministrationMethodBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "printName")) {
                    administrationMethodBO.setPrintName(saveAdministrationMethodBto.getPrintName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "administrationMethodType")) {
                    administrationMethodBO.setAdministrationMethodType(
                            saveAdministrationMethodBto.getAdministrationMethodType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "dripSpeedUnit")) {
                    administrationMethodBO.setDripSpeedUnit(
                            saveAdministrationMethodBto.getDripSpeedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "drugRoundUpMethod")) {
                    administrationMethodBO.setDrugRoundUpMethod(
                            saveAdministrationMethodBto.getDrugRoundUpMethod());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "decoctionFeeChargeMethod")) {
                    administrationMethodBO.setDecoctionFeeChargeMethod(
                            saveAdministrationMethodBto.getDecoctionFeeChargeMethod());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "defaultPreMadeDecoctionDoseCount")) {
                    administrationMethodBO.setDefaultPreMadeDecoctionDoseCount(
                            saveAdministrationMethodBto.getDefaultPreMadeDecoctionDoseCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "applicablePrescriptionTypeList")) {
                    administrationMethodBO.setApplicablePrescriptionTypeList(
                            saveAdministrationMethodBto.getApplicablePrescriptionTypeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "printCardType")) {
                    administrationMethodBO.setPrintCardType(
                            saveAdministrationMethodBto.getPrintCardType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "outpUseDrugLimitDays")) {
                    administrationMethodBO.setOutpUseDrugLimitDays(
                            saveAdministrationMethodBto.getOutpUseDrugLimitDays());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "useScopeList")) {
                    administrationMethodBO.setUseScopeList(
                            saveAdministrationMethodBto.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "decoctionUseFlag")) {
                    administrationMethodBO.setDecoctionUseFlag(
                            saveAdministrationMethodBto.getDecoctionUseFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "venousDistributeFlag")) {
                    administrationMethodBO.setVenousDistributeFlag(
                            saveAdministrationMethodBto.getVenousDistributeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "veinTypeFlag")) {
                    administrationMethodBO.setVeinTypeFlag(
                            saveAdministrationMethodBto.getVeinTypeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "sortNumber")) {
                    administrationMethodBO.setSortNumber(
                            saveAdministrationMethodBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "orderType")) {
                    administrationMethodBO.setOrderType(saveAdministrationMethodBto.getOrderType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "markFlag")) {
                    administrationMethodBO.setMarkFlag(saveAdministrationMethodBto.getMarkFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "dayFlag")) {
                    administrationMethodBO.setDayFlag(saveAdministrationMethodBto.getDayFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "updatedBy")) {
                    administrationMethodBO.setUpdatedBy(saveAdministrationMethodBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "createdBy")) {
                    administrationMethodBO.setCreatedBy(saveAdministrationMethodBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "enableFlag")) {
                    administrationMethodBO.setEnableFlag(
                            saveAdministrationMethodBto.getEnableFlag());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(administrationMethodBO.convertToAdministrationMethod());
                updatedBto.setBto(saveAdministrationMethodBto);
                updatedBto.setBo(administrationMethodBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "name")) {
                    administrationMethodBO.setName(saveAdministrationMethodBto.getName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "inputCode")) {
                    administrationMethodBO.setInputCode(saveAdministrationMethodBto.getInputCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "printName")) {
                    administrationMethodBO.setPrintName(saveAdministrationMethodBto.getPrintName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "administrationMethodType")) {
                    administrationMethodBO.setAdministrationMethodType(
                            saveAdministrationMethodBto.getAdministrationMethodType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "dripSpeedUnit")) {
                    administrationMethodBO.setDripSpeedUnit(
                            saveAdministrationMethodBto.getDripSpeedUnit());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "drugRoundUpMethod")) {
                    administrationMethodBO.setDrugRoundUpMethod(
                            saveAdministrationMethodBto.getDrugRoundUpMethod());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "decoctionFeeChargeMethod")) {
                    administrationMethodBO.setDecoctionFeeChargeMethod(
                            saveAdministrationMethodBto.getDecoctionFeeChargeMethod());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "defaultPreMadeDecoctionDoseCount")) {
                    administrationMethodBO.setDefaultPreMadeDecoctionDoseCount(
                            saveAdministrationMethodBto.getDefaultPreMadeDecoctionDoseCount());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "applicablePrescriptionTypeList")) {
                    administrationMethodBO.setApplicablePrescriptionTypeList(
                            saveAdministrationMethodBto.getApplicablePrescriptionTypeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "printCardType")) {
                    administrationMethodBO.setPrintCardType(
                            saveAdministrationMethodBto.getPrintCardType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "outpUseDrugLimitDays")) {
                    administrationMethodBO.setOutpUseDrugLimitDays(
                            saveAdministrationMethodBto.getOutpUseDrugLimitDays());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "useScopeList")) {
                    administrationMethodBO.setUseScopeList(
                            saveAdministrationMethodBto.getUseScopeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "decoctionUseFlag")) {
                    administrationMethodBO.setDecoctionUseFlag(
                            saveAdministrationMethodBto.getDecoctionUseFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "venousDistributeFlag")) {
                    administrationMethodBO.setVenousDistributeFlag(
                            saveAdministrationMethodBto.getVenousDistributeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "veinTypeFlag")) {
                    administrationMethodBO.setVeinTypeFlag(
                            saveAdministrationMethodBto.getVeinTypeFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "sortNumber")) {
                    administrationMethodBO.setSortNumber(
                            saveAdministrationMethodBto.getSortNumber());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "orderType")) {
                    administrationMethodBO.setOrderType(saveAdministrationMethodBto.getOrderType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "markFlag")) {
                    administrationMethodBO.setMarkFlag(saveAdministrationMethodBto.getMarkFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "dayFlag")) {
                    administrationMethodBO.setDayFlag(saveAdministrationMethodBto.getDayFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "updatedBy")) {
                    administrationMethodBO.setUpdatedBy(saveAdministrationMethodBto.getUpdatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "createdBy")) {
                    administrationMethodBO.setCreatedBy(saveAdministrationMethodBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        saveAdministrationMethodBto, "__$validPropertySet"),
                        "enableFlag")) {
                    administrationMethodBO.setEnableFlag(
                            saveAdministrationMethodBto.getEnableFlag());
                }
            }
        } else {
            administrationMethodBO = new AdministrationMethodBO();
            if (pkExist) {
                administrationMethodBO.setCode(saveAdministrationMethodBto.getCode());
            } else {
                administrationMethodBO.setCode(
                        String.valueOf(this.idGenerator.allocateId("administration_method")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "name")) {
                administrationMethodBO.setName(saveAdministrationMethodBto.getName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "inputCode")) {
                administrationMethodBO.setInputCode(saveAdministrationMethodBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "printName")) {
                administrationMethodBO.setPrintName(saveAdministrationMethodBto.getPrintName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "administrationMethodType")) {
                administrationMethodBO.setAdministrationMethodType(
                        saveAdministrationMethodBto.getAdministrationMethodType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "dripSpeedUnit")) {
                administrationMethodBO.setDripSpeedUnit(
                        saveAdministrationMethodBto.getDripSpeedUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "drugRoundUpMethod")) {
                administrationMethodBO.setDrugRoundUpMethod(
                        saveAdministrationMethodBto.getDrugRoundUpMethod());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "decoctionFeeChargeMethod")) {
                administrationMethodBO.setDecoctionFeeChargeMethod(
                        saveAdministrationMethodBto.getDecoctionFeeChargeMethod());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "defaultPreMadeDecoctionDoseCount")) {
                administrationMethodBO.setDefaultPreMadeDecoctionDoseCount(
                        saveAdministrationMethodBto.getDefaultPreMadeDecoctionDoseCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "applicablePrescriptionTypeList")) {
                administrationMethodBO.setApplicablePrescriptionTypeList(
                        saveAdministrationMethodBto.getApplicablePrescriptionTypeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "printCardType")) {
                administrationMethodBO.setPrintCardType(
                        saveAdministrationMethodBto.getPrintCardType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "outpUseDrugLimitDays")) {
                administrationMethodBO.setOutpUseDrugLimitDays(
                        saveAdministrationMethodBto.getOutpUseDrugLimitDays());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "useScopeList")) {
                administrationMethodBO.setUseScopeList(
                        saveAdministrationMethodBto.getUseScopeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "decoctionUseFlag")) {
                administrationMethodBO.setDecoctionUseFlag(
                        saveAdministrationMethodBto.getDecoctionUseFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "venousDistributeFlag")) {
                administrationMethodBO.setVenousDistributeFlag(
                        saveAdministrationMethodBto.getVenousDistributeFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "veinTypeFlag")) {
                administrationMethodBO.setVeinTypeFlag(
                        saveAdministrationMethodBto.getVeinTypeFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "sortNumber")) {
                administrationMethodBO.setSortNumber(saveAdministrationMethodBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "orderType")) {
                administrationMethodBO.setOrderType(saveAdministrationMethodBto.getOrderType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "markFlag")) {
                administrationMethodBO.setMarkFlag(saveAdministrationMethodBto.getMarkFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "dayFlag")) {
                administrationMethodBO.setDayFlag(saveAdministrationMethodBto.getDayFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "updatedBy")) {
                administrationMethodBO.setUpdatedBy(saveAdministrationMethodBto.getUpdatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "createdBy")) {
                administrationMethodBO.setCreatedBy(saveAdministrationMethodBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "enableFlag")) {
                administrationMethodBO.setEnableFlag(saveAdministrationMethodBto.getEnableFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveAdministrationMethodBto);
            addedBto.setBo(administrationMethodBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return administrationMethodBO;
    }

    /** 保存给药方式适用范围列表 */
    @AutoGenerated(locked = true)
    protected SaveAdministrationMethodApplicableScopeListBoResult
            saveAdministrationMethodApplicableScopeListBase(
                    SaveAdministrationMethodApplicableScopeListBto
                            saveAdministrationMethodApplicableScopeListBto) {
        if (saveAdministrationMethodApplicableScopeListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveAdministrationMethodApplicableScopeListBoResult boResult =
                new SaveAdministrationMethodApplicableScopeListBoResult();
        AdministrationMethodBO administrationMethodBO =
                updateSaveAdministrationMethodApplicableScopeListOnMissThrowEx(
                        boResult, saveAdministrationMethodApplicableScopeListBto);
        if (administrationMethodBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodApplicableScopeListBto,
                                    "__$validPropertySet"),
                    "administrationMethodApplicableScopeBtoList")) {
                createAdministrationMethodApplicableScopeBtoOnDuplicateUpdate(
                        boResult,
                        saveAdministrationMethodApplicableScopeListBto,
                        administrationMethodBO);
            }
        }
        boResult.setRootBo(administrationMethodBO);
        return boResult;
    }

    /** 保存给药方式 */
    @AutoGenerated(locked = true)
    protected SaveAdministrationMethodBoResult saveAdministrationMethodBase(
            SaveAdministrationMethodBto saveAdministrationMethodBto) {
        if (saveAdministrationMethodBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveAdministrationMethodBoResult boResult = new SaveAdministrationMethodBoResult();
        AdministrationMethodBO administrationMethodBO =
                createSaveAdministrationMethodOnDuplicateUpdate(
                        boResult, saveAdministrationMethodBto);
        if (administrationMethodBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "administrationMethodApplicableScopeBtoList")) {
                createAdministrationMethodApplicableScopeBtoOnDuplicateUpdate(
                        boResult, saveAdministrationMethodBto, administrationMethodBO);
            }
        }
        if (administrationMethodBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "administrationMethodChargeItemBtoList")) {
                createAdministrationMethodChargeItemBtoOnDuplicateUpdate(
                        boResult, saveAdministrationMethodBto, administrationMethodBO);
            }
        }
        if (administrationMethodBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodBto, "__$validPropertySet"),
                    "administrationMethodDefaultPerformDepartmentBtoList")) {
                createAdministrationMethodDefaultPerformDepartmentBtoOnDuplicateUpdate(
                        boResult, saveAdministrationMethodBto, administrationMethodBO);
            }
        }
        boResult.setRootBo(administrationMethodBO);
        return boResult;
    }

    /** 保存给药方式收费项目列表 */
    @AutoGenerated(locked = true)
    protected SaveAdministrationMethodChargeItemListBoResult
            saveAdministrationMethodChargeItemListBase(
                    SaveAdministrationMethodChargeItemListBto
                            saveAdministrationMethodChargeItemListBto) {
        if (saveAdministrationMethodChargeItemListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveAdministrationMethodChargeItemListBoResult boResult =
                new SaveAdministrationMethodChargeItemListBoResult();
        AdministrationMethodBO administrationMethodBO =
                updateSaveAdministrationMethodChargeItemListOnMissThrowEx(
                        boResult, saveAdministrationMethodChargeItemListBto);
        if (administrationMethodBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodChargeItemListBto,
                                    "__$validPropertySet"),
                    "administrationMethodChargeItemBtoList")) {
                createAdministrationMethodChargeItemBtoOnDuplicateUpdate(
                        boResult,
                        saveAdministrationMethodChargeItemListBto,
                        administrationMethodBO);
            }
        }
        boResult.setRootBo(administrationMethodBO);
        return boResult;
    }

    /** 保存给药方式默认执行科室列表 */
    @AutoGenerated(locked = true)
    protected SaveAdministrationMethodDefaultPerformDepartmentListBoResult
            saveAdministrationMethodDefaultPerformDepartmentListBase(
                    SaveAdministrationMethodDefaultPerformDepartmentListBto
                            saveAdministrationMethodDefaultPerformDepartmentListBto) {
        if (saveAdministrationMethodDefaultPerformDepartmentListBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveAdministrationMethodDefaultPerformDepartmentListBoResult boResult =
                new SaveAdministrationMethodDefaultPerformDepartmentListBoResult();
        AdministrationMethodBO administrationMethodBO =
                updateSaveAdministrationMethodDefaultPerformDepartmentListOnMissThrowEx(
                        boResult, saveAdministrationMethodDefaultPerformDepartmentListBto);
        if (administrationMethodBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveAdministrationMethodDefaultPerformDepartmentListBto,
                                    "__$validPropertySet"),
                    "administrationMethodDefaultPerformDepartmentBtoList")) {
                createAdministrationMethodDefaultPerformDepartmentBtoOnDuplicateUpdate(
                        boResult,
                        saveAdministrationMethodDefaultPerformDepartmentListBto,
                        administrationMethodBO);
            }
        }
        boResult.setRootBo(administrationMethodBO);
        return boResult;
    }

    /** 更新对象:changeAdministrationMethodEnableFlag,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private AdministrationMethodBO updateChangeAdministrationMethodEnableFlagOnMissThrowEx(
            BaseAdministrationMethodBOService.ChangeAdministrationMethodEnableFlagBoResult boResult,
            ChangeAdministrationMethodEnableFlagBto changeAdministrationMethodEnableFlagBto) {
        AdministrationMethodBO administrationMethodBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (changeAdministrationMethodEnableFlagBto.getCode() == null);
        if (!allNull && !found) {
            administrationMethodBO =
                    AdministrationMethodBO.getByCode(
                            changeAdministrationMethodEnableFlagBto.getCode());
            found = true;
        }
        if (administrationMethodBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(administrationMethodBO.convertToAdministrationMethod());
            updatedBto.setBto(changeAdministrationMethodEnableFlagBto);
            updatedBto.setBo(administrationMethodBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeAdministrationMethodEnableFlagBto, "__$validPropertySet"),
                    "enableFlag")) {
                administrationMethodBO.setEnableFlag(
                        changeAdministrationMethodEnableFlagBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    changeAdministrationMethodEnableFlagBto, "__$validPropertySet"),
                    "updatedBy")) {
                administrationMethodBO.setUpdatedBy(
                        changeAdministrationMethodEnableFlagBto.getUpdatedBy());
            }
            return administrationMethodBO;
        }
    }

    /** 更新对象:saveAdministrationMethodApplicableScopeList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private AdministrationMethodBO updateSaveAdministrationMethodApplicableScopeListOnMissThrowEx(
            BaseAdministrationMethodBOService.SaveAdministrationMethodApplicableScopeListBoResult
                    boResult,
            SaveAdministrationMethodApplicableScopeListBto
                    saveAdministrationMethodApplicableScopeListBto) {
        AdministrationMethodBO administrationMethodBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveAdministrationMethodApplicableScopeListBto.getCode() == null);
        if (!allNull && !found) {
            administrationMethodBO =
                    AdministrationMethodBO.getByCode(
                            saveAdministrationMethodApplicableScopeListBto.getCode());
            found = true;
        }
        if (administrationMethodBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(administrationMethodBO.convertToAdministrationMethod());
            updatedBto.setBto(saveAdministrationMethodApplicableScopeListBto);
            updatedBto.setBo(administrationMethodBO);
            boResult.getUpdatedList().add(updatedBto);
            return administrationMethodBO;
        }
    }

    /** 更新对象:saveAdministrationMethodChargeItemList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private AdministrationMethodBO updateSaveAdministrationMethodChargeItemListOnMissThrowEx(
            BaseAdministrationMethodBOService.SaveAdministrationMethodChargeItemListBoResult
                    boResult,
            SaveAdministrationMethodChargeItemListBto saveAdministrationMethodChargeItemListBto) {
        AdministrationMethodBO administrationMethodBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveAdministrationMethodChargeItemListBto.getCode() == null);
        if (!allNull && !found) {
            administrationMethodBO =
                    AdministrationMethodBO.getByCode(
                            saveAdministrationMethodChargeItemListBto.getCode());
            found = true;
        }
        if (administrationMethodBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(administrationMethodBO.convertToAdministrationMethod());
            updatedBto.setBto(saveAdministrationMethodChargeItemListBto);
            updatedBto.setBo(administrationMethodBO);
            boResult.getUpdatedList().add(updatedBto);
            return administrationMethodBO;
        }
    }

    /** 更新对象:saveAdministrationMethodDefaultPerformDepartmentList,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private AdministrationMethodBO
            updateSaveAdministrationMethodDefaultPerformDepartmentListOnMissThrowEx(
                    BaseAdministrationMethodBOService
                                    .SaveAdministrationMethodDefaultPerformDepartmentListBoResult
                            boResult,
                    SaveAdministrationMethodDefaultPerformDepartmentListBto
                            saveAdministrationMethodDefaultPerformDepartmentListBto) {
        AdministrationMethodBO administrationMethodBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (saveAdministrationMethodDefaultPerformDepartmentListBto.getCode() == null);
        if (!allNull && !found) {
            administrationMethodBO =
                    AdministrationMethodBO.getByCode(
                            saveAdministrationMethodDefaultPerformDepartmentListBto.getCode());
            found = true;
        }
        if (administrationMethodBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(administrationMethodBO.convertToAdministrationMethod());
            updatedBto.setBto(saveAdministrationMethodDefaultPerformDepartmentListBto);
            updatedBto.setBo(administrationMethodBO);
            boResult.getUpdatedList().add(updatedBto);
            return administrationMethodBO;
        }
    }

    public static class SaveAdministrationMethodBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public AdministrationMethodBO getRootBo() {
            return (AdministrationMethodBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveAdministrationMethodBto, AdministrationMethodBO> getCreatedBto(
                SaveAdministrationMethodBto saveAdministrationMethodBto) {
            return this.getAddedResult(saveAdministrationMethodBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveAdministrationMethodBto.AdministrationMethodApplicableScopeBto,
                        AdministrationMethodApplicableScopeBO>
                getCreatedBto(
                        SaveAdministrationMethodBto.AdministrationMethodApplicableScopeBto
                                administrationMethodApplicableScopeBto) {
            return this.getAddedResult(administrationMethodApplicableScopeBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveAdministrationMethodBto.AdministrationMethodChargeItemBto,
                        AdministrationMethodChargeItemBO>
                getCreatedBto(
                        SaveAdministrationMethodBto.AdministrationMethodChargeItemBto
                                administrationMethodChargeItemBto) {
            return this.getAddedResult(administrationMethodChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveAdministrationMethodBto.AdministrationMethodDefaultPerformDepartmentBto,
                        AdministrationMethodDefaultPerformDepartmentBO>
                getCreatedBto(
                        SaveAdministrationMethodBto.AdministrationMethodDefaultPerformDepartmentBto
                                administrationMethodDefaultPerformDepartmentBto) {
            return this.getAddedResult(administrationMethodDefaultPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AdministrationMethod getDeleted_AdministrationMethod() {
            return (AdministrationMethod)
                    CollectionUtil.getFirst(this.getDeletedEntityList(AdministrationMethod.class));
        }

        @AutoGenerated(locked = true)
        public AdministrationMethodApplicableScope
                getDeleted_AdministrationMethodApplicableScope() {
            return (AdministrationMethodApplicableScope)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(AdministrationMethodApplicableScope.class));
        }

        @AutoGenerated(locked = true)
        public AdministrationMethodChargeItem getDeleted_AdministrationMethodChargeItem() {
            return (AdministrationMethodChargeItem)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(AdministrationMethodChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public AdministrationMethodDefaultPerformDepartment
                getDeleted_AdministrationMethodDefaultPerformDepartment() {
            return (AdministrationMethodDefaultPerformDepartment)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(
                                    AdministrationMethodDefaultPerformDepartment.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveAdministrationMethodBto, AdministrationMethod, AdministrationMethodBO>
                getUpdatedBto(SaveAdministrationMethodBto saveAdministrationMethodBto) {
            return super.getUpdatedResult(saveAdministrationMethodBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodBto.AdministrationMethodApplicableScopeBto,
                        AdministrationMethodApplicableScope,
                        AdministrationMethodApplicableScopeBO>
                getUpdatedBto(
                        SaveAdministrationMethodBto.AdministrationMethodApplicableScopeBto
                                administrationMethodApplicableScopeBto) {
            return super.getUpdatedResult(administrationMethodApplicableScopeBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodBto.AdministrationMethodChargeItemBto,
                        AdministrationMethodChargeItem,
                        AdministrationMethodChargeItemBO>
                getUpdatedBto(
                        SaveAdministrationMethodBto.AdministrationMethodChargeItemBto
                                administrationMethodChargeItemBto) {
            return super.getUpdatedResult(administrationMethodChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodBto.AdministrationMethodDefaultPerformDepartmentBto,
                        AdministrationMethodDefaultPerformDepartment,
                        AdministrationMethodDefaultPerformDepartmentBO>
                getUpdatedBto(
                        SaveAdministrationMethodBto.AdministrationMethodDefaultPerformDepartmentBto
                                administrationMethodDefaultPerformDepartmentBto) {
            return super.getUpdatedResult(administrationMethodDefaultPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveAdministrationMethodBto, AdministrationMethodBO> getUnmodifiedBto(
                SaveAdministrationMethodBto saveAdministrationMethodBto) {
            return super.getUnmodifiedResult(saveAdministrationMethodBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveAdministrationMethodBto.AdministrationMethodApplicableScopeBto,
                        AdministrationMethodApplicableScopeBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodBto.AdministrationMethodApplicableScopeBto
                                administrationMethodApplicableScopeBto) {
            return super.getUnmodifiedResult(administrationMethodApplicableScopeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveAdministrationMethodBto.AdministrationMethodChargeItemBto,
                        AdministrationMethodChargeItemBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodBto.AdministrationMethodChargeItemBto
                                administrationMethodChargeItemBto) {
            return super.getUnmodifiedResult(administrationMethodChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveAdministrationMethodBto.AdministrationMethodDefaultPerformDepartmentBto,
                        AdministrationMethodDefaultPerformDepartmentBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodBto.AdministrationMethodDefaultPerformDepartmentBto
                                administrationMethodDefaultPerformDepartmentBto) {
            return super.getUnmodifiedResult(administrationMethodDefaultPerformDepartmentBto);
        }
    }

    public static class SaveAdministrationMethodChargeItemListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public AdministrationMethodBO getRootBo() {
            return (AdministrationMethodBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveAdministrationMethodChargeItemListBto.AdministrationMethodChargeItemBto,
                        AdministrationMethodChargeItemBO>
                getCreatedBto(
                        SaveAdministrationMethodChargeItemListBto.AdministrationMethodChargeItemBto
                                administrationMethodChargeItemBto) {
            return this.getAddedResult(administrationMethodChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveAdministrationMethodChargeItemListBto, AdministrationMethodBO>
                getCreatedBto(
                        SaveAdministrationMethodChargeItemListBto
                                saveAdministrationMethodChargeItemListBto) {
            return this.getAddedResult(saveAdministrationMethodChargeItemListBto);
        }

        @AutoGenerated(locked = true)
        public AdministrationMethodChargeItem getDeleted_AdministrationMethodChargeItem() {
            return (AdministrationMethodChargeItem)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(AdministrationMethodChargeItem.class));
        }

        @AutoGenerated(locked = true)
        public AdministrationMethod getDeleted_AdministrationMethod() {
            return (AdministrationMethod)
                    CollectionUtil.getFirst(this.getDeletedEntityList(AdministrationMethod.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodChargeItemListBto.AdministrationMethodChargeItemBto,
                        AdministrationMethodChargeItem,
                        AdministrationMethodChargeItemBO>
                getUpdatedBto(
                        SaveAdministrationMethodChargeItemListBto.AdministrationMethodChargeItemBto
                                administrationMethodChargeItemBto) {
            return super.getUpdatedResult(administrationMethodChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodChargeItemListBto,
                        AdministrationMethod,
                        AdministrationMethodBO>
                getUpdatedBto(
                        SaveAdministrationMethodChargeItemListBto
                                saveAdministrationMethodChargeItemListBto) {
            return super.getUpdatedResult(saveAdministrationMethodChargeItemListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveAdministrationMethodChargeItemListBto.AdministrationMethodChargeItemBto,
                        AdministrationMethodChargeItemBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodChargeItemListBto.AdministrationMethodChargeItemBto
                                administrationMethodChargeItemBto) {
            return super.getUnmodifiedResult(administrationMethodChargeItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveAdministrationMethodChargeItemListBto, AdministrationMethodBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodChargeItemListBto
                                saveAdministrationMethodChargeItemListBto) {
            return super.getUnmodifiedResult(saveAdministrationMethodChargeItemListBto);
        }
    }

    public static class SaveAdministrationMethodApplicableScopeListBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public AdministrationMethodBO getRootBo() {
            return (AdministrationMethodBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveAdministrationMethodApplicableScopeListBto
                                .AdministrationMethodApplicableScopeBto,
                        AdministrationMethodApplicableScopeBO>
                getCreatedBto(
                        SaveAdministrationMethodApplicableScopeListBto
                                        .AdministrationMethodApplicableScopeBto
                                administrationMethodApplicableScopeBto) {
            return this.getAddedResult(administrationMethodApplicableScopeBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveAdministrationMethodApplicableScopeListBto, AdministrationMethodBO>
                getCreatedBto(
                        SaveAdministrationMethodApplicableScopeListBto
                                saveAdministrationMethodApplicableScopeListBto) {
            return this.getAddedResult(saveAdministrationMethodApplicableScopeListBto);
        }

        @AutoGenerated(locked = true)
        public AdministrationMethodApplicableScope
                getDeleted_AdministrationMethodApplicableScope() {
            return (AdministrationMethodApplicableScope)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(AdministrationMethodApplicableScope.class));
        }

        @AutoGenerated(locked = true)
        public AdministrationMethod getDeleted_AdministrationMethod() {
            return (AdministrationMethod)
                    CollectionUtil.getFirst(this.getDeletedEntityList(AdministrationMethod.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodApplicableScopeListBto
                                .AdministrationMethodApplicableScopeBto,
                        AdministrationMethodApplicableScope,
                        AdministrationMethodApplicableScopeBO>
                getUpdatedBto(
                        SaveAdministrationMethodApplicableScopeListBto
                                        .AdministrationMethodApplicableScopeBto
                                administrationMethodApplicableScopeBto) {
            return super.getUpdatedResult(administrationMethodApplicableScopeBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodApplicableScopeListBto,
                        AdministrationMethod,
                        AdministrationMethodBO>
                getUpdatedBto(
                        SaveAdministrationMethodApplicableScopeListBto
                                saveAdministrationMethodApplicableScopeListBto) {
            return super.getUpdatedResult(saveAdministrationMethodApplicableScopeListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveAdministrationMethodApplicableScopeListBto
                                .AdministrationMethodApplicableScopeBto,
                        AdministrationMethodApplicableScopeBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodApplicableScopeListBto
                                        .AdministrationMethodApplicableScopeBto
                                administrationMethodApplicableScopeBto) {
            return super.getUnmodifiedResult(administrationMethodApplicableScopeBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveAdministrationMethodApplicableScopeListBto, AdministrationMethodBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodApplicableScopeListBto
                                saveAdministrationMethodApplicableScopeListBto) {
            return super.getUnmodifiedResult(saveAdministrationMethodApplicableScopeListBto);
        }
    }

    public static class SaveAdministrationMethodDefaultPerformDepartmentListBoResult
            extends BaseBoResult {

        @AutoGenerated(locked = true)
        public AdministrationMethodBO getRootBo() {
            return (AdministrationMethodBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                .AdministrationMethodDefaultPerformDepartmentBto,
                        AdministrationMethodDefaultPerformDepartmentBO>
                getCreatedBto(
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                        .AdministrationMethodDefaultPerformDepartmentBto
                                administrationMethodDefaultPerformDepartmentBto) {
            return this.getAddedResult(administrationMethodDefaultPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveAdministrationMethodDefaultPerformDepartmentListBto,
                        AdministrationMethodBO>
                getCreatedBto(
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                saveAdministrationMethodDefaultPerformDepartmentListBto) {
            return this.getAddedResult(saveAdministrationMethodDefaultPerformDepartmentListBto);
        }

        @AutoGenerated(locked = true)
        public AdministrationMethodDefaultPerformDepartment
                getDeleted_AdministrationMethodDefaultPerformDepartment() {
            return (AdministrationMethodDefaultPerformDepartment)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(
                                    AdministrationMethodDefaultPerformDepartment.class));
        }

        @AutoGenerated(locked = true)
        public AdministrationMethod getDeleted_AdministrationMethod() {
            return (AdministrationMethod)
                    CollectionUtil.getFirst(this.getDeletedEntityList(AdministrationMethod.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                .AdministrationMethodDefaultPerformDepartmentBto,
                        AdministrationMethodDefaultPerformDepartment,
                        AdministrationMethodDefaultPerformDepartmentBO>
                getUpdatedBto(
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                        .AdministrationMethodDefaultPerformDepartmentBto
                                administrationMethodDefaultPerformDepartmentBto) {
            return super.getUpdatedResult(administrationMethodDefaultPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveAdministrationMethodDefaultPerformDepartmentListBto,
                        AdministrationMethod,
                        AdministrationMethodBO>
                getUpdatedBto(
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                saveAdministrationMethodDefaultPerformDepartmentListBto) {
            return super.getUpdatedResult(saveAdministrationMethodDefaultPerformDepartmentListBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                .AdministrationMethodDefaultPerformDepartmentBto,
                        AdministrationMethodDefaultPerformDepartmentBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                        .AdministrationMethodDefaultPerformDepartmentBto
                                administrationMethodDefaultPerformDepartmentBto) {
            return super.getUnmodifiedResult(administrationMethodDefaultPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveAdministrationMethodDefaultPerformDepartmentListBto,
                        AdministrationMethodBO>
                getUnmodifiedBto(
                        SaveAdministrationMethodDefaultPerformDepartmentListBto
                                saveAdministrationMethodDefaultPerformDepartmentListBto) {
            return super.getUnmodifiedResult(
                    saveAdministrationMethodDefaultPerformDepartmentListBto);
        }
    }

    public static class ChangeAdministrationMethodEnableFlagBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public AdministrationMethodBO getRootBo() {
            return (AdministrationMethodBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<ChangeAdministrationMethodEnableFlagBto, AdministrationMethodBO>
                getCreatedBto(
                        ChangeAdministrationMethodEnableFlagBto
                                changeAdministrationMethodEnableFlagBto) {
            return this.getAddedResult(changeAdministrationMethodEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public AdministrationMethod getDeleted_AdministrationMethod() {
            return (AdministrationMethod)
                    CollectionUtil.getFirst(this.getDeletedEntityList(AdministrationMethod.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        ChangeAdministrationMethodEnableFlagBto,
                        AdministrationMethod,
                        AdministrationMethodBO>
                getUpdatedBto(
                        ChangeAdministrationMethodEnableFlagBto
                                changeAdministrationMethodEnableFlagBto) {
            return super.getUpdatedResult(changeAdministrationMethodEnableFlagBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<ChangeAdministrationMethodEnableFlagBto, AdministrationMethodBO>
                getUnmodifiedBto(
                        ChangeAdministrationMethodEnableFlagBto
                                changeAdministrationMethodEnableFlagBto) {
            return super.getUnmodifiedResult(changeAdministrationMethodEnableFlagBto);
        }
    }
}
