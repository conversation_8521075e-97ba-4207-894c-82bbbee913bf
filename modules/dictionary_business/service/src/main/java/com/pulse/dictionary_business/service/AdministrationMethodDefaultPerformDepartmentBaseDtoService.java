package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.AdministrationMethodDefaultPerformDepartmentBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.AdministrationMethodDefaultPerformDepartmentBaseDto;
import com.pulse.dictionary_business.service.converter.AdministrationMethodDefaultPerformDepartmentBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "16a6866e-ca10-4341-b66c-5d30247e532e|DTO|SERVICE")
public class AdministrationMethodDefaultPerformDepartmentBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodDefaultPerformDepartmentBaseDtoManager
            administrationMethodDefaultPerformDepartmentBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private AdministrationMethodDefaultPerformDepartmentBaseDtoServiceConverter
            administrationMethodDefaultPerformDepartmentBaseDtoServiceConverter;

    @PublicInterface(id = "8009c726-d164-4887-843b-306915dca468", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "5e65f825-b0dc-325a-833b-ba5289c311b1")
    public List<AdministrationMethodDefaultPerformDepartmentBaseDto> getByAdministrationMethodId(
            @NotNull(message = "给药方式ID不能为空") String administrationMethodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByAdministrationMethodIds(Arrays.asList(administrationMethodId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1db2efd4-b061-4ad9-ab53-c5098447049c", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "5e8f9cec-02db-3d4d-a339-41ed46f3d9de")
    public List<AdministrationMethodDefaultPerformDepartmentBaseDto> getByAdministrationMethodIds(
            @Valid @NotNull(message = "给药方式ID不能为空") List<String> administrationMethodId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        administrationMethodId = new ArrayList<>(new HashSet<>(administrationMethodId));
        List<AdministrationMethodDefaultPerformDepartmentBaseDto>
                administrationMethodDefaultPerformDepartmentBaseDtoList =
                        administrationMethodDefaultPerformDepartmentBaseDtoManager
                                .getByAdministrationMethodIds(administrationMethodId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return administrationMethodDefaultPerformDepartmentBaseDtoServiceConverter
                .AdministrationMethodDefaultPerformDepartmentBaseDtoConverter(
                        administrationMethodDefaultPerformDepartmentBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0ed86f8a-2e4f-4f35-956f-82dc525b5f55", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "8651e7e8-3f8d-3565-b254-2c5528d3f862")
    public List<AdministrationMethodDefaultPerformDepartmentBaseDto> getByPerformDepartmentId(
            @NotNull(message = "执行科室ID不能为空") String performDepartmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByPerformDepartmentIds(Arrays.asList(performDepartmentId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "32c21456-0631-40a0-abe6-94b61d0b6ad6", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "b3f7c4bc-c427-39a9-b62b-4f6c3bfbc86f")
    public AdministrationMethodDefaultPerformDepartmentBaseDto getById(
            @NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<AdministrationMethodDefaultPerformDepartmentBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "263f9295-5b5b-4cde-ad07-5e6350431a3f", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "cd710cd1-f3aa-3795-b2dd-9c8fde302bdb")
    public List<AdministrationMethodDefaultPerformDepartmentBaseDto> getByPerformDepartmentIds(
            @Valid @NotNull(message = "执行科室ID不能为空") List<String> performDepartmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        performDepartmentId = new ArrayList<>(new HashSet<>(performDepartmentId));
        List<AdministrationMethodDefaultPerformDepartmentBaseDto>
                administrationMethodDefaultPerformDepartmentBaseDtoList =
                        administrationMethodDefaultPerformDepartmentBaseDtoManager
                                .getByPerformDepartmentIds(performDepartmentId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return administrationMethodDefaultPerformDepartmentBaseDtoServiceConverter
                .AdministrationMethodDefaultPerformDepartmentBaseDtoConverter(
                        administrationMethodDefaultPerformDepartmentBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "640681ac-db28-4537-b4e3-bfa5b7835f85", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "ff94cf24-8870-3a9f-9393-405308a83638")
    public List<AdministrationMethodDefaultPerformDepartmentBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<AdministrationMethodDefaultPerformDepartmentBaseDto>
                administrationMethodDefaultPerformDepartmentBaseDtoList =
                        administrationMethodDefaultPerformDepartmentBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return administrationMethodDefaultPerformDepartmentBaseDtoServiceConverter
                .AdministrationMethodDefaultPerformDepartmentBaseDtoConverter(
                        administrationMethodDefaultPerformDepartmentBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
