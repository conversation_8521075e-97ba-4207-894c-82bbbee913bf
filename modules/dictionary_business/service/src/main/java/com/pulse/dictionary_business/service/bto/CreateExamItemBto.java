package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.service.bto.CreateExamItemBto.ExamItemDocumentTemplateBto;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamItem
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "9b73ebe6-e3ce-4d56-9d3b-ee3810fa6a1a|BTO|DEFINITION")
public class CreateExamItemBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 年龄上限 */
    @AutoGenerated(locked = true, uuid = "21bf551d-cfb7-414b-80af-fe8382273e66")
    private Long ageMaxLimit;

    @AutoGenerated(locked = true, uuid = "e1b7ae7c-4e91-4fb7-a4d3-5208f6058b91")
    private Long ageMinLimit;

    /** 可开单员工ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "3b6f33e2-a894-460f-93b9-35fb25343e13")
    private List<String> allowedStaffIdList;

    /** 预约类型 */
    @AutoGenerated(locked = true, uuid = "8b27d5f7-a6c3-47d9-a575-f33657e33225")
    private String appointType;

    /** 是否床边检查 */
    @AutoGenerated(locked = true, uuid = "55c5507d-6d26-4632-918b-919e8473014a")
    private Boolean bedsideFlag;

    /** 开展院区 */
    @Valid
    @AutoGenerated(locked = true, uuid = "8d6b1566-19d7-4e10-a256-a9a133d7cc11")
    private List<String> campusIdList;

    /** 项目ID */
    @AutoGenerated(locked = true, uuid = "f5109c69-7450-434b-a93c-0f12b1dd5362")
    private String clinicItemCode;

    /** 诊疗项目ID */
    @AutoGenerated(locked = true, uuid = "5bf24c55-2540-4c75-8dc8-3846216e1afa")
    private String clinicItemId;

    /** 项目名称 */
    @AutoGenerated(locked = true, uuid = "8a9ae4d6-075a-471e-a01b-6d2d19b926ef")
    private String clinicItemName;

    /** 是否空腹 */
    @AutoGenerated(locked = true, uuid = "1c700c06-6149-4bb7-8e19-520f767ba7fe")
    private Boolean emptyStomachFlag;

    /** 增强扫描标志 */
    @AutoGenerated(locked = true, uuid = "5ddb3029-aa51-4bf1-b9cb-48064fbfc613")
    private Boolean enhancedScanningFlag;

    /** 直接关联的分类ID */
    @AutoGenerated(locked = true, uuid = "da85b6c5-5745-4bd7-8f2a-10c49ef1b9d0")
    private String examCatalogId;

    /** 检查方向 */
    @Valid
    @AutoGenerated(locked = true, uuid = "21def5ce-4e7d-4dfb-b209-ef924d10f220")
    private List<String> examDirectionList;

    @Valid
    @AutoGenerated(locked = true, uuid = "9a2dd10f-7ba0-4a8f-a388-b2c2cdfbe071")
    private List<CreateExamItemBto.ExamItemBodyBto> examItemBodyBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "5d3da79e-fe36-4c39-84f6-f52d7b761c6b")
    private List<CreateExamItemBto.ExamItemDeviceBto> examItemDeviceBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "926bf4b1-481b-402d-aa05-7f2ef22f5d12")
    private ExamItemDocumentTemplateBto examItemDocumentTemplateBto;

    @Valid
    @AutoGenerated(locked = true, uuid = "b05e3702-9ba4-4bc9-9b52-e8580bb552fb")
    private List<CreateExamItemBto.ExamItemDrugBto> examItemDrugBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "e072bc84-776b-4213-9e75-628ec243b672")
    private List<CreateExamItemBto.ExamItemExtensionBto> examItemExtensionBtoList;

    @Valid
    @AutoGenerated(locked = true, uuid = "882f15d4-9de0-42d8-92ac-bd6e2995fd5c")
    private List<CreateExamItemBto.ExamItemMethodBto> examItemMethodBtoList;

    /** 检查类型id */
    @AutoGenerated(locked = true, uuid = "90a00899-9b26-4a03-94d7-4128ec803c22")
    private String examTypeId;

    /** 钆对比 */
    @AutoGenerated(locked = true, uuid = "8f58889a-6da4-4cfc-a7b8-a486d6bc528a")
    private Boolean gadoliniumContrastFlag;

    @Valid
    @AutoGenerated(locked = true, uuid = "9299afbf-e517-4e24-bbd0-189f2c4726bd")
    private InputCodeEo inputCode;

    /** 互联网启用 */
    @AutoGenerated(locked = true, uuid = "dc8299f6-410b-4803-a2c9-eb22dd2a4024")
    private Boolean internetEnableFlag;

    /** 碘对比 */
    @AutoGenerated(locked = true, uuid = "92a0fa2c-48f5-4640-baaf-89ec815257fa")
    private Boolean iodineContrastFlag;

    /** 检查肢位 */
    @Valid
    @AutoGenerated(locked = true, uuid = "f23bf8ec-250e-486e-81be-8003b1b5f123")
    private List<String> limbPositionCodeList;

    /** 性别限制 */
    @AutoGenerated(locked = true, uuid = "a20569bc-65e8-410a-8496-e85949267757")
    private String limitGender;

    /** 不可开单部门ID列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "90915bb2-91ab-43af-b0fc-dd7285201c73")
    private List<String> notAllowedDepartmentIdList;

    /** 部位数量 */
    @AutoGenerated(locked = true, uuid = "47c503d0-8e88-4a28-9a4b-4e251d01ddc9")
    private Long partNumber;

    /** 病理类型 */
    @AutoGenerated(locked = true, uuid = "f53d5ce0-b756-4c93-9604-54857c460580")
    private String pathologyType;

    /** 提示内容 */
    @AutoGenerated(locked = true, uuid = "6514a3c5-fec8-4989-9e9f-974bbd75db3c")
    private String promptContent;

    /** 自助开单标志 */
    @AutoGenerated(locked = true, uuid = "2d44a1ec-a15e-43e6-af04-baac5097a561")
    private Boolean selfServiceOrderFlag;

    /** 顺序号 */
    @AutoGenerated(locked = true, uuid = "2dca21c8-5f5a-45ee-acfb-61bfe733d161")
    private Long sortNumber;

    /** UCLA标志 */
    @AutoGenerated(locked = true, uuid = "fb4b972a-181f-42cb-ba3f-043487fe6acb")
    private Boolean uclaFlag;

    /** 单位重量 */
    @AutoGenerated(locked = true, uuid = "b512a62d-0c94-4bbd-8d06-4dd055450fa5")
    private BigDecimal unitWeight;

    /** 加急类型 */
    @AutoGenerated(locked = true, uuid = "d80febda-fcae-40dd-8afb-56a5ac244cfa")
    private String urgentType;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "4ea7e68a-8214-4c9c-9cb9-6615d79ffb28")
    private List<String> useScopeList;

    @AutoGenerated(locked = true)
    public void setAgeMaxLimit(Long ageMaxLimit) {
        this.__$validPropertySet.add("ageMaxLimit");
        this.ageMaxLimit = ageMaxLimit;
    }

    @AutoGenerated(locked = true)
    public void setAgeMinLimit(Long ageMinLimit) {
        this.__$validPropertySet.add("ageMinLimit");
        this.ageMinLimit = ageMinLimit;
    }

    @AutoGenerated(locked = true)
    public void setAllowedStaffIdList(List<String> allowedStaffIdList) {
        this.__$validPropertySet.add("allowedStaffIdList");
        this.allowedStaffIdList = allowedStaffIdList;
    }

    @AutoGenerated(locked = true)
    public void setAppointType(String appointType) {
        this.__$validPropertySet.add("appointType");
        this.appointType = appointType;
    }

    @AutoGenerated(locked = true)
    public void setBedsideFlag(Boolean bedsideFlag) {
        this.__$validPropertySet.add("bedsideFlag");
        this.bedsideFlag = bedsideFlag;
    }

    @AutoGenerated(locked = true)
    public void setCampusIdList(List<String> campusIdList) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusIdList;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemCode(String clinicItemCode) {
        this.__$validPropertySet.add("clinicItemCode");
        this.clinicItemCode = clinicItemCode;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemId(String clinicItemId) {
        this.__$validPropertySet.add("clinicItemId");
        this.clinicItemId = clinicItemId;
    }

    @AutoGenerated(locked = true)
    public void setClinicItemName(String clinicItemName) {
        this.__$validPropertySet.add("clinicItemName");
        this.clinicItemName = clinicItemName;
    }

    @AutoGenerated(locked = true)
    public void setEmptyStomachFlag(Boolean emptyStomachFlag) {
        this.__$validPropertySet.add("emptyStomachFlag");
        this.emptyStomachFlag = emptyStomachFlag;
    }

    @AutoGenerated(locked = true)
    public void setEnhancedScanningFlag(Boolean enhancedScanningFlag) {
        this.__$validPropertySet.add("enhancedScanningFlag");
        this.enhancedScanningFlag = enhancedScanningFlag;
    }

    @AutoGenerated(locked = true)
    public void setExamCatalogId(String examCatalogId) {
        this.__$validPropertySet.add("examCatalogId");
        this.examCatalogId = examCatalogId;
    }

    @AutoGenerated(locked = true)
    public void setExamDirection(List<String> examDirection) {
        this.__$validPropertySet.add("examDirectionList");
        this.examDirectionList = examDirection;
    }

    @AutoGenerated(locked = true)
    public void setExamDirectionList(List<String> examDirectionList) {
        this.__$validPropertySet.add("examDirectionList");
        this.examDirectionList = examDirectionList;
    }

    @AutoGenerated(locked = true)
    public void setExamItemBodyBtoList(
            List<CreateExamItemBto.ExamItemBodyBto> examItemBodyBtoList) {
        this.__$validPropertySet.add("examItemBodyBtoList");
        this.examItemBodyBtoList = examItemBodyBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamItemDeviceBtoList(
            List<CreateExamItemBto.ExamItemDeviceBto> examItemDeviceBtoList) {
        this.__$validPropertySet.add("examItemDeviceBtoList");
        this.examItemDeviceBtoList = examItemDeviceBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamItemDocumentTemplateBto(
            CreateExamItemBto.ExamItemDocumentTemplateBto examItemDocumentTemplateBto) {
        this.__$validPropertySet.add("examItemDocumentTemplateBto");
        this.examItemDocumentTemplateBto = examItemDocumentTemplateBto;
    }

    @AutoGenerated(locked = true)
    public void setExamItemDrugBtoList(
            List<CreateExamItemBto.ExamItemDrugBto> examItemDrugBtoList) {
        this.__$validPropertySet.add("examItemDrugBtoList");
        this.examItemDrugBtoList = examItemDrugBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamItemExtensionBtoList(
            List<CreateExamItemBto.ExamItemExtensionBto> examItemExtensionBtoList) {
        this.__$validPropertySet.add("examItemExtensionBtoList");
        this.examItemExtensionBtoList = examItemExtensionBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamItemMethodBtoList(
            List<CreateExamItemBto.ExamItemMethodBto> examItemMethodBtoList) {
        this.__$validPropertySet.add("examItemMethodBtoList");
        this.examItemMethodBtoList = examItemMethodBtoList;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeId(String examTypeId) {
        this.__$validPropertySet.add("examTypeId");
        this.examTypeId = examTypeId;
    }

    @AutoGenerated(locked = true)
    public void setGadoliniumContrastFlag(Boolean gadoliniumContrastFlag) {
        this.__$validPropertySet.add("gadoliniumContrastFlag");
        this.gadoliniumContrastFlag = gadoliniumContrastFlag;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setInternetEnableFlag(Boolean internetEnableFlag) {
        this.__$validPropertySet.add("internetEnableFlag");
        this.internetEnableFlag = internetEnableFlag;
    }

    @AutoGenerated(locked = true)
    public void setIodineContrastFlag(Boolean iodineContrastFlag) {
        this.__$validPropertySet.add("iodineContrastFlag");
        this.iodineContrastFlag = iodineContrastFlag;
    }

    @AutoGenerated(locked = true)
    public void setLimbPositionCode(List<String> limbPositionCode) {
        this.__$validPropertySet.add("limbPositionCodeList");
        this.limbPositionCodeList = limbPositionCode;
    }

    @AutoGenerated(locked = true)
    public void setLimbPositionCodeList(List<String> limbPositionCodeList) {
        this.__$validPropertySet.add("limbPositionCodeList");
        this.limbPositionCodeList = limbPositionCodeList;
    }

    @AutoGenerated(locked = true)
    public void setLimitGender(String limitGender) {
        this.__$validPropertySet.add("limitGender");
        this.limitGender = limitGender;
    }

    @AutoGenerated(locked = true)
    public void setNotAllowedDepartmentIdList(List<String> notAllowedDepartmentIdList) {
        this.__$validPropertySet.add("notAllowedDepartmentIdList");
        this.notAllowedDepartmentIdList = notAllowedDepartmentIdList;
    }

    @AutoGenerated(locked = true)
    public void setPartNumber(Long partNumber) {
        this.__$validPropertySet.add("partNumber");
        this.partNumber = partNumber;
    }

    @AutoGenerated(locked = true)
    public void setPathologyType(String pathologyType) {
        this.__$validPropertySet.add("pathologyType");
        this.pathologyType = pathologyType;
    }

    @AutoGenerated(locked = true)
    public void setPromptContent(String promptContent) {
        this.__$validPropertySet.add("promptContent");
        this.promptContent = promptContent;
    }

    @AutoGenerated(locked = true)
    public void setSelfServiceOrderFlag(Boolean selfServiceOrderFlag) {
        this.__$validPropertySet.add("selfServiceOrderFlag");
        this.selfServiceOrderFlag = selfServiceOrderFlag;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setUclaFlag(Boolean uclaFlag) {
        this.__$validPropertySet.add("uclaFlag");
        this.uclaFlag = uclaFlag;
    }

    @AutoGenerated(locked = true)
    public void setUnitWeight(BigDecimal unitWeight) {
        this.__$validPropertySet.add("unitWeight");
        this.unitWeight = unitWeight;
    }

    @AutoGenerated(locked = true)
    public void setUrgentType(String urgentType) {
        this.__$validPropertySet.add("urgentType");
        this.urgentType = urgentType;
    }

    @AutoGenerated(locked = true)
    public void setUseScopeList(List<String> useScopeList) {
        this.__$validPropertySet.add("useScopeList");
        this.useScopeList = useScopeList;
    }

    /**
     * <b>[源自]</b> ExamItemExtension
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamItemExtensionBto {
        /** 使用范围 */
        @AutoGenerated(locked = true, uuid = "af71c78b-274f-41a8-bb4f-3e0eb9cc8a78")
        private String usageScope;

        /** 院区 */
        @AutoGenerated(locked = true, uuid = "c37409c5-4d39-44ca-8f8a-d57870ccafdc")
        private String branchInstitutionId;

        /** 默认胶片选项 */
        @AutoGenerated(locked = true, uuid = "cd009ac4-045e-4fbb-9e38-ccbe279d2c27")
        private String radiationFilmOption;

        /** 预约方法 */
        @AutoGenerated(locked = true, uuid = "392e1e70-c28b-4fa7-a816-82637cea9965")
        private String appointMethod;

        /** 预约类型 */
        @AutoGenerated(locked = true, uuid = "ce9f9e1b-d74b-490e-8b6b-bb087c531b04")
        private String appointType;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setUsageScope(String usageScope) {
            this.__$validPropertySet.add("usageScope");
            this.usageScope = usageScope;
        }

        @AutoGenerated(locked = true)
        public void setBranchInstitutionId(String branchInstitutionId) {
            this.__$validPropertySet.add("branchInstitutionId");
            this.branchInstitutionId = branchInstitutionId;
        }

        @AutoGenerated(locked = true)
        public void setRadiationFilmOption(String radiationFilmOption) {
            this.__$validPropertySet.add("radiationFilmOption");
            this.radiationFilmOption = radiationFilmOption;
        }

        @AutoGenerated(locked = true)
        public void setAppointMethod(String appointMethod) {
            this.__$validPropertySet.add("appointMethod");
            this.appointMethod = appointMethod;
        }

        @AutoGenerated(locked = true)
        public void setAppointType(String appointType) {
            this.__$validPropertySet.add("appointType");
            this.appointType = appointType;
        }
    }

    /**
     * <b>[源自]</b> ExamItemMethod
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamItemMethodBto {
        /** 检查项目方法 */
        @AutoGenerated(locked = true, uuid = "38e956e4-44b6-4aff-805f-0ae3ee23cd8d")
        private String examMethodId;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setExamMethodId(String examMethodId) {
            this.__$validPropertySet.add("examMethodId");
            this.examMethodId = examMethodId;
        }
    }

    /**
     * <b>[源自]</b> ExamItemDrug
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamItemDrugBto {
        /** 收费项目id */
        @AutoGenerated(locked = true, uuid = "b7c58044-02d6-446a-893e-011d44039fb3")
        private String priceItemId;

        /** 项目类型 */
        @AutoGenerated(locked = true, uuid = "e21ff186-4cf1-4544-b479-b23bc240da66")
        private String priceItemType;

        /** 数量 */
        @AutoGenerated(locked = true, uuid = "f91fdc20-5ecf-4d05-a95c-aa65399b03d0")
        private Long count;

        /** 院区id列表 */
        @Valid
        @AutoGenerated(locked = true, uuid = "9f6fb3a9-a645-4265-bb1a-b5edd093063d")
        private List<String> campusIdList;

        /** 给药方式 */
        @AutoGenerated(locked = true, uuid = "1277763a-02c2-4167-823e-76c8b7ebed1c")
        private String administration;

        /** 科室药品标志 */
        @AutoGenerated(locked = true, uuid = "2f66e5bc-25f1-4e4a-abab-9edc07a7e1ac")
        private Boolean departmentDrugFlag;

        /** 使用范围 */
        @Valid
        @AutoGenerated(locked = true, uuid = "6f135eed-4c8c-44eb-a46f-e426489cce35")
        private List<String> useScopeList;

        /** 造影剂 */
        @AutoGenerated(locked = true, uuid = "4ce0a006-2223-448e-b9ed-e0b35d6bae09")
        private String contrastAgentType;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "dd436046-6d7c-4098-abe3-a0ceb86c4c83")
        private String updatedBy;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "e286c71a-fe00-48c0-a51e-99e9c1915aed")
        private String createdBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setPriceItemId(String priceItemId) {
            this.__$validPropertySet.add("priceItemId");
            this.priceItemId = priceItemId;
        }

        @AutoGenerated(locked = true)
        public void setPriceItemType(String priceItemType) {
            this.__$validPropertySet.add("priceItemType");
            this.priceItemType = priceItemType;
        }

        @AutoGenerated(locked = true)
        public void setCount(Long count) {
            this.__$validPropertySet.add("count");
            this.count = count;
        }

        @AutoGenerated(locked = true)
        public void setCampusIdList(List<String> campusIdList) {
            this.__$validPropertySet.add("campusIdList");
            this.campusIdList = campusIdList;
        }

        @AutoGenerated(locked = true)
        public void setAdministration(String administration) {
            this.__$validPropertySet.add("administration");
            this.administration = administration;
        }

        @AutoGenerated(locked = true)
        public void setDepartmentDrugFlag(Boolean departmentDrugFlag) {
            this.__$validPropertySet.add("departmentDrugFlag");
            this.departmentDrugFlag = departmentDrugFlag;
        }

        @AutoGenerated(locked = true)
        public void setUseScope(List<String> useScope) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScope;
        }

        @AutoGenerated(locked = true)
        public void setUseScopeList(List<String> useScopeList) {
            this.__$validPropertySet.add("useScopeList");
            this.useScopeList = useScopeList;
        }

        @AutoGenerated(locked = true)
        public void setContrastAgentType(String contrastAgentType) {
            this.__$validPropertySet.add("contrastAgentType");
            this.contrastAgentType = contrastAgentType;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }
    }

    /**
     * <b>[源自]</b> ExamItemDevice
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamItemDeviceBto {
        /** 院区id */
        @AutoGenerated(locked = true, uuid = "eb9e96d3-a5bf-4069-88dd-a3b578f3f69d")
        private String campusId;

        /** 检查设备ID */
        @AutoGenerated(locked = true, uuid = "4f0b6a21-f40c-40ed-b943-208162bcc7bc")
        private String examDeviceId;

        /** 优先级 */
        @AutoGenerated(locked = true, uuid = "65c1c689-6952-4748-bcbe-2d0b0622dd4b")
        private Long priority;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setCampusId(String campusId) {
            this.__$validPropertySet.add("campusId");
            this.campusId = campusId;
        }

        @AutoGenerated(locked = true)
        public void setExamDeviceId(String examDeviceId) {
            this.__$validPropertySet.add("examDeviceId");
            this.examDeviceId = examDeviceId;
        }

        @AutoGenerated(locked = true)
        public void setPriority(Long priority) {
            this.__$validPropertySet.add("priority");
            this.priority = priority;
        }
    }

    /**
     * <b>[源自]</b> ExamItemBody
     *
     * <p><b>[操作]</b> CREATE
     */
    @Getter
    @NoArgsConstructor
    public static class ExamItemBodyBto {
        /** 检查部位ID */
        @AutoGenerated(locked = true, uuid = "13693971-8836-449e-91c2-cc666a3fb0cb")
        private String examBodyId;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "edae4a4b-d239-49fe-88cc-ec3be3436400")
        private String createdBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setExamBodyId(String examBodyId) {
            this.__$validPropertySet.add("examBodyId");
            this.examBodyId = examBodyId;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }
    }

    /**
     * <b>[源自]</b> ExamItemDocumentTemplate
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class ExamItemDocumentTemplateBto {
        /** 门诊知情同意书 */
        @AutoGenerated(locked = true, uuid = "66bfec32-9f47-4ae9-a744-0f8b2565ddfb")
        private String outpatientInformedConsentForm;

        /** 住院知情同意书 */
        @AutoGenerated(locked = true, uuid = "d5b19b80-4ed8-42bf-8d4e-8dee72c393ad")
        private String inpatientInformedConsentForm;

        /** 门诊申请单模板 */
        @AutoGenerated(locked = true, uuid = "de7cdedf-912c-4684-8f46-2d31060290d9")
        private String outpatientApplyTemplate;

        /** 住院申请单模板 */
        @AutoGenerated(locked = true, uuid = "8c9b68d8-3bda-4315-86e3-3bd5b08f4fb9")
        private String inpatientApplyTemplate;

        /** 留观申请单模版 */
        @AutoGenerated(locked = true, uuid = "3776b3d1-7146-49bb-b672-3351e6da2f4a")
        private String emergencyObservationApplyTemplate;

        /** 院前申请单模板 */
        @AutoGenerated(locked = true, uuid = "55bcd90c-c4f2-4a5f-ae9c-5a5059915007")
        private String preHospitalApplyTemplate;

        /** 体检申请单模板 */
        @AutoGenerated(locked = true, uuid = "f3725479-a191-4141-8b48-39542ffbcfd3")
        private String physicalExamApplyTemplate;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "6b8b21f8-befe-4c54-9e54-929017078a3e")
        private String createdBy;

        /** 碘对比标志 */
        @AutoGenerated(locked = true, uuid = "aad5a7c1-4dab-4d3f-ba18-ef7f970f47b9")
        private Boolean iodineContrastFlag;

        /** 钆对比标志 */
        @AutoGenerated(locked = true, uuid = "47cf4236-b458-4b6d-8ae1-8f08f046e7ac")
        private Boolean gadoliniumContrastFlag;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setOutpatientInformedConsentForm(String outpatientInformedConsentForm) {
            this.__$validPropertySet.add("outpatientInformedConsentForm");
            this.outpatientInformedConsentForm = outpatientInformedConsentForm;
        }

        @AutoGenerated(locked = true)
        public void setInpatientInformedConsentForm(String inpatientInformedConsentForm) {
            this.__$validPropertySet.add("inpatientInformedConsentForm");
            this.inpatientInformedConsentForm = inpatientInformedConsentForm;
        }

        @AutoGenerated(locked = true)
        public void setOutpatientApplyTemplate(String outpatientApplyTemplate) {
            this.__$validPropertySet.add("outpatientApplyTemplate");
            this.outpatientApplyTemplate = outpatientApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setInpatientApplyTemplate(String inpatientApplyTemplate) {
            this.__$validPropertySet.add("inpatientApplyTemplate");
            this.inpatientApplyTemplate = inpatientApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setEmergencyObservationApplyTemplate(String emergencyObservationApplyTemplate) {
            this.__$validPropertySet.add("emergencyObservationApplyTemplate");
            this.emergencyObservationApplyTemplate = emergencyObservationApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setPreHospitalApplyTemplate(String preHospitalApplyTemplate) {
            this.__$validPropertySet.add("preHospitalApplyTemplate");
            this.preHospitalApplyTemplate = preHospitalApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setPhysicalExamApplyTemplate(String physicalExamApplyTemplate) {
            this.__$validPropertySet.add("physicalExamApplyTemplate");
            this.physicalExamApplyTemplate = physicalExamApplyTemplate;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setIodineContrastFlag(Boolean iodineContrastFlag) {
            this.__$validPropertySet.add("iodineContrastFlag");
            this.iodineContrastFlag = iodineContrastFlag;
        }

        @AutoGenerated(locked = true)
        public void setGadoliniumContrastFlag(Boolean gadoliniumContrastFlag) {
            this.__$validPropertySet.add("gadoliniumContrastFlag");
            this.gadoliniumContrastFlag = gadoliniumContrastFlag;
        }
    }
}
