package com.pulse.dictionary_business.service;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.dto.TreatItemBaseDto;
import com.pulse.dictionary_business.persist.dos.TreatChargeItem;
import com.pulse.dictionary_business.persist.dos.TreatItem;
import com.pulse.dictionary_business.service.base.BaseTreatItemBOService;
import com.pulse.dictionary_business.service.bto.MergeTreatItemBto;
import com.pulse.dictionary_business.service.bto.UpdateTreatItemBto;
import com.vs.bo.AddedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "70f25dbe-5c73-48c9-8130-a6bf9169418e|BO|SERVICE")
public class TreatItemBOService extends BaseTreatItemBOService {
    @AutoGenerated(locked = true)
    @Resource
    private TreatItemBaseDtoService treatItemBaseDtoService;

    /** 保存治疗项目 */
    @PublicInterface(id = "e752d7d2-e2aa-4293-ae59-3911e12e1751", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "044a6ad0-6d61-40fa-a9ff-8312a28f28d2")
    public String mergeTreatItem(@Valid @NotNull MergeTreatItemBto mergeTreatItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        TreatItemBaseDto treatItemBaseDto = null;
        if (mergeTreatItemBto.getId() != null) {
            treatItemBaseDto = treatItemBaseDtoService.getById(mergeTreatItemBto.getId());
        }
        MergeTreatItemBoResult boResult = super.mergeTreatItemBase(mergeTreatItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeTreatItemBto */
        {
            MergeTreatItemBto bto =
                    boResult.<MergeTreatItemBto>getBtoOfType(MergeTreatItemBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeTreatItemBto, TreatItem, TreatItemBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeTreatItemBto, TreatItemBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                TreatItemBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                TreatItem entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                TreatItemBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** 处理 MergeTreatItemBto.TreatChargeItemBto */
        {
            for (MergeTreatItemBto.TreatChargeItemBto bto :
                    boResult.<MergeTreatItemBto.TreatChargeItemBto>getBtoOfType(
                            MergeTreatItemBto.TreatChargeItemBto.class)) {
                UpdatedBto<MergeTreatItemBto.TreatChargeItemBto, TreatChargeItem, TreatChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<MergeTreatItemBto.TreatChargeItemBto, TreatChargeItemBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    TreatChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    TreatChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    TreatChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<TreatChargeItem> deletedEntityList =
                    boResult.getDeletedEntityList(TreatChargeItem.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 修改治疗项目 */
    @PublicInterface(id = "0b0c2384-96c6-4abb-ad92-d4aa50d236f8", module = "dictionary_business")
    @Transactional
    @AutoGenerated(locked = false, uuid = "3e552af7-5952-40e5-873a-cf0850665c55")
    public String updateTreatItem(@Valid @NotNull UpdateTreatItemBto updateTreatItemBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        TreatItemBaseDto treatItemBaseDto =
                treatItemBaseDtoService.getById(updateTreatItemBto.getId());
        UpdateTreatItemBoResult boResult = super.updateTreatItemBase(updateTreatItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 UpdateTreatItemBto */
        {
            UpdateTreatItemBto bto =
                    boResult.<UpdateTreatItemBto>getBtoOfType(UpdateTreatItemBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<UpdateTreatItemBto, TreatItem, TreatItemBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                TreatItemBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                TreatItem entity = updatedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** 处理 UpdateTreatItemBto.TreatChargeItemBto */
        {
            for (UpdateTreatItemBto.TreatChargeItemBto bto :
                    boResult.<UpdateTreatItemBto.TreatChargeItemBto>getBtoOfType(
                            UpdateTreatItemBto.TreatChargeItemBto.class)) {
                UpdatedBto<
                                UpdateTreatItemBto.TreatChargeItemBto,
                                TreatChargeItem,
                                TreatChargeItemBO>
                        updatedBto = boResult.getUpdatedResult(bto);
                boolean updated = (updatedBto != null);
                AddedBto<UpdateTreatItemBto.TreatChargeItemBto, TreatChargeItemBO> addedBto =
                        boResult.getAddedResult(bto);
                boolean created = (addedBto != null);
                if (updated) { // getUpdatedResult
                    // 合并后的待保存值
                    TreatChargeItemBO bo = updatedBto.getBo();
                    // 数据库现有值（前项），将被BO所覆盖
                    TreatChargeItem entity = updatedBto.getEntity();
                    // 其他自定义操作...
                } else if (created) { // getAddedResult
                    // 合并后的待保存值
                    TreatChargeItemBO bo = addedBto.getBo();
                    // 其他自定义操作...
                }
            }
            // 数据库中有，但是传入参数没有的待删除行
            List<TreatChargeItem> deletedEntityList =
                    boResult.getDeletedEntityList(TreatChargeItem.class);
            if (deletedEntityList != null && deletedEntityList.size() > 0) {
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
