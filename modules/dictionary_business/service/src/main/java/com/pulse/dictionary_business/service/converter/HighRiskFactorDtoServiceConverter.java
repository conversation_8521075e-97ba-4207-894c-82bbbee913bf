package com.pulse.dictionary_business.service.converter;

import com.pulse.dictionary_business.manager.dto.HighRiskFactorDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "9fc103ac-d38d-34f5-83a6-4a5df48a7651")
public class HighRiskFactorDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<HighRiskFactorDto> HighRiskFactorDtoConverter(
            List<HighRiskFactorDto> highRiskFactorDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return highRiskFactorDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
