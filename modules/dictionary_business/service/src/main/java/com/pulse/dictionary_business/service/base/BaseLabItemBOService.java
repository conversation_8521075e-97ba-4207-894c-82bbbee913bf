package com.pulse.dictionary_business.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.dictionary_business.manager.bo.*;
import com.pulse.dictionary_business.manager.bo.LabItemBO;
import com.pulse.dictionary_business.persist.dos.LabItem;
import com.pulse.dictionary_business.persist.dos.LabItemPackageDetail;
import com.pulse.dictionary_business.persist.dos.LabItemPerformDepartment;
import com.pulse.dictionary_business.persist.dos.LabItemRule;
import com.pulse.dictionary_business.persist.dos.LabItemVsSpecimen;
import com.pulse.dictionary_business.service.base.BaseLabItemBOService.CreateLabItemBoResult;
import com.pulse.dictionary_business.service.base.BaseLabItemBOService.EnableLabItemBoResult;
import com.pulse.dictionary_business.service.base.BaseLabItemBOService.MergeLabItemRuleBoResult;
import com.pulse.dictionary_business.service.base.BaseLabItemBOService.SaveLabItemDetailBoResult;
import com.pulse.dictionary_business.service.base.BaseLabItemBOService.SaveLabItemPerformDepartmentBoResult;
import com.pulse.dictionary_business.service.base.BaseLabItemBOService.SaveLabItemVsSpecimenBoResult;
import com.pulse.dictionary_business.service.base.BaseLabItemBOService.UpdateLabItemBoResult;
import com.pulse.dictionary_business.service.bto.CreateLabItemBto;
import com.pulse.dictionary_business.service.bto.EnableLabItemBto;
import com.pulse.dictionary_business.service.bto.MergeLabItemRuleBto;
import com.pulse.dictionary_business.service.bto.SaveLabItemDetailBto;
import com.pulse.dictionary_business.service.bto.SaveLabItemPerformDepartmentBto;
import com.pulse.dictionary_business.service.bto.SaveLabItemVsSpecimenBto;
import com.pulse.dictionary_business.service.bto.UpdateLabItemBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "ab0c4000-28ec-3e95-9a1b-f6d13899863b")
public class BaseLabItemBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private LabItemBO createCreateLabItemOnDuplicateThrowEx(
            CreateLabItemBoResult boResult, CreateLabItemBto createLabItemBto) {
        LabItemBO labItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createLabItemBto.getId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getById(createLabItemBto.getId());
            if (labItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull = (createLabItemBto.getClinicItemId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getByClinicItemId(createLabItemBto.getClinicItemId());
            if (labItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'clinic_item_id'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (labItemBO != null) {
            if (pkMatched) {
                log.error("主键冲突, id:{}的记录在数据库表:{}中已经存在!", labItemBO.getId(), "lab_item");
                throw new IgnoredException(400, "检验项目已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "lab_item",
                        labItemBO.getId(),
                        "lab_item");
                throw new IgnoredException(400, "检验项目已存在");
            }
        } else {
            labItemBO = new LabItemBO();
            if (pkExist) {
                labItemBO.setId(createLabItemBto.getId());
            } else {
                labItemBO.setId(String.valueOf(this.idGenerator.allocateId("lab_item")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "labClassId")) {
                labItemBO.setLabClassId(createLabItemBto.getLabClassId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "clinicItemId")) {
                labItemBO.setClinicItemId(createLabItemBto.getClinicItemId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "sortNumber")) {
                labItemBO.setSortNumber(createLabItemBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "urgentType")) {
                labItemBO.setUrgentType(createLabItemBto.getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "containerId")) {
                labItemBO.setContainerId(createLabItemBto.getContainerId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "packageFlag")) {
                labItemBO.setPackageFlag(createLabItemBto.getPackageFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "packageOrderMode")) {
                labItemBO.setPackageOrderMode(createLabItemBto.getPackageOrderMode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "collectBodyPart")) {
                labItemBO.setCollectBodyPart(createLabItemBto.getCollectBodyPart());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "collectType")) {
                labItemBO.setCollectType(createLabItemBto.getCollectType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "printName")) {
                labItemBO.setPrintName(createLabItemBto.getPrintName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "printCount")) {
                labItemBO.setPrintCount(createLabItemBto.getPrintCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "deliveryFlag")) {
                labItemBO.setDeliveryFlag(createLabItemBto.getDeliveryFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "enableFlag")) {
                labItemBO.setEnableFlag(createLabItemBto.getEnableFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "defaultSpecimenFlag")) {
                labItemBO.setDefaultSpecimenFlag(createLabItemBto.getDefaultSpecimenFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "bloodCollectionTime")) {
                labItemBO.setBloodCollectionTime(createLabItemBto.getBloodCollectionTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "bloodCollectionVolume")) {
                labItemBO.setBloodCollectionVolume(createLabItemBto.getBloodCollectionVolume());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "bloodCollectionInstruction")) {
                labItemBO.setBloodCollectionInstruction(
                        createLabItemBto.getBloodCollectionInstruction());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "labItemName")) {
                labItemBO.setLabItemName(createLabItemBto.getLabItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "specimenType")) {
                labItemBO.setSpecimenType(createLabItemBto.getSpecimenType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "inputCode")) {
                labItemBO.setInputCode(createLabItemBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "bloodCollectionLocation")) {
                labItemBO.setBloodCollectionLocation(createLabItemBto.getBloodCollectionLocation());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createLabItemBto);
            addedBto.setBo(labItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return labItemBO;
    }

    /** 创建检验项目 */
    @AutoGenerated(locked = true)
    protected CreateLabItemBoResult createLabItemBase(CreateLabItemBto createLabItemBto) {
        if (createLabItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateLabItemBoResult boResult = new CreateLabItemBoResult();
        LabItemBO labItemBO = createCreateLabItemOnDuplicateThrowEx(boResult, createLabItemBto);
        if (labItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "labItemPackageDetailBtoList")) {
                createLabItemPackageDetailBtoOnDuplicateThrowEx(
                        boResult, createLabItemBto, labItemBO);
            }
        }
        if (labItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "labItemVsSpecimenBtoList")) {
                createLabItemVsSpecimenBtoOnDuplicateThrowEx(boResult, createLabItemBto, labItemBO);
            }
        }
        if (labItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(createLabItemBto, "__$validPropertySet"),
                    "labItemRuleBto")) {
                createLabItemRuleBtoOnDuplicateThrowEx(boResult, createLabItemBto, labItemBO);
            }
        }
        boResult.setRootBo(labItemBO);
        return boResult;
    }

    /** 创建对象:LabItemPackageDetailBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createLabItemPackageDetailBtoOnDuplicateThrowEx(
            BaseLabItemBOService.CreateLabItemBoResult boResult,
            CreateLabItemBto createLabItemBto,
            LabItemBO labItemBO) {
        if (CollectionUtil.isNotEmpty(createLabItemBto.getLabItemPackageDetailBtoList())) {
            for (CreateLabItemBto.LabItemPackageDetailBto item :
                    createLabItemBto.getLabItemPackageDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<LabItemPackageDetailBO> any =
                        labItemBO.getLabItemPackageDetailBOSet().stream()
                                .filter(
                                        labItemPackageDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                labItemPackageDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error("主键冲突，id:{}在数据库表:{}中已经存在！", any.get().getId(), "lab_item_detail");
                        throw new IgnoredException(400, "检验项目套餐明细已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "lab_item_detail",
                                any.get().getId());
                        throw new IgnoredException(400, "检验项目套餐明细已存在");
                    }
                } else {
                    LabItemPackageDetailBO subBo = new LabItemPackageDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "labItemId")) {
                        subBo.setLabItemId(item.getLabItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "count")) {
                        subBo.setCount(item.getCount());
                    }
                    subBo.setLabItemBO(labItemBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("lab_item_detail"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    labItemBO.getLabItemPackageDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:LabItemPackageDetailBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createLabItemPackageDetailBtoOnDuplicateUpdate(
            BaseLabItemBOService.SaveLabItemDetailBoResult boResult,
            SaveLabItemDetailBto saveLabItemDetailBto,
            LabItemBO labItemBO) {
        if (CollectionUtil.isEmpty(saveLabItemDetailBto.getLabItemPackageDetailBtoList())) {
            saveLabItemDetailBto.setLabItemPackageDetailBtoList(List.of());
        }
        labItemBO
                .getLabItemPackageDetailBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveLabItemDetailBto.getLabItemPackageDetailBtoList().stream()
                                            .filter(
                                                    labItemPackageDetailBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (labItemPackageDetailBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            labItemPackageDetailBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToLabItemPackageDetail());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveLabItemDetailBto.getLabItemPackageDetailBtoList())) {
            for (SaveLabItemDetailBto.LabItemPackageDetailBto item :
                    saveLabItemDetailBto.getLabItemPackageDetailBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<LabItemPackageDetailBO> any =
                        labItemBO.getLabItemPackageDetailBOSet().stream()
                                .filter(
                                        labItemPackageDetailBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                labItemPackageDetailBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        LabItemPackageDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToLabItemPackageDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "labItemId")) {
                            bo.setLabItemId(item.getLabItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                    } else {
                        LabItemPackageDetailBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToLabItemPackageDetail());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "labItemId")) {
                            bo.setLabItemId(item.getLabItemId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "count")) {
                            bo.setCount(item.getCount());
                        }
                    }
                } else {
                    LabItemPackageDetailBO subBo = new LabItemPackageDetailBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "labItemId")) {
                        subBo.setLabItemId(item.getLabItemId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "count")) {
                        subBo.setCount(item.getCount());
                    }
                    subBo.setLabItemBO(labItemBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("lab_item_detail"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    labItemBO.getLabItemPackageDetailBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:LabItemPerformDepartmentBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createLabItemPerformDepartmentBtoOnDuplicateUpdate(
            BaseLabItemBOService.SaveLabItemPerformDepartmentBoResult boResult,
            SaveLabItemPerformDepartmentBto saveLabItemPerformDepartmentBto,
            LabItemBO labItemBO) {
        if (CollectionUtil.isEmpty(
                saveLabItemPerformDepartmentBto.getLabItemPerformDepartmentBtoList())) {
            saveLabItemPerformDepartmentBto.setLabItemPerformDepartmentBtoList(List.of());
        }
        labItemBO
                .getLabItemPerformDepartmentBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveLabItemPerformDepartmentBto
                                            .getLabItemPerformDepartmentBtoList()
                                            .stream()
                                            .filter(
                                                    labItemPerformDepartmentBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (labItemPerformDepartmentBtoList
                                                                                .getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            labItemPerformDepartmentBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList()
                                        .add(item.convertToLabItemPerformDepartment());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(
                saveLabItemPerformDepartmentBto.getLabItemPerformDepartmentBtoList())) {
            for (SaveLabItemPerformDepartmentBto.LabItemPerformDepartmentBto item :
                    saveLabItemPerformDepartmentBto.getLabItemPerformDepartmentBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<LabItemPerformDepartmentBO> any =
                        labItemBO.getLabItemPerformDepartmentBOSet().stream()
                                .filter(
                                        labItemPerformDepartmentBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                labItemPerformDepartmentBOSet
                                                                        .getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        LabItemPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToLabItemPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "labDepartmentType")) {
                            bo.setLabDepartmentType(item.getLabDepartmentType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sampleLocation")) {
                            bo.setSampleLocation(item.getSampleLocation());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        LabItemPerformDepartmentBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToLabItemPerformDepartment());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "performDepartmentId")) {
                            bo.setPerformDepartmentId(item.getPerformDepartmentId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "labDepartmentType")) {
                            bo.setLabDepartmentType(item.getLabDepartmentType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "sampleLocation")) {
                            bo.setSampleLocation(item.getSampleLocation());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    LabItemPerformDepartmentBO subBo = new LabItemPerformDepartmentBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "performDepartmentId")) {
                        subBo.setPerformDepartmentId(item.getPerformDepartmentId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "labDepartmentType")) {
                        subBo.setLabDepartmentType(item.getLabDepartmentType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "remark")) {
                        subBo.setRemark(item.getRemark());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "sampleLocation")) {
                        subBo.setSampleLocation(item.getSampleLocation());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setLabItemBO(labItemBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("lab_item_perform_department"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    labItemBO.getLabItemPerformDepartmentBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:LabItemRuleBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createLabItemRuleBtoOnDuplicateThrowEx(
            CreateLabItemBoResult boResult,
            CreateLabItemBto createLabItemBto,
            LabItemBO labItemBO) {
        if (labItemBO.getLabItemRuleBO() != null) {
            log.error("id:{}在数据库表:{}中已经存在！", labItemBO.getLabItemRuleBO().getId(), "lab_item_rule");
            throw new IgnoredException(400, "检验项目规则已存在");
        } else {
            if (createLabItemBto.getLabItemRuleBto() == null) {
                return;
            }
            LabItemRuleBO labItemRuleBO = labItemBO.getOrCreateLabItemRuleBO();
            CreateLabItemBto.LabItemRuleBto labItemRuleBto = createLabItemBto.getLabItemRuleBto();
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "urgentType")) {
                labItemRuleBO.setUrgentType(createLabItemBto.getLabItemRuleBto().getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitAgeMax")) {
                labItemRuleBO.setLimitAgeMax(createLabItemBto.getLabItemRuleBto().getLimitAgeMax());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitAgeUnit")) {
                labItemRuleBO.setLimitAgeUnit(
                        createLabItemBto.getLabItemRuleBto().getLimitAgeUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitAgeMin")) {
                labItemRuleBO.setLimitAgeMin(createLabItemBto.getLabItemRuleBto().getLimitAgeMin());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitGender")) {
                labItemRuleBO.setLimitGender(createLabItemBto.getLabItemRuleBto().getLimitGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "labItemInstruction")) {
                labItemRuleBO.setLabItemInstruction(
                        createLabItemBto.getLabItemRuleBto().getLabItemInstruction());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "applicableOrderDepartmentIdList")) {
                labItemRuleBO.setApplicableOrderDepartmentIdList(
                        createLabItemBto.getLabItemRuleBto().getApplicableOrderDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "applicableOrderDoctorIdList")) {
                labItemRuleBO.setApplicableOrderDoctorIdList(
                        createLabItemBto.getLabItemRuleBto().getApplicableOrderDoctorIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "notAllowedOrderDepartmentIdList")) {
                labItemRuleBO.setNotAllowedOrderDepartmentIdList(
                        createLabItemBto.getLabItemRuleBto().getNotAllowedOrderDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "notAllowedOrderDoctorIdList")) {
                labItemRuleBO.setNotAllowedOrderDoctorIdList(
                        createLabItemBto.getLabItemRuleBto().getNotAllowedOrderDoctorIdList());
            }

            if (createLabItemBto.getLabItemRuleBto().getId() != null) {
                labItemRuleBO.setId(createLabItemBto.getLabItemRuleBto().getId());
            } else {
                labItemRuleBO.setId(String.valueOf(this.idGenerator.allocateId("lab_item_rule")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(labItemRuleBto);
            addedBto.setBo(labItemRuleBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:LabItemRuleBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createLabItemRuleBtoOnDuplicateUpdate(
            MergeLabItemRuleBoResult boResult,
            MergeLabItemRuleBto mergeLabItemRuleBto,
            LabItemBO labItemBO) {
        if (labItemBO.getLabItemRuleBO() != null) {
            LabItemRuleBO bo = labItemBO.getOrCreateLabItemRuleBO();
            MergeLabItemRuleBto.LabItemRuleBto bto = mergeLabItemRuleBto.getLabItemRuleBto();
            if (bto == null) {
                LabItemRule deletedItem = labItemBO.getLabItemRuleBO().convertToLabItemRule();
                boResult.getDeletedList().add(deletedItem);
                labItemBO.setLabItemRuleBO(null);
                return;
            }
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setBto(bto);
            updatedBto.setBo(bo);
            updatedBto.setEntity(bo.convertToLabItemRule());
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "urgentType")) {
                bo.setUrgentType(bto.getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "limitAgeMax")) {
                bo.setLimitAgeMax(bto.getLimitAgeMax());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "limitAgeUnit")) {
                bo.setLimitAgeUnit(bto.getLimitAgeUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "limitAgeMin")) {
                bo.setLimitAgeMin(bto.getLimitAgeMin());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "limitGender")) {
                bo.setLimitGender(bto.getLimitGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "labItemInstruction")) {
                bo.setLabItemInstruction(bto.getLabItemInstruction());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "applicableOrderDepartmentIdList")) {
                bo.setApplicableOrderDepartmentIdList(bto.getApplicableOrderDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "applicableOrderDoctorIdList")) {
                bo.setApplicableOrderDoctorIdList(bto.getApplicableOrderDoctorIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "notAllowedOrderDepartmentIdList")) {
                bo.setNotAllowedOrderDepartmentIdList(bto.getNotAllowedOrderDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(bto, "__$validPropertySet"),
                    "notAllowedOrderDoctorIdList")) {
                bo.setNotAllowedOrderDoctorIdList(bto.getNotAllowedOrderDoctorIdList());
            }
            boResult.getUpdatedList().add(updatedBto);
        } else {
            if (mergeLabItemRuleBto.getLabItemRuleBto() == null) {
                return;
            }
            LabItemRuleBO labItemRuleBO = labItemBO.getOrCreateLabItemRuleBO();
            MergeLabItemRuleBto.LabItemRuleBto labItemRuleBto =
                    mergeLabItemRuleBto.getLabItemRuleBto();
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "urgentType")) {
                labItemRuleBO.setUrgentType(
                        mergeLabItemRuleBto.getLabItemRuleBto().getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitAgeMax")) {
                labItemRuleBO.setLimitAgeMax(
                        mergeLabItemRuleBto.getLabItemRuleBto().getLimitAgeMax());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitAgeUnit")) {
                labItemRuleBO.setLimitAgeUnit(
                        mergeLabItemRuleBto.getLabItemRuleBto().getLimitAgeUnit());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitAgeMin")) {
                labItemRuleBO.setLimitAgeMin(
                        mergeLabItemRuleBto.getLabItemRuleBto().getLimitAgeMin());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "limitGender")) {
                labItemRuleBO.setLimitGender(
                        mergeLabItemRuleBto.getLabItemRuleBto().getLimitGender());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "labItemInstruction")) {
                labItemRuleBO.setLabItemInstruction(
                        mergeLabItemRuleBto.getLabItemRuleBto().getLabItemInstruction());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "applicableOrderDepartmentIdList")) {
                labItemRuleBO.setApplicableOrderDepartmentIdList(
                        mergeLabItemRuleBto
                                .getLabItemRuleBto()
                                .getApplicableOrderDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "applicableOrderDoctorIdList")) {
                labItemRuleBO.setApplicableOrderDoctorIdList(
                        mergeLabItemRuleBto.getLabItemRuleBto().getApplicableOrderDoctorIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "notAllowedOrderDepartmentIdList")) {
                labItemRuleBO.setNotAllowedOrderDepartmentIdList(
                        mergeLabItemRuleBto
                                .getLabItemRuleBto()
                                .getNotAllowedOrderDepartmentIdList());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(labItemRuleBto, "__$validPropertySet"),
                    "notAllowedOrderDoctorIdList")) {
                labItemRuleBO.setNotAllowedOrderDoctorIdList(
                        mergeLabItemRuleBto.getLabItemRuleBto().getNotAllowedOrderDoctorIdList());
            }

            labItemRuleBO.setId(String.valueOf(this.idGenerator.allocateId("lab_item_rule")));
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(labItemRuleBto);
            addedBto.setBo(labItemRuleBO);
            boResult.getAddBtoList().add(addedBto);
        }
    }

    /** 创建对象:LabItemVsSpecimenBto,如果存在就抛出异常 */
    @AutoGenerated(locked = true)
    private void createLabItemVsSpecimenBtoOnDuplicateThrowEx(
            CreateLabItemBoResult boResult,
            CreateLabItemBto createLabItemBto,
            LabItemBO labItemBO) {
        if (CollectionUtil.isNotEmpty(createLabItemBto.getLabItemVsSpecimenBtoList())) {
            for (CreateLabItemBto.LabItemVsSpecimenBto item :
                    createLabItemBto.getLabItemVsSpecimenBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<LabItemVsSpecimenBO> any =
                        labItemBO.getLabItemVsSpecimenBOSet().stream()
                                .filter(
                                        labItemVsSpecimenBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                labItemVsSpecimenBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        log.error(
                                "主键冲突，id:{}在数据库表:{}中已经存在！",
                                any.get().getId(),
                                "lab_item_vs_specimen");
                        throw new IgnoredException(400, "检验项目与标本对照已存在");
                    } else {
                        log.error(
                                "唯一键 UK{}和数据库表:'{}'中id为:{}的记录冲突！",
                                matchedUkName.get(),
                                "lab_item_vs_specimen",
                                any.get().getId());
                        throw new IgnoredException(400, "检验项目与标本对照已存在");
                    }
                } else {
                    LabItemVsSpecimenBO subBo = new LabItemVsSpecimenBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "specimenId")) {
                        subBo.setSpecimenId(item.getSpecimenId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "defaultFlag")) {
                        subBo.setDefaultFlag(item.getDefaultFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setLabItemBO(labItemBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("lab_item_vs_specimen"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    labItemBO.getLabItemVsSpecimenBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:LabItemVsSpecimenBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createLabItemVsSpecimenBtoOnDuplicateUpdate(
            BaseLabItemBOService.SaveLabItemVsSpecimenBoResult boResult,
            SaveLabItemVsSpecimenBto saveLabItemVsSpecimenBto,
            LabItemBO labItemBO) {
        if (CollectionUtil.isEmpty(saveLabItemVsSpecimenBto.getLabItemVsSpecimenBtoList())) {
            saveLabItemVsSpecimenBto.setLabItemVsSpecimenBtoList(List.of());
        }
        labItemBO
                .getLabItemVsSpecimenBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    saveLabItemVsSpecimenBto.getLabItemVsSpecimenBtoList().stream()
                                            .filter(
                                                    labItemVsSpecimenBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (labItemVsSpecimenBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            labItemVsSpecimenBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToLabItemVsSpecimen());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(saveLabItemVsSpecimenBto.getLabItemVsSpecimenBtoList())) {
            for (SaveLabItemVsSpecimenBto.LabItemVsSpecimenBto item :
                    saveLabItemVsSpecimenBto.getLabItemVsSpecimenBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<LabItemVsSpecimenBO> any =
                        labItemBO.getLabItemVsSpecimenBOSet().stream()
                                .filter(
                                        labItemVsSpecimenBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                labItemVsSpecimenBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        LabItemVsSpecimenBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToLabItemVsSpecimen());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "specimenId")) {
                            bo.setSpecimenId(item.getSpecimenId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        LabItemVsSpecimenBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToLabItemVsSpecimen());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "specimenId")) {
                            bo.setSpecimenId(item.getSpecimenId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "defaultFlag")) {
                            bo.setDefaultFlag(item.getDefaultFlag());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    LabItemVsSpecimenBO subBo = new LabItemVsSpecimenBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "specimenId")) {
                        subBo.setSpecimenId(item.getSpecimenId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "defaultFlag")) {
                        subBo.setDefaultFlag(item.getDefaultFlag());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setLabItemBO(labItemBO);
                    if (item.getId() == null) {
                        subBo.setId(this.idGenerator.allocateId("lab_item_vs_specimen"));
                    } else {
                        subBo.setId(item.getId());
                    }

                    labItemBO.getLabItemVsSpecimenBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private LabItemBO createSaveLabItemDetailOnDuplicateUpdate(
            SaveLabItemDetailBoResult boResult, SaveLabItemDetailBto saveLabItemDetailBto) {
        LabItemBO labItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveLabItemDetailBto.getId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getById(saveLabItemDetailBto.getId());
            if (labItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (labItemBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(labItemBO.convertToLabItem());
                updatedBto.setBto(saveLabItemDetailBto);
                updatedBto.setBo(labItemBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(labItemBO.convertToLabItem());
                updatedBto.setBto(saveLabItemDetailBto);
                updatedBto.setBo(labItemBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            labItemBO = new LabItemBO();
            if (pkExist) {
                labItemBO.setId(saveLabItemDetailBto.getId());
            } else {
                labItemBO.setId(String.valueOf(this.idGenerator.allocateId("lab_item")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveLabItemDetailBto);
            addedBto.setBo(labItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return labItemBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private LabItemBO createSaveLabItemPerformDepartmentOnDuplicateUpdate(
            SaveLabItemPerformDepartmentBoResult boResult,
            SaveLabItemPerformDepartmentBto saveLabItemPerformDepartmentBto) {
        LabItemBO labItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveLabItemPerformDepartmentBto.getId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getById(saveLabItemPerformDepartmentBto.getId());
            if (labItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (labItemBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(labItemBO.convertToLabItem());
                updatedBto.setBto(saveLabItemPerformDepartmentBto);
                updatedBto.setBo(labItemBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(labItemBO.convertToLabItem());
                updatedBto.setBto(saveLabItemPerformDepartmentBto);
                updatedBto.setBo(labItemBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            labItemBO = new LabItemBO();
            if (pkExist) {
                labItemBO.setId(saveLabItemPerformDepartmentBto.getId());
            } else {
                labItemBO.setId(String.valueOf(this.idGenerator.allocateId("lab_item")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveLabItemPerformDepartmentBto);
            addedBto.setBo(labItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return labItemBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private LabItemBO createSaveLabItemVsSpecimenOnDuplicateUpdate(
            SaveLabItemVsSpecimenBoResult boResult,
            SaveLabItemVsSpecimenBto saveLabItemVsSpecimenBto) {
        LabItemBO labItemBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (saveLabItemVsSpecimenBto.getId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getById(saveLabItemVsSpecimenBto.getId());
            if (labItemBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (labItemBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(labItemBO.convertToLabItem());
                updatedBto.setBto(saveLabItemVsSpecimenBto);
                updatedBto.setBo(labItemBO);
                boResult.getUpdatedList().add(updatedBto);
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(labItemBO.convertToLabItem());
                updatedBto.setBto(saveLabItemVsSpecimenBto);
                updatedBto.setBo(labItemBO);
                boResult.getUpdatedList().add(updatedBto);
            }
        } else {
            labItemBO = new LabItemBO();
            if (pkExist) {
                labItemBO.setId(saveLabItemVsSpecimenBto.getId());
            } else {
                labItemBO.setId(String.valueOf(this.idGenerator.allocateId("lab_item")));
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(saveLabItemVsSpecimenBto);
            addedBto.setBo(labItemBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return labItemBO;
    }

    /** 启用检验项目 */
    @AutoGenerated(locked = true)
    protected EnableLabItemBoResult enableLabItemBase(EnableLabItemBto enableLabItemBto) {
        if (enableLabItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        EnableLabItemBoResult boResult = new EnableLabItemBoResult();
        LabItemBO labItemBO = updateEnableLabItemOnMissThrowEx(boResult, enableLabItemBto);
        boResult.setRootBo(labItemBO);
        return boResult;
    }

    /** 保存检验项目规则 */
    @AutoGenerated(locked = true)
    protected MergeLabItemRuleBoResult mergeLabItemRuleBase(
            MergeLabItemRuleBto mergeLabItemRuleBto) {
        if (mergeLabItemRuleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeLabItemRuleBoResult boResult = new MergeLabItemRuleBoResult();
        LabItemBO labItemBO = updateMergeLabItemRuleOnMissThrowEx(boResult, mergeLabItemRuleBto);
        if (labItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeLabItemRuleBto, "__$validPropertySet"),
                    "labItemRuleBto")) {
                createLabItemRuleBtoOnDuplicateUpdate(boResult, mergeLabItemRuleBto, labItemBO);
            }
        }
        boResult.setRootBo(labItemBO);
        return boResult;
    }

    /** 保存检验套餐明细 */
    @AutoGenerated(locked = true)
    protected SaveLabItemDetailBoResult saveLabItemDetailBase(
            SaveLabItemDetailBto saveLabItemDetailBto) {
        if (saveLabItemDetailBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveLabItemDetailBoResult boResult = new SaveLabItemDetailBoResult();
        LabItemBO labItemBO =
                createSaveLabItemDetailOnDuplicateUpdate(boResult, saveLabItemDetailBto);
        if (labItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(saveLabItemDetailBto, "__$validPropertySet"),
                    "labItemPackageDetailBtoList")) {
                createLabItemPackageDetailBtoOnDuplicateUpdate(
                        boResult, saveLabItemDetailBto, labItemBO);
            }
        }
        boResult.setRootBo(labItemBO);
        return boResult;
    }

    /** 保存检验项目执行科室信息 */
    @AutoGenerated(locked = true)
    protected SaveLabItemPerformDepartmentBoResult saveLabItemPerformDepartmentBase(
            SaveLabItemPerformDepartmentBto saveLabItemPerformDepartmentBto) {
        if (saveLabItemPerformDepartmentBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveLabItemPerformDepartmentBoResult boResult = new SaveLabItemPerformDepartmentBoResult();
        LabItemBO labItemBO =
                createSaveLabItemPerformDepartmentOnDuplicateUpdate(
                        boResult, saveLabItemPerformDepartmentBto);
        if (labItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveLabItemPerformDepartmentBto, "__$validPropertySet"),
                    "labItemPerformDepartmentBtoList")) {
                createLabItemPerformDepartmentBtoOnDuplicateUpdate(
                        boResult, saveLabItemPerformDepartmentBto, labItemBO);
            }
        }
        boResult.setRootBo(labItemBO);
        return boResult;
    }

    /** 保存检验项目对应标本信息 */
    @AutoGenerated(locked = true)
    protected SaveLabItemVsSpecimenBoResult saveLabItemVsSpecimenBase(
            SaveLabItemVsSpecimenBto saveLabItemVsSpecimenBto) {
        if (saveLabItemVsSpecimenBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        SaveLabItemVsSpecimenBoResult boResult = new SaveLabItemVsSpecimenBoResult();
        LabItemBO labItemBO =
                createSaveLabItemVsSpecimenOnDuplicateUpdate(boResult, saveLabItemVsSpecimenBto);
        if (labItemBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    saveLabItemVsSpecimenBto, "__$validPropertySet"),
                    "labItemVsSpecimenBtoList")) {
                createLabItemVsSpecimenBtoOnDuplicateUpdate(
                        boResult, saveLabItemVsSpecimenBto, labItemBO);
            }
        }
        boResult.setRootBo(labItemBO);
        return boResult;
    }

    /** 更新对象:enableLabItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private LabItemBO updateEnableLabItemOnMissThrowEx(
            BaseLabItemBOService.EnableLabItemBoResult boResult,
            EnableLabItemBto enableLabItemBto) {
        LabItemBO labItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (enableLabItemBto.getId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getById(enableLabItemBto.getId());
            found = true;
        }
        if (labItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(labItemBO.convertToLabItem());
            updatedBto.setBto(enableLabItemBto);
            updatedBto.setBo(labItemBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(enableLabItemBto, "__$validPropertySet"),
                    "enableFlag")) {
                labItemBO.setEnableFlag(enableLabItemBto.getEnableFlag());
            }
            return labItemBO;
        }
    }

    /** 修改检验信息 */
    @AutoGenerated(locked = true)
    protected UpdateLabItemBoResult updateLabItemBase(UpdateLabItemBto updateLabItemBto) {
        if (updateLabItemBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        UpdateLabItemBoResult boResult = new UpdateLabItemBoResult();
        LabItemBO labItemBO = updateUpdateLabItemOnMissThrowEx(boResult, updateLabItemBto);
        boResult.setRootBo(labItemBO);
        return boResult;
    }

    /** 更新对象:mergeLabItemRule,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private LabItemBO updateMergeLabItemRuleOnMissThrowEx(
            BaseLabItemBOService.MergeLabItemRuleBoResult boResult,
            MergeLabItemRuleBto mergeLabItemRuleBto) {
        LabItemBO labItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (mergeLabItemRuleBto.getId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getById(mergeLabItemRuleBto.getId());
            found = true;
        }
        if (labItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(labItemBO.convertToLabItem());
            updatedBto.setBto(mergeLabItemRuleBto);
            updatedBto.setBo(labItemBO);
            boResult.getUpdatedList().add(updatedBto);
            return labItemBO;
        }
    }

    /** 更新对象:updateLabItem,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private LabItemBO updateUpdateLabItemOnMissThrowEx(
            BaseLabItemBOService.UpdateLabItemBoResult boResult,
            UpdateLabItemBto updateLabItemBto) {
        LabItemBO labItemBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (updateLabItemBto.getId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getById(updateLabItemBto.getId());
            found = true;
        }
        allNull = (updateLabItemBto.getClinicItemId() == null);
        if (!allNull && !found) {
            labItemBO = LabItemBO.getByClinicItemId(updateLabItemBto.getClinicItemId());
            if (labItemBO != null) {
                found = true;
            }
        }
        if (labItemBO == null) {
            throw new IgnoredException(400, "更新失败，无法找到原对象！");
        } else {
            UpdatedBto updatedBto = new UpdatedBto();
            updatedBto.setEntity(labItemBO.convertToLabItem());
            updatedBto.setBto(updateLabItemBto);
            updatedBto.setBo(labItemBO);
            boResult.getUpdatedList().add(updatedBto);
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "labClassId")) {
                labItemBO.setLabClassId(updateLabItemBto.getLabClassId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "clinicItemId")) {
                labItemBO.setClinicItemId(updateLabItemBto.getClinicItemId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "sortNumber")) {
                labItemBO.setSortNumber(updateLabItemBto.getSortNumber());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "urgentType")) {
                labItemBO.setUrgentType(updateLabItemBto.getUrgentType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "containerId")) {
                labItemBO.setContainerId(updateLabItemBto.getContainerId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "packageFlag")) {
                labItemBO.setPackageFlag(updateLabItemBto.getPackageFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "packageOrderMode")) {
                labItemBO.setPackageOrderMode(updateLabItemBto.getPackageOrderMode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "collectBodyPart")) {
                labItemBO.setCollectBodyPart(updateLabItemBto.getCollectBodyPart());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "collectType")) {
                labItemBO.setCollectType(updateLabItemBto.getCollectType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "printName")) {
                labItemBO.setPrintName(updateLabItemBto.getPrintName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "printCount")) {
                labItemBO.setPrintCount(updateLabItemBto.getPrintCount());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "deliveryFlag")) {
                labItemBO.setDeliveryFlag(updateLabItemBto.getDeliveryFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "defaultSpecimenFlag")) {
                labItemBO.setDefaultSpecimenFlag(updateLabItemBto.getDefaultSpecimenFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "bloodCollectionTime")) {
                labItemBO.setBloodCollectionTime(updateLabItemBto.getBloodCollectionTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "bloodCollectionVolume")) {
                labItemBO.setBloodCollectionVolume(updateLabItemBto.getBloodCollectionVolume());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "bloodCollectionInstruction")) {
                labItemBO.setBloodCollectionInstruction(
                        updateLabItemBto.getBloodCollectionInstruction());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "labItemName")) {
                labItemBO.setLabItemName(updateLabItemBto.getLabItemName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "specimenType")) {
                labItemBO.setSpecimenType(updateLabItemBto.getSpecimenType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "inputCode")) {
                labItemBO.setInputCode(updateLabItemBto.getInputCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(updateLabItemBto, "__$validPropertySet"),
                    "bloodCollectionLocation")) {
                labItemBO.setBloodCollectionLocation(updateLabItemBto.getBloodCollectionLocation());
            }
            return labItemBO;
        }
    }

    public static class CreateLabItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public LabItemBO getRootBo() {
            return (LabItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateLabItemBto, LabItemBO> getCreatedBto(
                CreateLabItemBto createLabItemBto) {
            return this.getAddedResult(createLabItemBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateLabItemBto.LabItemPackageDetailBto, LabItemPackageDetailBO>
                getCreatedBto(CreateLabItemBto.LabItemPackageDetailBto labItemPackageDetailBto) {
            return this.getAddedResult(labItemPackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateLabItemBto.LabItemVsSpecimenBto, LabItemVsSpecimenBO> getCreatedBto(
                CreateLabItemBto.LabItemVsSpecimenBto labItemVsSpecimenBto) {
            return this.getAddedResult(labItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateLabItemBto.LabItemRuleBto, LabItemRuleBO> getCreatedBto(
                CreateLabItemBto.LabItemRuleBto labItemRuleBto) {
            return this.getAddedResult(labItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public LabItem getDeleted_LabItem() {
            return (LabItem) CollectionUtil.getFirst(this.getDeletedEntityList(LabItem.class));
        }

        @AutoGenerated(locked = true)
        public LabItemPackageDetail getDeleted_LabItemPackageDetail() {
            return (LabItemPackageDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(LabItemPackageDetail.class));
        }

        @AutoGenerated(locked = true)
        public LabItemVsSpecimen getDeleted_LabItemVsSpecimen() {
            return (LabItemVsSpecimen)
                    CollectionUtil.getFirst(this.getDeletedEntityList(LabItemVsSpecimen.class));
        }

        @AutoGenerated(locked = true)
        public LabItemRule getDeleted_LabItemRule() {
            return (LabItemRule)
                    CollectionUtil.getFirst(this.getDeletedEntityList(LabItemRule.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateLabItemBto, LabItem, LabItemBO> getUpdatedBto(
                CreateLabItemBto createLabItemBto) {
            return super.getUpdatedResult(createLabItemBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateLabItemBto.LabItemPackageDetailBto,
                        LabItemPackageDetail,
                        LabItemPackageDetailBO>
                getUpdatedBto(CreateLabItemBto.LabItemPackageDetailBto labItemPackageDetailBto) {
            return super.getUpdatedResult(labItemPackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        CreateLabItemBto.LabItemVsSpecimenBto,
                        LabItemVsSpecimen,
                        LabItemVsSpecimenBO>
                getUpdatedBto(CreateLabItemBto.LabItemVsSpecimenBto labItemVsSpecimenBto) {
            return super.getUpdatedResult(labItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateLabItemBto.LabItemRuleBto, LabItemRule, LabItemRuleBO>
                getUpdatedBto(CreateLabItemBto.LabItemRuleBto labItemRuleBto) {
            return super.getUpdatedResult(labItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateLabItemBto, LabItemBO> getUnmodifiedBto(
                CreateLabItemBto createLabItemBto) {
            return super.getUnmodifiedResult(createLabItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateLabItemBto.LabItemPackageDetailBto, LabItemPackageDetailBO>
                getUnmodifiedBto(CreateLabItemBto.LabItemPackageDetailBto labItemPackageDetailBto) {
            return super.getUnmodifiedResult(labItemPackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateLabItemBto.LabItemVsSpecimenBto, LabItemVsSpecimenBO>
                getUnmodifiedBto(CreateLabItemBto.LabItemVsSpecimenBto labItemVsSpecimenBto) {
            return super.getUnmodifiedResult(labItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateLabItemBto.LabItemRuleBto, LabItemRuleBO> getUnmodifiedBto(
                CreateLabItemBto.LabItemRuleBto labItemRuleBto) {
            return super.getUnmodifiedResult(labItemRuleBto);
        }
    }

    public static class SaveLabItemDetailBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public LabItemBO getRootBo() {
            return (LabItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveLabItemDetailBto.LabItemPackageDetailBto, LabItemPackageDetailBO>
                getCreatedBto(
                        SaveLabItemDetailBto.LabItemPackageDetailBto labItemPackageDetailBto) {
            return this.getAddedResult(labItemPackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveLabItemDetailBto, LabItemBO> getCreatedBto(
                SaveLabItemDetailBto saveLabItemDetailBto) {
            return this.getAddedResult(saveLabItemDetailBto);
        }

        @AutoGenerated(locked = true)
        public LabItemPackageDetail getDeleted_LabItemPackageDetail() {
            return (LabItemPackageDetail)
                    CollectionUtil.getFirst(this.getDeletedEntityList(LabItemPackageDetail.class));
        }

        @AutoGenerated(locked = true)
        public LabItem getDeleted_LabItem() {
            return (LabItem) CollectionUtil.getFirst(this.getDeletedEntityList(LabItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveLabItemDetailBto.LabItemPackageDetailBto,
                        LabItemPackageDetail,
                        LabItemPackageDetailBO>
                getUpdatedBto(
                        SaveLabItemDetailBto.LabItemPackageDetailBto labItemPackageDetailBto) {
            return super.getUpdatedResult(labItemPackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveLabItemDetailBto, LabItem, LabItemBO> getUpdatedBto(
                SaveLabItemDetailBto saveLabItemDetailBto) {
            return super.getUpdatedResult(saveLabItemDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveLabItemDetailBto.LabItemPackageDetailBto, LabItemPackageDetailBO>
                getUnmodifiedBto(
                        SaveLabItemDetailBto.LabItemPackageDetailBto labItemPackageDetailBto) {
            return super.getUnmodifiedResult(labItemPackageDetailBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveLabItemDetailBto, LabItemBO> getUnmodifiedBto(
                SaveLabItemDetailBto saveLabItemDetailBto) {
            return super.getUnmodifiedResult(saveLabItemDetailBto);
        }
    }

    public static class SaveLabItemVsSpecimenBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public LabItemBO getRootBo() {
            return (LabItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveLabItemVsSpecimenBto, LabItemBO> getCreatedBto(
                SaveLabItemVsSpecimenBto saveLabItemVsSpecimenBto) {
            return this.getAddedResult(saveLabItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveLabItemVsSpecimenBto.LabItemVsSpecimenBto, LabItemVsSpecimenBO>
                getCreatedBto(SaveLabItemVsSpecimenBto.LabItemVsSpecimenBto labItemVsSpecimenBto) {
            return this.getAddedResult(labItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public LabItem getDeleted_LabItem() {
            return (LabItem) CollectionUtil.getFirst(this.getDeletedEntityList(LabItem.class));
        }

        @AutoGenerated(locked = true)
        public LabItemVsSpecimen getDeleted_LabItemVsSpecimen() {
            return (LabItemVsSpecimen)
                    CollectionUtil.getFirst(this.getDeletedEntityList(LabItemVsSpecimen.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveLabItemVsSpecimenBto, LabItem, LabItemBO> getUpdatedBto(
                SaveLabItemVsSpecimenBto saveLabItemVsSpecimenBto) {
            return super.getUpdatedResult(saveLabItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveLabItemVsSpecimenBto.LabItemVsSpecimenBto,
                        LabItemVsSpecimen,
                        LabItemVsSpecimenBO>
                getUpdatedBto(SaveLabItemVsSpecimenBto.LabItemVsSpecimenBto labItemVsSpecimenBto) {
            return super.getUpdatedResult(labItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveLabItemVsSpecimenBto, LabItemBO> getUnmodifiedBto(
                SaveLabItemVsSpecimenBto saveLabItemVsSpecimenBto) {
            return super.getUnmodifiedResult(saveLabItemVsSpecimenBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveLabItemVsSpecimenBto.LabItemVsSpecimenBto, LabItemVsSpecimenBO>
                getUnmodifiedBto(
                        SaveLabItemVsSpecimenBto.LabItemVsSpecimenBto labItemVsSpecimenBto) {
            return super.getUnmodifiedResult(labItemVsSpecimenBto);
        }
    }

    public static class SaveLabItemPerformDepartmentBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public LabItemBO getRootBo() {
            return (LabItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<
                        SaveLabItemPerformDepartmentBto.LabItemPerformDepartmentBto,
                        LabItemPerformDepartmentBO>
                getCreatedBto(
                        SaveLabItemPerformDepartmentBto.LabItemPerformDepartmentBto
                                labItemPerformDepartmentBto) {
            return this.getAddedResult(labItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<SaveLabItemPerformDepartmentBto, LabItemBO> getCreatedBto(
                SaveLabItemPerformDepartmentBto saveLabItemPerformDepartmentBto) {
            return this.getAddedResult(saveLabItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public LabItemPerformDepartment getDeleted_LabItemPerformDepartment() {
            return (LabItemPerformDepartment)
                    CollectionUtil.getFirst(
                            this.getDeletedEntityList(LabItemPerformDepartment.class));
        }

        @AutoGenerated(locked = true)
        public LabItem getDeleted_LabItem() {
            return (LabItem) CollectionUtil.getFirst(this.getDeletedEntityList(LabItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<
                        SaveLabItemPerformDepartmentBto.LabItemPerformDepartmentBto,
                        LabItemPerformDepartment,
                        LabItemPerformDepartmentBO>
                getUpdatedBto(
                        SaveLabItemPerformDepartmentBto.LabItemPerformDepartmentBto
                                labItemPerformDepartmentBto) {
            return super.getUpdatedResult(labItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<SaveLabItemPerformDepartmentBto, LabItem, LabItemBO> getUpdatedBto(
                SaveLabItemPerformDepartmentBto saveLabItemPerformDepartmentBto) {
            return super.getUpdatedResult(saveLabItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<
                        SaveLabItemPerformDepartmentBto.LabItemPerformDepartmentBto,
                        LabItemPerformDepartmentBO>
                getUnmodifiedBto(
                        SaveLabItemPerformDepartmentBto.LabItemPerformDepartmentBto
                                labItemPerformDepartmentBto) {
            return super.getUnmodifiedResult(labItemPerformDepartmentBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<SaveLabItemPerformDepartmentBto, LabItemBO> getUnmodifiedBto(
                SaveLabItemPerformDepartmentBto saveLabItemPerformDepartmentBto) {
            return super.getUnmodifiedResult(saveLabItemPerformDepartmentBto);
        }
    }

    public static class UpdateLabItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public LabItemBO getRootBo() {
            return (LabItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<UpdateLabItemBto, LabItemBO> getCreatedBto(
                UpdateLabItemBto updateLabItemBto) {
            return this.getAddedResult(updateLabItemBto);
        }

        @AutoGenerated(locked = true)
        public LabItem getDeleted_LabItem() {
            return (LabItem) CollectionUtil.getFirst(this.getDeletedEntityList(LabItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<UpdateLabItemBto, LabItem, LabItemBO> getUpdatedBto(
                UpdateLabItemBto updateLabItemBto) {
            return super.getUpdatedResult(updateLabItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<UpdateLabItemBto, LabItemBO> getUnmodifiedBto(
                UpdateLabItemBto updateLabItemBto) {
            return super.getUnmodifiedResult(updateLabItemBto);
        }
    }

    public static class EnableLabItemBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public LabItemBO getRootBo() {
            return (LabItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<EnableLabItemBto, LabItemBO> getCreatedBto(
                EnableLabItemBto enableLabItemBto) {
            return this.getAddedResult(enableLabItemBto);
        }

        @AutoGenerated(locked = true)
        public LabItem getDeleted_LabItem() {
            return (LabItem) CollectionUtil.getFirst(this.getDeletedEntityList(LabItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<EnableLabItemBto, LabItem, LabItemBO> getUpdatedBto(
                EnableLabItemBto enableLabItemBto) {
            return super.getUpdatedResult(enableLabItemBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<EnableLabItemBto, LabItemBO> getUnmodifiedBto(
                EnableLabItemBto enableLabItemBto) {
            return super.getUnmodifiedResult(enableLabItemBto);
        }
    }

    public static class MergeLabItemRuleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public LabItemBO getRootBo() {
            return (LabItemBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeLabItemRuleBto.LabItemRuleBto, LabItemRuleBO> getCreatedBto(
                MergeLabItemRuleBto.LabItemRuleBto labItemRuleBto) {
            return this.getAddedResult(labItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeLabItemRuleBto, LabItemBO> getCreatedBto(
                MergeLabItemRuleBto mergeLabItemRuleBto) {
            return this.getAddedResult(mergeLabItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public LabItemRule getDeleted_LabItemRule() {
            return (LabItemRule)
                    CollectionUtil.getFirst(this.getDeletedEntityList(LabItemRule.class));
        }

        @AutoGenerated(locked = true)
        public LabItem getDeleted_LabItem() {
            return (LabItem) CollectionUtil.getFirst(this.getDeletedEntityList(LabItem.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeLabItemRuleBto.LabItemRuleBto, LabItemRule, LabItemRuleBO>
                getUpdatedBto(MergeLabItemRuleBto.LabItemRuleBto labItemRuleBto) {
            return super.getUpdatedResult(labItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeLabItemRuleBto, LabItem, LabItemBO> getUpdatedBto(
                MergeLabItemRuleBto mergeLabItemRuleBto) {
            return super.getUpdatedResult(mergeLabItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeLabItemRuleBto.LabItemRuleBto, LabItemRuleBO> getUnmodifiedBto(
                MergeLabItemRuleBto.LabItemRuleBto labItemRuleBto) {
            return super.getUnmodifiedResult(labItemRuleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeLabItemRuleBto, LabItemBO> getUnmodifiedBto(
                MergeLabItemRuleBto mergeLabItemRuleBto) {
            return super.getUnmodifiedResult(mergeLabItemRuleBto);
        }
    }
}
