package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.ExamDeviceChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.ExamDeviceChargeItemBaseDto;
import com.pulse.dictionary_business.service.converter.ExamDeviceChargeItemBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "0d3601d9-d9e8-4073-af38-308a9546f750|DTO|SERVICE")
public class ExamDeviceChargeItemBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private ExamDeviceChargeItemBaseDtoManager examDeviceChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private ExamDeviceChargeItemBaseDtoServiceConverter examDeviceChargeItemBaseDtoServiceConverter;

    @PublicInterface(id = "634d6ad2-2576-47eb-b808-aec034e3b7a0", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "22bc1996-dfc1-3175-b41e-45fdf212b226")
    public List<ExamDeviceChargeItemBaseDto> getByChargeItemId(
            @NotNull(message = "收费项目ID不能为空") String chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByChargeItemIds(Arrays.asList(chargeItemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "5b84314e-958e-4524-9e00-92985fa36e5f", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "4263c1d6-2898-3a14-8bc0-d09c646247dc")
    public List<ExamDeviceChargeItemBaseDto> getByChargeItemIds(
            @Valid @NotNull(message = "收费项目ID不能为空") List<String> chargeItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        chargeItemId = new ArrayList<>(new HashSet<>(chargeItemId));
        List<ExamDeviceChargeItemBaseDto> examDeviceChargeItemBaseDtoList =
                examDeviceChargeItemBaseDtoManager.getByChargeItemIds(chargeItemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return examDeviceChargeItemBaseDtoServiceConverter.ExamDeviceChargeItemBaseDtoConverter(
                examDeviceChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "d655827b-52b8-4268-a203-b45e00bd4a4f", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "4fec9ac7-8b19-3427-871f-6636363ae0e1")
    public List<ExamDeviceChargeItemBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<ExamDeviceChargeItemBaseDto> examDeviceChargeItemBaseDtoList =
                examDeviceChargeItemBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return examDeviceChargeItemBaseDtoServiceConverter.ExamDeviceChargeItemBaseDtoConverter(
                examDeviceChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "1b61d236-9340-4a59-8533-01b1a8847d3b", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "6f8470f7-2856-3be2-8c4e-4d3a70227ced")
    public List<ExamDeviceChargeItemBaseDto> getByExamDeviceIds(
            @Valid @NotNull(message = "检查设备id不能为空") List<String> examDeviceId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        examDeviceId = new ArrayList<>(new HashSet<>(examDeviceId));
        List<ExamDeviceChargeItemBaseDto> examDeviceChargeItemBaseDtoList =
                examDeviceChargeItemBaseDtoManager.getByExamDeviceIds(examDeviceId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return examDeviceChargeItemBaseDtoServiceConverter.ExamDeviceChargeItemBaseDtoConverter(
                examDeviceChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "09b44fa8-7876-404f-8ef7-84a093e2e9a7", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "7b4db19e-b6c8-3d94-87b2-665f337923b0")
    public List<ExamDeviceChargeItemBaseDto> getByExamDeviceId(
            @NotNull(message = "检查设备id不能为空") String examDeviceId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByExamDeviceIds(Arrays.asList(examDeviceId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "2c7f21ba-7d7d-4fa2-98bf-cb7319d7ce53", module = "dictionary_business")
    @AutoGenerated(locked = false, uuid = "89b5c7df-26e4-37bb-91f7-581bf5d80709")
    public ExamDeviceChargeItemBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ExamDeviceChargeItemBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
