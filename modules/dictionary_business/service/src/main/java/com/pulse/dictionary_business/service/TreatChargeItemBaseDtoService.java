package com.pulse.dictionary_business.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.dictionary_business.manager.TreatChargeItemBaseDtoManager;
import com.pulse.dictionary_business.manager.dto.TreatChargeItemBaseDto;
import com.pulse.dictionary_business.service.converter.TreatChargeItemBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "863e24be-e7b6-4591-8b47-f29ab42f5f22|DTO|SERVICE")
public class TreatChargeItemBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private TreatChargeItemBaseDtoManager treatChargeItemBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private TreatChargeItemBaseDtoServiceConverter treatChargeItemBaseDtoServiceConverter;

    @PublicInterface(
            id = "aa10f8b9-7574-4006-ade8-afbf49cb9efb",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1747991146314")
    @AutoGenerated(locked = false, uuid = "550f881f-754d-3a3e-b0e7-257d6d243aa9")
    public List<TreatChargeItemBaseDto> getByChargeItemCode(
            @NotNull(message = "收费项目代码不能为空") String chargeItemCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByChargeItemCodes(Arrays.asList(chargeItemCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "2de8371e-192c-48b5-91c2-0c025e5eaf69",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1747991077792")
    @AutoGenerated(locked = false, uuid = "5e1e5b49-3322-3fdf-888a-6a5fe24f3098")
    public TreatChargeItemBaseDto getById(@NotNull(message = "主键不能为空") Long id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<TreatChargeItemBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "14702664-5889-467e-9526-833a0a7d6d2c",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1747991146316")
    @AutoGenerated(locked = false, uuid = "73abd6a0-df5b-39ff-9f40-e9532154ef98")
    public List<TreatChargeItemBaseDto> getByChargeItemCodes(
            @Valid @NotNull(message = "收费项目代码不能为空") List<String> chargeItemCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        chargeItemCode = new ArrayList<>(new HashSet<>(chargeItemCode));
        List<TreatChargeItemBaseDto> treatChargeItemBaseDtoList =
                treatChargeItemBaseDtoManager.getByChargeItemCodes(chargeItemCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return treatChargeItemBaseDtoServiceConverter.TreatChargeItemBaseDtoConverter(
                treatChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "7be70663-30c7-4895-b998-76fe0c06031d",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1747991077795")
    @AutoGenerated(locked = false, uuid = "b45655b0-9d1e-36c9-b6a9-60a38065e1fd")
    public List<TreatChargeItemBaseDto> getByTreatItemId(
            @NotNull(message = "治疗项目ID不能为空") String treatItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByTreatItemIds(Arrays.asList(treatItemId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "d2dd21e6-4eac-40a2-afda-4521928a7557",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1747991077793")
    @AutoGenerated(locked = false, uuid = "bdb6f5de-5d60-387d-8eb6-6d0b4f67719a")
    public List<TreatChargeItemBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<Long> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<TreatChargeItemBaseDto> treatChargeItemBaseDtoList =
                treatChargeItemBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return treatChargeItemBaseDtoServiceConverter.TreatChargeItemBaseDtoConverter(
                treatChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "f6da283c-b952-496f-93b7-5527cce6dd8e",
            module = "dictionary_business",
            moduleId = "c87071a8-7ca8-438e-998f-eaf79a9cffee",
            pubRpc = true,
            version = "1747991077796")
    @AutoGenerated(locked = false, uuid = "e744578a-e310-3093-881c-cb1dd6aa2202")
    public List<TreatChargeItemBaseDto> getByTreatItemIds(
            @Valid @NotNull(message = "治疗项目ID不能为空") List<String> treatItemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        treatItemId = new ArrayList<>(new HashSet<>(treatItemId));
        List<TreatChargeItemBaseDto> treatChargeItemBaseDtoList =
                treatChargeItemBaseDtoManager.getByTreatItemIds(treatItemId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return treatChargeItemBaseDtoServiceConverter.TreatChargeItemBaseDtoConverter(
                treatChargeItemBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
