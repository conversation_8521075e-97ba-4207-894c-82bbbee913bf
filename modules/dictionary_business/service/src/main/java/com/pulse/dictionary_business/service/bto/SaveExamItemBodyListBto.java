package com.pulse.dictionary_business.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamItem
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "b9da5df7-5555-432a-bec9-030c9926dd97|BTO|DEFINITION")
public class SaveExamItemBodyListBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    @Valid
    @AutoGenerated(locked = true, uuid = "b0415b80-a5f4-4eb6-a717-05b149a9eff7")
    private List<SaveExamItemBodyListBto.ExamItemBodyBto> examItemBodyBtoList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "f492890d-88b0-4791-9018-e386c2b44ba1")
    private String id;

    @AutoGenerated(locked = true)
    public void setExamItemBodyBtoList(
            List<SaveExamItemBodyListBto.ExamItemBodyBto> examItemBodyBtoList) {
        this.__$validPropertySet.add("examItemBodyBtoList");
        this.examItemBodyBtoList = examItemBodyBtoList;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    /**
     * <b>[源自]</b> ExamItemBody
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE | ON_MISS_DELETE_ALL
     */
    @Getter
    @NoArgsConstructor
    public static class ExamItemBodyBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "0e775d80-0350-4d2c-bb00-f95fa28dd6b9")
        private String id;

        /** 检查部位ID */
        @AutoGenerated(locked = true, uuid = "72525e55-d821-4b7b-81ff-27500d206f8a")
        private String examBodyId;

        /** 创建者 */
        @AutoGenerated(locked = true, uuid = "72ce5f3e-4035-4141-9fdb-180289fabe48")
        private String createdBy;

        /** 更新者 */
        @AutoGenerated(locked = true, uuid = "9de9279c-c05d-42b8-a691-674b9f697b4b")
        private String updatedBy;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setExamBodyId(String examBodyId) {
            this.__$validPropertySet.add("examBodyId");
            this.examBodyId = examBodyId;
        }

        @AutoGenerated(locked = true)
        public void setCreatedBy(String createdBy) {
            this.__$validPropertySet.add("createdBy");
            this.createdBy = createdBy;
        }

        @AutoGenerated(locked = true)
        public void setUpdatedBy(String updatedBy) {
            this.__$validPropertySet.add("updatedBy");
            this.updatedBy = updatedBy;
        }
    }
}
