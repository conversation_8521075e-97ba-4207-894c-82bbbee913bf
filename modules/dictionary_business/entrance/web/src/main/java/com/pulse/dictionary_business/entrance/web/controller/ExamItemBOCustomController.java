package com.pulse.dictionary_business.entrance.web.controller;

import com.pulse.dictionary_business.manager.dto.ExamItemBaseDto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBOService;
import com.pulse.dictionary_business.service.ExamItemBaseDtoService;
import com.pulse.dictionary_business.service.bto.*;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "45240265-4f69-3466-b7f4-30c200dbe7fe")
public class ExamItemBOCustomController {

    @Resource private ClinicItemDictionaryBOService clinicItemDictionaryBOService;

    @Resource private ExamItemBaseDtoService examItemBaseDtoService;

    /** 更改检查项目导医说明启用标记 */
    @PublicInterface(id = "0a34687e-3e62-48fd-888b-e33a447943f1", version = "1742375441550")
    @AutoGenerated(locked = false, uuid = "0a34687e-3e62-48fd-888b-e33a447943f1")
    @RequestMapping(
            value = {"/api/dictionary-business/change-exam-item-guide-description"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String changeExamItemGuideDescriptionEnableFlag(
            @NotNull String clinicItemGuideDescriptionId, @NotNull Boolean enableFlag) {
        ChangeClinicItemGuideDescriptionEnableFlagBto.ClinicItemGuideDescriptionBto
                clinicItemGuideDescriptionBto =
                        new ChangeClinicItemGuideDescriptionEnableFlagBto
                                .ClinicItemGuideDescriptionBto();
        clinicItemGuideDescriptionBto.setId(clinicItemGuideDescriptionId);
        clinicItemGuideDescriptionBto.setEnableFlag(enableFlag);

        return clinicItemDictionaryBOService.changeClinicItemGuideDescriptionEnableFlag(
                clinicItemGuideDescriptionBto);
    }

    /** 保存检查项目收费项目列表 */
    @PublicInterface(id = "1565a18b-56c7-43f6-bd43-f293da5cb460", version = "1742451493483")
    @AutoGenerated(locked = false, uuid = "1565a18b-56c7-43f6-bd43-f293da5cb460")
    @RequestMapping(
            value = {"/api/dictionary-business/save-exam-item-charge-item-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String saveExamItemChargeItemList(
            @NotNull String examItemId, @Valid SaveClinicItemChargeItemBto examItemChargeItem) {

        ExamItemBaseDto examItemBaseDto = examItemBaseDtoService.getById(examItemId);
        if (examItemBaseDto == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "未查询到对应检查项目");
        }

        examItemChargeItem.setClinicItemId(examItemBaseDto.getClinicItemId());

        return clinicItemDictionaryBOService.saveClinicItemChargeItem(examItemChargeItem);
    }

    /** 更新检查项目导医说明 */
    @PublicInterface(id = "2c430c03-f2f0-454a-bc69-62165511ae57", version = "1742373170406")
    @AutoGenerated(locked = false, uuid = "2c430c03-f2f0-454a-bc69-62165511ae57")
    @RequestMapping(
            value = {"/api/business/update-exam-item-guide-description"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateExamItemGuideDescription(
            @Valid @NotNull
                    UpdateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                            examItemGuideDescription) {

        return clinicItemDictionaryBOService.updateClinicItemGuideDescription(
                examItemGuideDescription);
    }

    /** 创建检查项目导医说明 */
    @PublicInterface(id = "47b38969-a6a0-4547-96aa-78ecfe9437fc", version = "1742373964104")
    @AutoGenerated(locked = false, uuid = "47b38969-a6a0-4547-96aa-78ecfe9437fc")
    @RequestMapping(
            value = {"/api/dictionary-business/create-exam-item-guide-description"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createExamItemGuideDescription(
            @Valid @NotNull
                    CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto
                            createExamItemGuideDescription,
            @NotNull String examItemId) {

        ExamItemBaseDto examItemBaseDto = examItemBaseDtoService.getById(examItemId);
        if (examItemBaseDto == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "未查询到对应检查项目");
        }

        createExamItemGuideDescription.setId(null);
        createExamItemGuideDescription.setClinicItemId(examItemBaseDto.getClinicItemId());

        return clinicItemDictionaryBOService.createClinicItemGuideDescription(
                createExamItemGuideDescription);
    }

    /** 删除检查项目收费项目 */
    @PublicInterface(id = "729c78c1-abb0-4e8e-862d-2208aef7a1f9", version = "1742375211553")
    @AutoGenerated(locked = false, uuid = "729c78c1-abb0-4e8e-862d-2208aef7a1f9")
    @RequestMapping(
            value = {"/api/dictionary-business/delete-exam-item-charge-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteExamItemChargeItem(@NotNull String clinicItemChargeItemId) {
        // TODO implement method

        return null;
    }

    /** 创建检查项目对应收费项目 */
    @PublicInterface(id = "bc3d2b33-8b1d-41e2-9dbd-66d61c5681eb", version = "1742449319321")
    @AutoGenerated(locked = false, uuid = "bc3d2b33-8b1d-41e2-9dbd-66d61c5681eb")
    @RequestMapping(
            value = {"/api/dictionary-business/create-exam-item-charge-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createExamItemChargeItem(
            @NotNull String examItemId,
            @Valid @NotNull
                    CreateClinicItemChargeItemBto.ClinicItemChargeItemBto
                            createExamItemChargeItem) {

        ExamItemBaseDto examItemBaseDto = examItemBaseDtoService.getById(examItemId);
        if (examItemBaseDto == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "未查询到对应检查项目");
        }
        createExamItemChargeItem.setClinicItemId(examItemBaseDto.getClinicItemId());
        return clinicItemDictionaryBOService.createClinicItemChargeItem(createExamItemChargeItem);
    }

    /** 修改检查项目启用标记 */
    @PublicInterface(id = "cce4e8d0-f7bb-4984-97ec-ee150a113661", version = "1742289232506")
    @AutoGenerated(locked = false, uuid = "cce4e8d0-f7bb-4984-97ec-ee150a113661")
    @RequestMapping(
            value = {"/api/dictionary-business/change-exam-item-enable-flag"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String changeExamItemEnableFlag(
            @NotNull String examItemId, @NotNull Boolean enableFlag) {

        ExamItemBaseDto examItemBaseDto = examItemBaseDtoService.getById(examItemId);
        if (examItemBaseDto == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "未查询到对应检查项目");
        }

        EnableClinicItemBto enableClinicItemBto = new EnableClinicItemBto();
        enableClinicItemBto.setClinicItemId(examItemBaseDto.getClinicItemId());
        enableClinicItemBto.setEnableFlag(enableFlag);

        return clinicItemDictionaryBOService.enableClinicItem(enableClinicItemBto);
    }
}
