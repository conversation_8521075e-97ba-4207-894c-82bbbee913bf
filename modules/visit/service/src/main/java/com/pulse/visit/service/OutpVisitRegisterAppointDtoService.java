package com.pulse.visit.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.visit.manager.OutpVisitRegisterAppointDtoManager;
import com.pulse.visit.manager.dto.OutpVisitRegisterAppointDto;
import com.pulse.visit.service.converter.OutpVisitRegisterAppointDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "8b54bf7b-dcb7-4772-9753-f200973f06c5|DTO|SERVICE")
public class OutpVisitRegisterAppointDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OutpVisitRegisterAppointDtoManager outpVisitRegisterAppointDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpVisitRegisterAppointDtoServiceConverter outpVisitRegisterAppointDtoServiceConverter;

    @PublicInterface(id = "27302544-ae9e-444a-af6f-a15024896b60", module = "visit")
    @AutoGenerated(locked = false, uuid = "021b6540-6ce2-3db6-8204-9f3d07f37bac")
    public List<OutpVisitRegisterAppointDto> getByOutpRegisterIds(
            @Valid @NotNull(message = "挂号ID不能为空") List<String> outpRegisterId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        outpRegisterId = new ArrayList<>(new HashSet<>(outpRegisterId));
        List<OutpVisitRegisterAppointDto> outpVisitRegisterAppointDtoList =
                outpVisitRegisterAppointDtoManager.getByOutpRegisterIds(outpRegisterId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpVisitRegisterAppointDtoServiceConverter.OutpVisitRegisterAppointDtoConverter(
                outpVisitRegisterAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "cf8a3602-9ace-4b48-8867-f979c10b0d56", module = "visit")
    @AutoGenerated(locked = false, uuid = "5f1d41e8-43ea-3b7e-b97a-b99f8a141b6c")
    public List<OutpVisitRegisterAppointDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OutpVisitRegisterAppointDto> outpVisitRegisterAppointDtoList =
                outpVisitRegisterAppointDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpVisitRegisterAppointDtoServiceConverter.OutpVisitRegisterAppointDtoConverter(
                outpVisitRegisterAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "6886d07f-48e9-40cd-a291-23f9c0b394f9", module = "visit")
    @AutoGenerated(locked = false, uuid = "5f531723-7d36-3bb5-a08b-4887b1e7229e")
    public OutpVisitRegisterAppointDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OutpVisitRegisterAppointDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "d42378a6-762f-4144-9f88-bd3b34391d28", module = "visit")
    @AutoGenerated(locked = false, uuid = "a6017ba3-0eb0-377b-8457-c5ac513175fa")
    public List<OutpVisitRegisterAppointDto> getByPatientId(
            @NotNull(message = "患者ID不能为空") String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByPatientIds(Arrays.asList(patientId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "60b00f5a-7886-4a87-b422-aaf47c47745a", module = "visit")
    @AutoGenerated(locked = false, uuid = "aee05253-7c39-314d-abaf-217f6716e577")
    public List<OutpVisitRegisterAppointDto> getByPatientIds(
            @Valid @NotNull(message = "患者ID不能为空") List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        patientId = new ArrayList<>(new HashSet<>(patientId));
        List<OutpVisitRegisterAppointDto> outpVisitRegisterAppointDtoList =
                outpVisitRegisterAppointDtoManager.getByPatientIds(patientId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return outpVisitRegisterAppointDtoServiceConverter.OutpVisitRegisterAppointDtoConverter(
                outpVisitRegisterAppointDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "afedac0b-bb37-4806-ba51-f4b93c539c01", module = "visit")
    @AutoGenerated(locked = false, uuid = "f9c48e21-9d21-386d-b5ec-547aa5175152")
    public List<OutpVisitRegisterAppointDto> getByOutpRegisterId(
            @NotNull(message = "挂号ID不能为空") String outpRegisterId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOutpRegisterIds(Arrays.asList(outpRegisterId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
