package com.pulse.rule_engine.entrance.web.query.assembler;

import com.pulse.rule_engine.entrance.web.vo.RuleRefCategorySimpleVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** RuleRefCategorySimpleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "e52e1c77-a8fe-3713-8127-4f54e19e065a")
public class RuleRefCategorySimpleVoDataAssembler {

    /** 组装RuleRefCategorySimpleVo数据 */
    @AutoGenerated(locked = true, uuid = "454ae26a-2d59-3184-871a-f67e9d4d17b3")
    public void assembleData(Map<String, RuleRefCategorySimpleVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装RuleRefCategorySimpleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "e993e197-851c-316a-af33-0f75c65467fc")
    public void assembleDataCustomized(List<RuleRefCategorySimpleVo> dataList) {
        // 自定义数据组装

    }
}
