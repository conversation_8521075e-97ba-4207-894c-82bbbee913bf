package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleTestDataBaseVoDataAssembler;
import com.pulse.rule_engine.entrance.web.vo.RuleTestDataBaseVo;
import com.pulse.rule_engine.manager.dto.RuleTestDataBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleTestDataBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "d4e4def2-f1ca-4bcc-b0a2-4c0c6ea192b7|VO|CONVERTER")
public class RuleTestDataBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleTestDataBaseVoDataAssembler ruleTestDataBaseVoDataAssembler;

    /** 使用默认方式组装RuleTestDataBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "083c6283-e683-345d-9086-60c23147dcbf")
    public List<RuleTestDataBaseVo> convertAndAssembleDataList(List<RuleTestDataBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, RuleTestDataBaseVo> voMap =
                convertToRuleTestDataBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleTestDataBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把RuleTestDataBaseDto转换成RuleTestDataBaseVo */
    @AutoGenerated(locked = true, uuid = "ab7ad516-c247-301f-a38a-23bd7193744d")
    public RuleTestDataBaseVo convertToRuleTestDataBaseVo(RuleTestDataBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleTestDataBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把RuleTestDataBaseDto转换成RuleTestDataBaseVo */
    @AutoGenerated(locked = false, uuid = "d4e4def2-f1ca-4bcc-b0a2-4c0c6ea192b7-converter-Map")
    public Map<RuleTestDataBaseDto, RuleTestDataBaseVo> convertToRuleTestDataBaseVoMap(
            List<RuleTestDataBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleTestDataBaseDto, RuleTestDataBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleTestDataBaseVo vo = new RuleTestDataBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setRuleVersionId(dto.getRuleVersionId());
                                            vo.setTestData(dto.getTestData());
                                            vo.setDescription(dto.getDescription());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleTestDataBaseDto转换成RuleTestDataBaseVo */
    @AutoGenerated(locked = true, uuid = "d4e4def2-f1ca-4bcc-b0a2-4c0c6ea192b7-converter-list")
    public List<RuleTestDataBaseVo> convertToRuleTestDataBaseVoList(
            List<RuleTestDataBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleTestDataBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装RuleTestDataBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "ff25dc3c-de75-34a4-9e67-d1aafb51be74")
    public RuleTestDataBaseVo convertAndAssembleData(RuleTestDataBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
