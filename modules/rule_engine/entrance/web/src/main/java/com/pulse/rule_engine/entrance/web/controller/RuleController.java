package com.pulse.rule_engine.entrance.web.controller;

import com.pulse.rule_engine.entrance.web.converter.RuleAggVoConverter;
import com.pulse.rule_engine.entrance.web.converter.RuleExecutionLogBaseVoConverter;
import com.pulse.rule_engine.entrance.web.converter.RuleForDetailVoConverter;
import com.pulse.rule_engine.entrance.web.converter.RuleVersionBaseVoConverter;
import com.pulse.rule_engine.entrance.web.vo.RuleAggVo;
import com.pulse.rule_engine.entrance.web.vo.RuleExecutionLogBaseVo;
import com.pulse.rule_engine.entrance.web.vo.RuleForDetailVo;
import com.pulse.rule_engine.entrance.web.vo.RuleVersionBaseVo;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.eo.RuleVersionEo;
import com.pulse.rule_engine.persist.qto.ListRuleExecutionLogQto;
import com.pulse.rule_engine.persist.qto.ListRuleQto;
import com.pulse.rule_engine.service.RuleBOService;
import com.pulse.rule_engine.service.RuleBaseDtoService;
import com.pulse.rule_engine.service.RuleExecutionService;
import com.pulse.rule_engine.service.RuleVersionBaseDtoService;
import com.pulse.rule_engine.service.bto.DeleteRuleBto;
import com.pulse.rule_engine.service.bto.MergeRuleBto;
import com.pulse.rule_engine.service.query.RuleBaseDtoQueryService;
import com.pulse.rule_engine.service.query.RuleExecutionLogBaseDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "dfdd6994-80a5-3618-96e4-49bbe52511d0")
public class RuleController {
    @AutoGenerated(locked = true)
    @Resource
    private RuleAggVoConverter ruleAggVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleBOService ruleBOService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleBaseDtoQueryService ruleBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleBaseDtoService ruleBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleExecutionLogBaseDtoQueryService ruleExecutionLogBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleExecutionLogBaseVoConverter ruleExecutionLogBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleForDetailVoConverter ruleForDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RuleVersionBaseDtoService ruleVersionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleVersionBaseVoConverter ruleVersionBaseVoConverter;

    @Resource private RuleExecutionService ruleExecutionService;

    /** 执行规则 根据业务数据执行指定版本的规则：规则版本ID/版本号可选，如非空则使用指定版本的规则，如空则使用当前最新版的规则； */
    @PublicInterface(id = "1527dc95-c8b8-4d79-9b4d-737547703ca1", version = "1748596001040")
    @AutoGenerated(locked = false, uuid = "1527dc95-c8b8-4d79-9b4d-737547703ca1")
    @RequestMapping(
            value = {"/api/rule-engine/execute-rule"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public Boolean executeRule(
            @Valid @NotNull List<RuleVersionEo> ruleVersionList, @NotNull String businessData) {
        return ruleExecutionService.executeRule(ruleVersionList, businessData);
    }

    /** 保存规则，包括规则基础信息、规则版本、规则组织信息 */
    @PublicInterface(id = "18ae7561-96bb-45d7-aa76-507970521ccd", version = "1748583058624")
    @AutoGenerated(locked = false, uuid = "18ae7561-96bb-45d7-aa76-507970521ccd")
    @RequestMapping(
            value = {"/api/rule-engine/merge-rule"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeRule(@Valid @NotNull MergeRuleBto mergeRuleBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = ruleBOService.mergeRule(mergeRuleBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询规则执行日志列表(分页) */
    @PublicInterface(id = "2f7e67bc-aa6b-4420-b3a1-67a1638a8876", version = "1748582952297")
    @AutoGenerated(locked = false, uuid = "2f7e67bc-aa6b-4420-b3a1-67a1638a8876")
    @RequestMapping(
            value = {"/api/rule-engine/list-rule-execution-log-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<RuleExecutionLogBaseVo> listRuleExecutionLogPaged(
            @Valid @NotNull ListRuleExecutionLogQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<RuleExecutionLogBaseDto> dtoResult =
                ruleExecutionLogBaseDtoQueryService.listRuleExecutionLogPaged(qto);
        VSQueryResult<RuleExecutionLogBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                ruleExecutionLogBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据规则ID获取规则版本列表 */
    @PublicInterface(id = "8230f78d-b6f7-49c2-b86a-b06a40400a68", version = "1748582709933")
    @AutoGenerated(locked = false, uuid = "8230f78d-b6f7-49c2-b86a-b06a40400a68")
    @RequestMapping(
            value = {"/api/rule-engine/get-by-rule-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<RuleVersionBaseVo> getByRuleId(@NotNull String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> rpcResult = ruleVersionBaseDtoService.getByRuleId(ruleId);
        List<RuleVersionBaseVo> result =
                ruleVersionBaseVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除规则（逻辑删除） */
    @PublicInterface(id = "baa85a71-0c56-4219-80c4-5db2e819dc3b", version = "1748583129024")
    @AutoGenerated(locked = false, uuid = "baa85a71-0c56-4219-80c4-5db2e819dc3b")
    @RequestMapping(
            value = {"/api/rule-engine/delete-rule"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteRule(@Valid @NotNull DeleteRuleBto deleteRuleBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = ruleBOService.deleteRule(deleteRuleBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据主键获取规则详情 */
    @PublicInterface(id = "beffde68-c186-4852-a0dc-b9244d387042", version = "1748582623440")
    @AutoGenerated(locked = false, uuid = "beffde68-c186-4852-a0dc-b9244d387042")
    @RequestMapping(
            value = {"/api/rule-engine/get-detail-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public RuleForDetailVo getDetailById(@NotNull String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RuleBaseDto rpcResult = ruleBaseDtoService.getById(id);
        RuleForDetailVo result = ruleForDetailVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询规则列表(分页) */
    @PublicInterface(id = "e85d1046-2e10-46f2-9118-9a0b0e550fb6", version = "1748576774825")
    @AutoGenerated(locked = false, uuid = "e85d1046-2e10-46f2-9118-9a0b0e550fb6")
    @RequestMapping(
            value = {"/api/rule-engine/list-rule-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<RuleAggVo> listRulePaged(@Valid @NotNull ListRuleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<RuleBaseDto> dtoResult = ruleBaseDtoQueryService.listRulePaged(qto);
        VSQueryResult<RuleAggVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(ruleAggVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
