package com.pulse.rule_engine.entrance.web.vo;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "03bf3a97-30de-433e-9283-b8ef0281b9cb|VO|DEFINITION")
public class RuleForDetailVo {
    /** 规则编码 规则编码，英文唯一标识，如 "ANTIBIOTIC_APPROVAL" */
    @AutoGenerated(locked = true, uuid = "c36a586b-9dbd-41fb-abda-da210468d48f")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "045d7b1d-032b-4adb-b6e2-abe960b6b75c")
    private Date createdAt;

    /** 创建人 创建人ID，外键关联用户表 */
    @AutoGenerated(locked = true, uuid = "bd14ca7d-f0d6-44cb-a19e-7bad9a43c6d9")
    private String createdBy;

    /** 当前生效版本 当前生效版本 */
    @AutoGenerated(locked = true, uuid = "de6e2cd0-5b6f-44e3-913f-6808b3ecdf04")
    private String currentVersion;

    /** 描述 描述 */
    @AutoGenerated(locked = true, uuid = "9314b5fe-b140-49a6-a6a7-ade999412203")
    private String description;

    /** 显示名称 显示名称，如 "抗生素分级使用审批规则" */
    @AutoGenerated(locked = true, uuid = "f637ddf9-0080-4e42-8da8-a263a5aa998d")
    private String displayName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "e5802cc3-e89a-495d-a98c-10d1277aa0fc")
    private String id;

    /**
     * 规则分类 规则分类ID,关联分类表ID。用于分类规则。 - HIS 系统需适应医疗政策和业务变化，规则类型可能频繁调整，枚举扩展性不足。 -
     * 相比码表，分类表更专注于规则类型管理，避免通用码表的复杂性，维护成本更可控。 - 支持动态扩展和业务友好性，符合以人为本的设计原则。
     */
    @Valid
    @AutoGenerated(locked = true, uuid = "63eaa3ca-d320-4012-a5b1-bda69714cfb5")
    private RuleRefCategorySimpleVo ruleCategory;

    /** 规则组织列表 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2fe9408b-4d23-4e71-952c-8647d9ea0617")
    private List<RuleOrganizationBaseVo> ruleOrganizationList;

    /** 状态 状态，如 "草稿"、"测试"、"生效"、"已下线" */
    @AutoGenerated(locked = true, uuid = "621e91d4-0d11-4024-bab5-f6e3f1bcf080")
    private RuleStatusEnum status;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "8a9697a4-17bf-4db8-acc4-d3f699fcf5c5")
    private Date updatedAt;

    /** 更新人 更新人 */
    @AutoGenerated(locked = true, uuid = "b918343e-bee2-4d81-8482-a75a71bf9ffe")
    private String updatedBy;
}
