package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleExecutionLogBaseVoDataAssembler;
import com.pulse.rule_engine.entrance.web.vo.RuleExecutionLogBaseVo;
import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleExecutionLogBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "32682123-b655-474b-b855-458a17c3923d|VO|CONVERTER")
public class RuleExecutionLogBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleExecutionLogBaseVoDataAssembler ruleExecutionLogBaseVoDataAssembler;

    /** 把RuleExecutionLogBaseDto转换成RuleExecutionLogBaseVo */
    @AutoGenerated(locked = false, uuid = "32682123-b655-474b-b855-458a17c3923d-converter-Map")
    public Map<RuleExecutionLogBaseDto, RuleExecutionLogBaseVo> convertToRuleExecutionLogBaseVoMap(
            List<RuleExecutionLogBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleExecutionLogBaseDto, RuleExecutionLogBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleExecutionLogBaseVo vo =
                                                    new RuleExecutionLogBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setRuleId(dto.getRuleId());
                                            vo.setBusinessId(dto.getBusinessId());
                                            vo.setBusinessType(dto.getBusinessType());
                                            vo.setTriggerPoint(dto.getTriggerPoint());
                                            vo.setInputData(dto.getInputData());
                                            vo.setOutputData(dto.getOutputData());
                                            vo.setExecutionDuration(dto.getExecutionDuration());
                                            vo.setExecutionTime(dto.getExecutionTime());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setRuleCode(dto.getRuleCode());
                                            vo.setExecutionStatus(dto.getExecutionStatus());
                                            vo.setExecutedBy(dto.getExecutedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleExecutionLogBaseDto转换成RuleExecutionLogBaseVo */
    @AutoGenerated(locked = true, uuid = "32682123-b655-474b-b855-458a17c3923d-converter-list")
    public List<RuleExecutionLogBaseVo> convertToRuleExecutionLogBaseVoList(
            List<RuleExecutionLogBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleExecutionLogBaseVoMap(dtoList).values());
    }

    /** 把RuleExecutionLogBaseDto转换成RuleExecutionLogBaseVo */
    @AutoGenerated(locked = true, uuid = "35a8a0c8-70a4-3cb3-94d7-bc08b95e9f54")
    public RuleExecutionLogBaseVo convertToRuleExecutionLogBaseVo(RuleExecutionLogBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleExecutionLogBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleExecutionLogBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "4d1e096c-332c-359c-83cf-9d310dd2a615")
    public List<RuleExecutionLogBaseVo> convertAndAssembleDataList(
            List<RuleExecutionLogBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, RuleExecutionLogBaseVo> voMap =
                convertToRuleExecutionLogBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleExecutionLogBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装RuleExecutionLogBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "ddfe1993-f92b-333f-b311-86bf04f58768")
    public RuleExecutionLogBaseVo convertAndAssembleData(RuleExecutionLogBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
