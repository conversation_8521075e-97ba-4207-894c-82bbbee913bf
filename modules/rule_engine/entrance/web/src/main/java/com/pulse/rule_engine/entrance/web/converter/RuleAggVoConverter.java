package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleAggVoDataAssembler;
import com.pulse.rule_engine.entrance.web.query.assembler.RuleAggVoDataAssembler.RuleAggVoDataHolder;
import com.pulse.rule_engine.entrance.web.query.collector.RuleAggVoDataCollector;
import com.pulse.rule_engine.entrance.web.vo.RuleAggVo;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleAggVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "1b014ebc-4fc6-4e75-91fb-ceed97c0e1c5|VO|CONVERTER")
public class RuleAggVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleAggVoDataAssembler ruleAggVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RuleAggVoDataCollector ruleAggVoDataCollector;

    /** 把RuleBaseDto转换成RuleAggVo */
    @AutoGenerated(locked = true, uuid = "1004715c-811e-3a8a-81f2-0551aa0cd9a5")
    public RuleAggVo convertToRuleAggVo(RuleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleAggVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把RuleBaseDto转换成RuleAggVo */
    @AutoGenerated(locked = false, uuid = "1b014ebc-4fc6-4e75-91fb-ceed97c0e1c5-converter-Map")
    public Map<RuleBaseDto, RuleAggVo> convertToRuleAggVoMap(List<RuleBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleBaseDto, RuleAggVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleAggVo vo = new RuleAggVo();
                                            vo.setId(dto.getId());
                                            vo.setCode(dto.getCode());
                                            vo.setDisplayName(dto.getDisplayName());
                                            vo.setStatus(dto.getStatus());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCurrentVersion(dto.getCurrentVersion());
                                            vo.setDescription(dto.getDescription());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleBaseDto转换成RuleAggVo */
    @AutoGenerated(locked = true, uuid = "1b014ebc-4fc6-4e75-91fb-ceed97c0e1c5-converter-list")
    public List<RuleAggVo> convertToRuleAggVoList(List<RuleBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleAggVoMap(dtoList).values());
    }

    /** 使用默认方式组装RuleAggVo数据 */
    @AutoGenerated(locked = true, uuid = "809d5ac0-c194-34f8-a7c9-cd62d6c7cbf4")
    public RuleAggVo convertAndAssembleData(RuleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleAggVo列表数据 */
    @AutoGenerated(locked = true, uuid = "c087a8ec-13e3-3a90-a568-09c85f7f29e3")
    public List<RuleAggVo> convertAndAssembleDataList(List<RuleBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        RuleAggVoDataHolder dataHolder = new RuleAggVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, RuleAggVo> voMap =
                convertToRuleAggVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleAggVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        ruleAggVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
