package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleOrganizationBaseVoDataAssembler;
import com.pulse.rule_engine.entrance.web.vo.RuleOrganizationBaseVo;
import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleOrganizationBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "133d915f-ca5e-40db-862e-290aeaf1c2b8|VO|CONVERTER")
public class RuleOrganizationBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleOrganizationBaseVoDataAssembler ruleOrganizationBaseVoDataAssembler;

    /** 把RuleOrganizationBaseDto转换成RuleOrganizationBaseVo */
    @AutoGenerated(locked = false, uuid = "133d915f-ca5e-40db-862e-290aeaf1c2b8-converter-Map")
    public Map<RuleOrganizationBaseDto, RuleOrganizationBaseVo> convertToRuleOrganizationBaseVoMap(
            List<RuleOrganizationBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleOrganizationBaseDto, RuleOrganizationBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleOrganizationBaseVo vo =
                                                    new RuleOrganizationBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setRuleId(dto.getRuleId());
                                            vo.setOrganizationId(dto.getOrganizationId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setRelationType(dto.getRelationType());
                                            vo.setRemark(dto.getRemark());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleOrganizationBaseDto转换成RuleOrganizationBaseVo */
    @AutoGenerated(locked = true, uuid = "133d915f-ca5e-40db-862e-290aeaf1c2b8-converter-list")
    public List<RuleOrganizationBaseVo> convertToRuleOrganizationBaseVoList(
            List<RuleOrganizationBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleOrganizationBaseVoMap(dtoList).values());
    }

    /** 把RuleOrganizationBaseDto转换成RuleOrganizationBaseVo */
    @AutoGenerated(locked = true, uuid = "45b6dc94-d868-3bfd-af7d-dd2ba470aecb")
    public RuleOrganizationBaseVo convertToRuleOrganizationBaseVo(RuleOrganizationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleOrganizationBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleOrganizationBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "86691e15-6e67-3f2f-8d0a-cfdbfd5effa4")
    public List<RuleOrganizationBaseVo> convertAndAssembleDataList(
            List<RuleOrganizationBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, RuleOrganizationBaseVo> voMap =
                convertToRuleOrganizationBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleOrganizationBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装RuleOrganizationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "c80324ee-5b8c-3a1e-8711-1d4f103093c8")
    public RuleOrganizationBaseVo convertAndAssembleData(RuleOrganizationBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
