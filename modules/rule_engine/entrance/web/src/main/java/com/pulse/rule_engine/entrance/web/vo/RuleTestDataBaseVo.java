package com.pulse.rule_engine.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "d4e4def2-f1ca-4bcc-b0a2-4c0c6ea192b7|VO|DEFINITION")
public class RuleTestDataBaseVo {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "4b37cd65-bf40-46df-a576-d3aa4a92d3f6")
    private Date createdAt;

    /** 测试数据描述 测试数据描述 */
    @AutoGenerated(locked = true, uuid = "c7bafd21-8a41-44b5-b14a-f1918c620d68")
    private String description;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "d85ca3bd-06d4-4215-8485-5ce99189f6c0")
    private String id;

    /** 规则版本ID 关联规则版本表ID */
    @AutoGenerated(locked = true, uuid = "1d68e84b-57e3-4a7a-a4e3-b6a49f74e47c")
    private String ruleVersionId;

    /** 测试数据 测试数据集（JSON格式）,例如：{"bloodRoutine": {"WBC": 15}} */
    @AutoGenerated(locked = true, uuid = "a6d30ee6-0f5b-4153-8bca-994e3c0c83fd")
    private String testData;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "ec8057e7-acc4-4809-9901-7829140a67d9")
    private Date updatedAt;
}
