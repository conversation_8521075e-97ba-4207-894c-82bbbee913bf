package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleVersionBaseVoDataAssembler;
import com.pulse.rule_engine.entrance.web.vo.RuleVersionBaseVo;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleVersionBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "c76026c9-c208-4136-ad1f-0ab1d0aebe55|VO|CONVERTER")
public class RuleVersionBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleVersionBaseVoDataAssembler ruleVersionBaseVoDataAssembler;

    /** 把RuleVersionBaseDto转换成RuleVersionBaseVo */
    @AutoGenerated(locked = true, uuid = "18c6ad66-32a0-34a6-8cec-cfd7804aa650")
    public RuleVersionBaseVo convertToRuleVersionBaseVo(RuleVersionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleVersionBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleVersionBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "1d937059-71a0-3587-8ed3-bc082c434602")
    public List<RuleVersionBaseVo> convertAndAssembleDataList(List<RuleVersionBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, RuleVersionBaseVo> voMap =
                convertToRuleVersionBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleVersionBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 使用默认方式组装RuleVersionBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "8c8fc44a-8113-3d70-a278-3d03b34f4305")
    public RuleVersionBaseVo convertAndAssembleData(RuleVersionBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把RuleVersionBaseDto转换成RuleVersionBaseVo */
    @AutoGenerated(locked = false, uuid = "c76026c9-c208-4136-ad1f-0ab1d0aebe55-converter-Map")
    public Map<RuleVersionBaseDto, RuleVersionBaseVo> convertToRuleVersionBaseVoMap(
            List<RuleVersionBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleVersionBaseDto, RuleVersionBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleVersionBaseVo vo = new RuleVersionBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setRuleId(dto.getRuleId());
                                            vo.setVersionNumber(dto.getVersionNumber());
                                            vo.setDrlContent(dto.getDrlContent());
                                            vo.setEffectiveStartTime(dto.getEffectiveStartTime());
                                            vo.setEffectiveEndTime(dto.getEffectiveEndTime());
                                            vo.setApproverId(dto.getApproverId());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleVersionBaseDto转换成RuleVersionBaseVo */
    @AutoGenerated(locked = true, uuid = "c76026c9-c208-4136-ad1f-0ab1d0aebe55-converter-list")
    public List<RuleVersionBaseVo> convertToRuleVersionBaseVoList(
            List<RuleVersionBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleVersionBaseVoMap(dtoList).values());
    }
}
