package com.pulse.rule_engine.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.entrance.web.query.assembler.RuleForDetailVoDataAssembler;
import com.pulse.rule_engine.entrance.web.query.assembler.RuleForDetailVoDataAssembler.RuleForDetailVoDataHolder;
import com.pulse.rule_engine.entrance.web.query.collector.RuleForDetailVoDataCollector;
import com.pulse.rule_engine.entrance.web.vo.RuleForDetailVo;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到RuleForDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "03bf3a97-30de-433e-9283-b8ef0281b9cb|VO|CONVERTER")
public class RuleForDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private RuleForDetailVoDataAssembler ruleForDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RuleForDetailVoDataCollector ruleForDetailVoDataCollector;

    /** 把RuleBaseDto转换成RuleForDetailVo */
    @AutoGenerated(locked = false, uuid = "03bf3a97-30de-433e-9283-b8ef0281b9cb-converter-Map")
    public Map<RuleBaseDto, RuleForDetailVo> convertToRuleForDetailVoMap(
            List<RuleBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<RuleBaseDto, RuleForDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            RuleForDetailVo vo = new RuleForDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setCode(dto.getCode());
                                            vo.setDisplayName(dto.getDisplayName());
                                            vo.setStatus(dto.getStatus());
                                            vo.setCreatedBy(dto.getCreatedBy());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setCurrentVersion(dto.getCurrentVersion());
                                            vo.setDescription(dto.getDescription());
                                            vo.setUpdatedBy(dto.getUpdatedBy());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把RuleBaseDto转换成RuleForDetailVo */
    @AutoGenerated(locked = true, uuid = "03bf3a97-30de-433e-9283-b8ef0281b9cb-converter-list")
    public List<RuleForDetailVo> convertToRuleForDetailVoList(List<RuleBaseDto> dtoList) {
        return new ArrayList<>(convertToRuleForDetailVoMap(dtoList).values());
    }

    /** 使用默认方式组装RuleForDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "0668ded3-81d7-361f-821f-ca5d4ad2716e")
    public RuleForDetailVo convertAndAssembleData(RuleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装RuleForDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "10f9a55b-1910-3cfe-a59d-2c3958759d92")
    public List<RuleForDetailVo> convertAndAssembleDataList(List<RuleBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        RuleForDetailVoDataHolder dataHolder = new RuleForDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(dtoList);
        Map<String, RuleForDetailVo> voMap =
                convertToRuleForDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        ruleForDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        ruleForDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把RuleBaseDto转换成RuleForDetailVo */
    @AutoGenerated(locked = true, uuid = "913eb212-c4bf-3167-8e07-47920af8532f")
    public RuleForDetailVo convertToRuleForDetailVo(RuleBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToRuleForDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }
}
