package com.pulse.rule_engine.service.bto;

import com.pulse.rule_engine.common.enums.RuleExecutionStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;

/**
 * <b>[源自]</b> RuleExecutionLog
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "0dad5369-73bd-44eb-ac6c-8899d183a6fc|BTO|DEFINITION")
public class CreateRuleExecutionLogBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 业务ID 业务对象 ID，如患者 ID、药品 ID */
    @AutoGenerated(locked = true, uuid = "8f869c06-a770-481d-9ea3-be154ff6cd93")
    private String businessId;

    /** 业务类型 业务类型，如 "PATIENT"、"DRUG" */
    @AutoGenerated(locked = true, uuid = "384d46cc-**************-a9537443b5b9")
    private String businessType;

    /** 执行人 */
    @AutoGenerated(locked = true, uuid = "e5cb5569-b266-47ca-902b-9b335948a8ed")
    private String executedBy;

    /** 执行时长（毫秒数） 执行时长，单位毫秒 */
    @AutoGenerated(locked = true, uuid = "b83f4aba-b973-4080-ae06-458dcf28368f")
    private Long executionDuration;

    /** 执行状态 执行状态，如"SUCCESS"、"FAIL" */
    @AutoGenerated(locked = true, uuid = "b7e2bf45-d3c0-41aa-a44d-c41babb1503a")
    private RuleExecutionStatusEnum executionStatus;

    /** 执行时间 执行时间 */
    @AutoGenerated(locked = true, uuid = "c235f9aa-239a-477d-b866-cce8a2a41249")
    private Date executionTime;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "7f30e6b4-a5e1-49fc-bf73-c6712aa06249")
    private String id;

    /** 输入数据 输入数据，JSON 格式，如 {"drugs": ["莫西沙星"]} */
    @AutoGenerated(locked = true, uuid = "9dd30c39-cfb4-4096-9d12-5b2c5501c249")
    private String inputData;

    /** 输出数据 输出数据，JSON 格式，如 {"needApproval": true} */
    @AutoGenerated(locked = true, uuid = "6167a683-382f-4938-bc2e-5a6faaa9c06e")
    private String outputData;

    /** 规则编码 规则编码，便于业务识别 */
    @AutoGenerated(locked = true, uuid = "a352c1d0-659b-427b-9b16-1815a5966374")
    private String ruleCode;

    /** 规则ID 规则ID，外键关联规则表 */
    @AutoGenerated(locked = true, uuid = "eacf70c3-b0fe-4c2c-beea-f5598f9dcd75")
    private String ruleId;

    /** 触发点 触发点，如 "PRESCRIPTION_SUBMIT" */
    @AutoGenerated(locked = true, uuid = "16112e03-dc33-43bd-af61-25436447d9d4")
    private String triggerPoint;

    @AutoGenerated(locked = true)
    public void setBusinessId(String businessId) {
        this.__$validPropertySet.add("businessId");
        this.businessId = businessId;
    }

    @AutoGenerated(locked = true)
    public void setBusinessType(String businessType) {
        this.__$validPropertySet.add("businessType");
        this.businessType = businessType;
    }

    @AutoGenerated(locked = true)
    public void setExecutedBy(String executedBy) {
        this.__$validPropertySet.add("executedBy");
        this.executedBy = executedBy;
    }

    @AutoGenerated(locked = true)
    public void setExecutionDuration(Long executionDuration) {
        this.__$validPropertySet.add("executionDuration");
        this.executionDuration = executionDuration;
    }

    @AutoGenerated(locked = true)
    public void setExecutionStatus(RuleExecutionStatusEnum executionStatus) {
        this.__$validPropertySet.add("executionStatus");
        this.executionStatus = executionStatus;
    }

    @AutoGenerated(locked = true)
    public void setExecutionTime(Date executionTime) {
        this.__$validPropertySet.add("executionTime");
        this.executionTime = executionTime;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInputData(String inputData) {
        this.__$validPropertySet.add("inputData");
        this.inputData = inputData;
    }

    @AutoGenerated(locked = true)
    public void setOutputData(String outputData) {
        this.__$validPropertySet.add("outputData");
        this.outputData = outputData;
    }

    @AutoGenerated(locked = true)
    public void setRuleCode(String ruleCode) {
        this.__$validPropertySet.add("ruleCode");
        this.ruleCode = ruleCode;
    }

    @AutoGenerated(locked = true)
    public void setRuleId(String ruleId) {
        this.__$validPropertySet.add("ruleId");
        this.ruleId = ruleId;
    }

    @AutoGenerated(locked = true)
    public void setTriggerPoint(String triggerPoint) {
        this.__$validPropertySet.add("triggerPoint");
        this.triggerPoint = triggerPoint;
    }
}
