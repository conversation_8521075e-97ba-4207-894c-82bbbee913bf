package com.pulse.rule_engine.service;

import com.pulse.rule_engine.manager.bo.*;
import com.pulse.rule_engine.manager.dto.RuleTestDataBaseDto;
import com.pulse.rule_engine.persist.dos.RuleTestData;
import com.pulse.rule_engine.service.base.BaseRuleTestDataBOService;
import com.pulse.rule_engine.service.bto.DeleteRuleTestDataBto;
import com.pulse.rule_engine.service.bto.MergeRuleTestDataBto;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "64feb39f-e8a0-4ef8-8fcf-ebe43a3fc8c6|BO|SERVICE")
public class RuleTestDataBOService extends BaseRuleTestDataBOService {
    @AutoGenerated(locked = true)
    @Resource
    private RuleTestDataBaseDtoService ruleTestDataBaseDtoService;

    /** 保存规则测试数据 */
    @PublicInterface(id = "6a1361b3-0f43-4c0b-9794-26deef9a845d", module = "rule_engine")
    @Transactional
    @AutoGenerated(locked = false, uuid = "2a205bf0-56e8-4f5f-9aab-6ddf36543b4e")
    public String mergeRuleTestData(@Valid @NotNull MergeRuleTestDataBto mergeRuleTestDataBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RuleTestDataBaseDto ruleTestDataBaseDto = null;
        if (mergeRuleTestDataBto.getId() != null) {
            ruleTestDataBaseDto = ruleTestDataBaseDtoService.getById(mergeRuleTestDataBto.getId());
        }
        MergeRuleTestDataBoResult boResult = super.mergeRuleTestDataBase(mergeRuleTestDataBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 MergeRuleTestDataBto */
        {
            MergeRuleTestDataBto bto =
                    boResult.<MergeRuleTestDataBto>getBtoOfType(MergeRuleTestDataBto.class).stream()
                            .findAny()
                            .orElse(null);
            UpdatedBto<MergeRuleTestDataBto, RuleTestData, RuleTestDataBO> updatedBto =
                    boResult.getUpdatedResult(bto);
            boolean updated = (updatedBto != null);
            AddedBto<MergeRuleTestDataBto, RuleTestDataBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (updated) { // getUpdatedResult
                // 合并后的待保存值
                RuleTestDataBO bo = updatedBto.getBo();
                // 数据库现有值（前项），将被BO所覆盖
                RuleTestData entity = updatedBto.getEntity();
                // 其他自定义操作...
            } else if (created) { // getAddedResult
                // 合并后的待保存值
                RuleTestDataBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 删除规则测试数据 */
    @PublicInterface(id = "d2513bc0-2e72-497c-8290-5212e1355fea", module = "rule_engine")
    @Transactional
    @AutoGenerated(locked = false, uuid = "3bae7b8b-2304-472f-8d60-387461fbec3a")
    public String deleteRuleTestData(@Valid @NotNull DeleteRuleTestDataBto deleteRuleTestDataBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RuleTestDataBaseDto ruleTestDataBaseDto =
                ruleTestDataBaseDtoService.getById(deleteRuleTestDataBto.getId());
        DeleteRuleTestDataBoResult boResult = super.deleteRuleTestDataBase(deleteRuleTestDataBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteRuleTestDataBto */
        {
            DeleteRuleTestDataBto bto =
                    boResult
                            .<DeleteRuleTestDataBto>getBtoOfType(DeleteRuleTestDataBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteRuleTestDataBto, RuleTestData> deletedBto =
                    boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
