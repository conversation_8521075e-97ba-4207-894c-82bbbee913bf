package com.pulse.rule_engine.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> RuleTestData
 *
 * <p><b>[操作]</b> DELETE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "3bae7b8b-2304-472f-8d60-387461fbec3a|BTO|DEFINITION")
public class DeleteRuleTestDataBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "a66602a4-4d9c-4e69-917f-e71808d19412")
    private String id;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }
}
