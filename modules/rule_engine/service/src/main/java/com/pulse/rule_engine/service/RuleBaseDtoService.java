package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.RuleBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.service.converter.RuleBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "a3f4913a-8dff-4672-8a3c-467df7fd4f38|DTO|SERVICE")
public class RuleBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleBaseDtoManager ruleBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleBaseDtoServiceConverter ruleBaseDtoServiceConverter;

    @PublicInterface(id = "6d41524f-74de-4670-bc6e-3d493fd90e47", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "14e99039-b5bc-33c6-9d53-53dc585f7158")
    public RuleBaseDto getByCode(@NotNull(message = "规则编码不能为空") String code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleBaseDto> ret = getByCodes(Arrays.asList(code));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "13f00314-e46d-4e06-aad9-c7459e24fbb6", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "308da4ea-a985-3b3e-8a31-9e98a103d1ad")
    public List<RuleBaseDto> getByStatuss(@Valid @NotNull List<RuleStatusEnum> status) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        status = new ArrayList<>(new HashSet<>(status));
        List<RuleBaseDto> ruleBaseDtoList = ruleBaseDtoManager.getByStatuss(status);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleBaseDtoServiceConverter.RuleBaseDtoConverter(ruleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "d1cabe1c-a29e-441c-a7db-fdd0b51614c7", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "478c582b-0841-3c7b-9294-7c88b2667207")
    public List<RuleBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<RuleBaseDto> ruleBaseDtoList = ruleBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleBaseDtoServiceConverter.RuleBaseDtoConverter(ruleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "0d844ee9-68f3-4e80-93a3-a5d7948cd771", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "47ace080-42e2-31d6-ac00-db9c60a70f69")
    public RuleBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "371d5b5b-8e2b-498c-9cff-fea1d3da31ae", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "4c638a2b-52a8-3253-8585-ce493681568f")
    public List<RuleBaseDto> getByCodes(@Valid @NotNull(message = "规则编码不能为空") List<String> code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        code = new ArrayList<>(new HashSet<>(code));
        List<RuleBaseDto> ruleBaseDtoList = ruleBaseDtoManager.getByCodes(code);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleBaseDtoServiceConverter.RuleBaseDtoConverter(ruleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "ddcd82a3-826e-4ccf-b34f-eb462ef8c764", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "77258086-1d32-3c88-b578-1dada3ec0e9e")
    public List<RuleBaseDto> getByStatus(@NotNull RuleStatusEnum status) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStatuss(Arrays.asList(status));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "3f94f8b5-5fa4-41c2-8c30-128016d8113b", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "a13a6e01-3119-305e-8be9-22401c6ff0d7")
    public List<RuleBaseDto> getByRuleCategoryIds(
            @Valid @NotNull(message = "规则分类ID不能为空") List<String> ruleCategoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        ruleCategoryId = new ArrayList<>(new HashSet<>(ruleCategoryId));
        List<RuleBaseDto> ruleBaseDtoList = ruleBaseDtoManager.getByRuleCategoryIds(ruleCategoryId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleBaseDtoServiceConverter.RuleBaseDtoConverter(ruleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "cb9aa620-aece-4b92-b076-f9ab3c0c891d", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "f22a80ad-0bba-30d7-8e0b-856a18ee212b")
    public List<RuleBaseDto> getByRuleCategoryId(
            @NotNull(message = "规则分类ID不能为空") String ruleCategoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRuleCategoryIds(Arrays.asList(ruleCategoryId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
