package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleTestDataBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleTestDataBaseDto;
import com.pulse.rule_engine.service.converter.RuleTestDataBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "922b047d-f47b-452e-935c-00c47ebd8cf7|DTO|SERVICE")
public class RuleTestDataBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleTestDataBaseDtoManager ruleTestDataBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleTestDataBaseDtoServiceConverter ruleTestDataBaseDtoServiceConverter;

    @PublicInterface(id = "04e6a573-3bf9-41a9-a084-a2ef10ce51f2", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "8962296b-05a3-3f9a-8960-f31cd28d1936")
    public List<RuleTestDataBaseDto> getByRuleVersionId(
            @NotNull(message = "规则版本ID不能为空") String ruleVersionId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRuleVersionIds(Arrays.asList(ruleVersionId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "42b0f4b1-deb3-4a44-b16d-453326f00a08", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "aadd796d-9141-3f2a-adfd-469987f63b46")
    public List<RuleTestDataBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<RuleTestDataBaseDto> ruleTestDataBaseDtoList = ruleTestDataBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleTestDataBaseDtoServiceConverter.RuleTestDataBaseDtoConverter(
                ruleTestDataBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "479c6ec4-e0eb-42c9-8575-72bc751a1082", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "ae9ff7fe-8912-3cf6-917f-b71cfe8ead6e")
    public List<RuleTestDataBaseDto> getByRuleVersionIds(
            @Valid @NotNull(message = "规则版本ID不能为空") List<String> ruleVersionId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        ruleVersionId = new ArrayList<>(new HashSet<>(ruleVersionId));
        List<RuleTestDataBaseDto> ruleTestDataBaseDtoList =
                ruleTestDataBaseDtoManager.getByRuleVersionIds(ruleVersionId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleTestDataBaseDtoServiceConverter.RuleTestDataBaseDtoConverter(
                ruleTestDataBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "123b99f4-81d7-4d0a-9643-2af272932d06", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "defe0ec0-061a-3ed4-a057-6da3e3b7d88a")
    public RuleTestDataBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleTestDataBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
