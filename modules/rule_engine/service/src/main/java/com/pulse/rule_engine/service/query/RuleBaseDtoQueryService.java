package com.pulse.rule_engine.service.query;

import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.persist.qto.ListRuleQto;
import com.pulse.rule_engine.service.RuleBaseDtoService;
import com.pulse.rule_engine.service.index.entity.ListRuleQtoService;
import com.pulse.rule_engine.service.query.assembler.RuleBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** RuleBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "1618ec0f-3d22-3b23-98f4-cf734a1be67f")
public class RuleBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleQtoService listRuleQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleBaseDtoDataAssembler ruleBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RuleBaseDtoService ruleBaseDtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "be268cc7-957b-3218-9129-a2e61a073e9d")
    private List<RuleBaseDto> toDtoList(List<String> ids) {
        List<RuleBaseDto> baseDtoList = ruleBaseDtoService.getByIds(ids);
        Map<String, RuleBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(Collectors.toMap(RuleBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据ListRuleQto查询RuleBaseDto列表,分页 */
    @PublicInterface(id = "9a886f86-76ff-409f-adcc-04d0b812332d", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "e534626a-e9fd-3a63-a4e8-9ae1362a35c3")
    public VSQueryResult<RuleBaseDto> listRulePaged(@Valid @NotNull ListRuleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listRuleQtoService.queryPaged(qto);
        List<RuleBaseDto> dtoList = toDtoList(ids);
        ruleBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listRuleQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
