package com.pulse.rule_engine.service.converter;

import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "7ab74d52-bb72-355d-aebe-8d6e1df2d70b")
public class RuleOrganizationBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<RuleOrganizationBaseDto> RuleOrganizationBaseDtoConverter(
            List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return ruleOrganizationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
