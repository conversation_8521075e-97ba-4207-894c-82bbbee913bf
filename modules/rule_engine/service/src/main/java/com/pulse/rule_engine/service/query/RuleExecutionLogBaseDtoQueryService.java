package com.pulse.rule_engine.service.query;

import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.pulse.rule_engine.persist.qto.ListRuleExecutionLogQto;
import com.pulse.rule_engine.service.RuleExecutionLogBaseDtoService;
import com.pulse.rule_engine.service.index.entity.ListRuleExecutionLogQtoService;
import com.pulse.rule_engine.service.query.assembler.RuleExecutionLogBaseDtoDataAssembler;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** RuleExecutionLogBaseDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "e2415b94-2669-3d3d-a87f-08b645ff9879")
public class RuleExecutionLogBaseDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleExecutionLogQtoService listRuleExecutionLogQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RuleExecutionLogBaseDtoDataAssembler ruleExecutionLogBaseDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RuleExecutionLogBaseDtoService ruleExecutionLogBaseDtoService;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "4d349064-d844-3500-936c-4b6c972b14b0")
    private List<RuleExecutionLogBaseDto> toDtoList(List<String> ids) {
        List<RuleExecutionLogBaseDto> baseDtoList = ruleExecutionLogBaseDtoService.getByIds(ids);
        Map<String, RuleExecutionLogBaseDto> dtoMap =
                baseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        RuleExecutionLogBaseDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据ListRuleExecutionLogQto查询RuleExecutionLogBaseDto列表,分页 */
    @PublicInterface(id = "e893915e-df5c-4ddc-92e1-ecfb3beb9454", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "a6c86292-7c0c-3d70-809f-e7712f3188be")
    public VSQueryResult<RuleExecutionLogBaseDto> listRuleExecutionLogPaged(
            @Valid @NotNull ListRuleExecutionLogQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listRuleExecutionLogQtoService.queryPaged(qto);
        List<RuleExecutionLogBaseDto> dtoList = toDtoList(ids);
        ruleExecutionLogBaseDtoDataAssembler.assembleData(dtoList);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listRuleExecutionLogQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
