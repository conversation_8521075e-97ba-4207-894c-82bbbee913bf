package com.pulse.rule_engine.service.converter;

import com.pulse.rule_engine.manager.dto.RuleTestDataBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "ec05ec7d-8904-3155-876f-c583ff9259f5")
public class RuleTestDataBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<RuleTestDataBaseDto> RuleTestDataBaseDtoConverter(
            List<RuleTestDataBaseDto> ruleTestDataBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return ruleTestDataBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
