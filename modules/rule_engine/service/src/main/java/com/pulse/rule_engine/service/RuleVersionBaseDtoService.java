package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleVersionBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.dos.RuleVersion.RuleIdAndVersionNumber;
import com.pulse.rule_engine.persist.eo.UkRuleVersionEo;
import com.pulse.rule_engine.service.converter.RuleVersionBaseDtoServiceConverter;
import com.pulse.rule_engine.service.converter.voConverter.RuleVersionUkRuleVersionConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "0a60d032-acdf-4e6c-ad5a-2863ee2efa9f|DTO|SERVICE")
public class RuleVersionBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleVersionBaseDtoManager ruleVersionBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleVersionBaseDtoServiceConverter ruleVersionBaseDtoServiceConverter;

    @PublicInterface(id = "2e398b09-4d51-4006-a333-d6c7d49ec702", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "2d925938-a06d-3834-a032-4ede4048ee9f")
    public List<RuleVersionBaseDto> getByEffectiveStartTime(@NotNull Date effectiveStartTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByEffectiveStartTimes(Arrays.asList(effectiveStartTime));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "7d794e65-fad4-41dd-811c-155f88eaa6c2", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "4818c9b1-59be-3d79-8f23-b0dbbc8f58cb")
    public List<RuleVersionBaseDto> getByEffectiveStartTimes(
            @Valid @NotNull List<Date> effectiveStartTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        effectiveStartTime = new ArrayList<>(new HashSet<>(effectiveStartTime));
        List<RuleVersionBaseDto> ruleVersionBaseDtoList =
                ruleVersionBaseDtoManager.getByEffectiveStartTimes(effectiveStartTime);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleVersionBaseDtoServiceConverter.RuleVersionBaseDtoConverter(
                ruleVersionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "56c20c7a-704c-4cb8-bbfa-a9496d5a448c", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "5ea782fd-0e8c-320d-a2a2-1930b04c0955")
    public List<RuleVersionBaseDto> getByRuleIdsAndVersionNumbers(
            @Valid @NotNull List<UkRuleVersionEo> ukRuleVersionEo) {
        List<RuleIdAndVersionNumber> ruleIdAndVersionNumber =
                ukRuleVersionEo.stream()
                        .map(RuleVersionUkRuleVersionConverter::convertFromUkRuleVersionToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ruleVersionBaseDtoList =
                ruleVersionBaseDtoManager.getByRuleIdsAndVersionNumbers(ruleIdAndVersionNumber);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleVersionBaseDtoServiceConverter.RuleVersionBaseDtoConverter(
                ruleVersionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f055a6c6-b01d-48fd-8615-841f7be16d72", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "6c46d8e7-b0c6-3f2e-8241-01193ab76678")
    public List<RuleVersionBaseDto> getByIds(@Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<RuleVersionBaseDto> ruleVersionBaseDtoList = ruleVersionBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleVersionBaseDtoServiceConverter.RuleVersionBaseDtoConverter(
                ruleVersionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "907902d6-6924-40da-a11b-40caace4cf2b", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "a692c948-625c-3f39-b7cb-372614948d0e")
    public List<RuleVersionBaseDto> getByEffectiveEndTime(@NotNull Date effectiveEndTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByEffectiveEndTimes(Arrays.asList(effectiveEndTime));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "72ba98a6-11e2-4ba3-be7d-6095ae6a64fb", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "b36f97fc-4459-3e5a-bb28-546f06dca1b1")
    public RuleVersionBaseDto getByRuleIdAndVersionNumber(@Valid @NotNull UkRuleVersionEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ret = getByRuleIdsAndVersionNumbers(Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "838253a3-7e02-4f73-bfea-d38de6fc25cb", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "b531121f-8bad-3c4f-a13f-c59c8012a3fe")
    public List<RuleVersionBaseDto> getByRuleIds(
            @Valid @NotNull(message = "规则ID不能为空") List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        ruleId = new ArrayList<>(new HashSet<>(ruleId));
        List<RuleVersionBaseDto> ruleVersionBaseDtoList =
                ruleVersionBaseDtoManager.getByRuleIds(ruleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleVersionBaseDtoServiceConverter.RuleVersionBaseDtoConverter(
                ruleVersionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f36b27c6-5739-4d13-a673-981a3756c6fa", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "d11610eb-90a4-38eb-899b-2db9886fa4f0")
    public RuleVersionBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "d77c62d2-8266-4ffe-8551-db8ade08416f", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "d6ba4c13-b243-3e75-a1d7-c41b2358553c")
    public List<RuleVersionBaseDto> getByRuleId(@NotNull(message = "规则ID不能为空") String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRuleIds(Arrays.asList(ruleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "83ca79a1-9074-471e-985e-5a7356b533db", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "edb2d014-52c5-37bf-b0d5-eca8694d9594")
    public List<RuleVersionBaseDto> getByEffectiveEndTimes(
            @Valid @NotNull List<Date> effectiveEndTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        effectiveEndTime = new ArrayList<>(new HashSet<>(effectiveEndTime));
        List<RuleVersionBaseDto> ruleVersionBaseDtoList =
                ruleVersionBaseDtoManager.getByEffectiveEndTimes(effectiveEndTime);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleVersionBaseDtoServiceConverter.RuleVersionBaseDtoConverter(
                ruleVersionBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
