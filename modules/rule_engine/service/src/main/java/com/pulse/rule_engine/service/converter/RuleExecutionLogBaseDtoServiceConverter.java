package com.pulse.rule_engine.service.converter;

import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "d2b6b874-3e1d-3a57-8760-7e9ab11bebf3")
public class RuleExecutionLogBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<RuleExecutionLogBaseDto> RuleExecutionLogBaseDtoConverter(
            List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return ruleExecutionLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
