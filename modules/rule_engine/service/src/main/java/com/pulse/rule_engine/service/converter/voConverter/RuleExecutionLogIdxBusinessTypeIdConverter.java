package com.pulse.rule_engine.service.converter.voConverter;

import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog.BusinessIdAndBusinessType;
import com.pulse.rule_engine.persist.eo.IdxBusinessTypeIdEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "6e14b539-6e5d-39d8-8e53-3b8b1c1a8913")
public class RuleExecutionLogIdxBusinessTypeIdConverter {

    @AutoGenerated(locked = true)
    public static RuleExecutionLog.BusinessIdAndBusinessType convertFromIdxBusinessTypeIdToInner(
            IdxBusinessTypeIdEo idxBusinessTypeId) {
        if (null == idxBusinessTypeId) {
            return null;
        }

        BusinessIdAndBusinessType businessIdAndBusinessType = new BusinessIdAndBusinessType();
        businessIdAndBusinessType.setBusinessType(idxBusinessTypeId.getBusinessType());
        businessIdAndBusinessType.setBusinessId(idxBusinessTypeId.getBusinessId());
        return businessIdAndBusinessType;
    }
}
