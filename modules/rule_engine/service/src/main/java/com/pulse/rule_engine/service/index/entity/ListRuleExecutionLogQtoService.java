package com.pulse.rule_engine.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.rule_engine.persist.mapper.ListRuleExecutionLogQtoDao;
import com.pulse.rule_engine.persist.qto.ListRuleExecutionLogQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "0d9d43eb-d83f-4915-aa53-0907b5fb3043|QTO|SERVICE")
public class ListRuleExecutionLogQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleExecutionLogQtoDao listRuleExecutionLogMapper;

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "0d9d43eb-d83f-4915-aa53-0907b5fb3043-query-paged")
    public List<String> queryPaged(ListRuleExecutionLogQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listRuleExecutionLogMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListRuleExecutionLogQto qto) {
        return listRuleExecutionLogMapper.count(qto);
    }
}
