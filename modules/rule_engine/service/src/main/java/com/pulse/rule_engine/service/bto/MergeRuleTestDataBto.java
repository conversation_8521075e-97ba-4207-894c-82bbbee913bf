package com.pulse.rule_engine.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> RuleTestData
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "2a205bf0-56e8-4f5f-9aab-6ddf36543b4e|BTO|DEFINITION")
public class MergeRuleTestDataBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 测试数据描述 测试数据描述 */
    @AutoGenerated(locked = true, uuid = "f4e5b363-df9e-4a2f-ab22-39ba91fb20fb")
    private String description;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "7de1a4b8-e890-416f-b65d-956fc189a0c5")
    private String id;

    /** 规则版本ID 关联规则版本表ID */
    @AutoGenerated(locked = true, uuid = "a6f502e0-1b54-49fa-8520-dfcd76f60745")
    private String ruleVersionId;

    /** 测试数据 测试数据集（JSON格式）,例如：{"bloodRoutine": {"WBC": 15}} */
    @AutoGenerated(locked = true, uuid = "c4b5f8e2-0cef-4c4a-8403-bb894ec029e5")
    private String testData;

    @AutoGenerated(locked = true)
    public void setDescription(String description) {
        this.__$validPropertySet.add("description");
        this.description = description;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setRuleVersionId(String ruleVersionId) {
        this.__$validPropertySet.add("ruleVersionId");
        this.ruleVersionId = ruleVersionId;
    }

    @AutoGenerated(locked = true)
    public void setTestData(String testData) {
        this.__$validPropertySet.add("testData");
        this.testData = testData;
    }
}
