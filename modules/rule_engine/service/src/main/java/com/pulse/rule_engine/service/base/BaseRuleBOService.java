package com.pulse.rule_engine.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Holder;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.rule_engine.manager.bo.*;
import com.pulse.rule_engine.manager.bo.RuleBO;
import com.pulse.rule_engine.persist.dos.Rule;
import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.service.base.BaseRuleBOService.DeleteRuleBoResult;
import com.pulse.rule_engine.service.base.BaseRuleBOService.MergeRuleBoResult;
import com.pulse.rule_engine.service.bto.DeleteRuleBto;
import com.pulse.rule_engine.service.bto.MergeRuleBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.bo.util.BoUtil;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "a8adf9e5-cf61-3cd5-aeed-ec25b0f18e4d")
public class BaseRuleBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private RuleBO createMergeRuleOnDuplicateUpdate(
            MergeRuleBoResult boResult, MergeRuleBto mergeRuleBto) {
        RuleBO ruleBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeRuleBto.getId() == null);
        if (!allNull && !found) {
            ruleBO = RuleBO.getById(mergeRuleBto.getId());
            if (ruleBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        allNull = (mergeRuleBto.getCode() == null);
        if (!allNull && !found) {
            ruleBO = RuleBO.getByCode(mergeRuleBto.getCode());
            if (ruleBO != null) {
                matchedUkName += "(";
                matchedUkName += "'code'";
                matchedUkName += ")";
                found = true;
            }
        }
        if (ruleBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(ruleBO.convertToRule());
                updatedBto.setBto(mergeRuleBto);
                updatedBto.setBo(ruleBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "code")) {
                    ruleBO.setCode(mergeRuleBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "displayName")) {
                    ruleBO.setDisplayName(mergeRuleBto.getDisplayName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "ruleCategoryId")) {
                    ruleBO.setRuleCategoryId(mergeRuleBto.getRuleCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "status")) {
                    ruleBO.setStatus(mergeRuleBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "currentVersion")) {
                    ruleBO.setCurrentVersion(mergeRuleBto.getCurrentVersion());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "description")) {
                    ruleBO.setDescription(mergeRuleBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "createdBy")) {
                    ruleBO.setCreatedBy(mergeRuleBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "updatedBy")) {
                    ruleBO.setUpdatedBy(mergeRuleBto.getUpdatedBy());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(ruleBO.convertToRule());
                updatedBto.setBto(mergeRuleBto);
                updatedBto.setBo(ruleBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "code")) {
                    ruleBO.setCode(mergeRuleBto.getCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "displayName")) {
                    ruleBO.setDisplayName(mergeRuleBto.getDisplayName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "ruleCategoryId")) {
                    ruleBO.setRuleCategoryId(mergeRuleBto.getRuleCategoryId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "status")) {
                    ruleBO.setStatus(mergeRuleBto.getStatus());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "currentVersion")) {
                    ruleBO.setCurrentVersion(mergeRuleBto.getCurrentVersion());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "description")) {
                    ruleBO.setDescription(mergeRuleBto.getDescription());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "createdBy")) {
                    ruleBO.setCreatedBy(mergeRuleBto.getCreatedBy());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                        "updatedBy")) {
                    ruleBO.setUpdatedBy(mergeRuleBto.getUpdatedBy());
                }
            }
        } else {
            ruleBO = new RuleBO();
            if (pkExist) {
                ruleBO.setId(mergeRuleBto.getId());
            } else {
                ruleBO.setId(String.valueOf(this.idGenerator.allocateId("rule")));
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "code")) {
                ruleBO.setCode(mergeRuleBto.getCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "displayName")) {
                ruleBO.setDisplayName(mergeRuleBto.getDisplayName());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "ruleCategoryId")) {
                ruleBO.setRuleCategoryId(mergeRuleBto.getRuleCategoryId());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "status")) {
                ruleBO.setStatus(mergeRuleBto.getStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "currentVersion")) {
                ruleBO.setCurrentVersion(mergeRuleBto.getCurrentVersion());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "description")) {
                ruleBO.setDescription(mergeRuleBto.getDescription());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "createdBy")) {
                ruleBO.setCreatedBy(mergeRuleBto.getCreatedBy());
            }
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "updatedBy")) {
                ruleBO.setUpdatedBy(mergeRuleBto.getUpdatedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeRuleBto);
            addedBto.setBo(ruleBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return ruleBO;
    }

    /** 创建对象:RuleOrganizationBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createRuleOrganizationBtoOnDuplicateUpdate(
            MergeRuleBoResult boResult, MergeRuleBto mergeRuleBto, RuleBO ruleBO) {
        if (CollectionUtil.isEmpty(mergeRuleBto.getRuleOrganizationBtoList())) {
            mergeRuleBto.setRuleOrganizationBtoList(List.of());
        }
        ruleBO.getRuleOrganizationBOSet()
                .removeIf(
                        item -> {
                            boolean notExists =
                                    mergeRuleBto.getRuleOrganizationBtoList().stream()
                                            .filter(
                                                    ruleOrganizationBtoList -> {
                                                        boolean allNull = false;
                                                        boolean found = false;
                                                        allNull =
                                                                (ruleOrganizationBtoList.getId()
                                                                        == null);
                                                        if (!allNull && !found) {
                                                            found =
                                                                    BoUtil.equals(
                                                                            item.getId(),
                                                                            ruleOrganizationBtoList
                                                                                    .getId());
                                                            return found;
                                                        }
                                                        return found;
                                                    })
                                            .findAny()
                                            .isEmpty();
                            if (notExists) {
                                boResult.getDeletedList().add(item.convertToRuleOrganization());
                            }
                            return notExists;
                        });
        if (CollectionUtil.isNotEmpty(mergeRuleBto.getRuleOrganizationBtoList())) {
            for (MergeRuleBto.RuleOrganizationBto item :
                    mergeRuleBto.getRuleOrganizationBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<RuleOrganizationBO> any =
                        ruleBO.getRuleOrganizationBOSet().stream()
                                .filter(
                                        ruleOrganizationBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                ruleOrganizationBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        RuleOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRuleOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "relationType")) {
                            bo.setRelationType(item.getRelationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                    } else {
                        RuleOrganizationBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRuleOrganization());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "organizationId")) {
                            bo.setOrganizationId(item.getOrganizationId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "relationType")) {
                            bo.setRelationType(item.getRelationType());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "remark")) {
                            bo.setRemark(item.getRemark());
                        }
                    }
                } else {
                    RuleOrganizationBO subBo = new RuleOrganizationBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "organizationId")) {
                        subBo.setOrganizationId(item.getOrganizationId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "relationType")) {
                        subBo.setRelationType(item.getRelationType());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "remark")) {
                        subBo.setRemark(item.getRemark());
                    }
                    subBo.setRuleBO(ruleBO);
                    if (item.getId() == null) {
                        subBo.setId(
                                String.valueOf(this.idGenerator.allocateId("rule_organization")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    ruleBO.getRuleOrganizationBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 创建对象:RuleVersionBto,如果存在就覆盖 */
    @AutoGenerated(locked = true)
    private void createRuleVersionBtoOnDuplicateUpdate(
            BaseRuleBOService.MergeRuleBoResult boResult,
            MergeRuleBto mergeRuleBto,
            RuleBO ruleBO) {
        if (CollectionUtil.isNotEmpty(mergeRuleBto.getRuleVersionBtoList())) {
            for (MergeRuleBto.RuleVersionBto item : mergeRuleBto.getRuleVersionBtoList()) {
                Holder<Boolean> pkMatched = new Holder(false);
                Holder<String> matchedUkName = new Holder("");
                Optional<RuleVersionBO> any =
                        ruleBO.getRuleVersionBOSet().stream()
                                .filter(
                                        ruleVersionBOSet -> {
                                            boolean allNull = false;
                                            boolean found = false;
                                            allNull = (item.getId() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                ruleVersionBOSet.getId(),
                                                                item.getId());
                                                pkMatched.set(found);
                                                return found;
                                            }
                                            allNull = (item.getVersionNumber() == null);
                                            if (!allNull && !found) {
                                                found =
                                                        BoUtil.equals(
                                                                ruleVersionBOSet.getVersionNumber(),
                                                                item.getVersionNumber());
                                                if (found) {
                                                    String uk = "(";
                                                    uk += "'version_number'";
                                                    uk += ")";
                                                    matchedUkName.set(uk);
                                                    return true;
                                                }
                                            }
                                            return found;
                                        })
                                .findAny();
                if (any.isPresent()) {
                    if (pkMatched.get()) {
                        RuleVersionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRuleVersion());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "versionNumber")) {
                            bo.setVersionNumber(item.getVersionNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drlContent")) {
                            bo.setDrlContent(item.getDrlContent());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "effectiveStartTime")) {
                            bo.setEffectiveStartTime(item.getEffectiveStartTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "effectiveEndTime")) {
                            bo.setEffectiveEndTime(item.getEffectiveEndTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "approverId")) {
                            bo.setApproverId(item.getApproverId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    } else {
                        RuleVersionBO bo = any.get();
                        UpdatedBto updatedBto = new UpdatedBto();
                        updatedBto.setBto(item);
                        updatedBto.setBo(bo);
                        updatedBto.setEntity(bo.convertToRuleVersion());
                        boResult.getUpdatedList().add(updatedBto);
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "versionNumber")) {
                            bo.setVersionNumber(item.getVersionNumber());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "drlContent")) {
                            bo.setDrlContent(item.getDrlContent());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "effectiveStartTime")) {
                            bo.setEffectiveStartTime(item.getEffectiveStartTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "effectiveEndTime")) {
                            bo.setEffectiveEndTime(item.getEffectiveEndTime());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "approverId")) {
                            bo.setApproverId(item.getApproverId());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "createdBy")) {
                            bo.setCreatedBy(item.getCreatedBy());
                        }
                        if (CollectionUtil.contains(
                                (Set<String>)
                                        ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                                "updatedBy")) {
                            bo.setUpdatedBy(item.getUpdatedBy());
                        }
                    }
                } else {
                    RuleVersionBO subBo = new RuleVersionBO();
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "versionNumber")) {
                        subBo.setVersionNumber(item.getVersionNumber());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "drlContent")) {
                        subBo.setDrlContent(item.getDrlContent());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "effectiveStartTime")) {
                        subBo.setEffectiveStartTime(item.getEffectiveStartTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "effectiveEndTime")) {
                        subBo.setEffectiveEndTime(item.getEffectiveEndTime());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "approverId")) {
                        subBo.setApproverId(item.getApproverId());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "createdBy")) {
                        subBo.setCreatedBy(item.getCreatedBy());
                    }
                    if (CollectionUtil.contains(
                            (Set<String>) ReflectUtil.getFieldValue(item, "__$validPropertySet"),
                            "updatedBy")) {
                        subBo.setUpdatedBy(item.getUpdatedBy());
                    }
                    subBo.setRuleBO(ruleBO);
                    if (item.getId() == null) {
                        subBo.setId(String.valueOf(this.idGenerator.allocateId("rule_version")));
                    } else {
                        subBo.setId(item.getId());
                    }

                    ruleBO.getRuleVersionBOSet().add(subBo);
                    AddedBto addedBto = new AddedBto();
                    addedBto.setBo(subBo);
                    addedBto.setBto(item);
                    boResult.getAddBtoList().add(addedBto);
                }
            }
        }
    }

    /** 删除对象:deleteRule,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RuleBO deleteDeleteRuleOnMissThrowEx(
            BaseRuleBOService.DeleteRuleBoResult boResult, DeleteRuleBto deleteRuleBto) {
        RuleBO ruleBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteRuleBto.getId() == null);
        if (!allNull && !found) {
            ruleBO = RuleBO.getById(deleteRuleBto.getId());
            found = true;
        }
        if (ruleBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(ruleBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteRuleBto);
            deletedBto.setEntity(ruleBO.convertToRule());
            boResult.getDeletedBtoList().add(deletedBto);
            return ruleBO;
        }
    }

    /** 删除规则（逻辑删除） */
    @AutoGenerated(locked = true)
    protected DeleteRuleBoResult deleteRuleBase(DeleteRuleBto deleteRuleBto) {
        if (deleteRuleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteRuleBoResult boResult = new DeleteRuleBoResult();
        RuleBO ruleBO = deleteDeleteRuleOnMissThrowEx(boResult, deleteRuleBto);
        boResult.setRootBo(ruleBO);
        return boResult;
    }

    /** 保存规则，包括规则基础信息、规则版本、规则组织信息 */
    @AutoGenerated(locked = true)
    protected MergeRuleBoResult mergeRuleBase(MergeRuleBto mergeRuleBto) {
        if (mergeRuleBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeRuleBoResult boResult = new MergeRuleBoResult();
        RuleBO ruleBO = createMergeRuleOnDuplicateUpdate(boResult, mergeRuleBto);
        if (ruleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "ruleVersionBtoList")) {
                createRuleVersionBtoOnDuplicateUpdate(boResult, mergeRuleBto, ruleBO);
            }
        }
        if (ruleBO != null) {
            if (CollectionUtil.contains(
                    (Set<String>) ReflectUtil.getFieldValue(mergeRuleBto, "__$validPropertySet"),
                    "ruleOrganizationBtoList")) {
                createRuleOrganizationBtoOnDuplicateUpdate(boResult, mergeRuleBto, ruleBO);
            }
        }
        boResult.setRootBo(ruleBO);
        return boResult;
    }

    public static class MergeRuleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RuleBO getRootBo() {
            return (RuleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRuleBto.RuleVersionBto, RuleVersionBO> getCreatedBto(
                MergeRuleBto.RuleVersionBto ruleVersionBto) {
            return this.getAddedResult(ruleVersionBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRuleBto.RuleOrganizationBto, RuleOrganizationBO> getCreatedBto(
                MergeRuleBto.RuleOrganizationBto ruleOrganizationBto) {
            return this.getAddedResult(ruleOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRuleBto, RuleBO> getCreatedBto(MergeRuleBto mergeRuleBto) {
            return this.getAddedResult(mergeRuleBto);
        }

        @AutoGenerated(locked = true)
        public RuleVersion getDeleted_RuleVersion() {
            return (RuleVersion)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RuleVersion.class));
        }

        @AutoGenerated(locked = true)
        public RuleOrganization getDeleted_RuleOrganization() {
            return (RuleOrganization)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RuleOrganization.class));
        }

        @AutoGenerated(locked = true)
        public Rule getDeleted_Rule() {
            return (Rule) CollectionUtil.getFirst(this.getDeletedEntityList(Rule.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRuleBto.RuleVersionBto, RuleVersion, RuleVersionBO> getUpdatedBto(
                MergeRuleBto.RuleVersionBto ruleVersionBto) {
            return super.getUpdatedResult(ruleVersionBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRuleBto.RuleOrganizationBto, RuleOrganization, RuleOrganizationBO>
                getUpdatedBto(MergeRuleBto.RuleOrganizationBto ruleOrganizationBto) {
            return super.getUpdatedResult(ruleOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRuleBto, Rule, RuleBO> getUpdatedBto(MergeRuleBto mergeRuleBto) {
            return super.getUpdatedResult(mergeRuleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRuleBto.RuleVersionBto, RuleVersionBO> getUnmodifiedBto(
                MergeRuleBto.RuleVersionBto ruleVersionBto) {
            return super.getUnmodifiedResult(ruleVersionBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRuleBto.RuleOrganizationBto, RuleOrganizationBO> getUnmodifiedBto(
                MergeRuleBto.RuleOrganizationBto ruleOrganizationBto) {
            return super.getUnmodifiedResult(ruleOrganizationBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRuleBto, RuleBO> getUnmodifiedBto(MergeRuleBto mergeRuleBto) {
            return super.getUnmodifiedResult(mergeRuleBto);
        }
    }

    public static class DeleteRuleBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RuleBO getRootBo() {
            return (RuleBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteRuleBto, RuleBO> getCreatedBto(DeleteRuleBto deleteRuleBto) {
            return this.getAddedResult(deleteRuleBto);
        }

        @AutoGenerated(locked = true)
        public Rule getDeleted_Rule() {
            return (Rule) CollectionUtil.getFirst(this.getDeletedEntityList(Rule.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteRuleBto, Rule, RuleBO> getUpdatedBto(DeleteRuleBto deleteRuleBto) {
            return super.getUpdatedResult(deleteRuleBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteRuleBto, RuleBO> getUnmodifiedBto(DeleteRuleBto deleteRuleBto) {
            return super.getUnmodifiedResult(deleteRuleBto);
        }
    }
}
