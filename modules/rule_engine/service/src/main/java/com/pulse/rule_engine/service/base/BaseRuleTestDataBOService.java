package com.pulse.rule_engine.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.rule_engine.manager.bo.*;
import com.pulse.rule_engine.manager.bo.RuleTestDataBO;
import com.pulse.rule_engine.persist.dos.RuleTestData;
import com.pulse.rule_engine.service.base.BaseRuleTestDataBOService.DeleteRuleTestDataBoResult;
import com.pulse.rule_engine.service.base.BaseRuleTestDataBOService.MergeRuleTestDataBoResult;
import com.pulse.rule_engine.service.bto.DeleteRuleTestDataBto;
import com.pulse.rule_engine.service.bto.MergeRuleTestDataBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "942d2a39-cede-3e46-b002-ede7c54a681d")
public class BaseRuleTestDataBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private RuleTestDataBO createMergeRuleTestDataOnDuplicateUpdate(
            BaseRuleTestDataBOService.MergeRuleTestDataBoResult boResult,
            MergeRuleTestDataBto mergeRuleTestDataBto) {
        RuleTestDataBO ruleTestDataBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeRuleTestDataBto.getId() == null);
        if (!allNull && !found) {
            ruleTestDataBO = RuleTestDataBO.getById(mergeRuleTestDataBto.getId());
            if (ruleTestDataBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (ruleTestDataBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(ruleTestDataBO.convertToRuleTestData());
                updatedBto.setBto(mergeRuleTestDataBto);
                updatedBto.setBo(ruleTestDataBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeRuleTestDataBto, "__$validPropertySet"),
                        "ruleVersionId")) {
                    ruleTestDataBO.setRuleVersionId(mergeRuleTestDataBto.getRuleVersionId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeRuleTestDataBto, "__$validPropertySet"),
                        "testData")) {
                    ruleTestDataBO.setTestData(mergeRuleTestDataBto.getTestData());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeRuleTestDataBto, "__$validPropertySet"),
                        "description")) {
                    ruleTestDataBO.setDescription(mergeRuleTestDataBto.getDescription());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(ruleTestDataBO.convertToRuleTestData());
                updatedBto.setBto(mergeRuleTestDataBto);
                updatedBto.setBo(ruleTestDataBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeRuleTestDataBto, "__$validPropertySet"),
                        "ruleVersionId")) {
                    ruleTestDataBO.setRuleVersionId(mergeRuleTestDataBto.getRuleVersionId());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeRuleTestDataBto, "__$validPropertySet"),
                        "testData")) {
                    ruleTestDataBO.setTestData(mergeRuleTestDataBto.getTestData());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeRuleTestDataBto, "__$validPropertySet"),
                        "description")) {
                    ruleTestDataBO.setDescription(mergeRuleTestDataBto.getDescription());
                }
            }
        } else {
            ruleTestDataBO = new RuleTestDataBO();
            if (pkExist) {
                ruleTestDataBO.setId(mergeRuleTestDataBto.getId());
            } else {
                ruleTestDataBO.setId(String.valueOf(this.idGenerator.allocateId("rule_test_data")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleTestDataBto, "__$validPropertySet"),
                    "ruleVersionId")) {
                ruleTestDataBO.setRuleVersionId(mergeRuleTestDataBto.getRuleVersionId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleTestDataBto, "__$validPropertySet"),
                    "testData")) {
                ruleTestDataBO.setTestData(mergeRuleTestDataBto.getTestData());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeRuleTestDataBto, "__$validPropertySet"),
                    "description")) {
                ruleTestDataBO.setDescription(mergeRuleTestDataBto.getDescription());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeRuleTestDataBto);
            addedBto.setBo(ruleTestDataBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return ruleTestDataBO;
    }

    /** 删除对象:deleteRuleTestData,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private RuleTestDataBO deleteDeleteRuleTestDataOnMissThrowEx(
            BaseRuleTestDataBOService.DeleteRuleTestDataBoResult boResult,
            DeleteRuleTestDataBto deleteRuleTestDataBto) {
        RuleTestDataBO ruleTestDataBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteRuleTestDataBto.getId() == null);
        if (!allNull && !found) {
            ruleTestDataBO = RuleTestDataBO.getById(deleteRuleTestDataBto.getId());
            found = true;
        }
        if (ruleTestDataBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(ruleTestDataBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteRuleTestDataBto);
            deletedBto.setEntity(ruleTestDataBO.convertToRuleTestData());
            boResult.getDeletedBtoList().add(deletedBto);
            return ruleTestDataBO;
        }
    }

    /** 删除规则测试数据 */
    @AutoGenerated(locked = true)
    protected DeleteRuleTestDataBoResult deleteRuleTestDataBase(
            DeleteRuleTestDataBto deleteRuleTestDataBto) {
        if (deleteRuleTestDataBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteRuleTestDataBoResult boResult = new DeleteRuleTestDataBoResult();
        RuleTestDataBO ruleTestDataBO =
                deleteDeleteRuleTestDataOnMissThrowEx(boResult, deleteRuleTestDataBto);
        boResult.setRootBo(ruleTestDataBO);
        return boResult;
    }

    /** 保存规则测试数据 */
    @AutoGenerated(locked = true)
    protected MergeRuleTestDataBoResult mergeRuleTestDataBase(
            MergeRuleTestDataBto mergeRuleTestDataBto) {
        if (mergeRuleTestDataBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeRuleTestDataBoResult boResult = new MergeRuleTestDataBoResult();
        RuleTestDataBO ruleTestDataBO =
                createMergeRuleTestDataOnDuplicateUpdate(boResult, mergeRuleTestDataBto);
        boResult.setRootBo(ruleTestDataBO);
        return boResult;
    }

    public static class MergeRuleTestDataBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RuleTestDataBO getRootBo() {
            return (RuleTestDataBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeRuleTestDataBto, RuleTestDataBO> getCreatedBto(
                MergeRuleTestDataBto mergeRuleTestDataBto) {
            return this.getAddedResult(mergeRuleTestDataBto);
        }

        @AutoGenerated(locked = true)
        public RuleTestData getDeleted_RuleTestData() {
            return (RuleTestData)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RuleTestData.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeRuleTestDataBto, RuleTestData, RuleTestDataBO> getUpdatedBto(
                MergeRuleTestDataBto mergeRuleTestDataBto) {
            return super.getUpdatedResult(mergeRuleTestDataBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeRuleTestDataBto, RuleTestDataBO> getUnmodifiedBto(
                MergeRuleTestDataBto mergeRuleTestDataBto) {
            return super.getUnmodifiedResult(mergeRuleTestDataBto);
        }
    }

    public static class DeleteRuleTestDataBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RuleTestDataBO getRootBo() {
            return (RuleTestDataBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteRuleTestDataBto, RuleTestDataBO> getCreatedBto(
                DeleteRuleTestDataBto deleteRuleTestDataBto) {
            return this.getAddedResult(deleteRuleTestDataBto);
        }

        @AutoGenerated(locked = true)
        public RuleTestData getDeleted_RuleTestData() {
            return (RuleTestData)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RuleTestData.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteRuleTestDataBto, RuleTestData, RuleTestDataBO> getUpdatedBto(
                DeleteRuleTestDataBto deleteRuleTestDataBto) {
            return super.getUpdatedResult(deleteRuleTestDataBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteRuleTestDataBto, RuleTestDataBO> getUnmodifiedBto(
                DeleteRuleTestDataBto deleteRuleTestDataBto) {
            return super.getUnmodifiedResult(deleteRuleTestDataBto);
        }
    }
}
