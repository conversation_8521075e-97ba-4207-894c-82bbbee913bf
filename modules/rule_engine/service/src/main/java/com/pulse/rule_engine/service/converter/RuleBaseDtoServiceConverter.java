package com.pulse.rule_engine.service.converter;

import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "f483c68b-205a-3570-83f1-a326ff81060e")
public class RuleBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<RuleBaseDto> RuleBaseDtoConverter(List<RuleBaseDto> ruleBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return ruleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
