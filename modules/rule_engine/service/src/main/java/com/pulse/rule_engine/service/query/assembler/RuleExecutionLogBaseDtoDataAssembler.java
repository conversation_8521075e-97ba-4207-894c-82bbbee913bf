package com.pulse.rule_engine.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

/** RuleExecutionLogBaseDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "62fdc909-77ce-362c-ae27-559e4ad781b5")
public class RuleExecutionLogBaseDtoDataAssembler {

    /** 组装RuleExecutionLogBaseDto数据 */
    @AutoGenerated(locked = true, uuid = "06307a7e-c3b2-3352-b5a1-e41a18fcb5aa")
    public void assembleData(List<RuleExecutionLogBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 批量自定义组装RuleExecutionLogBaseDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "a6881233-654e-315e-ab3a-e830b07913e4")
    public void assembleDataCustomized(List<RuleExecutionLogBaseDto> dataList) {
        // 自定义数据组装

    }
}
