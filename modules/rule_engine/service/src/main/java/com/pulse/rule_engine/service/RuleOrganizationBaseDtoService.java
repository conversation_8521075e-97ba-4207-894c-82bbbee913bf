package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleOrganizationBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.pulse.rule_engine.persist.dos.RuleOrganization.OrganizationIdAndRuleId;
import com.pulse.rule_engine.persist.eo.IdxRuleOrganizationIdEo;
import com.pulse.rule_engine.service.converter.RuleOrganizationBaseDtoServiceConverter;
import com.pulse.rule_engine.service.converter.voConverter.RuleOrganizationIdxRuleOrganizationIdConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "7cb08d46-2f4a-4fde-a309-f0514ed9a32f|DTO|SERVICE")
public class RuleOrganizationBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleOrganizationBaseDtoManager ruleOrganizationBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleOrganizationBaseDtoServiceConverter ruleOrganizationBaseDtoServiceConverter;

    @PublicInterface(id = "0ae67c81-c917-4ce8-9299-6a178a6c94f0", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "15298ee6-486a-31a3-b5d5-abea7342b62d")
    public List<RuleOrganizationBaseDto> getByRuleId(@NotNull(message = "规则ID不能为空") String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRuleIds(Arrays.asList(ruleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "05464838-0306-4f0f-8cdb-6079f869f83a", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "197a2c5f-9750-35d6-a095-c7524d2f8590")
    public List<RuleOrganizationBaseDto> getByRuleIdAndOrganizationId(
            @Valid @NotNull IdxRuleOrganizationIdEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRuleIdsAndOrganizationIds(Arrays.asList(var));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "e6791a73-d2e2-47e9-a0f1-309bd5a045b4", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "2c28984f-dde7-3242-b78a-0b7662e92803")
    public List<RuleOrganizationBaseDto> getByOrganizationIds(
            @Valid @NotNull(message = "组织ID不能为空") List<String> organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        organizationId = new ArrayList<>(new HashSet<>(organizationId));
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList =
                ruleOrganizationBaseDtoManager.getByOrganizationIds(organizationId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleOrganizationBaseDtoServiceConverter.RuleOrganizationBaseDtoConverter(
                ruleOrganizationBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "3695f7d5-1df2-48de-a027-7b37ccbfd6c0", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "3b969dd9-383c-32e6-97bc-558efa2f845e")
    public List<RuleOrganizationBaseDto> getByRuleIdsAndOrganizationIds(
            @Valid @NotNull List<IdxRuleOrganizationIdEo> idxRuleOrganizationIdEo) {
        List<OrganizationIdAndRuleId> organizationIdAndRuleId =
                idxRuleOrganizationIdEo.stream()
                        .map(
                                RuleOrganizationIdxRuleOrganizationIdConverter
                                        ::convertFromIdxRuleOrganizationIdToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList =
                ruleOrganizationBaseDtoManager.getByRuleIdsAndOrganizationIds(
                        organizationIdAndRuleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleOrganizationBaseDtoServiceConverter.RuleOrganizationBaseDtoConverter(
                ruleOrganizationBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "2756bd95-f764-408a-9bff-2e05ccc58e70", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "7f8b366a-70f5-3667-a7d1-ac4c0b043d08")
    public RuleOrganizationBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleOrganizationBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "718a8f56-3359-4d36-b59c-c70d8d424f45", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "c8992298-23e4-335d-9110-4d430a5dea19")
    public List<RuleOrganizationBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList =
                ruleOrganizationBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleOrganizationBaseDtoServiceConverter.RuleOrganizationBaseDtoConverter(
                ruleOrganizationBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "f8d592e0-0934-4b9e-ba6d-bd0e12f37a11", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "f396ac6a-eef5-3ef9-905d-cf2ee40a34ed")
    public List<RuleOrganizationBaseDto> getByRuleIds(
            @Valid @NotNull(message = "规则ID不能为空") List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        ruleId = new ArrayList<>(new HashSet<>(ruleId));
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList =
                ruleOrganizationBaseDtoManager.getByRuleIds(ruleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleOrganizationBaseDtoServiceConverter.RuleOrganizationBaseDtoConverter(
                ruleOrganizationBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "e4b02e06-797c-4eb6-8ac3-b2d913e22dd5", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "f89b811d-2c32-3ad3-863f-e9780580e4a7")
    public List<RuleOrganizationBaseDto> getByOrganizationId(
            @NotNull(message = "组织ID不能为空") String organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByOrganizationIds(Arrays.asList(organizationId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
