package com.pulse.rule_engine.service.aspect;

import lombok.Data;

import java.lang.reflect.Method;

/**
 * 规则执行上下文
 *
 * <p>封装规则执行过程中的上下文信息，包括方法信息、参数、业务数据等
 *
 * <AUTHOR>
 */
@Data
public class RuleExecutionContext {

    /** 目标方法 */
    private Method method;

    /** 目标对象 */
    private Object target;

    /** 方法参数 */
    private Object[] args;

    /** 提取的业务数据 */
    private Object businessData;

    /** 业务数据的JSON字符串 */
    private String businessDataJson;

    /** 方法执行结果 */
    private Object methodResult;

    /** 方法执行异常 */
    private Throwable methodException;

    /** 规则执行结果 */
    private Boolean ruleExecutionResult;

    /** 规则执行异常 */
    private Exception ruleExecutionException;

    /** 执行开始时间 */
    private long startTime;

    /** 执行结束时间 */
    private long endTime;

    /**
     * 获取方法执行耗时（毫秒）
     *
     * @return 执行耗时
     */
    public long getExecutionTime() {
        return endTime - startTime;
    }

    /**
     * 判断方法是否执行成功
     *
     * @return 是否成功
     */
    public boolean isMethodSuccess() {
        return methodException == null;
    }

    /**
     * 判断规则是否执行成功
     *
     * @return 是否成功
     */
    public boolean isRuleExecutionSuccess() {
        return ruleExecutionException == null;
    }

    /**
     * 获取方法签名字符串
     *
     * @return 方法签名
     */
    public String getMethodSignature() {
        if (method == null) {
            return "unknown";
        }
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }
}
