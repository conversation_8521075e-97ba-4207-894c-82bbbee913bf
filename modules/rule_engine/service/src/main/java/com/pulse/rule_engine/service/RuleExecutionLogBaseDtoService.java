package com.pulse.rule_engine.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleExecutionLogBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog.BusinessIdAndBusinessType;
import com.pulse.rule_engine.persist.eo.IdxBusinessTypeIdEo;
import com.pulse.rule_engine.service.converter.RuleExecutionLogBaseDtoServiceConverter;
import com.pulse.rule_engine.service.converter.voConverter.RuleExecutionLogIdxBusinessTypeIdConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "d445df6b-c480-4fa5-a451-5773966cb9b7|DTO|SERVICE")
public class RuleExecutionLogBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleExecutionLogBaseDtoManager ruleExecutionLogBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleExecutionLogBaseDtoServiceConverter ruleExecutionLogBaseDtoServiceConverter;

    @PublicInterface(id = "cb8a42b9-4526-49a7-990c-8767e155443d", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "15a31b84-576d-3703-b27a-e2bd94df32d7")
    public List<RuleExecutionLogBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList =
                ruleExecutionLogBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleExecutionLogBaseDtoServiceConverter.RuleExecutionLogBaseDtoConverter(
                ruleExecutionLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "aa527429-2d0e-4228-9763-7acfb69b1832", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "31cf2208-646b-366b-8579-298bc7e98e04")
    public List<RuleExecutionLogBaseDto> getByRuleId(@NotNull(message = "规则ID不能为空") String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByRuleIds(Arrays.asList(ruleId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "fc0f02d9-25ff-4fde-8d2b-f1d25f45582a", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "454991b4-2092-3a36-8298-b0870e455f32")
    public RuleExecutionLogBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleExecutionLogBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "8c34c32c-102e-4f28-ad12-ddb211075f57", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "91719863-c2cb-31d1-afce-c6303c92859a")
    public List<RuleExecutionLogBaseDto> getByBusinessTypeAndBusinessId(
            @Valid @NotNull IdxBusinessTypeIdEo var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByBusinessTypesAndBusinessIds(Arrays.asList(var));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "5195847a-af95-49a3-bdeb-f7c2f6de3f13", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "a3a41024-e7dc-3e4f-8998-d3d834e89184")
    public List<RuleExecutionLogBaseDto> getByExecutionTime(@NotNull Date executionTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByExecutionTimes(Arrays.asList(executionTime));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "25f0b121-a8de-40d9-92dc-1ac19f54c72c", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "b0f7ce55-71eb-3686-9d35-75e6f8d13ff5")
    public List<RuleExecutionLogBaseDto> getByRuleIds(
            @Valid @NotNull(message = "规则ID不能为空") List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        ruleId = new ArrayList<>(new HashSet<>(ruleId));
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList =
                ruleExecutionLogBaseDtoManager.getByRuleIds(ruleId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleExecutionLogBaseDtoServiceConverter.RuleExecutionLogBaseDtoConverter(
                ruleExecutionLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "c9f731b5-1823-4f0b-b2b8-3704784abd44", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "dfe4faa0-99c7-3fc6-95b2-e612b0fc5237")
    public List<RuleExecutionLogBaseDto> getByExecutionTimes(
            @Valid @NotNull List<Date> executionTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        executionTime = new ArrayList<>(new HashSet<>(executionTime));
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList =
                ruleExecutionLogBaseDtoManager.getByExecutionTimes(executionTime);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleExecutionLogBaseDtoServiceConverter.RuleExecutionLogBaseDtoConverter(
                ruleExecutionLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "e0271fc5-1cb9-4e86-9b56-06f71266cf6b", module = "rule_engine")
    @AutoGenerated(locked = false, uuid = "eade23b6-de95-37a0-9002-b317c1b6f757")
    public List<RuleExecutionLogBaseDto> getByBusinessTypesAndBusinessIds(
            @Valid @NotNull List<IdxBusinessTypeIdEo> idxBusinessTypeIdEo) {
        List<BusinessIdAndBusinessType> businessIdAndBusinessType =
                idxBusinessTypeIdEo.stream()
                        .map(
                                RuleExecutionLogIdxBusinessTypeIdConverter
                                        ::convertFromIdxBusinessTypeIdToInner)
                        .collect(Collectors.toList());
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList =
                ruleExecutionLogBaseDtoManager.getByBusinessTypesAndBusinessIds(
                        businessIdAndBusinessType);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return ruleExecutionLogBaseDtoServiceConverter.RuleExecutionLogBaseDtoConverter(
                ruleExecutionLogBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
