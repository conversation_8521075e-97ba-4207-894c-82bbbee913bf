package com.pulse.rule_engine.service.aspect;

import com.pulse.pulse.common.utils.JsonUtils;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.manager.dto.RuleGroupBaseDto;
import com.pulse.rule_engine.manager.dto.RuleGroupDetailBaseDto;
import com.pulse.rule_engine.persist.eo.RuleVersionEo;
import com.pulse.rule_engine.service.RuleBaseDtoService;
import com.pulse.rule_engine.service.RuleExecutionService;
import com.pulse.rule_engine.service.RuleGroupBaseDtoService;
import com.pulse.rule_engine.service.RuleGroupDetailBaseDtoService;
import com.pulse.rule_engine_common.common.annotation.RuleExecution;
import com.pulse.rule_engine_common.common.enums.RuleExecutionTiming;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则执行AOP切面
 *
 * <p>拦截带有@RuleExecution注解的方法，自动执行指定的规则或规则组
 *
 * <p>支持多种执行时机：方法执行前、后、前后、异常时
 *
 * <p>支持SpEL表达式提取业务数据
 *
 * <p>支持异常处理和结果验证
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class RuleExecutionAspect {

    @Autowired private RuleExecutionService ruleExecutionService;

    @Autowired private RuleBaseDtoService ruleBaseDtoService;

    @Autowired private RuleGroupBaseDtoService ruleGroupBaseDtoService;

    @Autowired private RuleGroupDetailBaseDtoService ruleGroupDetailBaseDtoService;

    private final ExpressionParser expressionParser = new SpelExpressionParser();

    /**
     * 环绕通知：拦截带有@RuleExecution注解的方法
     *
     * @param joinPoint 连接点
     * @param ruleExecution 规则执行注解
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("@annotation(ruleExecution)")
    public Object around(ProceedingJoinPoint joinPoint, RuleExecution ruleExecution)
            throws Throwable {
        // 检查是否启用规则执行
        if (!ruleExecution.enabled()) {
            log.debug("规则执行已禁用，跳过规则执行: {}", getMethodSignature(joinPoint));
            return joinPoint.proceed();
        }

        // 创建执行上下文
        RuleExecutionContext context = createExecutionContext(joinPoint, ruleExecution);

        try {
            // 根据执行时机处理规则执行
            return handleRuleExecution(joinPoint, ruleExecution, context);

        } catch (Exception e) {
            context.setRuleExecutionException(e);
            log.error("规则执行切面处理失败: {}", getMethodSignature(joinPoint), e);

            // 如果配置了抛出异常，则抛出
            if (ruleExecution.throwOnFailure()) {
                String errorMessage =
                        StringUtils.hasText(ruleExecution.errorMessage())
                                ? ruleExecution.errorMessage()
                                : "规则执行失败";
                throw new IgnoredException(
                        ErrorCode.SYS_ERROR, errorMessage + ": " + e.getMessage());
            }

            // 否则继续执行原方法
            return joinPoint.proceed();
        }
    }

    /**
     * 根据执行时机处理规则执行
     *
     * @param joinPoint 连接点
     * @param ruleExecution 规则执行注解
     * @param context 执行上下文
     * @return 方法执行结果
     * @throws Throwable 执行异常
     */
    private Object handleRuleExecution(
            ProceedingJoinPoint joinPoint,
            RuleExecution ruleExecution,
            RuleExecutionContext context)
            throws Throwable {

        RuleExecutionTiming timing = ruleExecution.timing();

        switch (timing) {
            case BEFORE:
                return handleBeforeExecution(joinPoint, ruleExecution, context);

            case AFTER:
                return handleAfterExecution(joinPoint, ruleExecution, context);

            case AROUND:
                return handleAroundExecution(joinPoint, ruleExecution, context);

            case AFTER_THROWING:
                return handleAfterThrowingExecution(joinPoint, ruleExecution, context);

            default:
                log.warn("不支持的规则执行时机: {}, 方法: {}", timing, getMethodSignature(joinPoint));
                return joinPoint.proceed();
        }
    }

    /** 处理方法执行前的规则执行 */
    private Object handleBeforeExecution(
            ProceedingJoinPoint joinPoint,
            RuleExecution ruleExecution,
            RuleExecutionContext context)
            throws Throwable {

        // 执行规则
        executeRules(ruleExecution, context);

        // 执行原方法
        return proceedWithMethodExecution(joinPoint, context);
    }

    /** 处理方法执行后的规则执行 */
    private Object handleAfterExecution(
            ProceedingJoinPoint joinPoint,
            RuleExecution ruleExecution,
            RuleExecutionContext context)
            throws Throwable {

        // 先执行原方法
        Object result = proceedWithMethodExecution(joinPoint, context);

        // 再执行规则
        executeRules(ruleExecution, context);

        return result;
    }

    /** 处理方法执行前后的规则执行 */
    private Object handleAroundExecution(
            ProceedingJoinPoint joinPoint,
            RuleExecution ruleExecution,
            RuleExecutionContext context)
            throws Throwable {

        // 执行前的规则
        executeRules(ruleExecution, context);

        // 执行原方法
        Object result = proceedWithMethodExecution(joinPoint, context);

        // 执行后的规则
        executeRules(ruleExecution, context);

        return result;
    }

    /** 处理方法异常时的规则执行 */
    private Object handleAfterThrowingExecution(
            ProceedingJoinPoint joinPoint,
            RuleExecution ruleExecution,
            RuleExecutionContext context)
            throws Throwable {

        try {
            // 执行原方法
            return proceedWithMethodExecution(joinPoint, context);

        } catch (Throwable e) {
            context.setMethodException(e);

            // 方法抛出异常时执行规则
            try {
                executeRules(ruleExecution, context);
            } catch (Exception ruleException) {
                log.error("异常时规则执行失败: {}", getMethodSignature(joinPoint), ruleException);
                // 规则执行失败不影响原异常的抛出
            }

            // 重新抛出原异常
            throw e;
        }
    }

    /** 执行原方法并记录结果 */
    private Object proceedWithMethodExecution(
            ProceedingJoinPoint joinPoint, RuleExecutionContext context) throws Throwable {
        try {
            context.setStartTime(System.currentTimeMillis());
            Object result = joinPoint.proceed();
            context.setEndTime(System.currentTimeMillis());
            context.setMethodResult(result);
            return result;

        } catch (Throwable e) {
            context.setEndTime(System.currentTimeMillis());
            context.setMethodException(e);
            throw e;
        }
    }

    /**
     * 执行规则
     *
     * @param ruleExecution 规则执行注解
     * @param context 执行上下文
     */
    private void executeRules(RuleExecution ruleExecution, RuleExecutionContext context) {
        try {
            String businessDataJson = context.getBusinessDataJson();
            if (!StringUtils.hasText(businessDataJson)) {
                log.warn("业务数据为空，跳过规则执行: {}", context.getMethodSignature());
                return;
            }

            // 统一转换为RuleVersionEo列表
            List<RuleVersionEo> ruleVersionList = buildRuleVersionList(ruleExecution, context);

            if (CollectionUtils.isEmpty(ruleVersionList)) {
                log.warn("未配置任何规则代码或规则组代码，跳过规则执行: {}", context.getMethodSignature());
                return;
            }

            log.info(
                    "开始执行规则，规则数量: {}, 方法: {}",
                    ruleVersionList.size(),
                    context.getMethodSignature());

            // 调用统一的规则执行方法
            Boolean result = ruleExecutionService.executeRule(ruleVersionList, businessDataJson);

            log.info("规则执行完成，结果: {}, 方法: {}", result, context.getMethodSignature());

            context.setRuleExecutionResult(result);

            // 检查是否需要在结果为false时抛出异常
            if ((result == null || !result) && ruleExecution.throwOnFalseResult()) {
                String errorMessage =
                        StringUtils.hasText(ruleExecution.errorMessage())
                                ? ruleExecution.errorMessage()
                                : "规则执行结果为false";
                throw new IgnoredException(ErrorCode.SYS_ERROR, errorMessage);
            }

        } catch (IgnoredException e) {
            context.setRuleExecutionException(e);
            throw e;
        } catch (Exception e) {
            context.setRuleExecutionException(e);
            log.error("规则执行失败: {}", context.getMethodSignature(), e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则执行失败: " + e.getMessage());
        }
    }

    /**
     * 构建规则版本EO列表
     *
     * <p>将ruleCodes和ruleGroupCodes统一转换为RuleVersionEo列表
     *
     * <p>为了正确去重，统一将所有规则转换为使用ruleId
     *
     * @param ruleExecution 规则执行注解
     * @param context 执行上下文
     * @return 规则版本EO列表
     */
    private List<RuleVersionEo> buildRuleVersionList(
            RuleExecution ruleExecution, RuleExecutionContext context) {
        List<RuleVersionEo> ruleVersionList = new ArrayList<>();

        try {
            // 1. 处理规则代码列表 - 转换为ruleId
            if (ruleExecution.ruleCodes().length > 0) {
                List<String> ruleCodes = Arrays.asList(ruleExecution.ruleCodes());
                log.debug("处理规则代码列表: {}, 方法: {}", ruleCodes, context.getMethodSignature());

                List<RuleVersionEo> codeRuleVersions =
                        buildRuleVersionListFromRuleCodes(ruleCodes, context);
                ruleVersionList.addAll(codeRuleVersions);
            }

            // 2. 处理规则组代码列表 - 转换为ruleId
            if (ruleExecution.ruleGroupCodes().length > 0) {
                List<String> ruleGroupCodes = Arrays.asList(ruleExecution.ruleGroupCodes());
                log.debug("处理规则组代码列表: {}, 方法: {}", ruleGroupCodes, context.getMethodSignature());

                List<RuleVersionEo> groupRuleVersions =
                        buildRuleVersionListFromGroupCodes(ruleGroupCodes, context);
                ruleVersionList.addAll(groupRuleVersions);
            }

            // 3. 去重处理（基于ruleId）
            ruleVersionList = deduplicateRuleVersionList(ruleVersionList);

            log.debug(
                    "构建规则版本列表完成，总数量: {}, 方法: {}",
                    ruleVersionList.size(),
                    context.getMethodSignature());

        } catch (Exception e) {
            log.error("构建规则版本列表失败: {}", context.getMethodSignature(), e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "构建规则版本列表失败: " + e.getMessage());
        }

        return ruleVersionList;
    }

    /**
     * 根据规则代码列表构建规则版本EO列表
     *
     * <p>将规则代码转换为规则ID，确保去重逻辑的正确性
     *
     * @param ruleCodes 规则代码列表
     * @param context 执行上下文
     * @return 规则版本EO列表
     */
    private List<RuleVersionEo> buildRuleVersionListFromRuleCodes(
            List<String> ruleCodes, RuleExecutionContext context) {
        List<RuleVersionEo> ruleVersionList = new ArrayList<>();

        try {
            // 1. 根据规则代码查询规则信息
            List<RuleBaseDto> rules = ruleBaseDtoService.getByCodes(ruleCodes);
            if (CollectionUtils.isEmpty(rules)) {
                log.warn("未找到任何有效的规则，规则代码: {}, 方法: {}", ruleCodes, context.getMethodSignature());
                return ruleVersionList;
            }

            // 2. 构建规则版本EO列表，使用ruleId
            for (RuleBaseDto rule : rules) {
                RuleVersionEo ruleVersionEo = new RuleVersionEo();
                ruleVersionEo.setRuleId(rule.getId());
                // 保留原始的ruleCode用于日志记录
                ruleVersionEo.setRuleCode(rule.getCode());
                ruleVersionList.add(ruleVersionEo);
            }

            log.debug(
                    "根据规则代码构建规则版本列表，规则代码数量: {}, 转换后规则数量: {}, 方法: {}",
                    ruleCodes.size(),
                    ruleVersionList.size(),
                    context.getMethodSignature());

        } catch (Exception e) {
            log.error(
                    "根据规则代码构建规则版本列表失败，规则代码: {}, 方法: {}",
                    ruleCodes,
                    context.getMethodSignature(),
                    e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "根据规则代码构建规则版本列表失败: " + e.getMessage());
        }

        return ruleVersionList;
    }

    /**
     * 根据规则组代码列表构建规则版本EO列表
     *
     * @param ruleGroupCodes 规则组代码列表
     * @param context 执行上下文
     * @return 规则版本EO列表
     */
    private List<RuleVersionEo> buildRuleVersionListFromGroupCodes(
            List<String> ruleGroupCodes, RuleExecutionContext context) {
        List<RuleVersionEo> ruleVersionList = new ArrayList<>();

        try {
            // 1. 根据规则组代码查询规则组信息
            List<RuleGroupBaseDto> ruleGroups = ruleGroupBaseDtoService.getByCodes(ruleGroupCodes);
            if (CollectionUtils.isEmpty(ruleGroups)) {
                log.warn(
                        "未找到任何有效的规则组，规则组代码: {}, 方法: {}",
                        ruleGroupCodes,
                        context.getMethodSignature());
                return ruleVersionList;
            }

            // 2. 获取规则组ID列表
            List<String> ruleGroupIds =
                    ruleGroups.stream().map(RuleGroupBaseDto::getId).collect(Collectors.toList());

            // 3. 根据规则组ID查询规则组详情，获取关联的规则ID
            List<RuleGroupDetailBaseDto> ruleGroupDetails =
                    ruleGroupDetailBaseDtoService.getByGroupIds(ruleGroupIds);
            if (CollectionUtils.isEmpty(ruleGroupDetails)) {
                log.warn(
                        "规则组中未包含任何规则，规则组代码: {}, 方法: {}",
                        ruleGroupCodes,
                        context.getMethodSignature());
                return ruleVersionList;
            }

            // 4. 提取规则ID列表并去重
            List<String> ruleIds =
                    ruleGroupDetails.stream()
                            .map(RuleGroupDetailBaseDto::getRuleId)
                            .distinct()
                            .collect(Collectors.toList());

            log.debug(
                    "从规则组中获取到规则数量: {}, 规则组数量: {}, 方法: {}",
                    ruleIds.size(),
                    ruleGroups.size(),
                    context.getMethodSignature());

            // 5. 构建规则版本EO列表
            for (String ruleId : ruleIds) {
                RuleVersionEo ruleVersionEo = new RuleVersionEo();
                ruleVersionEo.setRuleId(ruleId);
                ruleVersionList.add(ruleVersionEo);
            }

        } catch (Exception e) {
            log.error(
                    "根据规则组代码构建规则版本列表失败，规则组代码: {}, 方法: {}",
                    ruleGroupCodes,
                    context.getMethodSignature(),
                    e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "根据规则组代码构建规则版本列表失败: " + e.getMessage());
        }

        return ruleVersionList;
    }

    /**
     * 去重规则版本列表
     *
     * <p>基于规则ID进行去重，避免重复执行同一个规则
     *
     * <p>由于现在统一使用ruleId，去重逻辑变得简单可靠
     *
     * @param ruleVersionList 原始规则版本列表
     * @return 去重后的规则版本列表
     */
    private List<RuleVersionEo> deduplicateRuleVersionList(List<RuleVersionEo> ruleVersionList) {
        if (CollectionUtils.isEmpty(ruleVersionList)) {
            return ruleVersionList;
        }

        // 使用ruleId进行去重
        List<RuleVersionEo> result =
                ruleVersionList.stream()
                        .filter(this::isValidRuleVersionEo)
                        .collect(
                                Collectors.toMap(
                                        ruleVersion -> ruleVersion.getRuleId(), // 使用ruleId作为key
                                        ruleVersion -> ruleVersion, // 值就是RuleVersionEo本身
                                        (existing, replacement) -> {
                                            // 如果有重复的ruleId，保留第一个，记录日志
                                            log.debug("发现重复的规则ID: {}, 保留第一个", existing.getRuleId());
                                            return existing;
                                        }))
                        .values()
                        .stream()
                        .collect(Collectors.toList());

        log.debug("去重完成，原始数量: {}, 去重后数量: {}", ruleVersionList.size(), result.size());
        return result;
    }

    /**
     * 检查RuleVersionEo是否有效
     *
     * <p>现在主要基于ruleId进行验证，因为统一转换后都应该有ruleId
     *
     * @param ruleVersionEo 规则版本EO
     * @return 是否有效
     */
    private boolean isValidRuleVersionEo(RuleVersionEo ruleVersionEo) {
        if (ruleVersionEo == null) {
            return false;
        }

        // 必须要有规则ID
        boolean hasRuleId = StringUtils.hasText(ruleVersionEo.getRuleId());
        if (!hasRuleId) {
            log.warn("规则版本EO缺少ruleId: {}", ruleVersionEo);
        }

        return hasRuleId;
    }

    /**
     * 创建执行上下文
     *
     * @param joinPoint 连接点
     * @param ruleExecution 规则执行注解
     * @return 执行上下文
     */
    private RuleExecutionContext createExecutionContext(
            ProceedingJoinPoint joinPoint, RuleExecution ruleExecution) {
        RuleExecutionContext context = new RuleExecutionContext();

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object target = joinPoint.getTarget();
        Object[] args = joinPoint.getArgs();

        context.setMethod(method);
        context.setTarget(target);
        context.setArgs(args);

        // 提取业务数据
        Object businessData = extractBusinessData(ruleExecution, method, args);
        context.setBusinessData(businessData);

        // 转换为JSON字符串
        String businessDataJson = convertToJson(businessData);
        context.setBusinessDataJson(businessDataJson);

        return context;
    }

    /**
     * 提取业务数据
     *
     * @param ruleExecution 规则执行注解
     * @param method 方法
     * @param args 方法参数
     * @return 业务数据
     */
    private Object extractBusinessData(RuleExecution ruleExecution, Method method, Object[] args) {
        String expression = ruleExecution.businessDataExpression();

        // 如果没有配置表达式，使用第一个参数作为业务数据
        if (!StringUtils.hasText(expression)) {
            if (args != null && args.length > 0) {
                return args[0];
            }
            return null;
        }

        try {
            // 使用SpEL表达式提取业务数据
            Expression spelExpression = expressionParser.parseExpression(expression);
            EvaluationContext evaluationContext = createEvaluationContext(method, args);
            return spelExpression.getValue(evaluationContext);

        } catch (Exception e) {
            log.error("SpEL表达式解析失败: {}, 方法: {}", expression, getMethodSignature(method), e);
            // 表达式解析失败时，回退到使用第一个参数
            if (args != null && args.length > 0) {
                return args[0];
            }
            return null;
        }
    }

    /**
     * 创建SpEL表达式求值上下文
     *
     * @param method 方法
     * @param args 方法参数
     * @return 求值上下文
     */
    private EvaluationContext createEvaluationContext(Method method, Object[] args) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 设置参数数组
        context.setVariable("args", args);

        // 设置具名参数
        if (args != null && args.length > 0) {
            String[] parameterNames = getParameterNames(method);
            for (int i = 0; i < args.length && i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i], args[i]);
            }
        }

        return context;
    }

    /**
     * 获取方法参数名称
     *
     * @param method 方法
     * @return 参数名称数组
     */
    private String[] getParameterNames(Method method) {
        // 简化实现：使用参数索引作为名称
        // 在实际项目中，可以使用Spring的ParameterNameDiscoverer或Java 8的参数名称反射
        int parameterCount = method.getParameterCount();
        String[] names = new String[parameterCount];
        for (int i = 0; i < parameterCount; i++) {
            names[i] = "arg" + i;
        }
        return names;
    }

    /**
     * 将对象转换为JSON字符串
     *
     * <p>优先使用toJsonSafe方法处理循环引用问题，确保在HIS系统中的安全性
     *
     * @param data 数据对象
     * @return JSON字符串
     */
    private String convertToJson(Object data) {
        if (data == null) {
            return null;
        }

        try {
            // 使用toJsonSafe方法处理循环引用问题，符合HIS系统的要求
            return JsonUtils.toJsonSafe(data);
        } catch (Exception e) {
            log.error("对象转JSON失败: {}", data.getClass().getSimpleName(), e);
            try {
                // 如果toJsonSafe失败，尝试使用普通的toJson方法
                return JsonUtils.toJson(data);
            } catch (Exception e2) {
                log.error("使用普通toJson方法也失败: {}", data.getClass().getSimpleName(), e2);
                // 转换失败时，使用toString作为备选
                return data.toString();
            }
        }
    }

    /**
     * 获取方法签名字符串
     *
     * @param joinPoint 连接点
     * @return 方法签名
     */
    private String getMethodSignature(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return signature.getDeclaringType().getSimpleName() + "." + signature.getName();
    }

    /**
     * 获取方法签名字符串
     *
     * @param method 方法
     * @return 方法签名
     */
    private String getMethodSignature(Method method) {
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }
}
