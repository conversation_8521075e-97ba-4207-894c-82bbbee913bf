package com.pulse.rule_engine.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;

/**
 * <b>[源自]</b> Rule
 *
 * <p><b>[操作]</b> DELETE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "1417736a-430e-4a89-90d3-2de45e738eb4|BTO|DEFINITION")
public class DeleteRuleBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9f7f50e5-cd92-43d9-8e14-95c3436de430")
    private String id;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }
}
