package com.pulse.rule_engine.service;

import com.pulse.rule_engine.manager.bo.*;
import com.pulse.rule_engine.service.base.BaseRuleExecutionLogBOService;
import com.pulse.rule_engine.service.bto.CreateRuleExecutionLogBto;
import com.vs.bo.AddedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "426e0e3d-2ff8-4ace-8aec-0ff4dffc5525|BO|SERVICE")
public class RuleExecutionLogBOService extends BaseRuleExecutionLogBOService {

    /** 创建规则执行日志 */
    @PublicInterface(id = "8c80910f-cda9-4b1a-a007-442b6504d9ae", module = "rule_engine")
    @Transactional
    @AutoGenerated(locked = false, uuid = "0dad5369-73bd-44eb-ac6c-8899d183a6fc")
    public String createRuleExecutionLog(
            @Valid @NotNull CreateRuleExecutionLogBto createRuleExecutionLogBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateRuleExecutionLogBoResult boResult =
                super.createRuleExecutionLogBase(createRuleExecutionLogBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateRuleExecutionLogBto */
        {
            CreateRuleExecutionLogBto bto =
                    boResult
                            .<CreateRuleExecutionLogBto>getBtoOfType(
                                    CreateRuleExecutionLogBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateRuleExecutionLogBto, RuleExecutionLogBO> addedBto =
                    boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                RuleExecutionLogBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
