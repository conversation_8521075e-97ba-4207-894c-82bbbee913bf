package com.pulse.rule_engine.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

/** RuleBaseDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "de6fc018-f7e4-368e-a334-874d1c96977c")
public class RuleBaseDtoDataAssembler {

    /** 批量自定义组装RuleBaseDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "1218bf82-fcc8-3402-8591-9e8cbcb4d82f")
    public void assembleDataCustomized(List<RuleBaseDto> dataList) {
        // 自定义数据组装

    }

    /** 组装RuleBaseDto数据 */
    @AutoGenerated(locked = true, uuid = "be871e30-942d-397c-9b9a-27f163ac5cbf")
    public void assembleData(List<RuleBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }
}
