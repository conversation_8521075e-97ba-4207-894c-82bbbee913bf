package com.pulse.rule_engine.service.converter.voConverter;

import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.pulse.rule_engine.persist.dos.RuleOrganization.OrganizationIdAndRuleId;
import com.pulse.rule_engine.persist.eo.IdxRuleOrganizationIdEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "088b65fc-7416-3bd0-a0e4-9d43bc794af1")
public class RuleOrganizationIdxRuleOrganizationIdConverter {

    @AutoGenerated(locked = true)
    public static RuleOrganization.OrganizationIdAndRuleId convertFromIdxRuleOrganizationIdToInner(
            IdxRuleOrganizationIdEo idxRuleOrganizationId) {
        if (null == idxRuleOrganizationId) {
            return null;
        }

        OrganizationIdAndRuleId organizationIdAndRuleId = new OrganizationIdAndRuleId();
        organizationIdAndRuleId.setRuleId(idxRuleOrganizationId.getRuleId());
        organizationIdAndRuleId.setOrganizationId(idxRuleOrganizationId.getOrganizationId());
        return organizationIdAndRuleId;
    }
}
