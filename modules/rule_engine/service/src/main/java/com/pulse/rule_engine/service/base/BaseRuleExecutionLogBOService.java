package com.pulse.rule_engine.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.rule_engine.manager.bo.*;
import com.pulse.rule_engine.manager.bo.RuleExecutionLogBO;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.pulse.rule_engine.service.base.BaseRuleExecutionLogBOService.CreateRuleExecutionLogBoResult;
import com.pulse.rule_engine.service.bto.CreateRuleExecutionLogBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "21284628-ed37-32e7-aeb6-0acb05bda552")
public class BaseRuleExecutionLogBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库中存在该UK，抛出异常, 数据库中存在该UK，抛出异常 */
    @AutoGenerated(locked = true)
    private RuleExecutionLogBO createCreateRuleExecutionLogOnDuplicateThrowEx(
            BaseRuleExecutionLogBOService.CreateRuleExecutionLogBoResult boResult,
            CreateRuleExecutionLogBto createRuleExecutionLogBto) {
        RuleExecutionLogBO ruleExecutionLogBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (createRuleExecutionLogBto.getId() == null);
        if (!allNull && !found) {
            ruleExecutionLogBO = RuleExecutionLogBO.getById(createRuleExecutionLogBto.getId());
            if (ruleExecutionLogBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (ruleExecutionLogBO != null) {
            if (pkMatched) {
                log.error(
                        "主键冲突, id:{}的记录在数据库表:{}中已经存在!",
                        ruleExecutionLogBO.getId(),
                        "rule_execution_log");
                throw new IgnoredException(400, "规则执行日志已存在");
            } else {
                log.error(
                        "唯一键UK{}和数据库表:'{}'中id为:{}的记录冲突",
                        matchedUkName,
                        "rule_execution_log",
                        ruleExecutionLogBO.getId(),
                        "rule_execution_log");
                throw new IgnoredException(400, "规则执行日志已存在");
            }
        } else {
            ruleExecutionLogBO = new RuleExecutionLogBO();
            if (pkExist) {
                ruleExecutionLogBO.setId(createRuleExecutionLogBto.getId());
            } else {
                ruleExecutionLogBO.setId(
                        String.valueOf(this.idGenerator.allocateId("rule_execution_log")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "ruleId")) {
                ruleExecutionLogBO.setRuleId(createRuleExecutionLogBto.getRuleId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "ruleCode")) {
                ruleExecutionLogBO.setRuleCode(createRuleExecutionLogBto.getRuleCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "businessId")) {
                ruleExecutionLogBO.setBusinessId(createRuleExecutionLogBto.getBusinessId());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "businessType")) {
                ruleExecutionLogBO.setBusinessType(createRuleExecutionLogBto.getBusinessType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "triggerPoint")) {
                ruleExecutionLogBO.setTriggerPoint(createRuleExecutionLogBto.getTriggerPoint());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "inputData")) {
                ruleExecutionLogBO.setInputData(createRuleExecutionLogBto.getInputData());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "outputData")) {
                ruleExecutionLogBO.setOutputData(createRuleExecutionLogBto.getOutputData());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "executionDuration")) {
                ruleExecutionLogBO.setExecutionDuration(
                        createRuleExecutionLogBto.getExecutionDuration());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "executionTime")) {
                ruleExecutionLogBO.setExecutionTime(createRuleExecutionLogBto.getExecutionTime());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "executionStatus")) {
                ruleExecutionLogBO.setExecutionStatus(
                        createRuleExecutionLogBto.getExecutionStatus());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    createRuleExecutionLogBto, "__$validPropertySet"),
                    "executedBy")) {
                ruleExecutionLogBO.setExecutedBy(createRuleExecutionLogBto.getExecutedBy());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(createRuleExecutionLogBto);
            addedBto.setBo(ruleExecutionLogBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return ruleExecutionLogBO;
    }

    /** 创建规则执行日志 */
    @AutoGenerated(locked = true)
    protected CreateRuleExecutionLogBoResult createRuleExecutionLogBase(
            CreateRuleExecutionLogBto createRuleExecutionLogBto) {
        if (createRuleExecutionLogBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        CreateRuleExecutionLogBoResult boResult = new CreateRuleExecutionLogBoResult();
        RuleExecutionLogBO ruleExecutionLogBO =
                createCreateRuleExecutionLogOnDuplicateThrowEx(boResult, createRuleExecutionLogBto);
        boResult.setRootBo(ruleExecutionLogBO);
        return boResult;
    }

    public static class CreateRuleExecutionLogBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public RuleExecutionLogBO getRootBo() {
            return (RuleExecutionLogBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<CreateRuleExecutionLogBto, RuleExecutionLogBO> getCreatedBto(
                CreateRuleExecutionLogBto createRuleExecutionLogBto) {
            return this.getAddedResult(createRuleExecutionLogBto);
        }

        @AutoGenerated(locked = true)
        public RuleExecutionLog getDeleted_RuleExecutionLog() {
            return (RuleExecutionLog)
                    CollectionUtil.getFirst(this.getDeletedEntityList(RuleExecutionLog.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<CreateRuleExecutionLogBto, RuleExecutionLog, RuleExecutionLogBO>
                getUpdatedBto(CreateRuleExecutionLogBto createRuleExecutionLogBto) {
            return super.getUpdatedResult(createRuleExecutionLogBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<CreateRuleExecutionLogBto, RuleExecutionLogBO> getUnmodifiedBto(
                CreateRuleExecutionLogBto createRuleExecutionLogBto) {
            return super.getUnmodifiedResult(createRuleExecutionLogBto);
        }
    }
}
