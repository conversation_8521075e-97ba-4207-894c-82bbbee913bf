package com.pulse.rule_engine.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.rule_engine.persist.mapper.ListRuleQtoDao;
import com.pulse.rule_engine.persist.qto.ListRuleQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "9bb93927-28e1-40b0-af62-164dd5a06f05|QTO|SERVICE")
public class ListRuleQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRuleQtoDao listRuleMapper;

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "9bb93927-28e1-40b0-af62-164dd5a06f05-query-paged")
    public List<String> queryPaged(ListRuleQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listRuleMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListRuleQto qto) {
        return listRuleMapper.count(qto);
    }
}
