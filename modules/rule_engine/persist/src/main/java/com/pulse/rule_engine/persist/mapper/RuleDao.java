package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.persist.dos.Rule;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "31e7474e-d551-4130-a705-8d03825be286|ENTITY|IDAO")
public interface RuleDao {

    @AutoGenerated(locked = true, uuid = "28dfa6c7-9651-320f-b891-6575c91fa7c6")
    List<Rule> getByRuleCategoryIds(List<String> ruleCategoryId);

    @AutoGenerated(locked = true, uuid = "2a53f9a4-6006-36e9-b0f9-0d1932c8ecb9")
    List<Rule> getByCodes(List<String> code);

    @AutoGenerated(locked = true, uuid = "472e2e81-f0a0-36ef-b117-f203329abdbc")
    List<Rule> getByStatuss(List<RuleStatusEnum> status);

    @AutoGenerated(locked = true, uuid = "71b16457-a8f5-349f-8e3b-0052a91a94b3")
    List<Rule> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "a7665547-f544-3862-8278-c7c9fec30722")
    List<Rule> getByRuleCategoryId(String ruleCategoryId);

    @AutoGenerated(locked = true, uuid = "b7e14cea-b42f-35ce-a816-8350d9a37b85")
    List<Rule> getByStatus(RuleStatusEnum status);

    @AutoGenerated(locked = true, uuid = "b9f6d81a-082c-38ee-95f3-6dd321259e76")
    Rule getByCode(String code);

    @AutoGenerated(locked = true, uuid = "d2f56d6f-5335-3cab-974a-a8bb0b61424f")
    Rule getById(String id);
}
