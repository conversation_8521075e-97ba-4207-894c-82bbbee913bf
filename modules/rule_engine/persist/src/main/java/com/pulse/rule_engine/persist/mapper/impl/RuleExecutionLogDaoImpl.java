package com.pulse.rule_engine.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.pulse.rule_engine.persist.mapper.RuleExecutionLogDao;
import com.pulse.rule_engine.persist.mapper.mybatis.RuleExecutionLogMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "27d5cdd3-d309-4e56-96ed-60d5cbf98583|ENTITY|DAO")
public class RuleExecutionLogDaoImpl implements RuleExecutionLogDao {
    @AutoGenerated(locked = true)
    @Resource
    private RuleExecutionLogMapper ruleExecutionLogMapper;

    @AutoGenerated(locked = true, uuid = "1c4c0a8d-2b37-3095-85d3-6e01cba1b610")
    @Override
    public List<RuleExecutionLog> getByBusinessTypesAndBusinessIds(
            List<RuleExecutionLog.BusinessIdAndBusinessType> varList) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        for (int i = 0; i < varList.size(); i++) {
            RuleExecutionLog.BusinessIdAndBusinessType var = varList.get(i);
            if (i == 0) {
                queryWrapper
                        .eq("business_type", var.getBusinessType())
                        .eq("business_id", var.getBusinessId());
            } else {
                queryWrapper
                        .or()
                        .eq("business_type", var.getBusinessType())
                        .eq("business_id", var.getBusinessId());
            }
        }
        queryWrapper.eq("deleted_at", 0L);
        queryWrapper.orderByAsc("id");

        return ruleExecutionLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "2df611d2-2a1b-3b00-9bfc-0624c625fd53")
    @Override
    public List<RuleExecutionLog> getByExecutionTime(Date executionTime) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("execution_time", executionTime).eq("deleted_at", 0L).orderByAsc("id");
        return ruleExecutionLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "60cc515c-a9ad-3338-85a2-e5f2a74972e7")
    @Override
    public RuleExecutionLog getById(String id) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return ruleExecutionLogMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "65c3f8dc-e7fa-3269-aa0c-67dd89794b5e")
    @Override
    public List<RuleExecutionLog> getByRuleIds(List<String> ruleId) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_id", ruleId).eq("deleted_at", 0L).orderByAsc("id");
        return ruleExecutionLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "706a4e49-3fb8-3dd4-a114-60b7a7f2989d")
    @Override
    public List<RuleExecutionLog> getByExecutionTimes(List<Date> executionTime) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("execution_time", executionTime).eq("deleted_at", 0L).orderByAsc("id");
        return ruleExecutionLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b1963aae-00b3-3fd8-a921-4cd21cf63c44")
    @Override
    public List<RuleExecutionLog> getByIds(List<String> id) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return ruleExecutionLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "cfad5880-f650-3649-91c2-0844ce3fe75e")
    @Override
    public List<RuleExecutionLog> getByRuleId(String ruleId) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_id", ruleId).eq("deleted_at", 0L).orderByAsc("id");
        return ruleExecutionLogMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "feb2a8fd-c2c1-3606-936e-249b54efb74a")
    @Override
    public List<RuleExecutionLog> getByBusinessTypeAndBusinessId(
            String businessType, String businessId) {
        QueryWrapper<RuleExecutionLog> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("business_type", businessType)
                .eq("business_id", businessId)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return ruleExecutionLogMapper.selectList(queryWrapper);
    }
}
