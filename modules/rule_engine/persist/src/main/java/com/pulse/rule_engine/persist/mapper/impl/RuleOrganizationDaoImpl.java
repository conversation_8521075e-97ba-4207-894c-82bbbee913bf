package com.pulse.rule_engine.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.pulse.rule_engine.persist.mapper.RuleOrganizationDao;
import com.pulse.rule_engine.persist.mapper.mybatis.RuleOrganizationMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "4adf87c2-6e6d-4f5a-9c73-3748de38a5fb|ENTITY|DAO")
public class RuleOrganizationDaoImpl implements RuleOrganizationDao {
    @AutoGenerated(locked = true)
    @Resource
    private RuleOrganizationMapper ruleOrganizationMapper;

    @AutoGenerated(locked = true, uuid = "2bb8b046-663a-33c6-9b1e-e3eb94fbde0b")
    @Override
    public List<RuleOrganization> getByRuleIds(List<String> ruleId) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_id", ruleId).orderByAsc("id");
        return ruleOrganizationMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "839ee951-05d4-34d2-a8d8-12828aab65f4")
    @Override
    public List<RuleOrganization> getByRuleIdsAndOrganizationIds(
            List<RuleOrganization.OrganizationIdAndRuleId> varList) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        for (int i = 0; i < varList.size(); i++) {
            RuleOrganization.OrganizationIdAndRuleId var = varList.get(i);
            if (i == 0) {
                queryWrapper
                        .eq("rule_id", var.getRuleId())
                        .eq("organization_id", var.getOrganizationId());
            } else {
                queryWrapper
                        .or()
                        .eq("rule_id", var.getRuleId())
                        .eq("organization_id", var.getOrganizationId());
            }
        }
        queryWrapper.orderByAsc("id");

        return ruleOrganizationMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "91ac0513-6d06-332a-90be-a0b22e449945")
    @Override
    public List<RuleOrganization> getByIds(List<String> id) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return ruleOrganizationMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b0c77317-d9fb-330d-b243-03a90aea615e")
    @Override
    public List<RuleOrganization> getByRuleIdAndOrganizationId(
            String ruleId, String organizationId) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_id", ruleId).eq("organization_id", organizationId).orderByAsc("id");
        return ruleOrganizationMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b1625ce6-3d43-334a-b5b8-3217f16397ec")
    @Override
    public RuleOrganization getById(String id) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return ruleOrganizationMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b71850b8-d8b1-305d-a531-2a394fb3ef2b")
    @Override
    public List<RuleOrganization> getByRuleId(String ruleId) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_id", ruleId).orderByAsc("id");
        return ruleOrganizationMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d4affdac-9f9a-3550-82aa-9ccda93d98d6")
    @Override
    public List<RuleOrganization> getByOrganizationId(String organizationId) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("organization_id", organizationId).orderByAsc("id");
        return ruleOrganizationMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e15cc14f-1c68-3798-b2bb-f4b166064ca6")
    @Override
    public List<RuleOrganization> getByOrganizationIds(List<String> organizationId) {
        QueryWrapper<RuleOrganization> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("organization_id", organizationId).orderByAsc("id");
        return ruleOrganizationMapper.selectList(queryWrapper);
    }
}
