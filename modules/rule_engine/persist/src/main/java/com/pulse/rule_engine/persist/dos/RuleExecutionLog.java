package com.pulse.rule_engine.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.rule_engine.common.enums.RuleExecutionStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rule_execution_log", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "27d5cdd3-d309-4e56-96ed-60d5cbf98583|ENTITY|DEFINITION")
public class RuleExecutionLog {
    /** 业务对象 ID，如患者 ID、药品 ID */
    @AutoGenerated(locked = true, uuid = "8c75dd8a-49ba-4cea-b319-e2c6ab4a512c")
    @TableField(value = "business_id")
    private String businessId;

    /** 业务类型，如 "PATIENT"、"DRUG" */
    @AutoGenerated(locked = true, uuid = "bead0e14-cdcb-4af6-9a4e-ff310549a293")
    @TableField(value = "business_type")
    private String businessType;

    @AutoGenerated(locked = true, uuid = "17a031fb-a00b-5a1f-ba36-beb4f0fdf720")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "8f600aac-fddb-5dcc-8947-3d2f192ed136")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    @AutoGenerated(locked = true, uuid = "859f300a-e507-4548-b601-498ee0a4c969")
    @TableField(value = "executed_by")
    private String executedBy;

    /** 执行时长，单位毫秒 */
    @AutoGenerated(locked = true, uuid = "78be37e4-1a73-4129-b359-ff04be3d9af1")
    @TableField(value = "execution_duration")
    private Long executionDuration;

    /** 执行状态，如"SUCCESS"、"FAIL" */
    @AutoGenerated(locked = true, uuid = "a45498c3-df0a-45bf-9867-9d53538e7d65")
    @TableField(value = "execution_status")
    private RuleExecutionStatusEnum executionStatus;

    /** 执行时间 */
    @AutoGenerated(locked = true, uuid = "bb53f7bb-ee7e-4a9b-b661-13211199a0c9")
    @TableField(value = "execution_time")
    private Date executionTime;

    @AutoGenerated(locked = true, uuid = "2771265b-20f7-4c05-ace9-7eb0bcf8c171")
    @TableId(value = "id")
    private String id;

    /** 输入数据，JSON 格式，如 {"drugs": ["莫西沙星"]} */
    @AutoGenerated(locked = true, uuid = "53669692-345b-40b0-81bf-c591c20e9488")
    @TableField(value = "input_data")
    private String inputData;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "bc332f3b-c976-4c38-a10f-9c8120ab4c15")
    @TableField(value = "lock_version")
    private Long lockVersion;

    /** 输出数据，JSON 格式，如 {"needApproval": true} */
    @AutoGenerated(locked = true, uuid = "c392aacb-1e43-47c7-a03a-87eaa651ac08")
    @TableField(value = "output_data")
    private String outputData;

    /** 规则编码，便于业务识别 */
    @AutoGenerated(locked = true, uuid = "53b70100-043f-4b2e-9907-199a998d96aa")
    @TableField(value = "rule_code")
    private String ruleCode;

    /** 规则ID，外键关联规则表 */
    @AutoGenerated(locked = true, uuid = "b93e5991-611f-4412-a780-8b5e8c1a0ddb")
    @TableField(value = "rule_id")
    private String ruleId;

    /** 触发点，如 "PRESCRIPTION_SUBMIT" */
    @AutoGenerated(locked = true, uuid = "a1b95bda-c315-4d84-9d51-39a849ff3acf")
    @TableField(value = "trigger_point")
    private String triggerPoint;

    @AutoGenerated(locked = true, uuid = "634697b8-34fa-59d3-87f4-37be4ae54164")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @Data
    public static class BusinessIdAndBusinessType {
        @AutoGenerated(locked = true)
        private String businessType;

        @AutoGenerated(locked = true)
        private String businessId;
    }
}
