package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.vs.code.AutoGenerated;

import java.util.Date;
import java.util.List;

@AutoGenerated(locked = false, uuid = "27d5cdd3-d309-4e56-96ed-60d5cbf98583|ENTITY|IDAO")
public interface RuleExecutionLogDao {

    @AutoGenerated(locked = true, uuid = "1c4c0a8d-2b37-3095-85d3-6e01cba1b610")
    List<RuleExecutionLog> getByBusinessTypesAndBusinessIds(
            List<RuleExecutionLog.BusinessIdAndBusinessType> varList);

    @AutoGenerated(locked = true, uuid = "2df611d2-2a1b-3b00-9bfc-0624c625fd53")
    List<RuleExecutionLog> getByExecutionTime(Date executionTime);

    @AutoGenerated(locked = true, uuid = "60cc515c-a9ad-3338-85a2-e5f2a74972e7")
    RuleExecutionLog getById(String id);

    @AutoGenerated(locked = true, uuid = "65c3f8dc-e7fa-3269-aa0c-67dd89794b5e")
    List<RuleExecutionLog> getByRuleIds(List<String> ruleId);

    @AutoGenerated(locked = true, uuid = "706a4e49-3fb8-3dd4-a114-60b7a7f2989d")
    List<RuleExecutionLog> getByExecutionTimes(List<Date> executionTime);

    @AutoGenerated(locked = true, uuid = "b1963aae-00b3-3fd8-a921-4cd21cf63c44")
    List<RuleExecutionLog> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "cfad5880-f650-3649-91c2-0844ce3fe75e")
    List<RuleExecutionLog> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "feb2a8fd-c2c1-3606-936e-249b54efb74a")
    List<RuleExecutionLog> getByBusinessTypeAndBusinessId(String businessType, String businessId);
}
