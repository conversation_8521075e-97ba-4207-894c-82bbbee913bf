package com.pulse.rule_engine.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.persist.qto.ListRuleQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "9bb93927-28e1-40b0-af62-164dd5a06f05|QTO|DAO")
public class ListRuleQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询规则列表 */
    @AutoGenerated(locked = false, uuid = "9bb93927-28e1-40b0-af62-164dd5a06f05-count")
    public Integer count(ListRuleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(rule.id) FROM rule WHERE rule.rule_category_id in #ruleCategoryIdIn"
                    + " AND rule.status in #statusIn AND rule.display_name like #displayNameLike ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getRuleCategoryIdIn())) {
            conditionToRemove.add("#ruleCategoryIdIn");
        }
        if (qto.getDisplayNameLike() == null) {
            conditionToRemove.add("#displayNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getStatusIn())) {
            conditionToRemove.add("#statusIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#ruleCategoryIdIn",
                                CollectionUtil.isEmpty(qto.getRuleCategoryIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getRuleCategoryIdIn().size()))
                        .replace("#displayNameLike", "?")
                        .replace(
                                "#statusIn",
                                CollectionUtil.isEmpty(qto.getStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getStatusIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#ruleCategoryIdIn")) {
                sqlParams.addAll(qto.getRuleCategoryIdIn());
            } else if (paramName.equalsIgnoreCase("#displayNameLike")) {
                sqlParams.add("%" + qto.getDisplayNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIn")) {
                sqlParams.addAll(
                        qto.getStatusIn().stream().map(Enum::name).collect(Collectors.toList()));
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则列表 */
    @AutoGenerated(locked = false, uuid = "9bb93927-28e1-40b0-af62-164dd5a06f05-query-all")
    public List<String> query(ListRuleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule.id FROM rule WHERE rule.rule_category_id in #ruleCategoryIdIn AND"
                    + " rule.status in #statusIn AND rule.display_name like #displayNameLike ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getRuleCategoryIdIn())) {
            conditionToRemove.add("#ruleCategoryIdIn");
        }
        if (qto.getDisplayNameLike() == null) {
            conditionToRemove.add("#displayNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getStatusIn())) {
            conditionToRemove.add("#statusIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#ruleCategoryIdIn",
                                CollectionUtil.isEmpty(qto.getRuleCategoryIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getRuleCategoryIdIn().size()))
                        .replace("#displayNameLike", "?")
                        .replace(
                                "#statusIn",
                                CollectionUtil.isEmpty(qto.getStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getStatusIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#ruleCategoryIdIn")) {
                sqlParams.addAll(qto.getRuleCategoryIdIn());
            } else if (paramName.equalsIgnoreCase("#displayNameLike")) {
                sqlParams.add("%" + qto.getDisplayNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIn")) {
                sqlParams.addAll(
                        qto.getStatusIn().stream().map(Enum::name).collect(Collectors.toList()));
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule.created_at desc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则列表 */
    @AutoGenerated(locked = false, uuid = "9bb93927-28e1-40b0-af62-164dd5a06f05-query-paginate")
    public List<String> queryPaged(ListRuleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule.id FROM rule WHERE rule.rule_category_id in #ruleCategoryIdIn AND"
                    + " rule.status in #statusIn AND rule.display_name like #displayNameLike ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (CollectionUtil.isEmpty(qto.getRuleCategoryIdIn())) {
            conditionToRemove.add("#ruleCategoryIdIn");
        }
        if (qto.getDisplayNameLike() == null) {
            conditionToRemove.add("#displayNameLike");
        }
        if (CollectionUtil.isEmpty(qto.getStatusIn())) {
            conditionToRemove.add("#statusIn");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace(
                                "#ruleCategoryIdIn",
                                CollectionUtil.isEmpty(qto.getRuleCategoryIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getRuleCategoryIdIn().size()))
                        .replace("#displayNameLike", "?")
                        .replace(
                                "#statusIn",
                                CollectionUtil.isEmpty(qto.getStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getStatusIn().size()));
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#ruleCategoryIdIn")) {
                sqlParams.addAll(qto.getRuleCategoryIdIn());
            } else if (paramName.equalsIgnoreCase("#displayNameLike")) {
                sqlParams.add("%" + qto.getDisplayNameLike() + "%");
            } else if (paramName.equalsIgnoreCase("#statusIn")) {
                sqlParams.addAll(
                        qto.getStatusIn().stream().map(Enum::name).collect(Collectors.toList()));
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule.created_at desc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
