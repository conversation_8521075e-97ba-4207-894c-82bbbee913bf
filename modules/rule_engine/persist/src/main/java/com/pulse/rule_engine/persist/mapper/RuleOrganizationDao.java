package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "4adf87c2-6e6d-4f5a-9c73-3748de38a5fb|ENTITY|IDAO")
public interface RuleOrganizationDao {

    @AutoGenerated(locked = true, uuid = "2bb8b046-663a-33c6-9b1e-e3eb94fbde0b")
    List<RuleOrganization> getByRuleIds(List<String> ruleId);

    @AutoGenerated(locked = true, uuid = "839ee951-05d4-34d2-a8d8-12828aab65f4")
    List<RuleOrganization> getByRuleIdsAndOrganizationIds(
            List<RuleOrganization.OrganizationIdAndRuleId> varList);

    @AutoGenerated(locked = true, uuid = "91ac0513-6d06-332a-90be-a0b22e449945")
    List<RuleOrganization> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "b0c77317-d9fb-330d-b243-03a90aea615e")
    List<RuleOrganization> getByRuleIdAndOrganizationId(String ruleId, String organizationId);

    @AutoGenerated(locked = true, uuid = "b1625ce6-3d43-334a-b5b8-3217f16397ec")
    RuleOrganization getById(String id);

    @AutoGenerated(locked = true, uuid = "b71850b8-d8b1-305d-a531-2a394fb3ef2b")
    List<RuleOrganization> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "d4affdac-9f9a-3550-82aa-9ccda93d98d6")
    List<RuleOrganization> getByOrganizationId(String organizationId);

    @AutoGenerated(locked = true, uuid = "e15cc14f-1c68-3798-b2bb-f4b166064ca6")
    List<RuleOrganization> getByOrganizationIds(List<String> organizationId);
}
