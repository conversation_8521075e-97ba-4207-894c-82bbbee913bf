package com.pulse.rule_engine.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.rule_engine.persist.dos.RuleGroup;
import com.pulse.rule_engine.persist.mapper.RuleGroupDao;
import com.pulse.rule_engine.persist.mapper.mybatis.RuleGroupMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "84064161-cec6-4cb6-b280-aba4c92d5bda|ENTITY|DAO")
public class RuleGroupDaoImpl implements RuleGroupDao {
    @AutoGenerated(locked = true)
    @Resource
    private RuleGroupMapper ruleGroupMapper;

    @AutoGenerated(locked = true, uuid = "248890df-3776-36f1-895d-6794ab911386")
    @Override
    public List<RuleGroup> getByIds(List<String> id) {
        QueryWrapper<RuleGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return ruleGroupMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "3d160238-3185-3ff2-9a19-8d9086353283")
    @Override
    public RuleGroup getByCode(String code) {
        QueryWrapper<RuleGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code);
        return ruleGroupMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "db139243-32d5-38bb-975c-d654fd457565")
    @Override
    public List<RuleGroup> getByCodes(List<String> code) {
        QueryWrapper<RuleGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("code", code).orderByAsc("id");
        return ruleGroupMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e48bf86a-b6a2-391a-b272-ff2d7ca94ef8")
    @Override
    public RuleGroup getById(String id) {
        QueryWrapper<RuleGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return ruleGroupMapper.selectOne(queryWrapper);
    }
}
