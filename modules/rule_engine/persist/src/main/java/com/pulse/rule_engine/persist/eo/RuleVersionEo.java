package com.pulse.rule_engine.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/** 规则版本值对象 */
@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "2099dcbc-a2d3-460b-8c83-a4905d1f8bdd|EO|DEFINITION")
public class RuleVersionEo {
    /** 规则ID 规则ID */
    @AutoGenerated(locked = true, uuid = "8b6070d3-**************-2dd51a09259b")
    @NotNull(message = "规则ID不能为空")
    private String ruleId;

    /** 规则版本ID 规则版本ID */
    @AutoGenerated(locked = true, uuid = "5aac22c6-a2f4-4f31-b185-7dcab8c2d44b")
    private String ruleVersionId;

    /** 版本号 版本号 */
    @AutoGenerated(locked = true, uuid = "c7330635-001c-4bac-965e-1d52787e85a2")
    private String versionNumber;

    /** 规则编码 规则编码 */
    @AutoGenerated(locked = true, uuid = "c4029237-99cb-496b-8c6c-6bf8a43e39b2")
    private String ruleCode;
}
