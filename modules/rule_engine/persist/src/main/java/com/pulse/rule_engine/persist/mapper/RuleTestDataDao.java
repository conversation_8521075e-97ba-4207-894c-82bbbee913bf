package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.persist.dos.RuleTestData;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "c13cd999-9ef3-4bf3-a342-710fa55d1fb7|ENTITY|IDAO")
public interface RuleTestDataDao {

    @AutoGenerated(locked = true, uuid = "0e082300-2630-3210-a416-3728ad7093ed")
    List<RuleTestData> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "31f8e37a-f826-30b8-9169-25da72a3037c")
    List<RuleTestData> getByRuleVersionIds(List<String> ruleVersionId);

    @AutoGenerated(locked = true, uuid = "8ec2c355-2741-3705-9e64-8b838cae2b46")
    List<RuleTestData> getByRuleVersionId(String ruleVersionId);

    @AutoGenerated(locked = true, uuid = "c7fb1d61-9450-33e8-8c98-095b576ba2d6")
    RuleTestData getById(String id);
}
