package com.pulse.rule_engine.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.rule_engine.persist.eo.IdxBusinessTypeIdEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for IdxBusinessTypeIdEo */
@Converter
@AutoGenerated(locked = true, uuid = "6faf8c9b-6b01-3486-9eba-067892a07457")
public class IdxBusinessTypeIdEoConverter
        implements AttributeConverter<IdxBusinessTypeIdEo, String> {

    /** convert DB column to IdxBusinessTypeIdEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(IdxBusinessTypeIdEo idxBusinessTypeIdEo) {
        if (idxBusinessTypeIdEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(idxBusinessTypeIdEo);
        }
    }

    /** convert DB column to IdxBusinessTypeIdEo */
    @AutoGenerated(locked = true)
    public IdxBusinessTypeIdEo convertToEntityAttribute(String idxBusinessTypeIdEoJson) {
        if (StrUtil.isEmpty(idxBusinessTypeIdEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    idxBusinessTypeIdEoJson, new TypeReference<IdxBusinessTypeIdEo>() {});
        }
    }
}
