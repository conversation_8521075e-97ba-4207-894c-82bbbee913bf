package com.pulse.rule_engine.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.rule_engine.persist.dos.RuleTestData;
import com.pulse.rule_engine.persist.mapper.RuleTestDataDao;
import com.pulse.rule_engine.persist.mapper.mybatis.RuleTestDataMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "c13cd999-9ef3-4bf3-a342-710fa55d1fb7|ENTITY|DAO")
public class RuleTestDataDaoImpl implements RuleTestDataDao {
    @AutoGenerated(locked = true)
    @Resource
    private RuleTestDataMapper ruleTestDataMapper;

    @AutoGenerated(locked = true, uuid = "0e082300-2630-3210-a416-3728ad7093ed")
    @Override
    public List<RuleTestData> getByIds(List<String> id) {
        QueryWrapper<RuleTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).orderByAsc("id");
        return ruleTestDataMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "31f8e37a-f826-30b8-9169-25da72a3037c")
    @Override
    public List<RuleTestData> getByRuleVersionIds(List<String> ruleVersionId) {
        QueryWrapper<RuleTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_version_id", ruleVersionId).orderByAsc("id");
        return ruleTestDataMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "8ec2c355-2741-3705-9e64-8b838cae2b46")
    @Override
    public List<RuleTestData> getByRuleVersionId(String ruleVersionId) {
        QueryWrapper<RuleTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_version_id", ruleVersionId).orderByAsc("id");
        return ruleTestDataMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "c7fb1d61-9450-33e8-8c98-095b576ba2d6")
    @Override
    public RuleTestData getById(String id) {
        QueryWrapper<RuleTestData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        return ruleTestDataMapper.selectOne(queryWrapper);
    }
}
