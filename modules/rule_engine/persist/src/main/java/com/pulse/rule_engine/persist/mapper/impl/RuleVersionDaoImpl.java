package com.pulse.rule_engine.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.persist.mapper.RuleVersionDao;
import com.pulse.rule_engine.persist.mapper.mybatis.RuleVersionMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "12195c31-69a5-4ebb-b167-a14fdc5a6a70|ENTITY|DAO")
public class RuleVersionDaoImpl implements RuleVersionDao {
    @AutoGenerated(locked = true)
    @Resource
    private RuleVersionMapper ruleVersionMapper;

    @AutoGenerated(locked = true, uuid = "43238a7d-c9f2-3036-b2f6-d17f712e7fa8")
    @Override
    public List<RuleVersion> getByRuleId(String ruleId) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_id", ruleId).eq("deleted_at", 0L).orderByAsc("id");
        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "5b62c7d5-0c45-3695-9c5d-71a2e23a3b6c")
    @Override
    public List<RuleVersion> getByIds(List<String> id) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "670b6db1-3599-3b5a-ba47-c0e854f24ad3")
    @Override
    public List<RuleVersion> getByRuleIdsAndVersionNumbers(
            List<RuleVersion.RuleIdAndVersionNumber> varList) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        for (int i = 0; i < varList.size(); i++) {
            RuleVersion.RuleIdAndVersionNumber var = varList.get(i);
            if (i == 0) {
                queryWrapper
                        .eq("rule_id", var.getRuleId())
                        .eq("version_number", var.getVersionNumber());
            } else {
                queryWrapper
                        .or()
                        .eq("rule_id", var.getRuleId())
                        .eq("version_number", var.getVersionNumber());
            }
        }
        queryWrapper.eq("deleted_at", 0L);
        queryWrapper.orderByAsc("id");

        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "773c0679-1751-3224-92b1-3ba0dcd55cb3")
    @Override
    public List<RuleVersion> getByEffectiveEndTime(Date effectiveEndTime) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("effective_end_time", effectiveEndTime)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "80e9ea0a-6f47-344f-8c21-4ec337973443")
    @Override
    public RuleVersion getByRuleIdAndVersionNumber(String ruleId, String versionNumber) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_id", ruleId).eq("version_number", versionNumber).eq("deleted_at", 0L);
        return ruleVersionMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "8218a2d1-c280-3d17-bce9-2bb34ee1bdb0")
    @Override
    public List<RuleVersion> getByRuleIds(List<String> ruleId) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_id", ruleId).eq("deleted_at", 0L).orderByAsc("id");
        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "861085d1-d199-304b-8452-fa167da81195")
    @Override
    public List<RuleVersion> getByEffectiveStartTimes(List<Date> effectiveStartTime) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("effective_start_time", effectiveStartTime)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "e2c9d61b-898e-397f-a884-2d417aa563d3")
    @Override
    public List<RuleVersion> getByEffectiveStartTime(Date effectiveStartTime) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("effective_start_time", effectiveStartTime)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "f0319ab1-6369-383c-885d-3c48c41d7e80")
    @Override
    public List<RuleVersion> getByEffectiveEndTimes(List<Date> effectiveEndTime) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("effective_end_time", effectiveEndTime)
                .eq("deleted_at", 0L)
                .orderByAsc("id");
        return ruleVersionMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "ffa9138f-18b6-3d50-87fb-69e10cd9be65")
    @Override
    public RuleVersion getById(String id) {
        QueryWrapper<RuleVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return ruleVersionMapper.selectOne(queryWrapper);
    }
}
