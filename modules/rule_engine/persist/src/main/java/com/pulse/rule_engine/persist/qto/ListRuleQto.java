package com.pulse.rule_engine.persist.qto;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "9bb93927-28e1-40b0-af62-164dd5a06f05|QTO|DEFINITION")
public class ListRuleQto {
    /** 显示名称 rule.display_name */
    @AutoGenerated(locked = true, uuid = "eb65f18a-f80e-42b8-9031-2fe384ddd695")
    private String displayNameLike;

    @AutoGenerated(locked = true, uuid = "46d6c0ab-4812-40c2-8535-32bfd949c54f")
    private Integer from;

    /** 规则分类ID rule.rule_category_id */
    @Valid
    @AutoGenerated(locked = true, uuid = "5dad5c96-5acc-414c-b434-9224318a9fad")
    private List<String> ruleCategoryIdIn;

    @AutoGenerated(locked = true, uuid = "6ca0061b-8078-47fb-921b-012b9a84c5db")
    private Integer size;

    /** 状态 rule.status */
    @Valid
    @AutoGenerated(locked = true, uuid = "68f153e0-0616-46df-90ad-c7aca73794d3")
    private List<RuleStatusEnum> statusIn;
}
