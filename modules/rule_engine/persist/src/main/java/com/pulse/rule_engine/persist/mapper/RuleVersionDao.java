package com.pulse.rule_engine.persist.mapper;

import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.vs.code.AutoGenerated;

import java.util.Date;
import java.util.List;

@AutoGenerated(locked = false, uuid = "12195c31-69a5-4ebb-b167-a14fdc5a6a70|ENTITY|IDAO")
public interface RuleVersionDao {

    @AutoGenerated(locked = true, uuid = "43238a7d-c9f2-3036-b2f6-d17f712e7fa8")
    List<RuleVersion> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "5b62c7d5-0c45-3695-9c5d-71a2e23a3b6c")
    List<RuleVersion> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "670b6db1-3599-3b5a-ba47-c0e854f24ad3")
    List<RuleVersion> getByRuleIdsAndVersionNumbers(
            List<RuleVersion.RuleIdAndVersionNumber> varList);

    @AutoGenerated(locked = true, uuid = "773c0679-1751-3224-92b1-3ba0dcd55cb3")
    List<RuleVersion> getByEffectiveEndTime(Date effectiveEndTime);

    @AutoGenerated(locked = true, uuid = "80e9ea0a-6f47-344f-8c21-4ec337973443")
    RuleVersion getByRuleIdAndVersionNumber(String ruleId, String versionNumber);

    @AutoGenerated(locked = true, uuid = "8218a2d1-c280-3d17-bce9-2bb34ee1bdb0")
    List<RuleVersion> getByRuleIds(List<String> ruleId);

    @AutoGenerated(locked = true, uuid = "861085d1-d199-304b-8452-fa167da81195")
    List<RuleVersion> getByEffectiveStartTimes(List<Date> effectiveStartTime);

    @AutoGenerated(locked = true, uuid = "e2c9d61b-898e-397f-a884-2d417aa563d3")
    List<RuleVersion> getByEffectiveStartTime(Date effectiveStartTime);

    @AutoGenerated(locked = true, uuid = "f0319ab1-6369-383c-885d-3c48c41d7e80")
    List<RuleVersion> getByEffectiveEndTimes(List<Date> effectiveEndTime);

    @AutoGenerated(locked = true, uuid = "ffa9138f-18b6-3d50-87fb-69e10cd9be65")
    RuleVersion getById(String id);
}
