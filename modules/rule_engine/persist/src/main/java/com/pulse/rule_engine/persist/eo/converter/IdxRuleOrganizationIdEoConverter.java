package com.pulse.rule_engine.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.rule_engine.persist.eo.IdxRuleOrganizationIdEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for IdxRuleOrganizationIdEo */
@Converter
@AutoGenerated(locked = true, uuid = "1d35f16f-7237-3897-8d06-0bf47936476b")
public class IdxRuleOrganizationIdEoConverter
        implements AttributeConverter<IdxRuleOrganizationIdEo, String> {

    /** convert DB column to IdxRuleOrganizationIdEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(IdxRuleOrganizationIdEo idxRuleOrganizationIdEo) {
        if (idxRuleOrganizationIdEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(idxRuleOrganizationIdEo);
        }
    }

    /** convert DB column to IdxRuleOrganizationIdEo */
    @AutoGenerated(locked = true)
    public IdxRuleOrganizationIdEo convertToEntityAttribute(String idxRuleOrganizationIdEoJson) {
        if (StrUtil.isEmpty(idxRuleOrganizationIdEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    idxRuleOrganizationIdEoJson, new TypeReference<IdxRuleOrganizationIdEo>() {});
        }
    }
}
