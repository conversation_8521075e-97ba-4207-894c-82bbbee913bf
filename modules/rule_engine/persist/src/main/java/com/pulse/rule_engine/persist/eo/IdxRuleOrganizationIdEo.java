package com.pulse.rule_engine.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "fec72911-3384-3ce9-b314-57231e5f62b2|EO|DEFINITION")
public class IdxRuleOrganizationIdEo {
    @AutoGenerated(locked = true, uuid = "02045e5c-3d59-352f-a907-7c29a72d7924")
    private String ruleId;

    @AutoGenerated(locked = true, uuid = "aba253b2-2d21-33b7-a83c-630e11f83daa")
    private String organizationId;
}
