package com.pulse.rule_engine.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.rule_engine.persist.eo.IdxRuleOrganizationIdEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<IdxRuleOrganizationIdEo> */
@Converter
@AutoGenerated(locked = true, uuid = "1d35f16f-7237-3897-8d06-0bf47936476b")
public class IdxRuleOrganizationIdEoListConverter
        implements AttributeConverter<List<IdxRuleOrganizationIdEo>, String> {

    /** convert List<IdxRuleOrganizationIdEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(
            List<IdxRuleOrganizationIdEo> idxRuleOrganizationIdEoList) {
        if (idxRuleOrganizationIdEoList == null || idxRuleOrganizationIdEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(idxRuleOrganizationIdEoList);
        }
    }

    /** convert DB column to List<IdxRuleOrganizationIdEo> */
    @AutoGenerated(locked = true)
    public List<IdxRuleOrganizationIdEo> convertToEntityAttribute(
            String idxRuleOrganizationIdEoListJson) {
        if (StrUtil.isEmpty(idxRuleOrganizationIdEoListJson)) {
            return new ArrayList<IdxRuleOrganizationIdEo>();
        } else {
            return JsonUtils.readObject(
                    idxRuleOrganizationIdEoListJson,
                    new TypeReference<List<IdxRuleOrganizationIdEo>>() {});
        }
    }
}
