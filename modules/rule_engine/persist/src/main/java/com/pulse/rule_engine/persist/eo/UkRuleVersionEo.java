package com.pulse.rule_engine.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "c5d00710-f12e-32df-8401-ba52a5841632|EO|DEFINITION")
public class UkRuleVersionEo {
    @AutoGenerated(locked = true, uuid = "251ef3f2-2491-3bd7-b1a5-e2819dea5096")
    private String ruleId;

    @AutoGenerated(locked = true, uuid = "1e4c0a4b-1127-3cb3-8683-283efcbc6227")
    private String versionNumber;
}
