package com.pulse.rule_engine.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.rule_engine.persist.eo.RuleVersionEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<RuleVersionEo> */
@Converter
@AutoGenerated(locked = true, uuid = "2babb60a-84ae-3c3d-81e6-6bff1779d741")
public class RuleVersionEoListConverter implements AttributeConverter<List<RuleVersionEo>, String> {

    /** convert List<RuleVersionEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<RuleVersionEo> ruleVersionEoList) {
        if (ruleVersionEoList == null || ruleVersionEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(ruleVersionEoList);
        }
    }

    /** convert DB column to List<RuleVersionEo> */
    @AutoGenerated(locked = true)
    public List<RuleVersionEo> convertToEntityAttribute(String ruleVersionEoListJson) {
        if (StrUtil.isEmpty(ruleVersionEoListJson)) {
            return new ArrayList<RuleVersionEo>();
        } else {
            return JsonUtils.readObject(
                    ruleVersionEoListJson, new TypeReference<List<RuleVersionEo>>() {});
        }
    }
}
