package com.pulse.rule_engine.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rule_test_data", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "c13cd999-9ef3-4bf3-a342-710fa55d1fb7|ENTITY|DEFINITION")
public class RuleTestData {
    @AutoGenerated(locked = true, uuid = "800b9914-c8d2-5498-a51e-a729d10d23c3")
    @TableField(value = "created_at")
    private Date createdAt;

    /** 测试数据描述 */
    @AutoGenerated(locked = true, uuid = "fd122f7b-be2c-4260-aeca-6a585accfd3e")
    @TableField(value = "description")
    private String description;

    @AutoGenerated(locked = true, uuid = "753aaa5a-3fd2-4261-8d45-7f01807d3ebe")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "414d9753-1718-48fe-8f21-c6fb4290f813")
    @TableField(value = "lock_version")
    private Long lockVersion;

    /** 关联规则版本表ID */
    @AutoGenerated(locked = true, uuid = "0bf20a00-89ee-43ed-ac01-cc3918af4fa6")
    @TableField(value = "rule_version_id")
    private String ruleVersionId;

    /** 测试数据集（JSON格式）,例如：{"bloodRoutine": {"WBC": 15}} */
    @AutoGenerated(locked = true, uuid = "9700c40c-81e1-4bc8-922d-bc318fd5ffcf")
    @TableField(value = "test_data")
    private String testData;

    @AutoGenerated(locked = true, uuid = "51eedcbd-06d0-562e-8527-c4e8e5237aa7")
    @TableField(value = "updated_at")
    private Date updatedAt;
}
