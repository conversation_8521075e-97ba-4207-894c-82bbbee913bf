package com.pulse.rule_engine.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rule_version", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "12195c31-69a5-4ebb-b167-a14fdc5a6a70|ENTITY|DEFINITION")
public class RuleVersion {
    /** 审批人ID，外键关联用户表 */
    @AutoGenerated(locked = true, uuid = "2116874c-2ce1-452c-bba3-cc44929b54fc")
    @TableField(value = "approver_id")
    private String approverId;

    @AutoGenerated(locked = true, uuid = "c66c0a24-09f7-5479-91c9-e318f85efa87")
    @TableField(value = "created_at")
    private Date createdAt;

    /** 创建人ID，关联用户表 */
    @AutoGenerated(locked = true, uuid = "65a19061-fc93-4461-a4b6-3b331b0c9db9")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "728c1346-4675-551b-8a9e-c599097e5d6a")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    /** DRL 规则内容，如 "rule '抗生素特殊使用'..." */
    @AutoGenerated(locked = true, uuid = "311af77a-ed86-4504-9e3b-9833677977b1")
    @TableField(value = "drl_content")
    private String drlContent;

    /** 生效结束时间，可为空表示一直生效 */
    @AutoGenerated(locked = true, uuid = "ac909e52-f578-4233-8ec1-4f81b472e12f")
    @TableField(value = "effective_end_time")
    private Date effectiveEndTime;

    /** 生效开始时间 */
    @AutoGenerated(locked = true, uuid = "604d4977-d9f2-4926-b184-45fead26dd86")
    @TableField(value = "effective_start_time")
    private Date effectiveStartTime;

    @AutoGenerated(locked = true, uuid = "c7a2e6e9-3d77-4bfb-896d-30a2df4909e3")
    @TableId(value = "id")
    private String id;

    /** 规则ID，外键关联 rule 表 */
    @AutoGenerated(locked = true, uuid = "d53e6adc-2d64-434f-aa08-725d6aa8d238")
    @TableField(value = "rule_id")
    private String ruleId;

    @AutoGenerated(locked = true, uuid = "c4d5cf4c-f8ad-57b9-b064-93acc38b8941")
    @TableField(value = "updated_at")
    private Date updatedAt;

    /** 更新人ID，关联用户表 */
    @AutoGenerated(locked = true, uuid = "edc3e01d-8e42-4497-acf7-b69dcc98a7ae")
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 版本号,如 "2.1.3"。 - HIS 系统中的规则版本需反映变化的性质（如新功能、政策调整），时间戳和自增ID缺乏可读性。 -
     * 语义化版本便于开发、运维和业务人员理解版本演进，支持版本回滚和兼容性判断。
     */
    @AutoGenerated(locked = true, uuid = "800fe213-fdd4-4ea8-87fa-b1d62b32b27b")
    @TableField(value = "version_number")
    private String versionNumber;

    @Data
    public static class RuleIdAndVersionNumber {
        @AutoGenerated(locked = true)
        private String ruleId;

        @AutoGenerated(locked = true)
        private String versionNumber;
    }
}
