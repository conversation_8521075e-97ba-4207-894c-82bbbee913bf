package com.pulse.rule_engine.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.rule_engine.persist.eo.UkRuleVersionEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for UkRuleVersionEo */
@Converter
@AutoGenerated(locked = true, uuid = "12269c72-3fc1-34ab-8fce-5f49de77512f")
public class UkRuleVersionEoConverter implements AttributeConverter<UkRuleVersionEo, String> {

    /** convert DB column to UkRuleVersionEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(UkRuleVersionEo ukRuleVersionEo) {
        if (ukRuleVersionEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(ukRuleVersionEo);
        }
    }

    /** convert DB column to UkRuleVersionEo */
    @AutoGenerated(locked = true)
    public UkRuleVersionEo convertToEntityAttribute(String ukRuleVersionEoJson) {
        if (StrUtil.isEmpty(ukRuleVersionEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    ukRuleVersionEoJson, new TypeReference<UkRuleVersionEo>() {});
        }
    }
}
