package com.pulse.rule_engine.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.rule_engine.persist.eo.IdxBusinessTypeIdEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<IdxBusinessTypeIdEo> */
@Converter
@AutoGenerated(locked = true, uuid = "6faf8c9b-6b01-3486-9eba-067892a07457")
public class IdxBusinessTypeIdEoListConverter
        implements AttributeConverter<List<IdxBusinessTypeIdEo>, String> {

    /** convert List<IdxBusinessTypeIdEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<IdxBusinessTypeIdEo> idxBusinessTypeIdEoList) {
        if (idxBusinessTypeIdEoList == null || idxBusinessTypeIdEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(idxBusinessTypeIdEoList);
        }
    }

    /** convert DB column to List<IdxBusinessTypeIdEo> */
    @AutoGenerated(locked = true)
    public List<IdxBusinessTypeIdEo> convertToEntityAttribute(String idxBusinessTypeIdEoListJson) {
        if (StrUtil.isEmpty(idxBusinessTypeIdEoListJson)) {
            return new ArrayList<IdxBusinessTypeIdEo>();
        } else {
            return JsonUtils.readObject(
                    idxBusinessTypeIdEoListJson, new TypeReference<List<IdxBusinessTypeIdEo>>() {});
        }
    }
}
