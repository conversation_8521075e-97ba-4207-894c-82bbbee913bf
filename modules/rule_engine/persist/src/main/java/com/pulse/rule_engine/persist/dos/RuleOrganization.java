package com.pulse.rule_engine.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.rule_engine.common.enums.RuleOrgRelationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "rule_organization", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "4adf87c2-6e6d-4f5a-9c73-3748de38a5fb|ENTITY|DEFINITION")
public class RuleOrganization {
    @AutoGenerated(locked = true, uuid = "3ae71ff6-a778-5231-a816-c541d3322f2c")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "37fa8787-ab9c-4bbb-8357-698d8fed4617")
    @TableId(value = "id")
    private String id;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "714375d6-892f-4a82-8206-1e9dc4caedb0")
    @TableField(value = "organization_id")
    private String organizationId;

    /** 关系类型 */
    @AutoGenerated(locked = true, uuid = "f17560c4-e2ff-4ff2-a2fc-30195328f50b")
    @TableField(value = "relation_type")
    private RuleOrgRelationTypeEnum relationType;

    /** 说明具体关系或备注 */
    @AutoGenerated(locked = true, uuid = "e8c3ca8e-f206-486d-87b1-280dea234b0b")
    @TableField(value = "remark")
    private String remark;

    /** 规则ID */
    @AutoGenerated(locked = true, uuid = "cd82c4b0-9461-4f53-a238-e49d1c61ce05")
    @TableField(value = "rule_id")
    private String ruleId;

    @AutoGenerated(locked = true, uuid = "9dc67ba1-ce45-5909-a93e-5ab87cf8eb5d")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @Data
    public static class OrganizationIdAndRuleId {
        @AutoGenerated(locked = true)
        private String ruleId;

        @AutoGenerated(locked = true)
        private String organizationId;
    }
}
