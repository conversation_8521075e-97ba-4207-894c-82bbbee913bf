package com.pulse.rule_engine.persist.mapper.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.persist.dos.Rule;
import com.pulse.rule_engine.persist.mapper.RuleDao;
import com.pulse.rule_engine.persist.mapper.mybatis.RuleMapper;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "31e7474e-d551-4130-a705-8d03825be286|ENTITY|DAO")
public class RuleDaoImpl implements RuleDao {
    @AutoGenerated(locked = true)
    @Resource
    private RuleMapper ruleMapper;

    @AutoGenerated(locked = true, uuid = "28dfa6c7-9651-320f-b891-6575c91fa7c6")
    @Override
    public List<Rule> getByRuleCategoryIds(List<String> ruleCategoryId) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("rule_category_id", ruleCategoryId).eq("deleted_at", 0L).orderByAsc("id");
        return ruleMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "2a53f9a4-6006-36e9-b0f9-0d1932c8ecb9")
    @Override
    public List<Rule> getByCodes(List<String> code) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("code", code).eq("deleted_at", 0L).orderByAsc("id");
        return ruleMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "472e2e81-f0a0-36ef-b117-f203329abdbc")
    @Override
    public List<Rule> getByStatuss(List<RuleStatusEnum> status) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", status).eq("deleted_at", 0L).orderByAsc("id");
        return ruleMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "71b16457-a8f5-349f-8e3b-0052a91a94b3")
    @Override
    public List<Rule> getByIds(List<String> id) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", id).eq("deleted_at", 0L).orderByAsc("id");
        return ruleMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "a7665547-f544-3862-8278-c7c9fec30722")
    @Override
    public List<Rule> getByRuleCategoryId(String ruleCategoryId) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rule_category_id", ruleCategoryId).eq("deleted_at", 0L).orderByAsc("id");
        return ruleMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b7e14cea-b42f-35ce-a816-8350d9a37b85")
    @Override
    public List<Rule> getByStatus(RuleStatusEnum status) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status).eq("deleted_at", 0L).orderByAsc("id");
        return ruleMapper.selectList(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "b9f6d81a-082c-38ee-95f3-6dd321259e76")
    @Override
    public Rule getByCode(String code) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", code).eq("deleted_at", 0L);
        return ruleMapper.selectOne(queryWrapper);
    }

    @AutoGenerated(locked = true, uuid = "d2f56d6f-5335-3cab-974a-a8bb0b61424f")
    @Override
    public Rule getById(String id) {
        QueryWrapper<Rule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id).eq("deleted_at", 0L);
        return ruleMapper.selectOne(queryWrapper);
    }
}
