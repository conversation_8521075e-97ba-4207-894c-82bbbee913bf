package com.pulse.rule_engine.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.rule_engine.persist.eo.UkRuleVersionEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<UkRuleVersionEo> */
@Converter
@AutoGenerated(locked = true, uuid = "12269c72-3fc1-34ab-8fce-5f49de77512f")
public class UkRuleVersionEoListConverter
        implements AttributeConverter<List<UkRuleVersionEo>, String> {

    /** convert List<UkRuleVersionEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<UkRuleVersionEo> ukRuleVersionEoList) {
        if (ukRuleVersionEoList == null || ukRuleVersionEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(ukRuleVersionEoList);
        }
    }

    /** convert DB column to List<UkRuleVersionEo> */
    @AutoGenerated(locked = true)
    public List<UkRuleVersionEo> convertToEntityAttribute(String ukRuleVersionEoListJson) {
        if (StrUtil.isEmpty(ukRuleVersionEoListJson)) {
            return new ArrayList<UkRuleVersionEo>();
        } else {
            return JsonUtils.readObject(
                    ukRuleVersionEoListJson, new TypeReference<List<UkRuleVersionEo>>() {});
        }
    }
}
