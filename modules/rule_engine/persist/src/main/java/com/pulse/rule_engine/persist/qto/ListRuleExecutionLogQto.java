package com.pulse.rule_engine.persist.qto;

import com.pulse.rule_engine.common.enums.RuleExecutionStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "0d9d43eb-d83f-4915-aa53-0907b5fb3043|QTO|DEFINITION")
public class ListRuleExecutionLogQto {
    /** 业务ID rule_execution_log.business_id */
    @Valid
    @AutoGenerated(locked = true, uuid = "7d4dc60a-4f05-49ae-8c01-c79da81ddf4f")
    private List<String> businessIdIn;

    /** 业务类型 rule_execution_log.business_type */
    @Valid
    @AutoGenerated(locked = true, uuid = "e8596b22-8890-4975-8b5b-76473760a9b9")
    private List<String> businessTypeIn;

    /** 执行状态 rule_execution_log.execution_status */
    @Valid
    @AutoGenerated(locked = true, uuid = "58badd23-3894-4de6-b70b-12aac3de4ebd")
    private List<RuleExecutionStatusEnum> executionStatusIn;

    /** 执行时间 rule_execution_log.execution_time */
    @AutoGenerated(locked = true, uuid = "be8d7d39-091d-4a5d-9d55-313993c3d229")
    private Date executionTimeBiggerThanEqual;

    /** 执行时间 rule_execution_log.execution_time */
    @AutoGenerated(locked = true, uuid = "cf9d4169-57a4-4ef8-a937-ad3b5213bc4f")
    private Date executionTimeLessThanEqual;

    @AutoGenerated(locked = true, uuid = "0763f44d-4249-456e-b522-851b69f690a2")
    private Integer from;

    /** 规则ID rule_execution_log.rule_id */
    @AutoGenerated(locked = true, uuid = "186655db-990e-46ee-a8fc-cb3239ce9e18")
    private String ruleIdIs;

    @AutoGenerated(locked = true, uuid = "a13db8dd-8072-43b3-a22f-5b32ee9e78e9")
    private Integer size;
}
