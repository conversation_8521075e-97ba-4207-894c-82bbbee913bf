package com.pulse.rule_engine.persist.mapper;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.persist.qto.ListRuleExecutionLogQto;
import com.vs.code.AutoGenerated;
import com.vs.qto.QtoUtil;
import com.vs.sqlmapper.core.DBObjectHandler;
import com.vs.sqlmapper.core.SqlManager;
import com.vs.util.SqlUtil;

import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = false, uuid = "0d9d43eb-d83f-4915-aa53-0907b5fb3043|QTO|DAO")
public class ListRuleExecutionLogQtoDao {
    @AutoGenerated(locked = true)
    @Resource
    private SqlManager sqlManager;

    /** 查询规则执行日志列表 */
    @AutoGenerated(locked = false, uuid = "0d9d43eb-d83f-4915-aa53-0907b5fb3043-count")
    public Integer count(ListRuleExecutionLogQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT count(rule_execution_log.id) FROM rule_execution_log WHERE"
                    + " rule_execution_log.rule_id = #ruleIdIs AND rule_execution_log.business_type"
                    + " in #businessTypeIn AND rule_execution_log.business_id in #businessIdIn AND"
                    + " rule_execution_log.execution_time >= #executionTimeBiggerThanEqual AND"
                    + " rule_execution_log.execution_time <= #executionTimeLessThanEqual AND"
                    + " rule_execution_log.execution_status in #executionStatusIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getExecutionTimeBiggerThanEqual() == null) {
            conditionToRemove.add("#executionTimeBiggerThanEqual");
        }
        if (qto.getExecutionTimeLessThanEqual() == null) {
            conditionToRemove.add("#executionTimeLessThanEqual");
        }
        if (CollectionUtil.isEmpty(qto.getExecutionStatusIn())) {
            conditionToRemove.add("#executionStatusIn");
        }
        if (CollectionUtil.isEmpty(qto.getBusinessTypeIn())) {
            conditionToRemove.add("#businessTypeIn");
        }
        if (CollectionUtil.isEmpty(qto.getBusinessIdIn())) {
            conditionToRemove.add("#businessIdIn");
        }
        if (qto.getRuleIdIs() == null) {
            conditionToRemove.add("#ruleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule_execution_log");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#executionTimeBiggerThanEqual", "?")
                        .replace("#executionTimeLessThanEqual", "?")
                        .replace(
                                "#executionStatusIn",
                                CollectionUtil.isEmpty(qto.getExecutionStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getExecutionStatusIn().size()))
                        .replace(
                                "#businessTypeIn",
                                CollectionUtil.isEmpty(qto.getBusinessTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getBusinessTypeIn().size()))
                        .replace(
                                "#businessIdIn",
                                CollectionUtil.isEmpty(qto.getBusinessIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getBusinessIdIn().size()))
                        .replace("#ruleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#executionTimeBiggerThanEqual")) {
                sqlParams.add(qto.getExecutionTimeBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#executionTimeLessThanEqual")) {
                sqlParams.add(qto.getExecutionTimeLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#executionStatusIn")) {
                sqlParams.addAll(
                        qto.getExecutionStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#businessTypeIn")) {
                sqlParams.addAll(qto.getBusinessTypeIn());
            } else if (paramName.equalsIgnoreCase("#businessIdIn")) {
                sqlParams.addAll(qto.getBusinessIdIn());
            } else if (paramName.equalsIgnoreCase("#ruleIdIs")) {
                sqlParams.add(qto.getRuleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，修改参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        int count = this.sqlManager.count(parsedSql, sqlParams);
        return count;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则执行日志列表 */
    @AutoGenerated(locked = false, uuid = "0d9d43eb-d83f-4915-aa53-0907b5fb3043-query-all")
    public List<String> query(ListRuleExecutionLogQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule_execution_log.id FROM rule_execution_log WHERE"
                    + " rule_execution_log.rule_id = #ruleIdIs AND rule_execution_log.business_type"
                    + " in #businessTypeIn AND rule_execution_log.business_id in #businessIdIn AND"
                    + " rule_execution_log.execution_time >= #executionTimeBiggerThanEqual AND"
                    + " rule_execution_log.execution_time <= #executionTimeLessThanEqual AND"
                    + " rule_execution_log.execution_status in #executionStatusIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getExecutionTimeBiggerThanEqual() == null) {
            conditionToRemove.add("#executionTimeBiggerThanEqual");
        }
        if (qto.getExecutionTimeLessThanEqual() == null) {
            conditionToRemove.add("#executionTimeLessThanEqual");
        }
        if (CollectionUtil.isEmpty(qto.getExecutionStatusIn())) {
            conditionToRemove.add("#executionStatusIn");
        }
        if (CollectionUtil.isEmpty(qto.getBusinessTypeIn())) {
            conditionToRemove.add("#businessTypeIn");
        }
        if (CollectionUtil.isEmpty(qto.getBusinessIdIn())) {
            conditionToRemove.add("#businessIdIn");
        }
        if (qto.getRuleIdIs() == null) {
            conditionToRemove.add("#ruleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule_execution_log");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#executionTimeBiggerThanEqual", "?")
                        .replace("#executionTimeLessThanEqual", "?")
                        .replace(
                                "#executionStatusIn",
                                CollectionUtil.isEmpty(qto.getExecutionStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getExecutionStatusIn().size()))
                        .replace(
                                "#businessTypeIn",
                                CollectionUtil.isEmpty(qto.getBusinessTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getBusinessTypeIn().size()))
                        .replace(
                                "#businessIdIn",
                                CollectionUtil.isEmpty(qto.getBusinessIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getBusinessIdIn().size()))
                        .replace("#ruleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#executionTimeBiggerThanEqual")) {
                sqlParams.add(qto.getExecutionTimeBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#executionTimeLessThanEqual")) {
                sqlParams.add(qto.getExecutionTimeLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#executionStatusIn")) {
                sqlParams.addAll(
                        qto.getExecutionStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#businessTypeIn")) {
                sqlParams.addAll(qto.getBusinessTypeIn());
            } else if (paramName.equalsIgnoreCase("#businessIdIn")) {
                sqlParams.addAll(qto.getBusinessIdIn());
            } else if (paramName.equalsIgnoreCase("#ruleIdIs")) {
                sqlParams.add(qto.getRuleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule_execution_log.created_at desc ";
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }

    /** 查询规则执行日志列表 */
    @AutoGenerated(locked = false, uuid = "0d9d43eb-d83f-4915-aa53-0907b5fb3043-query-paginate")
    public List<String> queryPaged(ListRuleExecutionLogQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String sql =
                "SELECT rule_execution_log.id FROM rule_execution_log WHERE"
                    + " rule_execution_log.rule_id = #ruleIdIs AND rule_execution_log.business_type"
                    + " in #businessTypeIn AND rule_execution_log.business_id in #businessIdIn AND"
                    + " rule_execution_log.execution_time >= #executionTimeBiggerThanEqual AND"
                    + " rule_execution_log.execution_time <= #executionTimeLessThanEqual AND"
                    + " rule_execution_log.execution_status in #executionStatusIn ";
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义代码，修改sql

        /** This block is generated by vs, do not modify, start anchor 2 */
        Set<String> conditionToRemove = new HashSet<>();
        conditionToRemove.add("#");
        if (qto.getExecutionTimeBiggerThanEqual() == null) {
            conditionToRemove.add("#executionTimeBiggerThanEqual");
        }
        if (qto.getExecutionTimeLessThanEqual() == null) {
            conditionToRemove.add("#executionTimeLessThanEqual");
        }
        if (CollectionUtil.isEmpty(qto.getExecutionStatusIn())) {
            conditionToRemove.add("#executionStatusIn");
        }
        if (CollectionUtil.isEmpty(qto.getBusinessTypeIn())) {
            conditionToRemove.add("#businessTypeIn");
        }
        if (CollectionUtil.isEmpty(qto.getBusinessIdIn())) {
            conditionToRemove.add("#businessIdIn");
        }
        if (qto.getRuleIdIs() == null) {
            conditionToRemove.add("#ruleIdIs");
        }
        sql = QtoUtil.modifySql(sql, conditionToRemove);
        Set<String> softDeleteTableAlias = new HashSet<>();
        softDeleteTableAlias.add("rule_execution_log");
        sql = QtoUtil.appendSoftDeleteFilter(sql, softDeleteTableAlias);
        String parsedSql =
                sql.replace("#executionTimeBiggerThanEqual", "?")
                        .replace("#executionTimeLessThanEqual", "?")
                        .replace(
                                "#executionStatusIn",
                                CollectionUtil.isEmpty(qto.getExecutionStatusIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getExecutionStatusIn().size()))
                        .replace(
                                "#businessTypeIn",
                                CollectionUtil.isEmpty(qto.getBusinessTypeIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getBusinessTypeIn().size()))
                        .replace(
                                "#businessIdIn",
                                CollectionUtil.isEmpty(qto.getBusinessIdIn())
                                        ? "()"
                                        : SqlUtil.buildInSqlPram(qto.getBusinessIdIn().size()))
                        .replace("#ruleIdIs", "?");
        List<Object> sqlParams = new ArrayList<>();
        List<String> paramNameList = QtoUtil.extractParamList(sql);
        for (String paramName : paramNameList) {
            if (paramName.equalsIgnoreCase("#executionTimeBiggerThanEqual")) {
                sqlParams.add(qto.getExecutionTimeBiggerThanEqual());
            } else if (paramName.equalsIgnoreCase("#executionTimeLessThanEqual")) {
                sqlParams.add(qto.getExecutionTimeLessThanEqual());
            } else if (paramName.equalsIgnoreCase("#executionStatusIn")) {
                sqlParams.addAll(
                        qto.getExecutionStatusIn().stream()
                                .map(Enum::name)
                                .collect(Collectors.toList()));
            } else if (paramName.equalsIgnoreCase("#businessTypeIn")) {
                sqlParams.addAll(qto.getBusinessTypeIn());
            } else if (paramName.equalsIgnoreCase("#businessIdIn")) {
                sqlParams.addAll(qto.getBusinessIdIn());
            } else if (paramName.equalsIgnoreCase("#ruleIdIs")) {
                sqlParams.add(qto.getRuleIdIs());
            }
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义代码，处理参数

        /** This block is generated by vs, do not modify, start anchor 3 */
        parsedSql += " order by  rule_execution_log.created_at desc ";
        parsedSql += " OFFSET ? ROWS FETCH NEXT ? ROWS ONLY ";
        sqlParams.add(qto.getFrom());
        sqlParams.add(qto.getSize());
        List<String> idList =
                (List<String>)
                        this.sqlManager.getList(
                                parsedSql,
                                new DBObjectHandler<String>() {
                                    @Override
                                    public String handle(ResultSet resultSet) throws SQLException {
                                        return resultSet.getString("id");
                                    }
                                },
                                sqlParams);
        return idList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
