package com.pulse.rule_engine.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "d7e4aedc-baee-3151-8d73-4710921e813f|EO|DEFINITION")
public class IdxBusinessTypeIdEo {
    @AutoGenerated(locked = true, uuid = "eaea1d3d-b443-30fe-9c6c-2be2173be78f")
    private String businessType;

    @AutoGenerated(locked = true, uuid = "eef5f76d-93d9-384b-a12d-f2c2a27e6c9f")
    private String businessId;
}
