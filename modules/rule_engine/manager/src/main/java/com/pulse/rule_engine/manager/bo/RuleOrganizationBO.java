package com.pulse.rule_engine.manager.bo;

import com.pulse.rule_engine.common.enums.RuleOrgRelationTypeEnum;
import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Getter
@Setter
@Table(name = "rule_organization")
@Entity
@AutoGenerated(locked = true, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|DEFINITION")
public class RuleOrganizationBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "3ae71ff6-a778-5231-a816-c541d3322f2c")
    private Date createdAt;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "37fa8787-ab9c-4bbb-8357-698d8fed4617")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 组织ID 组织ID */
    @Column(name = "organization_id")
    @AutoGenerated(locked = true, uuid = "714375d6-892f-4a82-8206-1e9dc4caedb0")
    private String organizationId;

    /** 关系类型 关系类型 */
    @Column(name = "relation_type")
    @AutoGenerated(locked = true, uuid = "f17560c4-e2ff-4ff2-a2fc-30195328f50b")
    @Enumerated(EnumType.STRING)
    private RuleOrgRelationTypeEnum relationType;

    /** 备注 说明具体关系或备注 */
    @Column(name = "remark")
    @AutoGenerated(locked = true, uuid = "e8c3ca8e-f206-486d-87b1-280dea234b0b")
    private String remark;

    @ManyToOne
    @JoinColumn(name = "rule_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private RuleBO ruleBO;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "9dc67ba1-ce45-5909-a93e-5ab87cf8eb5d")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "a341d04d-72ba-47ef-a2ab-5bb4a884ea27|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public RuleOrganization convertToRuleOrganization() {
        RuleOrganization entity = new RuleOrganization();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "organizationId",
                "relationType",
                "remark",
                "createdAt",
                "updatedAt");
        RuleBO ruleBO = this.getRuleBO();
        entity.setRuleId(ruleBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getOrganizationId() {
        return this.organizationId;
    }

    @AutoGenerated(locked = true)
    public RuleOrgRelationTypeEnum getRelationType() {
        return this.relationType;
    }

    @AutoGenerated(locked = true)
    public String getRemark() {
        return this.remark;
    }

    @AutoGenerated(locked = true)
    public RuleBO getRuleBO() {
        return this.ruleBO;
    }

    @AutoGenerated(locked = true)
    public String getRuleId() {
        return this.getRuleBO().getId();
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public RuleOrganizationBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleOrganizationBO setId(String id) {
        this.id = id;
        return (RuleOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleOrganizationBO setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
        return (RuleOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleOrganizationBO setRelationType(RuleOrgRelationTypeEnum relationType) {
        this.relationType = relationType;
        return (RuleOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleOrganizationBO setRemark(String remark) {
        this.remark = remark;
        return (RuleOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleOrganizationBO setRuleBO(RuleBO ruleBO) {
        this.ruleBO = ruleBO;
        return (RuleOrganizationBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleOrganizationBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleOrganizationBO) this;
    }
}
