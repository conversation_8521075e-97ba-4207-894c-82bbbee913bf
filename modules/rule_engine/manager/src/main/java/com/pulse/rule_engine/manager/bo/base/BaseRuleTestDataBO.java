package com.pulse.rule_engine.manager.bo.base;

import com.pulse.rule_engine.manager.bo.RuleTestDataBO;
import com.pulse.rule_engine.persist.dos.RuleTestData;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "rule_test_data")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "40546f9d-670e-3426-b04a-c57a5cad2fb4")
public abstract class BaseRuleTestDataBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "800b9914-c8d2-5498-a51e-a729d10d23c3")
    private Date createdAt;

    /** 测试数据描述 测试数据描述 */
    @Column(name = "description")
    @AutoGenerated(locked = true, uuid = "fd122f7b-be2c-4260-aeca-6a585accfd3e")
    private String description;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "753aaa5a-3fd2-4261-8d45-7f01807d3ebe")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "414d9753-1718-48fe-8f21-c6fb4290f813")
    @Version
    private Long lockVersion;

    /** 规则版本ID 关联规则版本表ID */
    @Column(name = "rule_version_id")
    @AutoGenerated(locked = true, uuid = "0bf20a00-89ee-43ed-ac01-cc3918af4fa6")
    private String ruleVersionId;

    /** 测试数据 测试数据集（JSON格式）,例如：{"bloodRoutine": {"WBC": 15}} */
    @Column(name = "test_data")
    @AutoGenerated(locked = true, uuid = "9700c40c-81e1-4bc8-922d-bc318fd5ffcf")
    private String testData;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "51eedcbd-06d0-562e-8527-c4e8e5237aa7")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public RuleTestData convertToRuleTestData() {
        RuleTestData entity = new RuleTestData();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "ruleVersionId",
                "testData",
                "description",
                "createdAt",
                "updatedAt",
                "lockVersion");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static RuleTestDataBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        RuleTestDataBO ruleTestData =
                (RuleTestDataBO)
                        session.createQuery("from RuleTestDataBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return ruleTestData;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getDescription() {
        return this.description;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getRuleVersionId() {
        return this.ruleVersionId;
    }

    @AutoGenerated(locked = true)
    public String getTestData() {
        return this.testData;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public RuleTestDataBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleTestDataBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleTestDataBO setDescription(String description) {
        this.description = description;
        return (RuleTestDataBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleTestDataBO setId(String id) {
        this.id = id;
        return (RuleTestDataBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleTestDataBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (RuleTestDataBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleTestDataBO setRuleVersionId(String ruleVersionId) {
        this.ruleVersionId = ruleVersionId;
        return (RuleTestDataBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleTestDataBO setTestData(String testData) {
        this.testData = testData;
        return (RuleTestDataBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleTestDataBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleTestDataBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
