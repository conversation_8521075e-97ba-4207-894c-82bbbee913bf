package com.pulse.rule_engine.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleOrganizationBaseDtoManager;
import com.pulse.rule_engine.manager.converter.RuleOrganizationBaseDtoConverter;
import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.pulse.rule_engine.persist.mapper.RuleOrganizationDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "7cb08d46-2f4a-4fde-a309-f0514ed9a32f|DTO|BASE_MANAGER_IMPL")
public abstract class RuleOrganizationBaseDtoManagerBaseImpl
        implements RuleOrganizationBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleOrganizationBaseDtoConverter ruleOrganizationBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleOrganizationDao ruleOrganizationDao;

    @AutoGenerated(locked = true, uuid = "0819d0bc-bb19-33f7-adfa-551671fdce94")
    @Override
    public List<RuleOrganizationBaseDto> getByOrganizationId(String organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList =
                getByOrganizationIds(Arrays.asList(organizationId));
        return ruleOrganizationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "393c1134-5af3-3d8f-9094-9b6ad1c649f8")
    @Override
    public List<RuleOrganizationBaseDto> getByRuleIdsAndOrganizationIds(
            List<RuleOrganization.OrganizationIdAndRuleId> var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(var)) {
            return Collections.emptyList();
        }

        List<RuleOrganization> ruleOrganizationList =
                ruleOrganizationDao.getByRuleIdsAndOrganizationIds(var);
        if (CollectionUtil.isEmpty(ruleOrganizationList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleOrganizationToRuleOrganizationBaseDto(ruleOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "61878d0b-071f-3871-b587-d341af25656a")
    @Override
    public List<RuleOrganizationBaseDto> getByOrganizationIds(List<String> organizationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(organizationId)) {
            return Collections.emptyList();
        }

        List<RuleOrganization> ruleOrganizationList =
                ruleOrganizationDao.getByOrganizationIds(organizationId);
        if (CollectionUtil.isEmpty(ruleOrganizationList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleOrganizationToRuleOrganizationBaseDto(ruleOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9ca2d39c-5e60-3d58-888b-befc0defe3d0")
    @Override
    public List<RuleOrganizationBaseDto> getByRuleId(String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList =
                getByRuleIds(Arrays.asList(ruleId));
        return ruleOrganizationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a0769bc6-5557-36fc-9794-52369b598ce6")
    @Override
    public List<RuleOrganizationBaseDto> getByRuleIds(List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleId)) {
            return Collections.emptyList();
        }

        List<RuleOrganization> ruleOrganizationList = ruleOrganizationDao.getByRuleIds(ruleId);
        if (CollectionUtil.isEmpty(ruleOrganizationList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleOrganizationToRuleOrganizationBaseDto(ruleOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cc9a8cba-8d26-39e7-9ad8-97f19415a2fe")
    public List<RuleOrganizationBaseDto> doConvertFromRuleOrganizationToRuleOrganizationBaseDto(
            List<RuleOrganization> ruleOrganizationList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleOrganizationList)) {
            return Collections.emptyList();
        }

        Map<String, RuleOrganizationBaseDto> dtoMap =
                ruleOrganizationBaseDtoConverter
                        .convertFromRuleOrganizationToRuleOrganizationBaseDto(ruleOrganizationList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        RuleOrganizationBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList = new ArrayList<>();
        for (RuleOrganization i : ruleOrganizationList) {
            RuleOrganizationBaseDto ruleOrganizationBaseDto = dtoMap.get(i.getId());
            if (ruleOrganizationBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleOrganizationBaseDtoList.add(ruleOrganizationBaseDto);
        }
        return ruleOrganizationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "d4d3a2d0-c1d0-3559-b97b-02d60d670a1c")
    @Override
    public List<RuleOrganizationBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<RuleOrganization> ruleOrganizationList = ruleOrganizationDao.getByIds(id);
        if (CollectionUtil.isEmpty(ruleOrganizationList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, RuleOrganization> ruleOrganizationMap =
                ruleOrganizationList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        ruleOrganizationList =
                id.stream()
                        .map(i -> ruleOrganizationMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRuleOrganizationToRuleOrganizationBaseDto(ruleOrganizationList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f0ac4ccd-d305-37ff-9454-a480e9ae45b2")
    @Override
    public RuleOrganizationBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleOrganizationBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleOrganizationBaseDto ruleOrganizationBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleOrganizationBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f4b8c837-deae-3520-a2c8-4f8a899b4023")
    @Override
    public List<RuleOrganizationBaseDto> getByRuleIdAndOrganizationId(
            RuleOrganization.OrganizationIdAndRuleId var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList =
                getByRuleIdsAndOrganizationIds(Arrays.asList(var));
        return ruleOrganizationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
