package com.pulse.rule_engine.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.persist.dos.Rule;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "a3f4913a-8dff-4672-8a3c-467df7fd4f38|DTO|BASE_CONVERTER")
public class RuleBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public RuleBaseDto convertFromRuleToRuleBaseDto(Rule rule) {
        return convertFromRuleToRuleBaseDto(List.of(rule)).stream().findAny().orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<RuleBaseDto> convertFromRuleToRuleBaseDto(List<Rule> ruleList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleList)) {
            return new ArrayList<>();
        }
        List<RuleBaseDto> ruleBaseDtoList = new ArrayList<>();
        for (Rule rule : ruleList) {
            if (rule == null) {
                continue;
            }
            RuleBaseDto ruleBaseDto = new RuleBaseDto();
            ruleBaseDto.setId(rule.getId());
            ruleBaseDto.setCode(rule.getCode());
            ruleBaseDto.setDisplayName(rule.getDisplayName());
            ruleBaseDto.setRuleCategoryId(rule.getRuleCategoryId());
            ruleBaseDto.setStatus(rule.getStatus());
            ruleBaseDto.setCurrentVersion(rule.getCurrentVersion());
            ruleBaseDto.setDescription(rule.getDescription());
            ruleBaseDto.setCreatedBy(rule.getCreatedBy());
            ruleBaseDto.setUpdatedBy(rule.getUpdatedBy());
            ruleBaseDto.setLockVersion(rule.getLockVersion());
            ruleBaseDto.setCreatedAt(rule.getCreatedAt());
            ruleBaseDto.setUpdatedAt(rule.getUpdatedAt());
            ruleBaseDto.setDeletedAt(rule.getDeletedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleBaseDtoList.add(ruleBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return ruleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
