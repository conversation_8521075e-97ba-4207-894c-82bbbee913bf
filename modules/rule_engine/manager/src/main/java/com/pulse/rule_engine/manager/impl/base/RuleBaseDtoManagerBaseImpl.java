package com.pulse.rule_engine.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.RuleBaseDtoManager;
import com.pulse.rule_engine.manager.converter.RuleBaseDtoConverter;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.persist.dos.Rule;
import com.pulse.rule_engine.persist.mapper.RuleDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "a3f4913a-8dff-4672-8a3c-467df7fd4f38|DTO|BASE_MANAGER_IMPL")
public abstract class RuleBaseDtoManagerBaseImpl implements RuleBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleBaseDtoConverter ruleBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleDao ruleDao;

    @AutoGenerated(locked = true, uuid = "27676605-716d-3bea-90ee-d2ffa85da6c2")
    @Override
    public List<RuleBaseDto> getByCodes(List<String> code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(code)) {
            return Collections.emptyList();
        }

        List<Rule> ruleList = ruleDao.getByCodes(code);
        if (CollectionUtil.isEmpty(ruleList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleToRuleBaseDto(ruleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "6cc33000-4db8-3dd0-9ef8-796614589d78")
    @Override
    public List<RuleBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Rule> ruleList = ruleDao.getByIds(id);
        if (CollectionUtil.isEmpty(ruleList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Rule> ruleMap =
                ruleList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        ruleList =
                id.stream()
                        .map(i -> ruleMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRuleToRuleBaseDto(ruleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "7dcfca8e-0d64-3630-aec0-3de526ce389d")
    @Override
    public List<RuleBaseDto> getByRuleCategoryIds(List<String> ruleCategoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleCategoryId)) {
            return Collections.emptyList();
        }

        List<Rule> ruleList = ruleDao.getByRuleCategoryIds(ruleCategoryId);
        if (CollectionUtil.isEmpty(ruleList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleToRuleBaseDto(ruleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "853521b4-9d5f-3b62-84a5-18d501f5a12d")
    @Override
    public List<RuleBaseDto> getByStatuss(List<RuleStatusEnum> status) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(status)) {
            return Collections.emptyList();
        }

        List<Rule> ruleList = ruleDao.getByStatuss(status);
        if (CollectionUtil.isEmpty(ruleList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleToRuleBaseDto(ruleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8a2f8cc9-8bff-37a5-bef7-7f83feae61b5")
    @Override
    public RuleBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleBaseDto ruleBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "99d311c1-82a5-3752-b1f0-b9908c89d3d1")
    @Override
    public List<RuleBaseDto> getByRuleCategoryId(String ruleCategoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleBaseDto> ruleBaseDtoList = getByRuleCategoryIds(Arrays.asList(ruleCategoryId));
        return ruleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b04d8c8c-07c0-3d80-978a-f318b53e459a")
    @Override
    public List<RuleBaseDto> getByStatus(RuleStatusEnum status) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleBaseDto> ruleBaseDtoList = getByStatuss(Arrays.asList(status));
        return ruleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cf66bca7-c7a3-32e5-9703-f613dd452b6d")
    public List<RuleBaseDto> doConvertFromRuleToRuleBaseDto(List<Rule> ruleList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleList)) {
            return Collections.emptyList();
        }

        Map<String, RuleBaseDto> dtoMap =
                ruleBaseDtoConverter.convertFromRuleToRuleBaseDto(ruleList).stream()
                        .collect(
                                Collectors.toMap(
                                        RuleBaseDto::getId, Function.identity(), (o1, o2) -> o1));

        List<RuleBaseDto> ruleBaseDtoList = new ArrayList<>();
        for (Rule i : ruleList) {
            RuleBaseDto ruleBaseDto = dtoMap.get(i.getId());
            if (ruleBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleBaseDtoList.add(ruleBaseDto);
        }
        return ruleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "e4cd0c7b-2209-3499-82d9-ebfd088bf010")
    @Override
    public RuleBaseDto getByCode(String code) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleBaseDto> ret = getByCodes(Arrays.asList(code));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleBaseDto ruleBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
