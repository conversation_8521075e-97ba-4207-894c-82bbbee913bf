package com.pulse.rule_engine.manager;

import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "a3f4913a-8dff-4672-8a3c-467df7fd4f38|DTO|MANAGER")
public interface RuleBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "1937a33e-f32e-34b8-8dfb-77059adc3819")
    RuleBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "1fb5a871-410c-3664-882b-d8358df3fef6")
    List<RuleBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "2614fd55-a112-3c90-94e2-24500929c923")
    RuleBaseDto getByCode(String code);

    @AutoGenerated(locked = true, uuid = "52919cd7-0a39-30ff-a0ef-73d2d101f077")
    List<RuleBaseDto> getByStatuss(List<RuleStatusEnum> status);

    @AutoGenerated(locked = true, uuid = "61d990d9-1e25-300d-9bfa-106070354d15")
    List<RuleBaseDto> getByRuleCategoryId(String ruleCategoryId);

    @AutoGenerated(locked = true, uuid = "970843cd-3b97-3697-b1a6-f43a93f05312")
    List<RuleBaseDto> getByStatus(RuleStatusEnum status);

    @AutoGenerated(locked = true, uuid = "9d037c66-b69f-305f-8135-8a2eee83bf35")
    List<RuleBaseDto> getByRuleCategoryIds(List<String> ruleCategoryId);

    @AutoGenerated(locked = true, uuid = "aa9538a2-1052-38f0-ba88-baed78422a9d")
    List<RuleBaseDto> getByCodes(List<String> code);
}
