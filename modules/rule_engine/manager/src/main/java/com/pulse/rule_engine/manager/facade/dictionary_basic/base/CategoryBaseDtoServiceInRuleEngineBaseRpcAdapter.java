package com.pulse.rule_engine.manager.facade.dictionary_basic.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.manager.dto.CategoryBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "7f73e3a7-29a3-333a-a072-994b31e3700e")
public class CategoryBaseDtoServiceInRuleEngineBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "2a8a6595-c8cc-46e9-9e73-8455235dd586|RPC|BASE_ADAPTER")
    public List<CategoryBaseDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/dictionary_basic/2a8a6595-c8cc-46e9-9e73-8455235dd586/CategoryBaseDtoService-getByIds",
                        "com.pulse.dictionary_basic.service.CategoryBaseDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "e03ca7ad-ee08-468d-9c8a-1b7d14950192",
                        "003f087c-177d-4a69-81e6-c6650b5f6080"),
                new TypeReference<>() {});
    }
}
