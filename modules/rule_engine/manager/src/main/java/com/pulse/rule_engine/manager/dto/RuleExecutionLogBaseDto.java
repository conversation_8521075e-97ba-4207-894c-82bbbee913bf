package com.pulse.rule_engine.manager.dto;

import com.pulse.rule_engine.common.enums.RuleExecutionStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "d445df6b-c480-4fa5-a451-5773966cb9b7|DTO|DEFINITION")
public class RuleExecutionLogBaseDto {
    /** 业务ID 业务对象 ID，如患者 ID、药品 ID */
    @AutoGenerated(locked = true, uuid = "2bfdb634-5d5c-432b-89aa-ae5e04fdfd37")
    private String businessId;

    /** 业务类型 业务类型，如 "PATIENT"、"DRUG" */
    @AutoGenerated(locked = true, uuid = "6cb00458-9598-4a10-814f-d64fa0c22fd7")
    private String businessType;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "0b88108c-5a88-4d38-ba6b-83aa9fea1985")
    private Date createdAt;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "68de6a5d-914f-4d61-8563-dafe0131d5ce")
    private Long deletedAt;

    /** 执行人 */
    @AutoGenerated(locked = true, uuid = "2ba46b7d-7e2c-4212-94d4-bfd9532f37d7")
    private String executedBy;

    /** 执行时长（毫秒数） 执行时长，单位毫秒 */
    @AutoGenerated(locked = true, uuid = "862a0962-8314-409c-97cd-8939e6c987e9")
    private Long executionDuration;

    /** 执行状态 执行状态，如"SUCCESS"、"FAIL" */
    @AutoGenerated(locked = true, uuid = "b73926b2-201f-4a62-ae73-d32da4e94b3b")
    private RuleExecutionStatusEnum executionStatus;

    /** 执行时间 执行时间 */
    @AutoGenerated(locked = true, uuid = "c9561afd-7275-4826-ab8b-6a9501d09e40")
    private Date executionTime;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "90da798d-d8c0-4a8e-a350-10b05e65157d")
    private String id;

    /** 输入数据 输入数据，JSON 格式，如 {"drugs": ["莫西沙星"]} */
    @AutoGenerated(locked = true, uuid = "0b79f076-cf31-43c5-8d99-07def9f359e9")
    private String inputData;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "e439d1a0-6fc6-40c8-ae48-b2bfe954f269")
    private Long lockVersion;

    /** 输出数据 输出数据，JSON 格式，如 {"needApproval": true} */
    @AutoGenerated(locked = true, uuid = "102b7ed1-a7d7-4ae3-9df6-3119a892cc43")
    private String outputData;

    /** 规则编码 规则编码，便于业务识别 */
    @AutoGenerated(locked = true, uuid = "8164820d-9608-41a6-999d-4aa1e7d03cca")
    private String ruleCode;

    /** 规则ID 规则ID，外键关联规则表 */
    @AutoGenerated(locked = true, uuid = "695684d2-2a0f-4991-9cc8-958139b269a0")
    private String ruleId;

    /** 触发点 触发点，如 "PRESCRIPTION_SUBMIT" */
    @AutoGenerated(locked = true, uuid = "67b26e49-da2c-4003-a963-c498c9cfba7a")
    private String triggerPoint;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "dd4ae204-8071-49f5-85ef-b5ccd48215e6")
    private Date updatedAt;
}
