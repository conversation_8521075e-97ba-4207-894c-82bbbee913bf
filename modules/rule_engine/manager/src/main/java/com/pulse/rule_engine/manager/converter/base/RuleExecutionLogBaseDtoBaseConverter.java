package com.pulse.rule_engine.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "d445df6b-c480-4fa5-a451-5773966cb9b7|DTO|BASE_CONVERTER")
public class RuleExecutionLogBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public RuleExecutionLogBaseDto convertFromRuleExecutionLogToRuleExecutionLogBaseDto(
            RuleExecutionLog ruleExecutionLog) {
        return convertFromRuleExecutionLogToRuleExecutionLogBaseDto(List.of(ruleExecutionLog))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<RuleExecutionLogBaseDto> convertFromRuleExecutionLogToRuleExecutionLogBaseDto(
            List<RuleExecutionLog> ruleExecutionLogList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleExecutionLogList)) {
            return new ArrayList<>();
        }
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList = new ArrayList<>();
        for (RuleExecutionLog ruleExecutionLog : ruleExecutionLogList) {
            if (ruleExecutionLog == null) {
                continue;
            }
            RuleExecutionLogBaseDto ruleExecutionLogBaseDto = new RuleExecutionLogBaseDto();
            ruleExecutionLogBaseDto.setId(ruleExecutionLog.getId());
            ruleExecutionLogBaseDto.setRuleId(ruleExecutionLog.getRuleId());
            ruleExecutionLogBaseDto.setRuleCode(ruleExecutionLog.getRuleCode());
            ruleExecutionLogBaseDto.setBusinessId(ruleExecutionLog.getBusinessId());
            ruleExecutionLogBaseDto.setBusinessType(ruleExecutionLog.getBusinessType());
            ruleExecutionLogBaseDto.setTriggerPoint(ruleExecutionLog.getTriggerPoint());
            ruleExecutionLogBaseDto.setInputData(ruleExecutionLog.getInputData());
            ruleExecutionLogBaseDto.setOutputData(ruleExecutionLog.getOutputData());
            ruleExecutionLogBaseDto.setExecutionDuration(ruleExecutionLog.getExecutionDuration());
            ruleExecutionLogBaseDto.setExecutionTime(ruleExecutionLog.getExecutionTime());
            ruleExecutionLogBaseDto.setExecutionStatus(ruleExecutionLog.getExecutionStatus());
            ruleExecutionLogBaseDto.setExecutedBy(ruleExecutionLog.getExecutedBy());
            ruleExecutionLogBaseDto.setLockVersion(ruleExecutionLog.getLockVersion());
            ruleExecutionLogBaseDto.setCreatedAt(ruleExecutionLog.getCreatedAt());
            ruleExecutionLogBaseDto.setUpdatedAt(ruleExecutionLog.getUpdatedAt());
            ruleExecutionLogBaseDto.setDeletedAt(ruleExecutionLog.getDeletedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleExecutionLogBaseDtoList.add(ruleExecutionLogBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return ruleExecutionLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
