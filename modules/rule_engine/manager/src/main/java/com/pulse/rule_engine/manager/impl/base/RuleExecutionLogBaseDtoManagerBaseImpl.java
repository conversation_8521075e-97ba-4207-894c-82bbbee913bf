package com.pulse.rule_engine.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleExecutionLogBaseDtoManager;
import com.pulse.rule_engine.manager.converter.RuleExecutionLogBaseDtoConverter;
import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.pulse.rule_engine.persist.mapper.RuleExecutionLogDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "d445df6b-c480-4fa5-a451-5773966cb9b7|DTO|BASE_MANAGER_IMPL")
public abstract class RuleExecutionLogBaseDtoManagerBaseImpl
        implements RuleExecutionLogBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleExecutionLogBaseDtoConverter ruleExecutionLogBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleExecutionLogDao ruleExecutionLogDao;

    @AutoGenerated(locked = true, uuid = "3d20854b-42ec-3b44-94c1-5f508214e6b8")
    @Override
    public List<RuleExecutionLogBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<RuleExecutionLog> ruleExecutionLogList = ruleExecutionLogDao.getByIds(id);
        if (CollectionUtil.isEmpty(ruleExecutionLogList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, RuleExecutionLog> ruleExecutionLogMap =
                ruleExecutionLogList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        ruleExecutionLogList =
                id.stream()
                        .map(i -> ruleExecutionLogMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRuleExecutionLogToRuleExecutionLogBaseDto(ruleExecutionLogList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "58817b92-16fa-3b62-965d-ec8934276196")
    public List<RuleExecutionLogBaseDto> doConvertFromRuleExecutionLogToRuleExecutionLogBaseDto(
            List<RuleExecutionLog> ruleExecutionLogList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleExecutionLogList)) {
            return Collections.emptyList();
        }

        Map<String, RuleExecutionLogBaseDto> dtoMap =
                ruleExecutionLogBaseDtoConverter
                        .convertFromRuleExecutionLogToRuleExecutionLogBaseDto(ruleExecutionLogList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        RuleExecutionLogBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList = new ArrayList<>();
        for (RuleExecutionLog i : ruleExecutionLogList) {
            RuleExecutionLogBaseDto ruleExecutionLogBaseDto = dtoMap.get(i.getId());
            if (ruleExecutionLogBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleExecutionLogBaseDtoList.add(ruleExecutionLogBaseDto);
        }
        return ruleExecutionLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "6c13d375-9bc0-361e-82d2-05bb97f7b557")
    @Override
    public List<RuleExecutionLogBaseDto> getByBusinessTypeAndBusinessId(
            RuleExecutionLog.BusinessIdAndBusinessType var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList =
                getByBusinessTypesAndBusinessIds(Arrays.asList(var));
        return ruleExecutionLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8f624e4b-43d4-33e1-86ab-113df752c129")
    @Override
    public RuleExecutionLogBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleExecutionLogBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleExecutionLogBaseDto ruleExecutionLogBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleExecutionLogBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a7b90d28-9149-36d1-acfa-f08f62e0bbb0")
    @Override
    public List<RuleExecutionLogBaseDto> getByExecutionTime(Date executionTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList =
                getByExecutionTimes(Arrays.asList(executionTime));
        return ruleExecutionLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b4a0e330-8084-3707-9dd9-c8b87cfc0f1d")
    @Override
    public List<RuleExecutionLogBaseDto> getByRuleIds(List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleId)) {
            return Collections.emptyList();
        }

        List<RuleExecutionLog> ruleExecutionLogList = ruleExecutionLogDao.getByRuleIds(ruleId);
        if (CollectionUtil.isEmpty(ruleExecutionLogList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleExecutionLogToRuleExecutionLogBaseDto(ruleExecutionLogList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bb4be514-0256-3629-ac30-b931a8250a14")
    @Override
    public List<RuleExecutionLogBaseDto> getByExecutionTimes(List<Date> executionTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(executionTime)) {
            return Collections.emptyList();
        }

        List<RuleExecutionLog> ruleExecutionLogList =
                ruleExecutionLogDao.getByExecutionTimes(executionTime);
        if (CollectionUtil.isEmpty(ruleExecutionLogList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleExecutionLogToRuleExecutionLogBaseDto(ruleExecutionLogList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f4128804-78ba-366c-8556-4760ad90505b")
    @Override
    public List<RuleExecutionLogBaseDto> getByRuleId(String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleExecutionLogBaseDto> ruleExecutionLogBaseDtoList =
                getByRuleIds(Arrays.asList(ruleId));
        return ruleExecutionLogBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "faf2c4a4-87c3-3629-9d68-d553a1131c12")
    @Override
    public List<RuleExecutionLogBaseDto> getByBusinessTypesAndBusinessIds(
            List<RuleExecutionLog.BusinessIdAndBusinessType> var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(var)) {
            return Collections.emptyList();
        }

        List<RuleExecutionLog> ruleExecutionLogList =
                ruleExecutionLogDao.getByBusinessTypesAndBusinessIds(var);
        if (CollectionUtil.isEmpty(ruleExecutionLogList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleExecutionLogToRuleExecutionLogBaseDto(ruleExecutionLogList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
