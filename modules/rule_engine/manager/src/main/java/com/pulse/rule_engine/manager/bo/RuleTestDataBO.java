package com.pulse.rule_engine.manager.bo;

import com.pulse.rule_engine.manager.bo.base.BaseRuleTestDataBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Table(name = "rule_test_data")
@Entity
@AutoGenerated(locked = false, uuid = "64feb39f-e8a0-4ef8-8fcf-ebe43a3fc8c6|BO|DEFINITION")
public class RuleTestDataBO extends BaseRuleTestDataBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "64feb39f-e8a0-4ef8-8fcf-ebe43a3fc8c6|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
