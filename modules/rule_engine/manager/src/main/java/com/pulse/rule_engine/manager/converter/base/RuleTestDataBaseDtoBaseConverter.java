package com.pulse.rule_engine.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleTestDataBaseDto;
import com.pulse.rule_engine.persist.dos.RuleTestData;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "922b047d-f47b-452e-935c-00c47ebd8cf7|DTO|BASE_CONVERTER")
public class RuleTestDataBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public RuleTestDataBaseDto convertFromRuleTestDataToRuleTestDataBaseDto(
            RuleTestData ruleTestData) {
        return convertFromRuleTestDataToRuleTestDataBaseDto(List.of(ruleTestData)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<RuleTestDataBaseDto> convertFromRuleTestDataToRuleTestDataBaseDto(
            List<RuleTestData> ruleTestDataList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleTestDataList)) {
            return new ArrayList<>();
        }
        List<RuleTestDataBaseDto> ruleTestDataBaseDtoList = new ArrayList<>();
        for (RuleTestData ruleTestData : ruleTestDataList) {
            if (ruleTestData == null) {
                continue;
            }
            RuleTestDataBaseDto ruleTestDataBaseDto = new RuleTestDataBaseDto();
            ruleTestDataBaseDto.setId(ruleTestData.getId());
            ruleTestDataBaseDto.setRuleVersionId(ruleTestData.getRuleVersionId());
            ruleTestDataBaseDto.setTestData(ruleTestData.getTestData());
            ruleTestDataBaseDto.setDescription(ruleTestData.getDescription());
            ruleTestDataBaseDto.setCreatedAt(ruleTestData.getCreatedAt());
            ruleTestDataBaseDto.setUpdatedAt(ruleTestData.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleTestDataBaseDtoList.add(ruleTestDataBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return ruleTestDataBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
