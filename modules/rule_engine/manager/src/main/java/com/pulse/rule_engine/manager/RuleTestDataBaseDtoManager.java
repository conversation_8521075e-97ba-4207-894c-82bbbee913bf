package com.pulse.rule_engine.manager;

import com.pulse.rule_engine.manager.dto.RuleTestDataBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "922b047d-f47b-452e-935c-00c47ebd8cf7|DTO|MANAGER")
public interface RuleTestDataBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "74a39745-ed3d-33ad-aba8-a9fe7903ffb8")
    List<RuleTestDataBaseDto> getByRuleVersionId(String ruleVersionId);

    @AutoGenerated(locked = true, uuid = "82deebfa-b7af-3cf5-a008-3bd587a49339")
    RuleTestDataBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "a0e593b6-8b35-36ab-842a-b49fc7b0ad3b")
    List<RuleTestDataBaseDto> getByRuleVersionIds(List<String> ruleVersionId);

    @AutoGenerated(locked = true, uuid = "fabd8e75-b2da-398d-a46b-ded6b598c97a")
    List<RuleTestDataBaseDto> getByIds(List<String> id);
}
