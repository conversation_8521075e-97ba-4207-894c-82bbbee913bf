package com.pulse.rule_engine.manager;

import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "7cb08d46-2f4a-4fde-a309-f0514ed9a32f|DTO|MANAGER")
public interface RuleOrganizationBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "1f2f8f54-3c6e-39d4-9044-4330896404c3")
    List<RuleOrganizationBaseDto> getByRuleIdAndOrganizationId(
            RuleOrganization.OrganizationIdAndRuleId var);

    @AutoGenerated(locked = true, uuid = "407dbc89-3511-31a5-8ab3-ae44a69075f0")
    List<RuleOrganizationBaseDto> getByOrganizationIds(List<String> organizationId);

    @AutoGenerated(locked = true, uuid = "40af8fe8-494b-322c-9db7-2b503df94a2e")
    List<RuleOrganizationBaseDto> getByOrganizationId(String organizationId);

    @AutoGenerated(locked = true, uuid = "47405060-931c-31fb-9ca5-d8a9928b5495")
    List<RuleOrganizationBaseDto> getByRuleIdsAndOrganizationIds(
            List<RuleOrganization.OrganizationIdAndRuleId> var);

    @AutoGenerated(locked = true, uuid = "4a4a5e30-f0a8-37b7-931e-cde7f0e9a99f")
    List<RuleOrganizationBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "c5caa4aa-4c15-3041-9b14-09d1bf9e21ac")
    RuleOrganizationBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "e8eea225-926e-3a08-bcc2-f38aec21f497")
    List<RuleOrganizationBaseDto> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "ea3ba9c1-d3c7-38fa-9469-31f74b38e083")
    List<RuleOrganizationBaseDto> getByRuleIds(List<String> ruleId);
}
