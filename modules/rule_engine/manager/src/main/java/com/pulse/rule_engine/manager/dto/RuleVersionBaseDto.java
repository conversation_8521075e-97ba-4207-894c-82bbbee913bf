package com.pulse.rule_engine.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "0a60d032-acdf-4e6c-ad5a-2863ee2efa9f|DTO|DEFINITION")
public class RuleVersionBaseDto {
    /** 审批人ID 审批人ID，外键关联用户表 */
    @AutoGenerated(locked = true, uuid = "8ddc938b-4863-484b-b4f2-e98d22d237f1")
    private String approverId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "a6655054-51c8-499f-b87f-************")
    private Date createdAt;

    /** 创建人 创建人ID，关联用户表 */
    @AutoGenerated(locked = true, uuid = "f5c84955-b34d-4632-a500-6d4106eba5fb")
    private String createdBy;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "32ea6f4b-44d1-4d85-81cd-8b87e1d0fd6a")
    private Long deletedAt;

    /** 决策规则内容 DRL 规则内容，如 "rule '抗生素特殊使用'..." */
    @AutoGenerated(locked = true, uuid = "0d04d96a-a748-4d0e-b795-19e1330d286e")
    private String drlContent;

    /** 有效结束时间 生效结束时间，可为空表示一直生效 */
    @AutoGenerated(locked = true, uuid = "dd39a2d4-f805-4f56-840c-bf2ed8a787a1")
    private Date effectiveEndTime;

    /** 生效开始时间 生效开始时间 */
    @AutoGenerated(locked = true, uuid = "d591348d-4f07-49e6-8a47-3fee3e36eb4a")
    private Date effectiveStartTime;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "faa34178-55fe-4e88-b9f3-35cd0573b073")
    private String id;

    /** 规则ID 规则ID，外键关联 rule 表 */
    @AutoGenerated(locked = true, uuid = "af1d290d-47b4-4baf-8738-d6f7d7b8cae4")
    private String ruleId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "ab326d2c-116a-4551-8614-16f690938e27")
    private Date updatedAt;

    /** 更新人 更新人ID，关联用户表 */
    @AutoGenerated(locked = true, uuid = "eaaaede8-8428-40d0-912e-192f09c2fa5f")
    private String updatedBy;

    /**
     * 版本号 版本号,如 "2.1.3"。 - HIS 系统中的规则版本需反映变化的性质（如新功能、政策调整），时间戳和自增ID缺乏可读性。 -
     * 语义化版本便于开发、运维和业务人员理解版本演进，支持版本回滚和兼容性判断。
     */
    @AutoGenerated(locked = true, uuid = "1e9c6752-9943-4ae3-b66c-3daf2a084d71")
    private String versionNumber;
}
