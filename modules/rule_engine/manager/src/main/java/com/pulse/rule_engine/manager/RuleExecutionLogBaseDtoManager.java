package com.pulse.rule_engine.manager;

import com.pulse.rule_engine.manager.dto.RuleExecutionLogBaseDto;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.vs.code.AutoGenerated;

import java.util.Date;
import java.util.List;

@AutoGenerated(locked = false, uuid = "d445df6b-c480-4fa5-a451-5773966cb9b7|DTO|MANAGER")
public interface RuleExecutionLogBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "1f5fd7d9-2c9b-310c-90fa-f85e3e4610f0")
    RuleExecutionLogBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "2650429e-96db-3dd0-8649-02ba8bb8ff51")
    List<RuleExecutionLogBaseDto> getByBusinessTypesAndBusinessIds(
            List<RuleExecutionLog.BusinessIdAndBusinessType> var);

    @AutoGenerated(locked = true, uuid = "67e9acc6-4484-3bc4-bfdb-a32ff6a594e0")
    List<RuleExecutionLogBaseDto> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "828e89bc-79af-3eb2-8c0e-0b2f3d27b9a4")
    List<RuleExecutionLogBaseDto> getByBusinessTypeAndBusinessId(
            RuleExecutionLog.BusinessIdAndBusinessType var);

    @AutoGenerated(locked = true, uuid = "95d42548-0cc4-36f3-86c9-8e3ed24186d5")
    List<RuleExecutionLogBaseDto> getByExecutionTime(Date executionTime);

    @AutoGenerated(locked = true, uuid = "b9fb2f05-6ebe-37eb-92d9-13cd903ac3ed")
    List<RuleExecutionLogBaseDto> getByRuleIds(List<String> ruleId);

    @AutoGenerated(locked = true, uuid = "c2c291bd-e75d-3255-b6f2-bb3526077633")
    List<RuleExecutionLogBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "f303c3bd-2a4f-355d-a483-6451be16dfc4")
    List<RuleExecutionLogBaseDto> getByExecutionTimes(List<Date> executionTime);
}
