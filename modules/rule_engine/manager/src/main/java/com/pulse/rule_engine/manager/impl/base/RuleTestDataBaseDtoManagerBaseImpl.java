package com.pulse.rule_engine.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleTestDataBaseDtoManager;
import com.pulse.rule_engine.manager.converter.RuleTestDataBaseDtoConverter;
import com.pulse.rule_engine.manager.dto.RuleTestDataBaseDto;
import com.pulse.rule_engine.persist.dos.RuleTestData;
import com.pulse.rule_engine.persist.mapper.RuleTestDataDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "922b047d-f47b-452e-935c-00c47ebd8cf7|DTO|BASE_MANAGER_IMPL")
public abstract class RuleTestDataBaseDtoManagerBaseImpl implements RuleTestDataBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleTestDataBaseDtoConverter ruleTestDataBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleTestDataDao ruleTestDataDao;

    @AutoGenerated(locked = true, uuid = "04c7cd3b-4244-3c21-9aba-8ff7fbe7779a")
    @Override
    public List<RuleTestDataBaseDto> getByRuleVersionId(String ruleVersionId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleTestDataBaseDto> ruleTestDataBaseDtoList =
                getByRuleVersionIds(Arrays.asList(ruleVersionId));
        return ruleTestDataBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8a498d0f-7c49-323e-b11e-ba6cf636ef36")
    public List<RuleTestDataBaseDto> doConvertFromRuleTestDataToRuleTestDataBaseDto(
            List<RuleTestData> ruleTestDataList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleTestDataList)) {
            return Collections.emptyList();
        }

        Map<String, RuleTestDataBaseDto> dtoMap =
                ruleTestDataBaseDtoConverter
                        .convertFromRuleTestDataToRuleTestDataBaseDto(ruleTestDataList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        RuleTestDataBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<RuleTestDataBaseDto> ruleTestDataBaseDtoList = new ArrayList<>();
        for (RuleTestData i : ruleTestDataList) {
            RuleTestDataBaseDto ruleTestDataBaseDto = dtoMap.get(i.getId());
            if (ruleTestDataBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleTestDataBaseDtoList.add(ruleTestDataBaseDto);
        }
        return ruleTestDataBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "a6c0109c-90d8-31e8-b56e-0f0014cd433e")
    @Override
    public RuleTestDataBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleTestDataBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleTestDataBaseDto ruleTestDataBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleTestDataBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bf78e719-f9ca-3092-bb51-f4fc7d89f48a")
    @Override
    public List<RuleTestDataBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<RuleTestData> ruleTestDataList = ruleTestDataDao.getByIds(id);
        if (CollectionUtil.isEmpty(ruleTestDataList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, RuleTestData> ruleTestDataMap =
                ruleTestDataList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        ruleTestDataList =
                id.stream()
                        .map(i -> ruleTestDataMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRuleTestDataToRuleTestDataBaseDto(ruleTestDataList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c1d0d423-69d5-3d1e-b0b8-d6164ed922aa")
    @Override
    public List<RuleTestDataBaseDto> getByRuleVersionIds(List<String> ruleVersionId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleVersionId)) {
            return Collections.emptyList();
        }

        List<RuleTestData> ruleTestDataList = ruleTestDataDao.getByRuleVersionIds(ruleVersionId);
        if (CollectionUtil.isEmpty(ruleTestDataList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleTestDataToRuleTestDataBaseDto(ruleTestDataList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
