package com.pulse.rule_engine.manager;

import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.vs.code.AutoGenerated;

import java.util.Date;
import java.util.List;

@AutoGenerated(locked = false, uuid = "0a60d032-acdf-4e6c-ad5a-2863ee2efa9f|DTO|MANAGER")
public interface RuleVersionBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "120c3579-dabb-3266-a82a-ee9c205a49d4")
    List<RuleVersionBaseDto> getByEffectiveStartTime(Date effectiveStartTime);

    @AutoGenerated(locked = true, uuid = "1c412835-ff1f-3241-852f-35931a510209")
    List<RuleVersionBaseDto> getByRuleId(String ruleId);

    @AutoGenerated(locked = true, uuid = "22804c5c-e275-3078-8129-28fac772539f")
    List<RuleVersionBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "2eb27db5-898e-3592-98e0-efb9f8518670")
    RuleVersionBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "69664df1-0940-35d3-b7db-f2c4bbbc6f88")
    List<RuleVersionBaseDto> getByEffectiveStartTimes(List<Date> effectiveStartTime);

    @AutoGenerated(locked = true, uuid = "74de1c28-a616-316f-8820-3208336ff2d5")
    RuleVersionBaseDto getByRuleIdAndVersionNumber(RuleVersion.RuleIdAndVersionNumber var);

    @AutoGenerated(locked = true, uuid = "8e7c16ac-18c4-3b6f-9828-79c432101925")
    List<RuleVersionBaseDto> getByEffectiveEndTimes(List<Date> effectiveEndTime);

    @AutoGenerated(locked = true, uuid = "a0de195e-7a60-3fcc-8d11-ec538bf0daed")
    List<RuleVersionBaseDto> getByRuleIdsAndVersionNumbers(
            List<RuleVersion.RuleIdAndVersionNumber> var);

    @AutoGenerated(locked = true, uuid = "b3f2935b-1949-3bb5-aa43-110954171c84")
    List<RuleVersionBaseDto> getByRuleIds(List<String> ruleId);

    @AutoGenerated(locked = true, uuid = "b6f6a873-6079-3031-9c8a-512718f1dbc1")
    List<RuleVersionBaseDto> getByEffectiveEndTime(Date effectiveEndTime);
}
