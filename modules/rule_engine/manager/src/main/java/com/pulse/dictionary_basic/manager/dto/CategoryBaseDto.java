package com.pulse.dictionary_basic.manager.dto;

import com.pulse.dictionary_basic.common.enums.CategoryTypeEnum;
import com.pulse.dictionary_basic.common.enums.SourceCategoryTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "b514e7fa-90da-48bb-b12a-90ad357747a9|DTO|DEFINITION")
public class CategoryBaseDto {
    /** 编码 */
    @AutoGenerated(locked = true, uuid = "e614512f-ea4e-45bb-a1ea-edfff933bebb")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "476ac6c2-a90c-426b-97df-d37411fd5090")
    private Date createdAt;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "67a91db7-0dd9-47a5-8f1b-0fc199c2be51")
    private Long deletedAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "75c07632-bbd4-452f-a064-d79d0251c761")
    private String description;

    /** 禁用原因 */
    @AutoGenerated(locked = true, uuid = "bc52a01a-c5b6-4bf5-ad8b-c0413f6f2c54")
    private String disabledReason;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "911b5fa8-63cf-461d-9268-10da4f0892e9")
    private Boolean enableFlag;

    /** 扩展信息 */
    @AutoGenerated(locked = true, uuid = "0c219609-00d2-4dfc-8f9c-abde1d9a6e8c")
    private String extensionInfo;

    /** 末级标志 */
    @AutoGenerated(locked = true, uuid = "7f509240-1123-4bf3-9467-af34d71fcb0f")
    private Boolean finalFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "24c4ac35-8879-4f6b-a3f8-b2c0929e0987")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "281216b0-5c55-46e5-b42f-dbd4b2265638")
    private InputCodeEo inputCode;

    /** 机构ID */
    @AutoGenerated(locked = true, uuid = "a2372fdd-a84c-4230-b924-18cb1fb2f798")
    private String institutionId;

    /** 层级计数 */
    @AutoGenerated(locked = true, uuid = "0bc3583d-95f9-4dde-90ff-7fd0058715fb")
    private Long levelCount;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "be1d5757-382f-4eb5-99f5-6980e1bd02b0")
    private Long lockVersion;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "7bd0db50-a9c9-4072-9a3d-537a6835490e")
    private String name;

    /** 父ID */
    @AutoGenerated(locked = true, uuid = "03406e79-f493-4a27-93ae-1f6cbfbf6ec1")
    private String parentId;

    /** 根ID */
    @AutoGenerated(locked = true, uuid = "6580a074-7a62-4f59-9010-f316be9d04db")
    private String rootId;

    /** 场景 */
    @AutoGenerated(locked = true, uuid = "4925e360-54e2-40e8-984c-fbaa3f70cc8d")
    private String scene;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "c863821f-ecf6-485b-9b68-d7ef744e4c1f")
    private Long sortNumber;

    /** 来源类型 */
    @AutoGenerated(locked = true, uuid = "2e47d57a-96b4-4507-9e0e-939a40a2ef63")
    private SourceCategoryTypeEnum sourceType;

    /** 类型 */
    @AutoGenerated(locked = true, uuid = "5f18ceec-4c7a-4961-8846-5c65e126f7d5")
    private CategoryTypeEnum type;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "f8c89d00-6171-4186-bc17-6c7f64f588a0")
    private Date updatedAt;

    /** 使用范围 */
    @Valid
    @AutoGenerated(locked = true, uuid = "0ff0d73d-3ef4-4f7f-baae-8d0b41d1e567")
    private List<String> useScopeList;
}
