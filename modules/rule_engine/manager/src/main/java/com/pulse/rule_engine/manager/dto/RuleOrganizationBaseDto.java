package com.pulse.rule_engine.manager.dto;

import com.pulse.rule_engine.common.enums.RuleOrgRelationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "7cb08d46-2f4a-4fde-a309-f0514ed9a32f|DTO|DEFINITION")
public class RuleOrganizationBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "71a61d2e-555d-494d-ab19-b210b8ec20b0")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "27784535-28c8-427e-8ac6-4257d3c7ba60")
    private String id;

    /** 组织ID 组织ID */
    @AutoGenerated(locked = true, uuid = "85db8411-84a7-4f51-b340-2bacb49da989")
    private String organizationId;

    /** 关系类型 关系类型 */
    @AutoGenerated(locked = true, uuid = "b57da9ad-4f44-4f89-a047-8025f777daf8")
    private RuleOrgRelationTypeEnum relationType;

    /** 备注 说明具体关系或备注 */
    @AutoGenerated(locked = true, uuid = "e1b50e60-7bb1-42ea-a559-1d6adac75b12")
    private String remark;

    /** 规则ID 规则ID */
    @AutoGenerated(locked = true, uuid = "7cccf630-bf35-4888-b17b-4ab22a671577")
    private String ruleId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "e0d9652e-ee46-4df8-85b7-768eaf7a25de")
    private Date updatedAt;
}
