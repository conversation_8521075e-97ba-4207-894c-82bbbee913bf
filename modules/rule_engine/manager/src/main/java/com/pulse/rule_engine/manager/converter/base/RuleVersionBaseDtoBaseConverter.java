package com.pulse.rule_engine.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "0a60d032-acdf-4e6c-ad5a-2863ee2efa9f|DTO|BASE_CONVERTER")
public class RuleVersionBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public RuleVersionBaseDto convertFromRuleVersionToRuleVersionBaseDto(RuleVersion ruleVersion) {
        return convertFromRuleVersionToRuleVersionBaseDto(List.of(ruleVersion)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<RuleVersionBaseDto> convertFromRuleVersionToRuleVersionBaseDto(
            List<RuleVersion> ruleVersionList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleVersionList)) {
            return new ArrayList<>();
        }
        List<RuleVersionBaseDto> ruleVersionBaseDtoList = new ArrayList<>();
        for (RuleVersion ruleVersion : ruleVersionList) {
            if (ruleVersion == null) {
                continue;
            }
            RuleVersionBaseDto ruleVersionBaseDto = new RuleVersionBaseDto();
            ruleVersionBaseDto.setId(ruleVersion.getId());
            ruleVersionBaseDto.setRuleId(ruleVersion.getRuleId());
            ruleVersionBaseDto.setVersionNumber(ruleVersion.getVersionNumber());
            ruleVersionBaseDto.setDrlContent(ruleVersion.getDrlContent());
            ruleVersionBaseDto.setEffectiveStartTime(ruleVersion.getEffectiveStartTime());
            ruleVersionBaseDto.setEffectiveEndTime(ruleVersion.getEffectiveEndTime());
            ruleVersionBaseDto.setApproverId(ruleVersion.getApproverId());
            ruleVersionBaseDto.setCreatedBy(ruleVersion.getCreatedBy());
            ruleVersionBaseDto.setUpdatedBy(ruleVersion.getUpdatedBy());
            ruleVersionBaseDto.setCreatedAt(ruleVersion.getCreatedAt());
            ruleVersionBaseDto.setUpdatedAt(ruleVersion.getUpdatedAt());
            ruleVersionBaseDto.setDeletedAt(ruleVersion.getDeletedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleVersionBaseDtoList.add(ruleVersionBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return ruleVersionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
