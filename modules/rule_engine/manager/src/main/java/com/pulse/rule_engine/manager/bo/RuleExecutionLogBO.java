package com.pulse.rule_engine.manager.bo;

import com.pulse.rule_engine.manager.bo.base.BaseRuleExecutionLogBO;
import com.vs.code.AutoGenerated;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.Entity;
import javax.persistence.Table;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE rule_execution_log  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ? and lock_version = ?")
@Table(name = "rule_execution_log")
@Entity
@AutoGenerated(locked = false, uuid = "426e0e3d-2ff8-4ace-8aec-0ff4dffc5525|BO|DEFINITION")
public class RuleExecutionLogBO extends BaseRuleExecutionLogBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "426e0e3d-2ff8-4ace-8aec-0ff4dffc5525|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {}
}
