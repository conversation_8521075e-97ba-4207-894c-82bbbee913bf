package com.pulse.rule_engine.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.RuleVersionBaseDtoManager;
import com.pulse.rule_engine.manager.converter.RuleVersionBaseDtoConverter;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.persist.mapper.RuleVersionDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "0a60d032-acdf-4e6c-ad5a-2863ee2efa9f|DTO|BASE_MANAGER_IMPL")
public abstract class RuleVersionBaseDtoManagerBaseImpl implements RuleVersionBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private RuleVersionBaseDtoConverter ruleVersionBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private RuleVersionDao ruleVersionDao;

    @AutoGenerated(locked = true, uuid = "061d6920-e69c-34f0-92ca-9ffaf2b6e622")
    @Override
    public List<RuleVersionBaseDto> getByEffectiveStartTime(Date effectiveStartTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ruleVersionBaseDtoList =
                getByEffectiveStartTimes(Arrays.asList(effectiveStartTime));
        return ruleVersionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1f596b5b-219d-386a-b0d5-8885e58e2c0d")
    @Override
    public List<RuleVersionBaseDto> getByRuleIdsAndVersionNumbers(
            List<RuleVersion.RuleIdAndVersionNumber> var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(var)) {
            return Collections.emptyList();
        }

        List<RuleVersion> ruleVersionList = ruleVersionDao.getByRuleIdsAndVersionNumbers(var);
        if (CollectionUtil.isEmpty(ruleVersionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleVersionToRuleVersionBaseDto(ruleVersionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "39bf809c-fd77-3fef-b30e-a92bf2d451bb")
    @Override
    public List<RuleVersionBaseDto> getByEffectiveEndTime(Date effectiveEndTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ruleVersionBaseDtoList =
                getByEffectiveEndTimes(Arrays.asList(effectiveEndTime));
        return ruleVersionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4a4bba68-cd8a-3650-a540-7ce13013a5da")
    @Override
    public List<RuleVersionBaseDto> getByEffectiveEndTimes(List<Date> effectiveEndTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(effectiveEndTime)) {
            return Collections.emptyList();
        }

        List<RuleVersion> ruleVersionList = ruleVersionDao.getByEffectiveEndTimes(effectiveEndTime);
        if (CollectionUtil.isEmpty(ruleVersionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleVersionToRuleVersionBaseDto(ruleVersionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "60d06492-335a-3352-815f-2723075e1f33")
    @Override
    public List<RuleVersionBaseDto> getByRuleId(String ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ruleVersionBaseDtoList = getByRuleIds(Arrays.asList(ruleId));
        return ruleVersionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "74c178a4-9752-3fe0-8bac-fcd2a9ea19dd")
    @Override
    public List<RuleVersionBaseDto> getByRuleIds(List<String> ruleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleId)) {
            return Collections.emptyList();
        }

        List<RuleVersion> ruleVersionList = ruleVersionDao.getByRuleIds(ruleId);
        if (CollectionUtil.isEmpty(ruleVersionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleVersionToRuleVersionBaseDto(ruleVersionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9195d31f-53c8-3275-b297-2bd164fdfda1")
    @Override
    public List<RuleVersionBaseDto> getByEffectiveStartTimes(List<Date> effectiveStartTime) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(effectiveStartTime)) {
            return Collections.emptyList();
        }

        List<RuleVersion> ruleVersionList =
                ruleVersionDao.getByEffectiveStartTimes(effectiveStartTime);
        if (CollectionUtil.isEmpty(ruleVersionList)) {
            return Collections.emptyList();
        }

        return doConvertFromRuleVersionToRuleVersionBaseDto(ruleVersionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9bbc6479-9055-3a78-a91a-4392b5391035")
    @Override
    public RuleVersionBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleVersionBaseDto ruleVersionBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleVersionBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a57ce999-1447-3d41-accc-b25ae25fea84")
    @Override
    public List<RuleVersionBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<RuleVersion> ruleVersionList = ruleVersionDao.getByIds(id);
        if (CollectionUtil.isEmpty(ruleVersionList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, RuleVersion> ruleVersionMap =
                ruleVersionList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        ruleVersionList =
                id.stream()
                        .map(i -> ruleVersionMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromRuleVersionToRuleVersionBaseDto(ruleVersionList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d904175e-1d9c-3231-b344-9412caf0e096")
    public List<RuleVersionBaseDto> doConvertFromRuleVersionToRuleVersionBaseDto(
            List<RuleVersion> ruleVersionList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleVersionList)) {
            return Collections.emptyList();
        }

        Map<String, RuleVersionBaseDto> dtoMap =
                ruleVersionBaseDtoConverter
                        .convertFromRuleVersionToRuleVersionBaseDto(ruleVersionList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        RuleVersionBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<RuleVersionBaseDto> ruleVersionBaseDtoList = new ArrayList<>();
        for (RuleVersion i : ruleVersionList) {
            RuleVersionBaseDto ruleVersionBaseDto = dtoMap.get(i.getId());
            if (ruleVersionBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleVersionBaseDtoList.add(ruleVersionBaseDto);
        }
        return ruleVersionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "f2218c4d-bbd1-3238-bbf3-d8093a5392a2")
    @Override
    public RuleVersionBaseDto getByRuleIdAndVersionNumber(RuleVersion.RuleIdAndVersionNumber var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RuleVersionBaseDto> ret = getByRuleIdsAndVersionNumbers(Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        RuleVersionBaseDto ruleVersionBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return ruleVersionBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
