package com.pulse.rule_engine.manager.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.pulse.pulse.common.exception.ExceptionHandlerUtils;
import com.pulse.rule_engine.common.enums.RuleLogicOperatorEnum;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;
import com.pulse.rule_engine.manager.bo.base.BaseRuleGroupBO;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.hibernate.annotations.DynamicInsert;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Entity;
import javax.persistence.Table;

@Slf4j
@DynamicInsert
@Table(name = "rule_group")
@Entity
@AutoGenerated(locked = false, uuid = "cdb1ae8e-d981-44ae-9c3b-07c33a040d9f|BO|DEFINITION")
public class RuleGroupBO extends BaseRuleGroupBO {

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true, uuid = "cdb1ae8e-d981-44ae-9c3b-07c33a040d9f|BO|AGG|VALIDATOR")
    @Override
    protected void validateAggregate() {
        log.debug("开始执行规则组聚合校验，规则组ID: {}, 规则组编码: {}", getId(), getCode());

        // 校验规则组编码唯一性（仅在规则组编码不为空时校验）
        validateRuleGroupCodeUniqueness();

        // 校验规则组状态业务逻辑（仅在状态不为空时校验）
        validateRuleGroupStatus();

        // 校验逻辑运算符（仅在逻辑运算符不为空时校验）
        validateLogicOperator();

        // 校验规则组详情聚合（仅在规则组详情集合不为空时校验）
        validateRuleGroupDetailAggregate();

        log.debug("规则组聚合校验完成");
    }

    /**
     * 校验规则组编码唯一性
     *
     * <p>仅在规则组编码不为空时进行校验，确保规则组编码在系统中的唯一性 校验逻辑：排除当前对象本身，检查是否存在相同编码的其他规则组
     */
    private void validateRuleGroupCodeUniqueness() {
        if (StrUtil.isBlank(getCode())) {
            log.debug("规则组编码为空，跳过编码唯一性校验");
            return;
        }

        ExceptionHandlerUtils.executeWithExceptionHandling(
                "规则组编码唯一性校验",
                () -> {
                    // 根据编码查询规则组，排除当前对象本身
                    RuleGroupBO existingRuleGroup = RuleGroupBO.getByCode(getCode());
                    if (existingRuleGroup != null && !existingRuleGroup.getId().equals(getId())) {
                        String errorMessage = String.format("规则组编码【%s】已存在，请使用其他编码", getCode());
                        log.warn(
                                "规则组编码唯一性校验失败: 编码={}, 已存在规则组ID={}",
                                getCode(),
                                existingRuleGroup.getId());
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }
                    log.debug("规则组编码唯一性校验通过: {}", getCode());
                });
    }

    /**
     * 校验规则组状态业务逻辑
     *
     * <p>仅在状态不为空时进行校验，确保规则组状态变更符合业务规则
     */
    private void validateRuleGroupStatus() {
        if (getStatus() == null) {
            log.debug("规则组状态为空，跳过状态校验");
            return;
        }

        ExceptionHandlerUtils.executeWithExceptionHandling(
                "规则组状态业务逻辑校验",
                () -> {
                    // 判断是否为新增规则组
                    boolean isNewRuleGroup = isNewRuleGroup();

                    if (isNewRuleGroup) {
                        // 如果是新增规则组，状态只能是草稿
                        if (getStatus() != RuleStatusEnum.DRAFT) {
                            String errorMessage = "新增规则组的状态只能是草稿状态";
                            log.warn("新增规则组状态校验失败: 当前状态={}", getStatus());
                            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                        }
                        log.debug("新增规则组状态校验通过: {}", getStatus());
                        return;
                    }

                    // 对于更新规则组，需要校验状态变更的合理性
                    validateRuleGroupStatusTransition();
                    log.debug("规则组状态业务逻辑校验通过: {}", getStatus());
                });
    }

    /**
     * 校验逻辑运算符
     *
     * <p>仅在逻辑运算符不为空时进行校验，确保逻辑运算符的有效性
     */
    private void validateLogicOperator() {
        if (getLogicOperator() == null) {
            log.debug("逻辑运算符为空，跳过逻辑运算符校验");
            return;
        }

        ExceptionHandlerUtils.executeWithExceptionHandling(
                "逻辑运算符校验",
                () -> {
                    // 校验逻辑运算符是否为有效值
                    if (getLogicOperator() != RuleLogicOperatorEnum.AND
                            && getLogicOperator() != RuleLogicOperatorEnum.OR) {
                        String errorMessage = "逻辑运算符只能是AND或OR";
                        log.warn("逻辑运算符校验失败: 当前逻辑运算符={}", getLogicOperator());
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }
                    log.debug("逻辑运算符校验通过: {}", getLogicOperator());
                });
    }

    /**
     * 校验规则组详情聚合
     *
     * <p>仅在规则组详情集合不为空时进行校验，确保规则组详情聚合的合理性
     */
    private void validateRuleGroupDetailAggregate() {
        Set<RuleGroupDetailBO> ruleGroupDetailBOSet = getRuleGroupDetailBOSet();
        if (CollectionUtil.isEmpty(ruleGroupDetailBOSet)) {
            log.debug("规则组详情集合为空，跳过规则组详情聚合校验");
            return;
        }

        ExceptionHandlerUtils.executeWithExceptionHandling(
                "规则组详情聚合校验",
                () -> {
                    // 校验规则组详情数量限制
                    if (ruleGroupDetailBOSet.size() > 50) {
                        String errorMessage = "规则组关联的规则数量不能超过50个";
                        log.warn("规则组详情数量校验失败: 当前数量={}", ruleGroupDetailBOSet.size());
                        throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                    }

                    // 校验规则ID唯一性
                    validateRuleIdUniqueness(ruleGroupDetailBOSet);

                    log.debug("规则组详情聚合校验通过，规则数量: {}", ruleGroupDetailBOSet.size());
                });
    }

    /**
     * 校验规则组状态变更的合理性
     *
     * <p>根据当前状态和目标状态，校验状态变更是否符合业务规则
     */
    private void validateRuleGroupStatusTransition() {
        // 获取原始规则组对象
        RuleGroupBO originalRuleGroup = getOriginalRuleGroup();
        if (originalRuleGroup == null) {
            log.debug("未找到原始规则组记录，跳过状态变更校验");
            return;
        }

        RuleStatusEnum originalStatus = originalRuleGroup.getStatus();
        RuleStatusEnum currentStatus = getStatus();

        // 如果状态没有变更，跳过校验
        if (originalStatus == currentStatus) {
            log.debug("规则组状态未变更，跳过状态变更校验");
            return;
        }

        // 校验状态变更的合理性
        boolean isValidTransition = isValidStatusTransition(originalStatus, currentStatus);
        if (!isValidTransition) {
            String errorMessage =
                    String.format("规则组状态不能从【%s】变更为【%s】", originalStatus, currentStatus);
            log.warn("规则组状态变更校验失败: 原状态={}, 目标状态={}", originalStatus, currentStatus);
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
        }

        log.debug("规则组状态变更校验通过: {} -> {}", originalStatus, currentStatus);
    }

    /**
     * 校验规则ID唯一性
     *
     * @param ruleGroupDetailBOSet 规则组详情集合
     */
    private void validateRuleIdUniqueness(Set<RuleGroupDetailBO> ruleGroupDetailBOSet) {
        if (CollectionUtil.isEmpty(ruleGroupDetailBOSet)) {
            return;
        }

        Set<String> ruleIds = new HashSet<>();
        for (RuleGroupDetailBO ruleGroupDetailBO : ruleGroupDetailBOSet) {
            String ruleId = ruleGroupDetailBO.getRuleId();
            if (StrUtil.isNotBlank(ruleId)) {
                if (!ruleIds.add(ruleId)) {
                    String errorMessage = "规则组内规则ID重复: " + ruleId;
                    log.warn("规则ID唯一性校验失败: {}", errorMessage);
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER, errorMessage);
                }
            }
        }
        log.debug("规则ID唯一性校验通过，共校验{}个规则", ruleIds.size());
    }

    /**
     * 判断状态变更是否有效
     *
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @return true表示变更有效，false表示变更无效
     */
    private boolean isValidStatusTransition(RuleStatusEnum fromStatus, RuleStatusEnum toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        switch (fromStatus) {
            case DRAFT:
                // 草稿状态可以变更为测试、生效状态
                return toStatus == RuleStatusEnum.TEST || toStatus == RuleStatusEnum.EFFECTIVE;
            case TEST:
                // 测试状态可以变更为生效、已下线状态
                return toStatus == RuleStatusEnum.EFFECTIVE || toStatus == RuleStatusEnum.OFFLINE;
            case EFFECTIVE:
                // 生效状态只能变更为已下线状态
                return toStatus == RuleStatusEnum.OFFLINE;
            case OFFLINE:
                // 已下线状态不能变更为其他状态
                return false;
            default:
                return false;
        }
    }

    /**
     * 判断是否为新增规则组
     *
     * @return true表示新增，false表示更新
     */
    private boolean isNewRuleGroup() {
        // 通过查询数据库判断是否为新增实体
        if (StrUtil.isBlank(getId())) {
            return true;
        }
        RuleGroupBO existingRuleGroup = RuleGroupBO.getById(getId());
        return existingRuleGroup == null;
    }

    /**
     * 获取原始规则组对象
     *
     * @return 原始规则组对象，如果不存在则返回null
     */
    private RuleGroupBO getOriginalRuleGroup() {
        if (StrUtil.isBlank(getId())) {
            return null;
        }
        return RuleGroupBO.getById(getId());
    }
}
