package com.pulse.rule_engine.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.rule_engine.manager.dto.RuleOrganizationBaseDto;
import com.pulse.rule_engine.persist.dos.RuleOrganization;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "7cb08d46-2f4a-4fde-a309-f0514ed9a32f|DTO|BASE_CONVERTER")
public class RuleOrganizationBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public RuleOrganizationBaseDto convertFromRuleOrganizationToRuleOrganizationBaseDto(
            RuleOrganization ruleOrganization) {
        return convertFromRuleOrganizationToRuleOrganizationBaseDto(List.of(ruleOrganization))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<RuleOrganizationBaseDto> convertFromRuleOrganizationToRuleOrganizationBaseDto(
            List<RuleOrganization> ruleOrganizationList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(ruleOrganizationList)) {
            return new ArrayList<>();
        }
        List<RuleOrganizationBaseDto> ruleOrganizationBaseDtoList = new ArrayList<>();
        for (RuleOrganization ruleOrganization : ruleOrganizationList) {
            if (ruleOrganization == null) {
                continue;
            }
            RuleOrganizationBaseDto ruleOrganizationBaseDto = new RuleOrganizationBaseDto();
            ruleOrganizationBaseDto.setId(ruleOrganization.getId());
            ruleOrganizationBaseDto.setRuleId(ruleOrganization.getRuleId());
            ruleOrganizationBaseDto.setOrganizationId(ruleOrganization.getOrganizationId());
            ruleOrganizationBaseDto.setCreatedAt(ruleOrganization.getCreatedAt());
            ruleOrganizationBaseDto.setUpdatedAt(ruleOrganization.getUpdatedAt());
            ruleOrganizationBaseDto.setRelationType(ruleOrganization.getRelationType());
            ruleOrganizationBaseDto.setRemark(ruleOrganization.getRemark());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            ruleOrganizationBaseDtoList.add(ruleOrganizationBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return ruleOrganizationBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
