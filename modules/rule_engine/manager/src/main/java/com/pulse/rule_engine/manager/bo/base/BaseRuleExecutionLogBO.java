package com.pulse.rule_engine.manager.bo.base;

import com.pulse.rule_engine.common.enums.RuleExecutionStatusEnum;
import com.pulse.rule_engine.manager.bo.RuleExecutionLogBO;
import com.pulse.rule_engine.persist.dos.RuleExecutionLog;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "rule_execution_log")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "6f11200d-5338-3bd8-a660-03c8550172c1")
public abstract class BaseRuleExecutionLogBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 业务ID 业务对象 ID，如患者 ID、药品 ID */
    @Column(name = "business_id")
    @AutoGenerated(locked = true, uuid = "8c75dd8a-49ba-4cea-b319-e2c6ab4a512c")
    private String businessId;

    /** 业务类型 业务类型，如 "PATIENT"、"DRUG" */
    @Column(name = "business_type")
    @AutoGenerated(locked = true, uuid = "bead0e14-cdcb-4af6-9a4e-ff310549a293")
    private String businessType;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "17a031fb-a00b-5a1f-ba36-beb4f0fdf720")
    private Date createdAt;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "8f600aac-fddb-5dcc-8947-3d2f192ed136")
    private Long deletedAt = 0L;

    /** 执行人 */
    @Column(name = "executed_by")
    @AutoGenerated(locked = true, uuid = "859f300a-e507-4548-b601-498ee0a4c969")
    private String executedBy;

    /** 执行时长（毫秒数） 执行时长，单位毫秒 */
    @Column(name = "execution_duration")
    @AutoGenerated(locked = true, uuid = "78be37e4-1a73-4129-b359-ff04be3d9af1")
    private Long executionDuration;

    /** 执行状态 执行状态，如"SUCCESS"、"FAIL" */
    @Column(name = "execution_status")
    @AutoGenerated(locked = true, uuid = "a45498c3-df0a-45bf-9867-9d53538e7d65")
    @Enumerated(EnumType.STRING)
    private RuleExecutionStatusEnum executionStatus;

    /** 执行时间 执行时间 */
    @Column(name = "execution_time")
    @AutoGenerated(locked = true, uuid = "bb53f7bb-ee7e-4a9b-b661-13211199a0c9")
    private Date executionTime;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "2771265b-20f7-4c05-ace9-7eb0bcf8c171")
    @Id
    private String id;

    /** 输入数据 输入数据，JSON 格式，如 {"drugs": ["莫西沙星"]} */
    @Column(name = "input_data")
    @AutoGenerated(locked = true, uuid = "53669692-345b-40b0-81bf-c591c20e9488")
    private String inputData;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "bc332f3b-c976-4c38-a10f-9c8120ab4c15")
    @Version
    private Long lockVersion;

    /** 输出数据 输出数据，JSON 格式，如 {"needApproval": true} */
    @Column(name = "output_data")
    @AutoGenerated(locked = true, uuid = "c392aacb-1e43-47c7-a03a-87eaa651ac08")
    private String outputData;

    /** 规则编码 规则编码，便于业务识别 */
    @Column(name = "rule_code")
    @AutoGenerated(locked = true, uuid = "53b70100-043f-4b2e-9907-199a998d96aa")
    private String ruleCode;

    /** 规则ID 规则ID，外键关联规则表 */
    @Column(name = "rule_id")
    @AutoGenerated(locked = true, uuid = "b93e5991-611f-4412-a780-8b5e8c1a0ddb")
    private String ruleId;

    /** 触发点 触发点，如 "PRESCRIPTION_SUBMIT" */
    @Column(name = "trigger_point")
    @AutoGenerated(locked = true, uuid = "a1b95bda-c315-4d84-9d51-39a849ff3acf")
    private String triggerPoint;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "634697b8-34fa-59d3-87f4-37be4ae54164")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public RuleExecutionLog convertToRuleExecutionLog() {
        RuleExecutionLog entity = new RuleExecutionLog();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "ruleId",
                "ruleCode",
                "businessId",
                "businessType",
                "triggerPoint",
                "inputData",
                "outputData",
                "executionDuration",
                "executionTime",
                "executionStatus",
                "executedBy",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public String getBusinessId() {
        return this.businessId;
    }

    @AutoGenerated(locked = true)
    public String getBusinessType() {
        return this.businessType;
    }

    @AutoGenerated(locked = true)
    public static RuleExecutionLogBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        RuleExecutionLogBO ruleExecutionLog =
                (RuleExecutionLogBO)
                        session.createQuery("from RuleExecutionLogBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return ruleExecutionLog;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getExecutedBy() {
        return this.executedBy;
    }

    @AutoGenerated(locked = true)
    public Long getExecutionDuration() {
        return this.executionDuration;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionStatusEnum getExecutionStatus() {
        return this.executionStatus;
    }

    @AutoGenerated(locked = true)
    public Date getExecutionTime() {
        return this.executionTime;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getInputData() {
        return this.inputData;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getOutputData() {
        return this.outputData;
    }

    @AutoGenerated(locked = true)
    public String getRuleCode() {
        return this.ruleCode;
    }

    @AutoGenerated(locked = true)
    public String getRuleId() {
        return this.ruleId;
    }

    @AutoGenerated(locked = true)
    public String getTriggerPoint() {
        return this.triggerPoint;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setBusinessId(String businessId) {
        this.businessId = businessId;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setBusinessType(String businessType) {
        this.businessType = businessType;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setExecutedBy(String executedBy) {
        this.executedBy = executedBy;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setExecutionDuration(Long executionDuration) {
        this.executionDuration = executionDuration;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setExecutionStatus(RuleExecutionStatusEnum executionStatus) {
        this.executionStatus = executionStatus;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setExecutionTime(Date executionTime) {
        this.executionTime = executionTime;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setId(String id) {
        this.id = id;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setInputData(String inputData) {
        this.inputData = inputData;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setOutputData(String outputData) {
        this.outputData = outputData;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setRuleId(String ruleId) {
        this.ruleId = ruleId;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setTriggerPoint(String triggerPoint) {
        this.triggerPoint = triggerPoint;
        return (RuleExecutionLogBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleExecutionLogBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleExecutionLogBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
