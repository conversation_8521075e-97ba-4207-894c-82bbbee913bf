package com.pulse.rule_engine.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "922b047d-f47b-452e-935c-00c47ebd8cf7|DTO|DEFINITION")
public class RuleTestDataBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "0685c52a-66ea-437d-b287-67c32ffb7511")
    private Date createdAt;

    /** 测试数据描述 测试数据描述 */
    @AutoGenerated(locked = true, uuid = "f23679fd-4b85-49ff-a753-331927b3e649")
    private String description;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "96c52f3e-5052-41ac-8847-9bafc3b29d1f")
    private String id;

    /** 规则版本ID 关联规则版本表ID */
    @AutoGenerated(locked = true, uuid = "cf7819b5-0990-4d0d-8f65-24e4781a31cc")
    private String ruleVersionId;

    /** 测试数据 测试数据集（JSON格式）,例如：{"bloodRoutine": {"WBC": 15}} */
    @AutoGenerated(locked = true, uuid = "07acfb22-a78e-4574-bbcf-d99092316718")
    private String testData;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "68754b09-93a9-4f75-ac02-822812b1c956")
    private Date updatedAt;
}
