package com.pulse.orders.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.orders.manager.dto.OrderOperationAppointBaseDto;
import com.pulse.orders.persist.dos.OrderOperationAppoint;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "1a11be04-d194-48be-af7c-2ee696fd0736|DTO|BASE_CONVERTER")
public class OrderOperationAppointBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public OrderOperationAppointBaseDto
            convertFromOrderOperationAppointToOrderOperationAppointBaseDto(
                    OrderOperationAppoint orderOperationAppoint) {
        return convertFromOrderOperationAppointToOrderOperationAppointBaseDto(
                        List.of(orderOperationAppoint))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OrderOperationAppointBaseDto>
            convertFromOrderOperationAppointToOrderOperationAppointBaseDto(
                    List<OrderOperationAppoint> orderOperationAppointList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderOperationAppointList)) {
            return new ArrayList<>();
        }
        List<OrderOperationAppointBaseDto> orderOperationAppointBaseDtoList = new ArrayList<>();
        for (OrderOperationAppoint orderOperationAppoint : orderOperationAppointList) {
            if (orderOperationAppoint == null) {
                continue;
            }
            OrderOperationAppointBaseDto orderOperationAppointBaseDto =
                    new OrderOperationAppointBaseDto();
            orderOperationAppointBaseDto.setId(orderOperationAppoint.getId());
            orderOperationAppointBaseDto.setOrderOperationId(
                    orderOperationAppoint.getOrderOperationId());
            orderOperationAppointBaseDto.setCreatedAt(orderOperationAppoint.getCreatedAt());
            orderOperationAppointBaseDto.setUpdatedAt(orderOperationAppoint.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            orderOperationAppointBaseDtoList.add(orderOperationAppointBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return orderOperationAppointBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
