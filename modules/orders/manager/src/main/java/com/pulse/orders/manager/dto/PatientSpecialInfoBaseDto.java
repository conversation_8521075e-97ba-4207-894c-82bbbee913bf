package com.pulse.orders.manager.dto;

import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@AutoGenerated(locked = false, uuid = "275af916-4674-4a9b-bf83-e2cbadbdd4ec|DTO|DEFINITION")
public class PatientSpecialInfoBaseDto {
    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "7ca5ab27-e7c7-41c4-a8ea-e806667d7dfc")
    private Date createdAt;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "a5725235-7f1e-4466-8a67-4b5d2b7b9438")
    private String id;

    /** 传染病分类ID */
    @AutoGenerated(locked = true, uuid = "ad5debba-beb1-419d-9c0a-63930e2d6151")
    private String infectClassId;

    /** 传染病结果 */
    @AutoGenerated(locked = true, uuid = "b5746703-daa3-4922-a902-06e76b889507")
    private String infectResult;

    /** 医嘱ID */
    @AutoGenerated(locked = true, uuid = "c7287512-ac93-4b18-93e2-996dc23ed59c")
    private String orderInfoId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "baef87b1-6d0f-4e41-b628-ce82f2b7c5e3")
    private Date updatedAt;
}
