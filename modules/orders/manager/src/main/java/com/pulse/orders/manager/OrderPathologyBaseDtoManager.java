package com.pulse.orders.manager;

import com.pulse.orders.manager.dto.OrderPathologyBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "f31bbdcb-ce57-4890-b7ae-1551f71c888a|DTO|MANAGER")
public interface OrderPathologyBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "43e757fd-d31a-37b4-be7a-c1bd0ebd719a")
    List<OrderPathologyBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "54e8f839-7e11-3de7-b987-0e6c9bc290eb")
    OrderPathologyBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "beec0204-ceae-3344-be03-50f429d81501")
    List<OrderPathologyBaseDto> getByOrderInfoIds(List<String> orderInfoId);

    @AutoGenerated(locked = true, uuid = "bfc60a08-2ef3-32ac-964c-b276de7adc45")
    OrderPathologyBaseDto getByOrderInfoId(String orderInfoId);
}
