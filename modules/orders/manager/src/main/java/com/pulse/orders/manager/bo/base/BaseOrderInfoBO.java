package com.pulse.orders.manager.bo.base;

import com.pulse.orders.common.enums.OrderClassEnum;
import com.pulse.orders.common.enums.OrderSourceTypeEnum;
import com.pulse.orders.manager.bo.OrderDisposalBO;
import com.pulse.orders.manager.bo.OrderDrugBO;
import com.pulse.orders.manager.bo.OrderExamBO;
import com.pulse.orders.manager.bo.OrderHerbBO;
import com.pulse.orders.manager.bo.OrderInfectInfoBO;
import com.pulse.orders.manager.bo.OrderInfoBO;
import com.pulse.orders.manager.bo.OrderLabBO;
import com.pulse.orders.manager.bo.OrderOperationBO;
import com.pulse.orders.manager.bo.OrderPathologyBO;
import com.pulse.orders.manager.bo.OrderTreatBO;
import com.pulse.orders.persist.dos.OrderInfo;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "order_info")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "9811d956-6c81-326f-89c3-7bd5a470b320")
public abstract class BaseOrderInfoBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 计费属性 */
    @Column(name = "billing_attribute")
    @AutoGenerated(locked = true, uuid = "330102b0-c90a-4f0d-8460-bdd5e9c262fc")
    private String billingAttribute;

    /** 计费标志 */
    @Column(name = "billing_flag")
    @AutoGenerated(locked = true, uuid = "dbae6283-757e-492d-9baf-41dd87880e5e")
    private Boolean billingFlag;

    /** 作废人ID */
    @Column(name = "cancel_by")
    @AutoGenerated(locked = true, uuid = "1d2d95b3-0568-4491-8079-db9a1e91e046")
    private String cancelBy;

    /** 作废日期 */
    @Column(name = "cancel_date")
    @AutoGenerated(locked = true, uuid = "f0255cde-f6e3-4cee-9599-aa602556ac4a")
    private Date cancelDate;

    /** 收费状态 */
    @Column(name = "charge_status")
    @AutoGenerated(locked = true, uuid = "0fbb28ac-3d2f-4326-91ce-23ae03a5da2e")
    private String chargeStatus;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "ed7d4929-91ea-5a2d-b885-c1f86f751ee7")
    private Date createdAt;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "7113fb4c-ca61-5e45-b406-fc3ef68e82ab")
    private Long deletedAt = 0L;

    /** 病种类型 */
    @Column(name = "disease_type")
    @AutoGenerated(locked = true, uuid = "0ff99a2e-bbfc-4f42-a89b-ad159093b8af")
    private String diseaseType;

    /** 医嘱显示ID */
    @Column(name = "display_id")
    @AutoGenerated(locked = true, uuid = "c3115de6-0694-44cd-ba8a-2884289491fd")
    private String displayId;

    /** 医生嘱托 */
    @Column(name = "doctor_instruction")
    @AutoGenerated(locked = true, uuid = "260737ba-deae-4f45-ad8c-4a5c5068a395")
    private String doctorInstruction;

    /** 医嘱下达时间 */
    @Column(name = "enter_date")
    @AutoGenerated(locked = true, uuid = "07acc92d-0e42-4c0e-a65f-6fbc2feece66")
    private Date enterDate;

    /** 急诊就诊ID */
    @Column(name = "erp_visit_id")
    @AutoGenerated(locked = true, uuid = "9ae0195c-99c1-4c3f-a4fa-fbcd09ac3e02")
    private String erpVisitId;

    /** 排斥类型 */
    @Column(name = "exclusion_type")
    @AutoGenerated(locked = true, uuid = "afe78fb1-77d7-4771-a8be-a8066af3635a")
    private String exclusionType;

    /** 强制自费类型 */
    @Column(name = "force_self_payment_flag")
    @AutoGenerated(locked = true, uuid = "655a8552-e3e3-496f-88e9-c07e71b4e1e8")
    private String forceSelfPaymentFlag;

    /** 临床试验项目ID */
    @Column(name = "gcp_id")
    @AutoGenerated(locked = true, uuid = "97e1bcc1-6691-4398-8b61-2df2e4ab29ed")
    private String gcpId;

    /** 医嘱组号 */
    @Column(name = "group_number")
    @AutoGenerated(locked = true, uuid = "75fe1248-7d11-4e3f-8d6c-787dc1e93f1f")
    private Long groupNumber;

    /** 医嘱组内顺序号 */
    @Column(name = "group_sort_number")
    @AutoGenerated(locked = true, uuid = "80453c31-0582-429b-b74c-dbc587775120")
    private Long groupSortNumber;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "d2cc1b68-25c7-4279-892d-73f453c826e5")
    @Id
    private String id;

    /** 住院就诊ID */
    @Column(name = "inp_visit_id")
    @AutoGenerated(locked = true, uuid = "48b7a094-1288-4f9a-aeee-f927a20970e1")
    private String inpVisitId;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "cea3c8e7-6ce2-463f-9d17-827e2e8363ee")
    @Version
    private Long lockVersion;

    /** 医嘱类别 */
    @Column(name = "order_class")
    @AutoGenerated(locked = true, uuid = "8d04a1ff-5cc0-4def-9995-b2e031ce92e3")
    @Enumerated(EnumType.STRING)
    private OrderClassEnum orderClass;

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderDisposalBO> orderDisposalBOSet = new HashSet<>();

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderDrugBO> orderDrugBOSet = new HashSet<>();

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderExamBO> orderExamBOSet = new HashSet<>();

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderHerbBO> orderHerbBOSet = new HashSet<>();

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderInfectInfoBO> orderInfectInfoBOSet = new HashSet<>();

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderLabBO> orderLabBOSet = new HashSet<>();

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderOperationBO> orderOperationBOSet = new HashSet<>();

    @JoinColumn(name = "order_info_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderPathologyBO> orderPathologyBOSet = new HashSet<>();

    /** 医嘱单打印次数 */
    @Column(name = "order_print_count")
    @AutoGenerated(locked = true, uuid = "287eee3b-5d1c-4fbd-9194-ef5620b54d22")
    private Long orderPrintCount;

    /** 医嘱来源ID */
    @Column(name = "order_source_id")
    @AutoGenerated(locked = true, uuid = "6bccc839-8848-44ce-b07e-16ab09f600f5")
    private String orderSourceId;

    /** 医嘱来源类型 */
    @Column(name = "order_source_type")
    @AutoGenerated(locked = true, uuid = "6867a087-2365-4a62-815e-54889fc34133")
    @Enumerated(EnumType.STRING)
    private OrderSourceTypeEnum orderSourceType;

    /** 医嘱状态 */
    @Column(name = "order_status")
    @AutoGenerated(locked = true, uuid = "706c3e33-b518-4b39-b638-81e4910cf4fd")
    private String orderStatus;

    /** 医嘱名称 */
    @Column(name = "order_text")
    @AutoGenerated(locked = true, uuid = "08cc417a-5cda-4cd3-a667-2852454ff9ab")
    private String orderText;

    @JoinColumn(name = "order_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<OrderTreatBO> orderTreatBOSet = new HashSet<>();

    /** 开立医嘱应用ID */
    @Column(name = "ordering_application_id")
    @AutoGenerated(locked = true, uuid = "c6e92055-a8b7-4575-b602-2a543fe7f4cc")
    private String orderingApplicationId;

    /** 开单医生ID */
    @Column(name = "ordering_by")
    @AutoGenerated(locked = true, uuid = "193be4fa-3e3a-4a48-9d5a-da7c53b42fd0")
    private String orderingBy;

    /** 开立医嘱院区ID */
    @Column(name = "ordering_campus_id")
    @AutoGenerated(locked = true, uuid = "91a55c77-18a5-4927-a253-1e037c926fcb")
    private String orderingCampusId;

    /** 开单科室ID */
    @Column(name = "ordering_department_id")
    @AutoGenerated(locked = true, uuid = "5dc67cf9-72da-4fb3-9f8b-b05f778ff406")
    private String orderingDepartmentId;

    /** 开单医疗组ID */
    @Column(name = "ordering_medical_group_id")
    @AutoGenerated(locked = true, uuid = "d828b880-3674-4145-baf4-a518feb716b4")
    private String orderingMedicalGroupId;

    /** 门诊接诊记录ID */
    @Column(name = "outp_visit_encounter_id")
    @AutoGenerated(locked = true, uuid = "3866cb57-728c-4c01-a415-7d23562caa9c")
    private String outpVisitEncounterId;

    /** 门诊就诊ID */
    @Column(name = "outp_visit_id")
    @AutoGenerated(locked = true, uuid = "340afca3-b4f5-4d87-bc7f-2bccf31fd070")
    private String outpVisitId;

    /** 父医嘱ID */
    @Column(name = "parent_order_id")
    @AutoGenerated(locked = true, uuid = "aef32687-628b-451e-96a1-c46878e5c7e3")
    private String parentOrderId;

    /** 病人所在床位ID */
    @Column(name = "patient_bed_id")
    @AutoGenerated(locked = true, uuid = "c2f5b6a1-8991-4e2a-a3f4-990fad9f1115")
    private String patientBedId;

    /** 病人所在床号 */
    @Column(name = "patient_bed_number")
    @AutoGenerated(locked = true, uuid = "05fb939f-0a9a-42ea-a812-66f4b8da5056")
    private String patientBedNumber;

    /** 病人所在科室ID */
    @Column(name = "patient_department_id")
    @AutoGenerated(locked = true, uuid = "5966af93-a315-48c5-a59e-274f9b9fb091")
    private String patientDepartmentId;

    /** 患者ID */
    @Column(name = "patient_id")
    @AutoGenerated(locked = true, uuid = "d2095c69-06a0-47f8-b858-18f5bdf6a2e8")
    private String patientId;

    /** 病人所在病区ID */
    @Column(name = "patient_ward_id")
    @AutoGenerated(locked = true, uuid = "9ad31e78-db48-4c9b-a9af-************")
    private String patientWardId;

    /** 执行科室ID */
    @Column(name = "perform_department_id")
    @AutoGenerated(locked = true, uuid = "85210c60-4ec5-4c11-9154-08af90f0a4e9")
    private String performDepartmentId;

    /** 执行单打印时间 */
    @Column(name = "perform_print_date")
    @AutoGenerated(locked = true, uuid = "e6b6282a-9fc7-4a21-ab98-c7c0d99ca381")
    private Date performPrintDate;

    /** 院前医嘱标记 */
    @Column(name = "pre_hospital_order_flag")
    @AutoGenerated(locked = true, uuid = "687893f7-cca3-4574-833a-ea60f403fd87")
    private Boolean preHospitalOrderFlag;

    /** 处方ID */
    @Column(name = "prescription_id")
    @AutoGenerated(locked = true, uuid = "f2490030-28ca-4a34-8897-efb2baf899bd")
    private String prescriptionId;

    /** 医嘱校对人ID */
    @Column(name = "proofread_by")
    @AutoGenerated(locked = true, uuid = "a50bd1d9-7d10-43b2-a473-e9bfb039c5e7")
    private String proofreadBy;

    /** 医嘱校对时间 */
    @Column(name = "proofread_date")
    @AutoGenerated(locked = true, uuid = "291c095d-1151-495c-bdc3-54a3ed788f2c")
    private Date proofreadDate;

    /** 长期医嘱标志 */
    @Column(name = "repeat_order_flag")
    @AutoGenerated(locked = true, uuid = "470bd910-539d-4d56-a55f-d280dea94752")
    private Boolean repeatOrderFlag;

    /** 医嘱复核人ID */
    @Column(name = "review_by")
    @AutoGenerated(locked = true, uuid = "e9f73fe9-e0c7-48f3-9cf0-0a2d25934505")
    private String reviewBy;

    /** 医嘱复核时间 */
    @Column(name = "review_date")
    @AutoGenerated(locked = true, uuid = "3d24652d-a3d4-4aca-ad31-dc3967635fdf")
    private Date reviewDate;

    /** 撤销人ID */
    @Column(name = "revoke_by")
    @AutoGenerated(locked = true, uuid = "701a64b1-d79a-4e08-a748-1ed84777161c")
    private String revokeBy;

    /** 撤销日期 */
    @Column(name = "revoke_date")
    @AutoGenerated(locked = true, uuid = "8c0a0f2e-cf60-4a8d-899c-d3f3d074c8e1")
    private Date revokeDate;

    /** 撤销原因 */
    @Column(name = "revoke_reason")
    @AutoGenerated(locked = true, uuid = "87f0f0de-d391-4562-97f2-7e3752f91ae4")
    private String revokeReason;

    /** 撤销类型 */
    @Column(name = "revoke_type")
    @AutoGenerated(locked = true, uuid = "7bd9c8e1-2138-42e7-95a7-c0098b27d1ff")
    private String revokeType;

    /** 排序号 */
    @Column(name = "sort_number")
    @AutoGenerated(locked = true, uuid = "ae0e6f88-3877-4185-a517-a72732f5b284")
    private Long sortNumber;

    /** 医嘱开始时间 */
    @Column(name = "start_date")
    @AutoGenerated(locked = true, uuid = "f74315e8-cb34-44f9-81e5-6ecda4941180")
    private Date startDate;

    /** 停嘱医生ID */
    @Column(name = "stop_by")
    @AutoGenerated(locked = true, uuid = "8ed90462-9cea-4bd7-93ad-3eaa44749e9c")
    private String stopBy;

    /** 医嘱停嘱时间 */
    @Column(name = "stop_date")
    @AutoGenerated(locked = true, uuid = "47f9dbee-f558-4903-9f0f-f179da231eaa")
    private Date stopDate;

    /** 医嘱提交时间 */
    @Column(name = "submit_date")
    @AutoGenerated(locked = true, uuid = "9c2e796f-e39e-49f2-bec0-b7af8416fa0c")
    private Date submitDate;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "2c13a48f-e5b1-5512-b219-28fc3469ebbb")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public OrderInfo convertToOrderInfo() {
        OrderInfo entity = new OrderInfo();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "outpVisitId",
                "outpVisitEncounterId",
                "prescriptionId",
                "erpVisitId",
                "inpVisitId",
                "orderingCampusId",
                "orderingApplicationId",
                "patientId",
                "patientDepartmentId",
                "patientWardId",
                "patientBedId",
                "patientBedNumber",
                "displayId",
                "sortNumber",
                "parentOrderId",
                "groupNumber",
                "groupSortNumber",
                "orderStatus",
                "preHospitalOrderFlag",
                "repeatOrderFlag",
                "orderClass",
                "exclusionType",
                "orderText",
                "doctorInstruction",
                "orderingDepartmentId",
                "orderingBy",
                "orderingMedicalGroupId",
                "enterDate",
                "submitDate",
                "startDate",
                "stopDate",
                "stopBy",
                "proofreadBy",
                "proofreadDate",
                "reviewBy",
                "reviewDate",
                "cancelDate",
                "cancelBy",
                "revokeDate",
                "revokeBy",
                "revokeType",
                "revokeReason",
                "performDepartmentId",
                "billingFlag",
                "billingAttribute",
                "forceSelfPaymentFlag",
                "chargeStatus",
                "diseaseType",
                "gcpId",
                "orderSourceType",
                "orderSourceId",
                "performPrintDate",
                "orderPrintCount",
                "lockVersion",
                "createdAt",
                "updatedAt",
                "deletedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public String getBillingAttribute() {
        return this.billingAttribute;
    }

    @AutoGenerated(locked = true)
    public Boolean getBillingFlag() {
        return this.billingFlag;
    }

    @AutoGenerated(locked = true)
    public static OrderInfoBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        OrderInfoBO orderInfo =
                (OrderInfoBO)
                        session.createQuery("from OrderInfoBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return orderInfo;
    }

    @AutoGenerated(locked = true)
    public String getCancelBy() {
        return this.cancelBy;
    }

    @AutoGenerated(locked = true)
    public Date getCancelDate() {
        return this.cancelDate;
    }

    @AutoGenerated(locked = true)
    public String getChargeStatus() {
        return this.chargeStatus;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getDiseaseType() {
        return this.diseaseType;
    }

    @AutoGenerated(locked = true)
    public String getDisplayId() {
        return this.displayId;
    }

    @AutoGenerated(locked = true)
    public String getDoctorInstruction() {
        return this.doctorInstruction;
    }

    @AutoGenerated(locked = true)
    public Date getEnterDate() {
        return this.enterDate;
    }

    @AutoGenerated(locked = true)
    public String getErpVisitId() {
        return this.erpVisitId;
    }

    @AutoGenerated(locked = true)
    public String getExclusionType() {
        return this.exclusionType;
    }

    @AutoGenerated(locked = true)
    public String getForceSelfPaymentFlag() {
        return this.forceSelfPaymentFlag;
    }

    @AutoGenerated(locked = true)
    public String getGcpId() {
        return this.gcpId;
    }

    @AutoGenerated(locked = true)
    public Long getGroupNumber() {
        return this.groupNumber;
    }

    @AutoGenerated(locked = true)
    public Long getGroupSortNumber() {
        return this.groupSortNumber;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public String getInpVisitId() {
        return this.inpVisitId;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public OrderDisposalBO getOrCreateOrderDisposalBO() {
        if (this.getOrderDisposalBO() == null) {
            OrderDisposalBO obj = new OrderDisposalBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderDisposalBO(obj);
            return obj;
        } else {
            return this.getOrderDisposalBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderDrugBO getOrCreateOrderDrugBO() {
        if (this.getOrderDrugBO() == null) {
            OrderDrugBO obj = new OrderDrugBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderDrugBO(obj);
            return obj;
        } else {
            return this.getOrderDrugBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderExamBO getOrCreateOrderExamBO() {
        if (this.getOrderExamBO() == null) {
            OrderExamBO obj = new OrderExamBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderExamBO(obj);
            return obj;
        } else {
            return this.getOrderExamBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderHerbBO getOrCreateOrderHerbBO() {
        if (this.getOrderHerbBO() == null) {
            OrderHerbBO obj = new OrderHerbBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderHerbBO(obj);
            return obj;
        } else {
            return this.getOrderHerbBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderLabBO getOrCreateOrderLabBO() {
        if (this.getOrderLabBO() == null) {
            OrderLabBO obj = new OrderLabBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderLabBO(obj);
            return obj;
        } else {
            return this.getOrderLabBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderOperationBO getOrCreateOrderOperationBO() {
        if (this.getOrderOperationBO() == null) {
            OrderOperationBO obj = new OrderOperationBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderOperationBO(obj);
            return obj;
        } else {
            return this.getOrderOperationBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderPathologyBO getOrCreateOrderPathologyBO() {
        if (this.getOrderPathologyBO() == null) {
            OrderPathologyBO obj = new OrderPathologyBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderPathologyBO(obj);
            return obj;
        } else {
            return this.getOrderPathologyBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderTreatBO getOrCreateOrderTreatBO() {
        if (this.getOrderTreatBO() == null) {
            OrderTreatBO obj = new OrderTreatBO();
            obj.setOrderInfoBO((OrderInfoBO) this);
            setOrderTreatBO(obj);
            return obj;
        } else {
            return this.getOrderTreatBO();
        }
    }

    @AutoGenerated(locked = true)
    public OrderClassEnum getOrderClass() {
        return this.orderClass;
    }

    @AutoGenerated(locked = true)
    public OrderDisposalBO getOrderDisposalBO() {
        return this.orderDisposalBOSet.isEmpty()
                ? null
                : this.orderDisposalBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderDisposalBO> getOrderDisposalBOSet() {
        return this.orderDisposalBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderDrugBO getOrderDrugBO() {
        return this.orderDrugBOSet.isEmpty() ? null : this.orderDrugBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderDrugBO> getOrderDrugBOSet() {
        return this.orderDrugBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderExamBO getOrderExamBO() {
        return this.orderExamBOSet.isEmpty() ? null : this.orderExamBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderExamBO> getOrderExamBOSet() {
        return this.orderExamBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderHerbBO getOrderHerbBO() {
        return this.orderHerbBOSet.isEmpty() ? null : this.orderHerbBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderHerbBO> getOrderHerbBOSet() {
        return this.orderHerbBOSet;
    }

    @AutoGenerated(locked = true)
    public Set<OrderInfectInfoBO> getOrderInfectInfoBOSet() {
        return this.orderInfectInfoBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO getOrderLabBO() {
        return this.orderLabBOSet.isEmpty() ? null : this.orderLabBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderLabBO> getOrderLabBOSet() {
        return this.orderLabBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderOperationBO getOrderOperationBO() {
        return this.orderOperationBOSet.isEmpty()
                ? null
                : this.orderOperationBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderOperationBO> getOrderOperationBOSet() {
        return this.orderOperationBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderPathologyBO getOrderPathologyBO() {
        return this.orderPathologyBOSet.isEmpty()
                ? null
                : this.orderPathologyBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderPathologyBO> getOrderPathologyBOSet() {
        return this.orderPathologyBOSet;
    }

    @AutoGenerated(locked = true)
    public Long getOrderPrintCount() {
        return this.orderPrintCount;
    }

    @AutoGenerated(locked = true)
    public String getOrderSourceId() {
        return this.orderSourceId;
    }

    @AutoGenerated(locked = true)
    public OrderSourceTypeEnum getOrderSourceType() {
        return this.orderSourceType;
    }

    @AutoGenerated(locked = true)
    public String getOrderStatus() {
        return this.orderStatus;
    }

    @AutoGenerated(locked = true)
    public String getOrderText() {
        return this.orderText;
    }

    @AutoGenerated(locked = true)
    public OrderTreatBO getOrderTreatBO() {
        return this.orderTreatBOSet.isEmpty()
                ? null
                : this.orderTreatBOSet.stream().findAny().get();
    }

    @AutoGenerated(locked = true)
    private Set<OrderTreatBO> getOrderTreatBOSet() {
        return this.orderTreatBOSet;
    }

    @AutoGenerated(locked = true)
    public String getOrderingApplicationId() {
        return this.orderingApplicationId;
    }

    @AutoGenerated(locked = true)
    public String getOrderingBy() {
        return this.orderingBy;
    }

    @AutoGenerated(locked = true)
    public String getOrderingCampusId() {
        return this.orderingCampusId;
    }

    @AutoGenerated(locked = true)
    public String getOrderingDepartmentId() {
        return this.orderingDepartmentId;
    }

    @AutoGenerated(locked = true)
    public String getOrderingMedicalGroupId() {
        return this.orderingMedicalGroupId;
    }

    @AutoGenerated(locked = true)
    public String getOutpVisitEncounterId() {
        return this.outpVisitEncounterId;
    }

    @AutoGenerated(locked = true)
    public String getOutpVisitId() {
        return this.outpVisitId;
    }

    @AutoGenerated(locked = true)
    public String getParentOrderId() {
        return this.parentOrderId;
    }

    @AutoGenerated(locked = true)
    public String getPatientBedId() {
        return this.patientBedId;
    }

    @AutoGenerated(locked = true)
    public String getPatientBedNumber() {
        return this.patientBedNumber;
    }

    @AutoGenerated(locked = true)
    public String getPatientDepartmentId() {
        return this.patientDepartmentId;
    }

    @AutoGenerated(locked = true)
    public String getPatientId() {
        return this.patientId;
    }

    @AutoGenerated(locked = true)
    public String getPatientWardId() {
        return this.patientWardId;
    }

    @AutoGenerated(locked = true)
    public String getPerformDepartmentId() {
        return this.performDepartmentId;
    }

    @AutoGenerated(locked = true)
    public Date getPerformPrintDate() {
        return this.performPrintDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getPreHospitalOrderFlag() {
        return this.preHospitalOrderFlag;
    }

    @AutoGenerated(locked = true)
    public String getPrescriptionId() {
        return this.prescriptionId;
    }

    @AutoGenerated(locked = true)
    public String getProofreadBy() {
        return this.proofreadBy;
    }

    @AutoGenerated(locked = true)
    public Date getProofreadDate() {
        return this.proofreadDate;
    }

    @AutoGenerated(locked = true)
    public Boolean getRepeatOrderFlag() {
        return this.repeatOrderFlag;
    }

    @AutoGenerated(locked = true)
    public String getReviewBy() {
        return this.reviewBy;
    }

    @AutoGenerated(locked = true)
    public Date getReviewDate() {
        return this.reviewDate;
    }

    @AutoGenerated(locked = true)
    public String getRevokeBy() {
        return this.revokeBy;
    }

    @AutoGenerated(locked = true)
    public Date getRevokeDate() {
        return this.revokeDate;
    }

    @AutoGenerated(locked = true)
    public String getRevokeReason() {
        return this.revokeReason;
    }

    @AutoGenerated(locked = true)
    public String getRevokeType() {
        return this.revokeType;
    }

    @AutoGenerated(locked = true)
    public Long getSortNumber() {
        return this.sortNumber;
    }

    @AutoGenerated(locked = true)
    public Date getStartDate() {
        return this.startDate;
    }

    @AutoGenerated(locked = true)
    public String getStopBy() {
        return this.stopBy;
    }

    @AutoGenerated(locked = true)
    public Date getStopDate() {
        return this.stopDate;
    }

    @AutoGenerated(locked = true)
    public Date getSubmitDate() {
        return this.submitDate;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setBillingAttribute(String billingAttribute) {
        this.billingAttribute = billingAttribute;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setBillingFlag(Boolean billingFlag) {
        this.billingFlag = billingFlag;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setCancelBy(String cancelBy) {
        this.cancelBy = cancelBy;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setChargeStatus(String chargeStatus) {
        this.chargeStatus = chargeStatus;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setDiseaseType(String diseaseType) {
        this.diseaseType = diseaseType;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setDisplayId(String displayId) {
        this.displayId = displayId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setDoctorInstruction(String doctorInstruction) {
        this.doctorInstruction = doctorInstruction;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setEnterDate(Date enterDate) {
        this.enterDate = enterDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setErpVisitId(String erpVisitId) {
        this.erpVisitId = erpVisitId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setExclusionType(String exclusionType) {
        this.exclusionType = exclusionType;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setForceSelfPaymentFlag(String forceSelfPaymentFlag) {
        this.forceSelfPaymentFlag = forceSelfPaymentFlag;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setGcpId(String gcpId) {
        this.gcpId = gcpId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setGroupNumber(Long groupNumber) {
        this.groupNumber = groupNumber;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setGroupSortNumber(Long groupSortNumber) {
        this.groupSortNumber = groupSortNumber;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setId(String id) {
        this.id = id;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setInpVisitId(String inpVisitId) {
        this.inpVisitId = inpVisitId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderClass(OrderClassEnum orderClass) {
        this.orderClass = orderClass;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderDisposalBO(OrderDisposalBO orderDisposalBO) {
        if (this.orderDisposalBOSet.size() > 0) {
            this.orderDisposalBOSet.clear();
        }
        if (orderDisposalBO != null) {
            this.orderDisposalBOSet.add(orderDisposalBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderDisposalBOSet(Set<OrderDisposalBO> orderDisposalBOSet) {
        this.orderDisposalBOSet = orderDisposalBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderDrugBO(OrderDrugBO orderDrugBO) {
        if (this.orderDrugBOSet.size() > 0) {
            this.orderDrugBOSet.clear();
        }
        if (orderDrugBO != null) {
            this.orderDrugBOSet.add(orderDrugBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderDrugBOSet(Set<OrderDrugBO> orderDrugBOSet) {
        this.orderDrugBOSet = orderDrugBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderExamBO(OrderExamBO orderExamBO) {
        if (this.orderExamBOSet.size() > 0) {
            this.orderExamBOSet.clear();
        }
        if (orderExamBO != null) {
            this.orderExamBOSet.add(orderExamBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderExamBOSet(Set<OrderExamBO> orderExamBOSet) {
        this.orderExamBOSet = orderExamBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderHerbBO(OrderHerbBO orderHerbBO) {
        if (this.orderHerbBOSet.size() > 0) {
            this.orderHerbBOSet.clear();
        }
        if (orderHerbBO != null) {
            this.orderHerbBOSet.add(orderHerbBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderHerbBOSet(Set<OrderHerbBO> orderHerbBOSet) {
        this.orderHerbBOSet = orderHerbBOSet;
    }

    @AutoGenerated(locked = true)
    private void setOrderInfectInfoBOSet(Set<OrderInfectInfoBO> orderInfectInfoBOSet) {
        this.orderInfectInfoBOSet = orderInfectInfoBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderLabBO(OrderLabBO orderLabBO) {
        if (this.orderLabBOSet.size() > 0) {
            this.orderLabBOSet.clear();
        }
        if (orderLabBO != null) {
            this.orderLabBOSet.add(orderLabBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderLabBOSet(Set<OrderLabBO> orderLabBOSet) {
        this.orderLabBOSet = orderLabBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderOperationBO(OrderOperationBO orderOperationBO) {
        if (this.orderOperationBOSet.size() > 0) {
            this.orderOperationBOSet.clear();
        }
        if (orderOperationBO != null) {
            this.orderOperationBOSet.add(orderOperationBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderOperationBOSet(Set<OrderOperationBO> orderOperationBOSet) {
        this.orderOperationBOSet = orderOperationBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderPathologyBO(OrderPathologyBO orderPathologyBO) {
        if (this.orderPathologyBOSet.size() > 0) {
            this.orderPathologyBOSet.clear();
        }
        if (orderPathologyBO != null) {
            this.orderPathologyBOSet.add(orderPathologyBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderPathologyBOSet(Set<OrderPathologyBO> orderPathologyBOSet) {
        this.orderPathologyBOSet = orderPathologyBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderPrintCount(Long orderPrintCount) {
        this.orderPrintCount = orderPrintCount;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderSourceId(String orderSourceId) {
        this.orderSourceId = orderSourceId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderSourceType(OrderSourceTypeEnum orderSourceType) {
        this.orderSourceType = orderSourceType;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderText(String orderText) {
        this.orderText = orderText;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderTreatBO(OrderTreatBO orderTreatBO) {
        if (this.orderTreatBOSet.size() > 0) {
            this.orderTreatBOSet.clear();
        }
        if (orderTreatBO != null) {
            this.orderTreatBOSet.add(orderTreatBO);
        }
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    private void setOrderTreatBOSet(Set<OrderTreatBO> orderTreatBOSet) {
        this.orderTreatBOSet = orderTreatBOSet;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderingApplicationId(String orderingApplicationId) {
        this.orderingApplicationId = orderingApplicationId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderingBy(String orderingBy) {
        this.orderingBy = orderingBy;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderingCampusId(String orderingCampusId) {
        this.orderingCampusId = orderingCampusId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderingDepartmentId(String orderingDepartmentId) {
        this.orderingDepartmentId = orderingDepartmentId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOrderingMedicalGroupId(String orderingMedicalGroupId) {
        this.orderingMedicalGroupId = orderingMedicalGroupId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOutpVisitEncounterId(String outpVisitEncounterId) {
        this.outpVisitEncounterId = outpVisitEncounterId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setOutpVisitId(String outpVisitId) {
        this.outpVisitId = outpVisitId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setParentOrderId(String parentOrderId) {
        this.parentOrderId = parentOrderId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPatientBedId(String patientBedId) {
        this.patientBedId = patientBedId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPatientBedNumber(String patientBedNumber) {
        this.patientBedNumber = patientBedNumber;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPatientDepartmentId(String patientDepartmentId) {
        this.patientDepartmentId = patientDepartmentId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPatientId(String patientId) {
        this.patientId = patientId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPatientWardId(String patientWardId) {
        this.patientWardId = patientWardId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPerformDepartmentId(String performDepartmentId) {
        this.performDepartmentId = performDepartmentId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPerformPrintDate(Date performPrintDate) {
        this.performPrintDate = performPrintDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPreHospitalOrderFlag(Boolean preHospitalOrderFlag) {
        this.preHospitalOrderFlag = preHospitalOrderFlag;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setPrescriptionId(String prescriptionId) {
        this.prescriptionId = prescriptionId;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setProofreadBy(String proofreadBy) {
        this.proofreadBy = proofreadBy;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setProofreadDate(Date proofreadDate) {
        this.proofreadDate = proofreadDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setRepeatOrderFlag(Boolean repeatOrderFlag) {
        this.repeatOrderFlag = repeatOrderFlag;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setReviewBy(String reviewBy) {
        this.reviewBy = reviewBy;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setReviewDate(Date reviewDate) {
        this.reviewDate = reviewDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setRevokeBy(String revokeBy) {
        this.revokeBy = revokeBy;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setRevokeDate(Date revokeDate) {
        this.revokeDate = revokeDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setRevokeType(String revokeType) {
        this.revokeType = revokeType;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setStartDate(Date startDate) {
        this.startDate = startDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setStopBy(String stopBy) {
        this.stopBy = stopBy;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setStopDate(Date stopDate) {
        this.stopDate = stopDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setSubmitDate(Date submitDate) {
        this.submitDate = submitDate;
        return (OrderInfoBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (OrderInfoBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
