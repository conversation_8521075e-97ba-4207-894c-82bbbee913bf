package com.pulse.orders.manager;

import com.pulse.orders.manager.dto.OrderLabBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "978cc744-6e68-46f5-b50a-48739875e2b9|DTO|MANAGER")
public interface OrderLabBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "2213eb80-800b-3c69-91e6-036bb61aa211")
    OrderLabBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "6f93a16c-b35f-3af1-9188-6461e7a5a60a")
    OrderLabBaseDto getByOrderInfoId(String orderInfoId);

    @AutoGenerated(locked = true, uuid = "7fd00a9e-cb29-3ae7-8be4-a893a623c812")
    List<OrderLabBaseDto> getByOrderInfoIds(List<String> orderInfoId);

    @AutoGenerated(locked = true, uuid = "9cab1719-a7d4-305c-b0f4-4a9028d0edbb")
    List<OrderLabBaseDto> getByIds(List<String> id);
}
