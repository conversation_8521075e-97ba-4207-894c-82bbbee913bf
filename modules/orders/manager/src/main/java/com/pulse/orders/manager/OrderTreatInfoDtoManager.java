package com.pulse.orders.manager;

import com.pulse.orders.manager.dto.OrderTreatInfoDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "0c4d1520-9cd3-499b-b7a0-c053b6636aed|DTO|MANAGER")
public interface OrderTreatInfoDtoManager {

    @AutoGenerated(locked = true, uuid = "7ab041c9-067a-3fae-a13f-156b45936ea3")
    OrderTreatInfoDto getById(String id);

    @AutoGenerated(locked = true, uuid = "a7561a82-2243-31c4-a014-5267f855f4f1")
    List<OrderTreatInfoDto> getByItemIds(List<String> itemId);

    @AutoGenerated(locked = true, uuid = "ac5a3f0e-cb30-30db-ab98-9d5770d99ef9")
    List<OrderTreatInfoDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "b7e30115-3696-389f-baf6-9b3d9fe46f67")
    OrderTreatInfoDto getByOrderId(String orderId);

    @AutoGenerated(locked = true, uuid = "bb97507f-f909-38d5-8737-235d127e59b5")
    List<OrderTreatInfoDto> getByItemId(String itemId);

    @AutoGenerated(locked = true, uuid = "e866898e-0a92-3a8b-9f2d-661223c39745")
    List<OrderTreatInfoDto> getByOrderIds(List<String> orderId);
}
