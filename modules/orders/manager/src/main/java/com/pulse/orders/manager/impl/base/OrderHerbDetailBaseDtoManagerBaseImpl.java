package com.pulse.orders.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.orders.manager.OrderHerbDetailBaseDtoManager;
import com.pulse.orders.manager.converter.OrderHerbDetailBaseDtoConverter;
import com.pulse.orders.manager.dto.OrderHerbDetailBaseDto;
import com.pulse.orders.persist.dos.OrderHerbDetail;
import com.pulse.orders.persist.mapper.OrderHerbDetailDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "faac288b-b7e2-491e-98c5-aa5104667b98|DTO|BASE_MANAGER_IMPL")
public abstract class OrderHerbDetailBaseDtoManagerBaseImpl
        implements OrderHerbDetailBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private OrderHerbDetailBaseDtoConverter orderHerbDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private OrderHerbDetailDao orderHerbDetailDao;

    @AutoGenerated(locked = true, uuid = "55ac7ea6-bbc1-3203-a623-01d53117b575")
    public List<OrderHerbDetailBaseDto> doConvertFromOrderHerbDetailToOrderHerbDetailBaseDto(
            List<OrderHerbDetail> orderHerbDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderHerbDetailList)) {
            return Collections.emptyList();
        }

        Map<String, OrderHerbDetailBaseDto> dtoMap =
                orderHerbDetailBaseDtoConverter
                        .convertFromOrderHerbDetailToOrderHerbDetailBaseDto(orderHerbDetailList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        OrderHerbDetailBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<OrderHerbDetailBaseDto> orderHerbDetailBaseDtoList = new ArrayList<>();
        for (OrderHerbDetail i : orderHerbDetailList) {
            OrderHerbDetailBaseDto orderHerbDetailBaseDto = dtoMap.get(i.getId());
            if (orderHerbDetailBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            orderHerbDetailBaseDtoList.add(orderHerbDetailBaseDto);
        }
        return orderHerbDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "acbf6301-443b-390a-a933-1e8f3d209519")
    @Override
    public List<OrderHerbDetailBaseDto> getByOrderHerbIds(List<String> orderHerbId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderHerbId)) {
            return Collections.emptyList();
        }

        List<OrderHerbDetail> orderHerbDetailList =
                orderHerbDetailDao.getByOrderHerbIds(orderHerbId);
        if (CollectionUtil.isEmpty(orderHerbDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromOrderHerbDetailToOrderHerbDetailBaseDto(orderHerbDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "cfe01355-c24a-3670-a371-45e65e511bb3")
    @Override
    public OrderHerbDetailBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrderHerbDetailBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        OrderHerbDetailBaseDto orderHerbDetailBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return orderHerbDetailBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ee4cfa83-7d25-3fe6-8c07-4b5865ef26f6")
    @Override
    public List<OrderHerbDetailBaseDto> getByOrderHerbId(String orderHerbId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrderHerbDetailBaseDto> orderHerbDetailBaseDtoList =
                getByOrderHerbIds(Arrays.asList(orderHerbId));
        return orderHerbDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "fead9060-1c1b-3298-87bc-c86c2dc9e824")
    @Override
    public List<OrderHerbDetailBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<OrderHerbDetail> orderHerbDetailList = orderHerbDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(orderHerbDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, OrderHerbDetail> orderHerbDetailMap =
                orderHerbDetailList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        orderHerbDetailList =
                id.stream()
                        .map(i -> orderHerbDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromOrderHerbDetailToOrderHerbDetailBaseDto(orderHerbDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
