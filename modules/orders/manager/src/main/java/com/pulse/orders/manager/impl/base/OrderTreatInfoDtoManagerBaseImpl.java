package com.pulse.orders.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.orders.manager.OrderTreatBaseDtoManager;
import com.pulse.orders.manager.OrderTreatInfoDtoManager;
import com.pulse.orders.manager.converter.OrderTreatBaseDtoConverter;
import com.pulse.orders.manager.converter.OrderTreatInfoDtoConverter;
import com.pulse.orders.manager.dto.OrderTreatBaseDto;
import com.pulse.orders.manager.dto.OrderTreatInfoDto;
import com.pulse.orders.persist.dos.OrderTreat;
import com.pulse.orders.persist.mapper.OrderTreatDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "0c4d1520-9cd3-499b-b7a0-c053b6636aed|DTO|BASE_MANAGER_IMPL")
public abstract class OrderTreatInfoDtoManagerBaseImpl implements OrderTreatInfoDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private OrderTreatBaseDtoConverter orderTreatBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private OrderTreatBaseDtoManager orderTreatBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OrderTreatDao orderTreatDao;

    @AutoGenerated(locked = true)
    @Autowired
    private OrderTreatInfoDtoConverter orderTreatInfoDtoConverter;

    @AutoGenerated(locked = true, uuid = "62173b65-7928-32d2-9e90-c5dfe6e30a5a")
    @Override
    public OrderTreatInfoDto getByOrderId(String orderId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrderTreatInfoDto> ret = getByOrderIds(Arrays.asList(orderId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        OrderTreatInfoDto orderTreatInfoDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return orderTreatInfoDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "90273f93-9372-3f3c-b045-dea316d9edfd")
    @Override
    public OrderTreatInfoDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrderTreatInfoDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        OrderTreatInfoDto orderTreatInfoDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return orderTreatInfoDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9cb01c30-aabc-3412-92ba-3c60ffe00685")
    @Override
    public List<OrderTreatInfoDto> getByItemIds(List<String> itemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(itemId)) {
            return Collections.emptyList();
        }

        List<OrderTreat> orderTreatList = orderTreatDao.getByItemIds(itemId);
        if (CollectionUtil.isEmpty(orderTreatList)) {
            return Collections.emptyList();
        }

        return doConvertFromOrderTreatToOrderTreatInfoDto(orderTreatList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d6a03e73-7d6e-3baf-a3bd-cbce03dbafd5")
    @Override
    public List<OrderTreatInfoDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<OrderTreat> orderTreatList = orderTreatDao.getByIds(id);
        if (CollectionUtil.isEmpty(orderTreatList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, OrderTreat> orderTreatMap =
                orderTreatList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        orderTreatList =
                id.stream()
                        .map(i -> orderTreatMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromOrderTreatToOrderTreatInfoDto(orderTreatList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "e4ec62d4-28bf-3a57-b9b0-d46869349ce3")
    public List<OrderTreatInfoDto> doConvertFromOrderTreatToOrderTreatInfoDto(
            List<OrderTreat> orderTreatList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderTreatList)) {
            return Collections.emptyList();
        }

        List<OrderTreatBaseDto> baseDtoList =
                orderTreatBaseDtoConverter.convertFromOrderTreatToOrderTreatBaseDto(orderTreatList);
        Map<String, OrderTreatInfoDto> dtoMap =
                orderTreatInfoDtoConverter
                        .convertFromOrderTreatBaseDtoToOrderTreatInfoDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        OrderTreatInfoDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<OrderTreatInfoDto> orderTreatInfoDtoList = new ArrayList<>();
        for (OrderTreat i : orderTreatList) {
            OrderTreatInfoDto orderTreatInfoDto = dtoMap.get(i.getId());
            if (orderTreatInfoDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            orderTreatInfoDtoList.add(orderTreatInfoDto);
        }
        return orderTreatInfoDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "e909299b-6668-3544-97c7-5ebc915d974d")
    @Override
    public List<OrderTreatInfoDto> getByOrderIds(List<String> orderId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderId)) {
            return Collections.emptyList();
        }

        List<OrderTreat> orderTreatList = orderTreatDao.getByOrderIds(orderId);
        if (CollectionUtil.isEmpty(orderTreatList)) {
            return Collections.emptyList();
        }

        return doConvertFromOrderTreatToOrderTreatInfoDto(orderTreatList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f6907db0-31af-3baa-bf0a-de37f37ebc03")
    @Override
    public List<OrderTreatInfoDto> getByItemId(String itemId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrderTreatInfoDto> orderTreatInfoDtoList = getByItemIds(Arrays.asList(itemId));
        return orderTreatInfoDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
