package com.pulse.orders.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.orders.manager.dto.OrderBaseDto;
import com.pulse.orders.persist.dos.OrderInfo;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "e67040b3-175b-4247-af9b-eda13cd78145|DTO|BASE_CONVERTER")
public class OrderBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public OrderBaseDto convertFromOrderInfoToOrderBaseDto(OrderInfo orderInfo) {
        return convertFromOrderInfoToOrderBaseDto(List.of(orderInfo)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OrderBaseDto> convertFromOrderInfoToOrderBaseDto(List<OrderInfo> orderInfoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderInfoList)) {
            return new ArrayList<>();
        }
        List<OrderBaseDto> orderBaseDtoList = new ArrayList<>();
        for (OrderInfo orderInfo : orderInfoList) {
            if (orderInfo == null) {
                continue;
            }
            OrderBaseDto orderBaseDto = new OrderBaseDto();
            orderBaseDto.setId(orderInfo.getId());
            orderBaseDto.setOutpVisitId(orderInfo.getOutpVisitId());
            orderBaseDto.setOutpVisitEncounterId(orderInfo.getOutpVisitEncounterId());
            orderBaseDto.setPrescriptionId(orderInfo.getPrescriptionId());
            orderBaseDto.setErpVisitId(orderInfo.getErpVisitId());
            orderBaseDto.setInpVisitId(orderInfo.getInpVisitId());
            orderBaseDto.setOrderingCampusId(orderInfo.getOrderingCampusId());
            orderBaseDto.setOrderingApplicationId(orderInfo.getOrderingApplicationId());
            orderBaseDto.setPatientId(orderInfo.getPatientId());
            orderBaseDto.setPatientDepartmentId(orderInfo.getPatientDepartmentId());
            orderBaseDto.setPatientWardId(orderInfo.getPatientWardId());
            orderBaseDto.setPatientBedId(orderInfo.getPatientBedId());
            orderBaseDto.setPatientBedNumber(orderInfo.getPatientBedNumber());
            orderBaseDto.setDisplayId(orderInfo.getDisplayId());
            orderBaseDto.setSortNumber(orderInfo.getSortNumber());
            orderBaseDto.setParentOrderId(orderInfo.getParentOrderId());
            orderBaseDto.setGroupNumber(orderInfo.getGroupNumber());
            orderBaseDto.setGroupSortNumber(orderInfo.getGroupSortNumber());
            orderBaseDto.setOrderStatus(orderInfo.getOrderStatus());
            orderBaseDto.setPreHospitalOrderFlag(orderInfo.getPreHospitalOrderFlag());
            orderBaseDto.setRepeatOrderFlag(orderInfo.getRepeatOrderFlag());
            orderBaseDto.setOrderClass(orderInfo.getOrderClass());
            orderBaseDto.setExclusionType(orderInfo.getExclusionType());
            orderBaseDto.setOrderText(orderInfo.getOrderText());
            orderBaseDto.setDoctorInstruction(orderInfo.getDoctorInstruction());
            orderBaseDto.setOrderingDepartmentId(orderInfo.getOrderingDepartmentId());
            orderBaseDto.setOrderingBy(orderInfo.getOrderingBy());
            orderBaseDto.setOrderingMedicalGroupId(orderInfo.getOrderingMedicalGroupId());
            orderBaseDto.setEnterDate(orderInfo.getEnterDate());
            orderBaseDto.setSubmitDate(orderInfo.getSubmitDate());
            orderBaseDto.setStartDate(orderInfo.getStartDate());
            orderBaseDto.setStopDate(orderInfo.getStopDate());
            orderBaseDto.setStopBy(orderInfo.getStopBy());
            orderBaseDto.setProofreadBy(orderInfo.getProofreadBy());
            orderBaseDto.setProofreadDate(orderInfo.getProofreadDate());
            orderBaseDto.setReviewBy(orderInfo.getReviewBy());
            orderBaseDto.setReviewDate(orderInfo.getReviewDate());
            orderBaseDto.setCancelDate(orderInfo.getCancelDate());
            orderBaseDto.setCancelBy(orderInfo.getCancelBy());
            orderBaseDto.setRevokeDate(orderInfo.getRevokeDate());
            orderBaseDto.setRevokeBy(orderInfo.getRevokeBy());
            orderBaseDto.setRevokeType(orderInfo.getRevokeType());
            orderBaseDto.setRevokeReason(orderInfo.getRevokeReason());
            orderBaseDto.setPerformDepartmentId(orderInfo.getPerformDepartmentId());
            orderBaseDto.setBillingFlag(orderInfo.getBillingFlag());
            orderBaseDto.setBillingAttribute(orderInfo.getBillingAttribute());
            orderBaseDto.setForceSelfPaymentFlag(orderInfo.getForceSelfPaymentFlag());
            orderBaseDto.setChargeStatus(orderInfo.getChargeStatus());
            orderBaseDto.setDiseaseType(orderInfo.getDiseaseType());
            orderBaseDto.setGcpId(orderInfo.getGcpId());
            orderBaseDto.setOrderSourceType(orderInfo.getOrderSourceType());
            orderBaseDto.setOrderSourceId(orderInfo.getOrderSourceId());
            orderBaseDto.setPerformPrintDate(orderInfo.getPerformPrintDate());
            orderBaseDto.setOrderPrintCount(orderInfo.getOrderPrintCount());
            orderBaseDto.setLockVersion(orderInfo.getLockVersion());
            orderBaseDto.setCreatedAt(orderInfo.getCreatedAt());
            orderBaseDto.setUpdatedAt(orderInfo.getUpdatedAt());
            orderBaseDto.setDeletedAt(orderInfo.getDeletedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            orderBaseDtoList.add(orderBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return orderBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
