package com.pulse.orders.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.orders.manager.dto.OrderPathologyDetailBaseDto;
import com.pulse.orders.persist.dos.OrderPathologyDetail;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "6ce7def0-4f09-49c6-a5ee-8059bdfaf53a|DTO|BASE_CONVERTER")
public class OrderPathologyDetailBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public OrderPathologyDetailBaseDto convertFromOrderPathologyDetailToOrderPathologyDetailBaseDto(
            OrderPathologyDetail orderPathologyDetail) {
        return convertFromOrderPathologyDetailToOrderPathologyDetailBaseDto(
                        List.of(orderPathologyDetail))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OrderPathologyDetailBaseDto>
            convertFromOrderPathologyDetailToOrderPathologyDetailBaseDto(
                    List<OrderPathologyDetail> orderPathologyDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderPathologyDetailList)) {
            return new ArrayList<>();
        }
        List<OrderPathologyDetailBaseDto> orderPathologyDetailBaseDtoList = new ArrayList<>();
        for (OrderPathologyDetail orderPathologyDetail : orderPathologyDetailList) {
            if (orderPathologyDetail == null) {
                continue;
            }
            OrderPathologyDetailBaseDto orderPathologyDetailBaseDto =
                    new OrderPathologyDetailBaseDto();
            orderPathologyDetailBaseDto.setId(orderPathologyDetail.getId());
            orderPathologyDetailBaseDto.setOrderPathologyId(
                    orderPathologyDetail.getOrderPathologyId());
            orderPathologyDetailBaseDto.setCreatedAt(orderPathologyDetail.getCreatedAt());
            orderPathologyDetailBaseDto.setUpdatedAt(orderPathologyDetail.getUpdatedAt());
            orderPathologyDetailBaseDto.setDeletedAt(orderPathologyDetail.getDeletedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            orderPathologyDetailBaseDtoList.add(orderPathologyDetailBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return orderPathologyDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
