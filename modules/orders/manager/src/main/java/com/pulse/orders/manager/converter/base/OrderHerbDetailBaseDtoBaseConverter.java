package com.pulse.orders.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.orders.manager.dto.OrderHerbDetailBaseDto;
import com.pulse.orders.persist.dos.OrderHerbDetail;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "faac288b-b7e2-491e-98c5-aa5104667b98|DTO|BASE_CONVERTER")
public class OrderHerbDetailBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public OrderHerbDetailBaseDto convertFromOrderHerbDetailToOrderHerbDetailBaseDto(
            OrderHerbDetail orderHerbDetail) {
        return convertFromOrderHerbDetailToOrderHerbDetailBaseDto(List.of(orderHerbDetail)).stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<OrderHerbDetailBaseDto> convertFromOrderHerbDetailToOrderHerbDetailBaseDto(
            List<OrderHerbDetail> orderHerbDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(orderHerbDetailList)) {
            return new ArrayList<>();
        }
        List<OrderHerbDetailBaseDto> orderHerbDetailBaseDtoList = new ArrayList<>();
        for (OrderHerbDetail orderHerbDetail : orderHerbDetailList) {
            if (orderHerbDetail == null) {
                continue;
            }
            OrderHerbDetailBaseDto orderHerbDetailBaseDto = new OrderHerbDetailBaseDto();
            orderHerbDetailBaseDto.setId(orderHerbDetail.getId());
            orderHerbDetailBaseDto.setOrderHerbId(orderHerbDetail.getOrderHerbId());
            orderHerbDetailBaseDto.setDrugOriginBatchInventoryId(
                    orderHerbDetail.getDrugOriginBatchInventoryId());
            orderHerbDetailBaseDto.setSortNumber(orderHerbDetail.getSortNumber());
            orderHerbDetailBaseDto.setDosage(orderHerbDetail.getDosage());
            orderHerbDetailBaseDto.setDosageUnit(orderHerbDetail.getDosageUnit());
            orderHerbDetailBaseDto.setTotalAmount(orderHerbDetail.getTotalAmount());
            orderHerbDetailBaseDto.setHerbDecoctionMethod(orderHerbDetail.getHerbDecoctionMethod());
            orderHerbDetailBaseDto.setAdjuvantMaterialFlag(
                    orderHerbDetail.getAdjuvantMaterialFlag());
            orderHerbDetailBaseDto.setCreatedAt(orderHerbDetail.getCreatedAt());
            orderHerbDetailBaseDto.setUpdatedAt(orderHerbDetail.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            orderHerbDetailBaseDtoList.add(orderHerbDetailBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return orderHerbDetailBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
