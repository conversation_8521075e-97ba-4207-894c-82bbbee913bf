package com.pulse.orders.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.orders.manager.OrderPathologyBaseDtoManager;
import com.pulse.orders.manager.dto.OrderPathologyBaseDto;
import com.pulse.orders.service.converter.OrderPathologyBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "f31bbdcb-ce57-4890-b7ae-1551f71c888a|DTO|SERVICE")
public class OrderPathologyBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private OrderPathologyBaseDtoManager orderPathologyBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private OrderPathologyBaseDtoServiceConverter orderPathologyBaseDtoServiceConverter;

    @PublicInterface(id = "e27a1ad0-fdaa-4192-a0f5-f1586929ea96", module = "orders")
    @AutoGenerated(locked = false, uuid = "1ca72bbe-6aec-3fe7-a818-0e43c338ab7c")
    public List<OrderPathologyBaseDto> getByOrderInfoIds(
            @Valid @NotNull(message = "医嘱ID不能为空") List<String> orderInfoId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        orderInfoId = new ArrayList<>(new HashSet<>(orderInfoId));
        List<OrderPathologyBaseDto> orderPathologyBaseDtoList =
                orderPathologyBaseDtoManager.getByOrderInfoIds(orderInfoId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return orderPathologyBaseDtoServiceConverter.OrderPathologyBaseDtoConverter(
                orderPathologyBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "94e47239-5d23-4621-b030-dc4c94e1af2a", module = "orders")
    @AutoGenerated(locked = false, uuid = "44e096a8-dfef-357a-b153-81d4301f28af")
    public OrderPathologyBaseDto getByOrderInfoId(
            @NotNull(message = "医嘱ID不能为空") String orderInfoId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrderPathologyBaseDto> ret = getByOrderInfoIds(Arrays.asList(orderInfoId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(id = "1da44281-616d-4d46-9882-e8723262a0bb", module = "orders")
    @AutoGenerated(locked = false, uuid = "7afb6ce8-4318-31c8-a978-8ea00108f529")
    public List<OrderPathologyBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<OrderPathologyBaseDto> orderPathologyBaseDtoList =
                orderPathologyBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return orderPathologyBaseDtoServiceConverter.OrderPathologyBaseDtoConverter(
                orderPathologyBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(id = "4bbcb1fc-d87e-4e44-8013-383c7c6c3bdb", module = "orders")
    @AutoGenerated(locked = false, uuid = "af98d81b-dee1-3f5e-b6c5-b2579534d47c")
    public OrderPathologyBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrderPathologyBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
