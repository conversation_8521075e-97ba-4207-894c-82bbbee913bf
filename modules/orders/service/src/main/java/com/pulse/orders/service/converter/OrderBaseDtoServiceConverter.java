package com.pulse.orders.service.converter;

import com.pulse.orders.manager.dto.OrderBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "5ba1d4ce-abc0-3ec9-8cf0-9f78f7cae1c0")
public class OrderBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OrderBaseDto> OrderBaseDtoConverter(List<OrderBaseDto> orderBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return orderBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
