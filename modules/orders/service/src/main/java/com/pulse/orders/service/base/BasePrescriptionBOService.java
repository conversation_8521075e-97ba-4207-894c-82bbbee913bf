package com.pulse.orders.service.base;

import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "bc76aaec-a2a7-395a-85b8-92b9a42f9bc9")
public class BasePrescriptionBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;
}
