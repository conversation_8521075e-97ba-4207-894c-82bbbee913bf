package com.pulse.drug_financial.entrance.web.query.assembler;

import com.pulse.drug_dictionary.manager.dto.DrugProducerDictionaryBaseDto;
import com.pulse.drug_financial.entrance.web.vo.DrugExportImportSettleVo;
import com.pulse.drug_financial.manager.dto.DrugExportImportSettleBaseDto;
import com.pulse.drug_financial.service.DrugExportImportSettleBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** DrugExportImportSettleVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "eef9e239-2a8c-30bb-b683-c1f963f9266c")
public class DrugExportImportSettleVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private DrugExportImportSettleBaseDtoService drugExportImportSettleBaseDtoService;

    /** 组装DrugExportImportSettleVo数据 */
    @AutoGenerated(locked = true, uuid = "1e24063c-017d-3eff-bedb-d1f79f3ede61")
    public void assembleData(
            Map<String, DrugExportImportSettleVo> voMap,
            DrugExportImportSettleVoDataAssembler.DrugExportImportSettleVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<DrugExportImportSettleBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<
                        String,
                        Pair<
                                DrugProducerDictionaryBaseDto,
                                DrugExportImportSettleVo.DrugFirmDictionaryVo>>
                supplier =
                        dataHolder.supplier.keySet().stream()
                                .collect(
                                        Collectors.toMap(
                                                dto -> dto.getId(),
                                                dto -> Pair.of(dto, dataHolder.supplier.get(dto)),
                                                (o1, o2) -> o1));

        for (DrugExportImportSettleBaseDto baseDto : baseDtoList) {
            DrugExportImportSettleVo vo = voMap.get(baseDto.getId());
            vo.setSupplier(
                    Optional.ofNullable(supplier.get(baseDto.getSupplierId()))
                            .map(tmp -> tmp.getRight())
                            .orElse(null));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装DrugExportImportSettleVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "8a43c05e-9ba8-3145-9931-f4a69848b8f9")
    public void assembleDataCustomized(List<DrugExportImportSettleVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class DrugExportImportSettleVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<DrugExportImportSettleBaseDto> rootBaseDtoList;

        /** 持有字段supplier的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<DrugProducerDictionaryBaseDto, DrugExportImportSettleVo.DrugFirmDictionaryVo>
                supplier;
    }
}
