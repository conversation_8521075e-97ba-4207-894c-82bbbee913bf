package com.pulse.drug_financial.service.bto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;

/**
 * <b>[源自]</b> DrugExportImportSettle
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "5b358188-df38-4f9a-8253-5c4371468cc9|BTO|DEFINITION")
public class CancelExportImportSettleBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "b5f5093d-afd0-4b27-ad8a-452250f2f072")
    private String id;

    /** 作废日期 */
    @AutoGenerated(locked = true, uuid = "bd40e917-ac07-4477-bf20-6cc6962c178e")
    private Date invalidDateTime;

    /** 作废标志 */
    @AutoGenerated(locked = true, uuid = "a1830d13-171b-4e71-9760-0e6a8fafca47")
    private Boolean invalidFlag;

    /** 作废人 */
    @AutoGenerated(locked = true, uuid = "8db5bcb9-cb69-423e-a69f-a73619fbcd5c")
    private String invalidStaffId;

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInvalidDateTime(Date invalidDateTime) {
        this.__$validPropertySet.add("invalidDateTime");
        this.invalidDateTime = invalidDateTime;
    }

    @AutoGenerated(locked = true)
    public void setInvalidFlag(Boolean invalidFlag) {
        this.__$validPropertySet.add("invalidFlag");
        this.invalidFlag = invalidFlag;
    }

    @AutoGenerated(locked = true)
    public void setInvalidStaffId(String invalidStaffId) {
        this.__$validPropertySet.add("invalidStaffId");
        this.invalidStaffId = invalidStaffId;
    }
}
