package com.pulse.drug_financial.service.converter.voConverter;

import com.pulse.drug_financial.persist.dos.DrugPriceContrast;
import com.pulse.drug_financial.persist.dos.DrugPriceContrast.PackageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType;
import com.pulse.drug_financial.persist.eo.UkPackageSpecificationIdPriceTypeEo;
import com.vs.code.AutoGenerated;

@AutoGenerated(locked = true, uuid = "e35948c6-6cbc-37ad-9e84-fc47209d8a40")
public class DrugPriceContrastUkPackageSpecificationIdPriceTypeConverter {

    @AutoGenerated(locked = true)
    public static DrugPriceContrast
                    .PackageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType
            convertFromUkPackageSpecificationIdPriceTypeToInner(
                    UkPackageSpecificationIdPriceTypeEo ukPackageSpecificationIdPriceType) {
        if (null == ukPackageSpecificationIdPriceType) {
            return null;
        }

        PackageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType
                packageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType =
                        new PackageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType();
        packageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType
                .setPackageOriginSpecificationId(
                        ukPackageSpecificationIdPriceType.getPackageOriginSpecificationId());
        packageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType
                .setPackageSpecificationPrice(
                        ukPackageSpecificationIdPriceType.getPackageSpecificationPrice());
        packageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType.setPriceType(
                ukPackageSpecificationIdPriceType.getPriceType());
        return packageOriginSpecificationIdAndPackageSpecificationPriceAndPriceType;
    }
}
