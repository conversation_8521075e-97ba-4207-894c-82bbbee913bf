package com.pulse.drug_financial.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_financial.manager.DrugExportImportSettleBaseDtoManager;
import com.pulse.drug_financial.manager.dto.DrugExportImportSettleBaseDto;
import com.pulse.drug_financial.service.converter.DrugExportImportSettleBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "98ee38b4-3e20-4575-845e-96714d7ef69f|DTO|SERVICE")
public class DrugExportImportSettleBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportImportSettleBaseDtoManager drugExportImportSettleBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugExportImportSettleBaseDtoServiceConverter
            drugExportImportSettleBaseDtoServiceConverter;

    @PublicInterface(
            id = "94e077af-e40a-4ab9-b0d2-2cc2d163c788",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020895")
    @AutoGenerated(locked = false, uuid = "192c4c35-c218-33d8-9883-09f4627e01a9")
    public List<DrugExportImportSettleBaseDto> getBySettleStaffIds(
            @Valid @NotNull(message = "结算人不能为空") List<String> settleStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        settleStaffId = new ArrayList<>(new HashSet<>(settleStaffId));
        List<DrugExportImportSettleBaseDto> drugExportImportSettleBaseDtoList =
                drugExportImportSettleBaseDtoManager.getBySettleStaffIds(settleStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportImportSettleBaseDtoServiceConverter.DrugExportImportSettleBaseDtoConverter(
                drugExportImportSettleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "ca32b818-dcdc-423f-bb72-78ab37351b87",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020910")
    @AutoGenerated(locked = false, uuid = "3afeeffb-40c8-3036-b0ae-7329cbd0ccd4")
    public List<DrugExportImportSettleBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugExportImportSettleBaseDto> drugExportImportSettleBaseDtoList =
                drugExportImportSettleBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportImportSettleBaseDtoServiceConverter.DrugExportImportSettleBaseDtoConverter(
                drugExportImportSettleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "f5381e09-6299-4f5d-a548-b85d83240770",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020897")
    @AutoGenerated(locked = false, uuid = "41e93386-080e-3c27-b1dd-6fdf2e2c97a3")
    public List<DrugExportImportSettleBaseDto> getBySupplierId(
            @NotNull(message = "供应商id不能为空") String supplierId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySupplierIds(Arrays.asList(supplierId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "a22b1a58-7d28-4c97-8711-0bb34521bd29",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020902")
    @AutoGenerated(locked = false, uuid = "5b3d8715-708a-3142-a5c3-7882a4fcc36e")
    public List<DrugExportImportSettleBaseDto> getByInvalidStaffId(
            @NotNull(message = "作废人不能为空") String invalidStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByInvalidStaffIds(Arrays.asList(invalidStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "2f9d4504-1fa5-48df-bbea-117891d37807",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020892")
    @AutoGenerated(locked = false, uuid = "5ddf12be-b221-3322-b2c8-cc0ffc5857ad")
    public List<DrugExportImportSettleBaseDto> getBySettleStaffId(
            @NotNull(message = "结算人不能为空") String settleStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getBySettleStaffIds(Arrays.asList(settleStaffId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "0206b21a-9ff1-4030-a574-22aebc3e7613",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020907")
    @AutoGenerated(locked = false, uuid = "707f4b5f-65c7-3e26-900d-77a5eff636b2")
    public DrugExportImportSettleBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugExportImportSettleBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "5a18da68-aaab-41aa-ae65-a6b290b4159a",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020900")
    @AutoGenerated(locked = false, uuid = "744f218a-17ef-310d-a01a-3f435d1ac4f6")
    public List<DrugExportImportSettleBaseDto> getBySupplierIds(
            @Valid @NotNull(message = "供应商id不能为空") List<String> supplierId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        supplierId = new ArrayList<>(new HashSet<>(supplierId));
        List<DrugExportImportSettleBaseDto> drugExportImportSettleBaseDtoList =
                drugExportImportSettleBaseDtoManager.getBySupplierIds(supplierId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportImportSettleBaseDtoServiceConverter.DrugExportImportSettleBaseDtoConverter(
                drugExportImportSettleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "54d6599b-175f-4d4a-8c62-65879c11d4d9",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020889")
    @AutoGenerated(locked = false, uuid = "87ce531a-59f1-3353-af81-864313e40398")
    public List<DrugExportImportSettleBaseDto> getByStorageCodes(
            @Valid @NotNull(message = "库房编码不能为空") List<String> storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        storageCode = new ArrayList<>(new HashSet<>(storageCode));
        List<DrugExportImportSettleBaseDto> drugExportImportSettleBaseDtoList =
                drugExportImportSettleBaseDtoManager.getByStorageCodes(storageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportImportSettleBaseDtoServiceConverter.DrugExportImportSettleBaseDtoConverter(
                drugExportImportSettleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "2a05d98c-7a09-46c6-bad6-ca0b26bb8a26",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020887")
    @AutoGenerated(locked = false, uuid = "9b350b25-30de-37f4-811e-2ada0e08b63b")
    public List<DrugExportImportSettleBaseDto> getByStorageCode(
            @NotNull(message = "库房编码不能为空") String storageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByStorageCodes(Arrays.asList(storageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "b1625ef3-a6eb-4d66-9805-98878b3f76a8",
            module = "drug_financial",
            moduleId = "4f94196f-1324-436b-9078-4ef844e150fb",
            pubRpc = true,
            version = "1743571020905")
    @AutoGenerated(locked = false, uuid = "b282cf55-6650-3087-bf40-24257116f2e5")
    public List<DrugExportImportSettleBaseDto> getByInvalidStaffIds(
            @Valid @NotNull(message = "作废人不能为空") List<String> invalidStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        invalidStaffId = new ArrayList<>(new HashSet<>(invalidStaffId));
        List<DrugExportImportSettleBaseDto> drugExportImportSettleBaseDtoList =
                drugExportImportSettleBaseDtoManager.getByInvalidStaffIds(invalidStaffId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugExportImportSettleBaseDtoServiceConverter.DrugExportImportSettleBaseDtoConverter(
                drugExportImportSettleBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
