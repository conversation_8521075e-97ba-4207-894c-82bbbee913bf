package com.pulse.drug_financial.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_financial.manager.dto.DrugMonthlyReportDisplayWayBaseDto;
import com.pulse.drug_financial.persist.dos.DrugMonthlyReportDisplayWay;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "ab50cf03-e52a-49ad-be2c-1c7773425b17|DTO|BASE_CONVERTER")
public class DrugMonthlyReportDisplayWayBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugMonthlyReportDisplayWayBaseDto
            convertFromDrugMonthlyReportDisplayWayToDrugMonthlyReportDisplayWayBaseDto(
                    DrugMonthlyReportDisplayWay drugMonthlyReportDisplayWay) {
        return convertFromDrugMonthlyReportDisplayWayToDrugMonthlyReportDisplayWayBaseDto(
                        List.of(drugMonthlyReportDisplayWay))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugMonthlyReportDisplayWayBaseDto>
            convertFromDrugMonthlyReportDisplayWayToDrugMonthlyReportDisplayWayBaseDto(
                    List<DrugMonthlyReportDisplayWay> drugMonthlyReportDisplayWayList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugMonthlyReportDisplayWayList)) {
            return new ArrayList<>();
        }
        List<DrugMonthlyReportDisplayWayBaseDto> drugMonthlyReportDisplayWayBaseDtoList =
                new ArrayList<>();
        for (DrugMonthlyReportDisplayWay drugMonthlyReportDisplayWay :
                drugMonthlyReportDisplayWayList) {
            if (drugMonthlyReportDisplayWay == null) {
                continue;
            }
            DrugMonthlyReportDisplayWayBaseDto drugMonthlyReportDisplayWayBaseDto =
                    new DrugMonthlyReportDisplayWayBaseDto();
            drugMonthlyReportDisplayWayBaseDto.setId(drugMonthlyReportDisplayWay.getId());
            drugMonthlyReportDisplayWayBaseDto.setStorageCode(
                    drugMonthlyReportDisplayWay.getStorageCode());
            drugMonthlyReportDisplayWayBaseDto.setBranchInstitutionId(
                    drugMonthlyReportDisplayWay.getBranchInstitutionId());
            drugMonthlyReportDisplayWayBaseDto.setWayName(drugMonthlyReportDisplayWay.getWayName());
            drugMonthlyReportDisplayWayBaseDto.setWayType(drugMonthlyReportDisplayWay.getWayType());
            drugMonthlyReportDisplayWayBaseDto.setRemark(drugMonthlyReportDisplayWay.getRemark());
            drugMonthlyReportDisplayWayBaseDto.setUpdateBy(
                    drugMonthlyReportDisplayWay.getUpdateBy());
            drugMonthlyReportDisplayWayBaseDto.setExportImportWayCodeList(
                    drugMonthlyReportDisplayWay.getExportImportWayCodeList());
            drugMonthlyReportDisplayWayBaseDto.setSortNumber(
                    drugMonthlyReportDisplayWay.getSortNumber());
            drugMonthlyReportDisplayWayBaseDto.setLockVersion(
                    drugMonthlyReportDisplayWay.getLockVersion());
            drugMonthlyReportDisplayWayBaseDto.setCreatedAt(
                    drugMonthlyReportDisplayWay.getCreatedAt());
            drugMonthlyReportDisplayWayBaseDto.setUpdatedAt(
                    drugMonthlyReportDisplayWay.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugMonthlyReportDisplayWayBaseDtoList.add(drugMonthlyReportDisplayWayBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugMonthlyReportDisplayWayBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
