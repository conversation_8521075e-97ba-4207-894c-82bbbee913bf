package com.pulse.drug_financial.manager.bo;

import com.pulse.drug_financial.persist.dos.DrugPriceAdjustDetail;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Getter
@Setter
@Table(name = "drug_price_adjust_detail")
@Entity
@AutoGenerated(locked = true, uuid = "71d20dab-e77c-42ae-9078-0b9069c9bc0b|BO|DEFINITION")
public class DrugPriceAdjustDetailBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 已调价标志 */
    @Column(name = "adjusted_flag")
    @AutoGenerated(locked = true, uuid = "8c661a2b-2928-40bd-9410-62e80acda491")
    private Boolean adjustedFlag;

    /** 调价数量 */
    @Column(name = "amount")
    @AutoGenerated(locked = true, uuid = "7b5adb23-f732-4305-80c4-62957065b836")
    private BigDecimal amount;

    /** 拆分系数 */
    @Column(name = "amount_per_package")
    @AutoGenerated(locked = true, uuid = "417aa6cc-2672-38ca-9e93-17f5ef5e8003")
    private Long amountPerPackage;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "1a611a1d-77e9-301b-9763-9966923b76a6")
    private Date createdAt;

    /** 药品产地编码 */
    @Column(name = "drug_origin_code")
    @AutoGenerated(locked = true, uuid = "d9cabb6c-5af2-3861-94b1-1b82b7e03205")
    private String drugOriginCode;

    /** 药品名称 */
    @Column(name = "drug_origin_name")
    @AutoGenerated(locked = true, uuid = "cd1da881-8e15-34b1-b343-cb6e1c2c5e07")
    private String drugOriginName;

    /** 药品产地规格id */
    @Column(name = "drug_origin_specification_id")
    @AutoGenerated(locked = true, uuid = "8486a0a9-dc30-339c-9efc-681efa75262a")
    private String drugOriginSpecificationId;

    @ManyToOne
    @JoinColumn(name = "adjust_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private DrugPriceAdjustBO drugPriceAdjustBO;

    /** 生产商ID */
    @Column(name = "drug_producer_id")
    @AutoGenerated(locked = true, uuid = "bc8f241c-0283-3d75-8e1e-0ed3d95c3496")
    private String drugProducerId;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "357ac637-84a6-31fc-8968-cfeed6d6d820")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 现招标进价 */
    @Column(name = "new_bid_purchase_price")
    @AutoGenerated(locked = true, uuid = "96ac2d9d-dc4e-4f09-9c59-da6be8f7e8cf")
    private BigDecimal newBidPurchasePrice;

    /** 调价后售价 */
    @Column(name = "new_retail_price")
    @AutoGenerated(locked = true, uuid = "682f7401-d877-36ef-8153-405f268b6041")
    private BigDecimal newRetailPrice;

    /** 调价后进价 */
    @Column(name = "new_stock_price")
    @AutoGenerated(locked = true, uuid = "ec79c2ad-cc5a-3384-89b1-dc0ecb66665c")
    private BigDecimal newStockPrice;

    /** 原招标进价 */
    @Column(name = "old_bid_purchase_price")
    @AutoGenerated(locked = true, uuid = "4ae25506-4805-425c-9ebf-8108de56ab27")
    private BigDecimal oldBidPurchasePrice;

    /** 调价前售价 */
    @Column(name = "old_retail_price")
    @AutoGenerated(locked = true, uuid = "370e23cc-ad06-302f-937e-b8090f07fc5f")
    private BigDecimal oldRetailPrice;

    /** 调价前进价 */
    @Column(name = "old_stock_price")
    @AutoGenerated(locked = true, uuid = "ee6608d0-7f52-3d1f-8601-0fdb46d485ef")
    private BigDecimal oldStockPrice;

    /** 排序号 */
    @Column(name = "sort_number")
    @AutoGenerated(locked = true, uuid = "e7d76c8f-e644-3e6e-b49d-3ce1701dad0b")
    private Integer sortNumber;

    /** 规格 */
    @Column(name = "specification")
    @AutoGenerated(locked = true, uuid = "ca7f6069-7737-325d-957d-a3b2f99a56ed")
    private String specification;

    /** 单位 */
    @Column(name = "unit")
    @AutoGenerated(locked = true, uuid = "329bc57d-3eb8-3168-aa49-a71bf9dbfe6a")
    private String unit;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "567a1231-2122-3acf-8dfe-d7dc7cb059f3")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "807e96e5-573e-4bde-bdbd-4370cd09714b|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetail convertToDrugPriceAdjustDetail() {
        DrugPriceAdjustDetail entity = new DrugPriceAdjustDetail();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "sortNumber",
                "drugOriginCode",
                "drugOriginSpecificationId",
                "drugOriginName",
                "specification",
                "amountPerPackage",
                "unit",
                "drugProducerId",
                "oldStockPrice",
                "oldRetailPrice",
                "newStockPrice",
                "newRetailPrice",
                "amount",
                "oldBidPurchasePrice",
                "newBidPurchasePrice",
                "adjustedFlag",
                "createdAt",
                "updatedAt");
        DrugPriceAdjustBO drugPriceAdjustBO = this.getDrugPriceAdjustBO();
        entity.setAdjustId(drugPriceAdjustBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getAdjustId() {
        return this.getDrugPriceAdjustBO().getId();
    }

    @AutoGenerated(locked = true)
    public Boolean getAdjustedFlag() {
        return this.adjustedFlag;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getAmount() {
        return this.amount;
    }

    @AutoGenerated(locked = true)
    public Long getAmountPerPackage() {
        return this.amountPerPackage;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginCode() {
        return this.drugOriginCode;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginName() {
        return this.drugOriginName;
    }

    @AutoGenerated(locked = true)
    public String getDrugOriginSpecificationId() {
        return this.drugOriginSpecificationId;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustBO getDrugPriceAdjustBO() {
        return this.drugPriceAdjustBO;
    }

    @AutoGenerated(locked = true)
    public String getDrugProducerId() {
        return this.drugProducerId;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getNewBidPurchasePrice() {
        return this.newBidPurchasePrice;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getNewRetailPrice() {
        return this.newRetailPrice;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getNewStockPrice() {
        return this.newStockPrice;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getOldBidPurchasePrice() {
        return this.oldBidPurchasePrice;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getOldRetailPrice() {
        return this.oldRetailPrice;
    }

    @AutoGenerated(locked = true)
    public BigDecimal getOldStockPrice() {
        return this.oldStockPrice;
    }

    @AutoGenerated(locked = true)
    public Integer getSortNumber() {
        return this.sortNumber;
    }

    @AutoGenerated(locked = true)
    public String getSpecification() {
        return this.specification;
    }

    @AutoGenerated(locked = true)
    public String getUnit() {
        return this.unit;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setAdjustedFlag(Boolean adjustedFlag) {
        this.adjustedFlag = adjustedFlag;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setAmount(BigDecimal amount) {
        this.amount = amount;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setAmountPerPackage(Long amountPerPackage) {
        this.amountPerPackage = amountPerPackage;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setDrugOriginCode(String drugOriginCode) {
        this.drugOriginCode = drugOriginCode;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setDrugOriginName(String drugOriginName) {
        this.drugOriginName = drugOriginName;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setDrugOriginSpecificationId(String drugOriginSpecificationId) {
        this.drugOriginSpecificationId = drugOriginSpecificationId;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setDrugPriceAdjustBO(DrugPriceAdjustBO drugPriceAdjustBO) {
        this.drugPriceAdjustBO = drugPriceAdjustBO;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setDrugProducerId(String drugProducerId) {
        this.drugProducerId = drugProducerId;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setId(String id) {
        this.id = id;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setNewBidPurchasePrice(BigDecimal newBidPurchasePrice) {
        this.newBidPurchasePrice = newBidPurchasePrice;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setNewRetailPrice(BigDecimal newRetailPrice) {
        this.newRetailPrice = newRetailPrice;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setNewStockPrice(BigDecimal newStockPrice) {
        this.newStockPrice = newStockPrice;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setOldBidPurchasePrice(BigDecimal oldBidPurchasePrice) {
        this.oldBidPurchasePrice = oldBidPurchasePrice;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setOldRetailPrice(BigDecimal oldRetailPrice) {
        this.oldRetailPrice = oldRetailPrice;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setOldStockPrice(BigDecimal oldStockPrice) {
        this.oldStockPrice = oldStockPrice;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setSortNumber(Integer sortNumber) {
        this.sortNumber = sortNumber;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setSpecification(String specification) {
        this.specification = specification;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setUnit(String unit) {
        this.unit = unit;
        return (DrugPriceAdjustDetailBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPriceAdjustDetailBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugPriceAdjustDetailBO) this;
    }
}
