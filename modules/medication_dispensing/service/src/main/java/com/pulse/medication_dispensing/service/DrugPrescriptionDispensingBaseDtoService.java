package com.pulse.medication_dispensing.service;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.medication_dispensing.manager.DrugPrescriptionDispensingBaseDtoManager;
import com.pulse.medication_dispensing.manager.dto.DrugPrescriptionDispensingBaseDto;
import com.pulse.medication_dispensing.service.converter.DrugPrescriptionDispensingBaseDtoServiceConverter;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "41e5d1a8-e523-4c0f-9aa8-a6b917fd7858|DTO|SERVICE")
public class DrugPrescriptionDispensingBaseDtoService {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugPrescriptionDispensingBaseDtoManager drugPrescriptionDispensingBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPrescriptionDispensingBaseDtoServiceConverter
            drugPrescriptionDispensingBaseDtoServiceConverter;

    @PublicInterface(
            id = "7ec68765-4bc0-4025-a65a-fdd7a978de69",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743576888252")
    @AutoGenerated(locked = false, uuid = "06a6acaf-4bca-3daa-8b2d-14ef1f6b789d")
    public List<DrugPrescriptionDispensingBaseDto> getByIds(
            @Valid @NotNull(message = "主键不能为空") List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        id = new ArrayList<>(new HashSet<>(id));
        List<DrugPrescriptionDispensingBaseDto> drugPrescriptionDispensingBaseDtoList =
                drugPrescriptionDispensingBaseDtoManager.getByIds(id);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPrescriptionDispensingBaseDtoServiceConverter
                .DrugPrescriptionDispensingBaseDtoConverter(drugPrescriptionDispensingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "e048f04a-64b8-47b8-a1ae-216bfd427ce4",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743576888264")
    @AutoGenerated(locked = false, uuid = "40504318-d771-3a2d-88bb-78542534e8d3")
    public List<DrugPrescriptionDispensingBaseDto> getByDispenseWindowIds(
            @Valid @NotNull(message = "发药窗口id不能为空") List<String> dispenseWindowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        dispenseWindowId = new ArrayList<>(new HashSet<>(dispenseWindowId));
        List<DrugPrescriptionDispensingBaseDto> drugPrescriptionDispensingBaseDtoList =
                drugPrescriptionDispensingBaseDtoManager.getByDispenseWindowIds(dispenseWindowId);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPrescriptionDispensingBaseDtoServiceConverter
                .DrugPrescriptionDispensingBaseDtoConverter(drugPrescriptionDispensingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "0bd113fb-0551-448b-a9cb-1bf84488e887",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743576888258")
    @AutoGenerated(locked = false, uuid = "4462f1f9-9e05-33cb-9ad1-adcd1d9337e6")
    public List<DrugPrescriptionDispensingBaseDto> getByDispenseStorageCodes(
            @Valid @NotNull(message = "发药药房编码不能为空") List<String> dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        // 去重
        dispenseStorageCode = new ArrayList<>(new HashSet<>(dispenseStorageCode));
        List<DrugPrescriptionDispensingBaseDto> drugPrescriptionDispensingBaseDtoList =
                drugPrescriptionDispensingBaseDtoManager.getByDispenseStorageCodes(
                        dispenseStorageCode);
        /** This block is generated by vs, do not modify, end anchor 1 */

        /** This block is generated by vs, do not modify, start anchor 2 */
        return drugPrescriptionDispensingBaseDtoServiceConverter
                .DrugPrescriptionDispensingBaseDtoConverter(drugPrescriptionDispensingBaseDtoList);
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @PublicInterface(
            id = "d1bf0e1d-8b8a-4615-8a2c-950dcc637461",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743576888249")
    @AutoGenerated(locked = false, uuid = "58a3735d-e8e6-3dbb-803e-8655e39b9aac")
    public DrugPrescriptionDispensingBaseDto getById(@NotNull(message = "主键不能为空") String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispensingBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        return CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "9ea5c72c-f776-4ff4-a074-527704cb1af4",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743576888261")
    @AutoGenerated(locked = false, uuid = "a315b9c5-e791-345a-a231-ede06525302f")
    public List<DrugPrescriptionDispensingBaseDto> getByDispenseWindowId(
            @NotNull(message = "发药窗口id不能为空") String dispenseWindowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDispenseWindowIds(Arrays.asList(dispenseWindowId));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @PublicInterface(
            id = "340d4888-9c4c-408e-86c2-b2869627d040",
            module = "medication_dispensing",
            moduleId = "6b86a381-1fd8-450b-b63f-5af34c1f49f6",
            pubRpc = true,
            version = "1743576888255")
    @AutoGenerated(locked = false, uuid = "fca2c759-79cf-3b45-83a5-4d0b499bf14c")
    public List<DrugPrescriptionDispensingBaseDto> getByDispenseStorageCode(
            @NotNull(message = "发药药房编码不能为空") String dispenseStorageCode) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return getByDispenseStorageCodes(Arrays.asList(dispenseStorageCode));
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
