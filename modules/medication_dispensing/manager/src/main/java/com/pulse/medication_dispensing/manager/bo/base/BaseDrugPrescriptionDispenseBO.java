package com.pulse.medication_dispensing.manager.bo.base;

import com.pulse.drug_permission.common.enums.ClinicTypeEnum;
import com.pulse.medication_dispensing.common.enums.OrderClassEnum;
import com.pulse.medication_dispensing.common.enums.PreparationDispenseStatusEnum;
import com.pulse.medication_dispensing.manager.bo.DrugPrescriptionDispenseBO;
import com.pulse.medication_dispensing.manager.bo.DrugPrescriptionDispenseDetailBO;
import com.pulse.medication_dispensing.persist.dos.DrugPrescriptionDispense;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_prescription_dispense")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "e614418d-a3c7-3d1f-88bf-a0d2e0778c37")
public abstract class BaseDrugPrescriptionDispenseBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 审核人 */
    @Column(name = "audit_staff_id")
    @AutoGenerated(locked = true, uuid = "4ad26ad9-a6bd-351b-a423-5a073fb7d1b8")
    private String auditStaffId;

    /** 核对人 */
    @Column(name = "check_staff_id")
    @AutoGenerated(locked = true, uuid = "7694320a-b225-3963-a46f-e24d6b2c5989")
    private String checkStaffId;

    /** 处方来源 门诊、住院、收费处、药房、急诊绿区、急诊红区 */
    @Column(name = "clinic_type")
    @AutoGenerated(locked = true, uuid = "014c26c6-dc03-3386-84c3-470a8a3bf87d")
    @Enumerated(EnumType.STRING)
    private ClinicTypeEnum clinicType;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "b57bfcf0-9ac3-3ade-9bc0-79c3e27e24e2")
    private Date createdAt;

    /** 发/退药人 */
    @Column(name = "dispense_staff_id")
    @AutoGenerated(locked = true, uuid = "fca1338b-008b-3e52-8187-ba95cd72d008")
    private String dispenseStaffId;

    /** 发药药房编码 */
    @Column(name = "dispense_storage_code")
    @AutoGenerated(locked = true, uuid = "534d1b0d-84f8-3cf2-bee4-ec7b8ceb4858")
    private String dispenseStorageCode;

    /** 发药时间 */
    @Column(name = "dispense_time")
    @AutoGenerated(locked = true, uuid = "f2ed47f8-0c37-31d3-af34-b07db4940891")
    private Date dispenseTime;

    /** 发药窗口id */
    @Column(name = "dispense_window_id")
    @AutoGenerated(locked = true, uuid = "a893cd11-d245-3e19-b4f6-33589a470368")
    private String dispenseWindowId;

    /** 配药单id */
    @Column(name = "dispensing_prepare_id")
    @AutoGenerated(locked = true, uuid = "5c0bfea6-700e-4996-8604-4a4ec6a24394")
    private String dispensingPrepareId;

    @JoinColumn(name = "dispense_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugPrescriptionDispenseDetailBO> drugPrescriptionDispenseDetailBOSet =
            new HashSet<>();

    /** 是否紧急处方 */
    @Column(name = "emergency_flag")
    @AutoGenerated(locked = true, uuid = "cbcce89e-2f52-346c-b05d-121b11746114")
    private Boolean emergencyFlag;

    /** 中药已退付数 已经退药的付数，每次退药累积，不能超出开单付数 */
    @Column(name = "herb_refund_regimen")
    @AutoGenerated(locked = true, uuid = "4ee0650c-4fe0-3b2d-9e8f-d7514bbdd74a")
    private Long herbRefundRegimen;

    /** 中药待退付数 退药业务中间字段，退药申请审核通过后写入需要退药的付数，退药成功后重置为0 */
    @Column(name = "herb_refunding_regimen")
    @AutoGenerated(locked = true, uuid = "1c3c0c06-73b6-3d6a-aaf6-b1c4f7687b08")
    private Long herbRefundingRegimen;

    /** 中药发药付数 发药：发药付数（如果待发药时产生了部分退药，则发药付数和开单付数会不一致） 退药：退药付数 */
    @Column(name = "herb_regimen")
    @AutoGenerated(locked = true, uuid = "83f16b66-68f5-38f3-98be-58fc1f1ffee9")
    private Long herbRegimen;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "14426201-f3e0-36f8-8868-3756f7458bda")
    @Id
    private String id;

    /** 作废标志 */
    @Column(name = "invalid_flag")
    @AutoGenerated(locked = true, uuid = "cf92584a-8296-308c-a2df-c7dfd2041002")
    private Boolean invalidFlag;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "168c3955-7a22-4b64-8bd9-a94390ec4837")
    @Version
    private Long lockVersion;

    /** 药品类型 西药、中药 */
    @Column(name = "order_class")
    @AutoGenerated(locked = true, uuid = "62ac3838-5ff3-35f7-85b6-620123d35182")
    @Enumerated(EnumType.STRING)
    private OrderClassEnum orderClass;

    /** 门诊结算号 */
    @Column(name = "outp_settle_id")
    @AutoGenerated(locked = true, uuid = "db3b22d9-cb2d-3291-be8d-f06b7dece920")
    private String outpSettleId;

    /** 门诊就诊id */
    @Column(name = "outp_visit_id")
    @AutoGenerated(locked = true, uuid = "691e270d-59db-3c6f-80f5-ce4234a57908")
    private String outpVisitId;

    /** 病人标识号 */
    @Column(name = "patient_id")
    @AutoGenerated(locked = true, uuid = "087a1822-0317-3395-82e2-b44e68fa6e41")
    private String patientId;

    /** 配药人 */
    @Column(name = "prepare_staff_id")
    @AutoGenerated(locked = true, uuid = "833f57ea-9e08-3275-a6c2-58b639341970")
    private String prepareStaffId;

    /** 处方id */
    @Column(name = "prescription_id")
    @AutoGenerated(locked = true, uuid = "4cf2282a-0b43-3b3c-944f-cea075e86bdb")
    private String prescriptionId;

    /** 打印次数 */
    @Column(name = "print_count")
    @AutoGenerated(locked = true, uuid = "cf2833ce-bdc1-38bc-99ce-08d7eab0f86d")
    private Long printCount;

    /** 打印状态 */
    @Column(name = "print_flag")
    @AutoGenerated(locked = true, uuid = "11673d69-89c6-3f60-a3f9-16a3bfb40bf1")
    private Boolean printFlag;

    /** 退药申请发起人id */
    @Column(name = "refund_apply_staff_id")
    @AutoGenerated(locked = true, uuid = "312b1f15-10d8-4893-9f30-6ae3023d3570")
    private String refundApplyStaffId;

    /** 退药对应原发药id */
    @Column(name = "refund_dispense_id")
    @AutoGenerated(locked = true, uuid = "bfc7df0d-5e25-39b5-b95e-14c5695bb781")
    private String refundDispenseId;

    /** 退药标识 */
    @Column(name = "refund_flag")
    @AutoGenerated(locked = true, uuid = "74e89c41-5eb3-37c0-9fe0-37d6736bec89")
    private Boolean refundFlag;

    /** 备注 */
    @Column(name = "remark")
    @AutoGenerated(locked = true, uuid = "daa3fa0b-fc0b-3ba2-9264-4d59195e39c5")
    private String remark;

    /** 第二配药人 */
    @Column(name = "second_prepare_staff_id")
    @AutoGenerated(locked = true, uuid = "d7747b03-9a2e-34e1-a8ad-9fae2c857121")
    private String secondPrepareStaffId;

    /** 发药状态 已发药、部分退药、全退、取消退药 */
    @Column(name = "status")
    @AutoGenerated(locked = true, uuid = "b1060968-f216-31b0-a737-a795175d41b8")
    @Enumerated(EnumType.STRING)
    private PreparationDispenseStatusEnum status;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "ca35e5ca-6c64-3041-a6c7-fe7c3fc7066c")
    private Date updatedAt;

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispense convertToDrugPrescriptionDispense() {
        DrugPrescriptionDispense entity = new DrugPrescriptionDispense();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "dispenseStorageCode",
                "dispenseWindowId",
                "prescriptionId",
                "orderClass",
                "clinicType",
                "dispenseStaffId",
                "status",
                "refundFlag",
                "dispenseTime",
                "herbRegimen",
                "herbRefundRegimen",
                "herbRefundingRegimen",
                "prepareStaffId",
                "secondPrepareStaffId",
                "invalidFlag",
                "outpSettleId",
                "printFlag",
                "printCount",
                "patientId",
                "outpVisitId",
                "emergencyFlag",
                "checkStaffId",
                "auditStaffId",
                "remark",
                "refundDispenseId",
                "dispensingPrepareId",
                "refundApplyStaffId",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public String getAuditStaffId() {
        return this.auditStaffId;
    }

    @AutoGenerated(locked = true)
    public static DrugPrescriptionDispenseBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugPrescriptionDispenseBO drugPrescriptionDispense =
                (DrugPrescriptionDispenseBO)
                        session.createQuery("from DrugPrescriptionDispenseBO where " + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugPrescriptionDispense;
    }

    @AutoGenerated(locked = true)
    public String getCheckStaffId() {
        return this.checkStaffId;
    }

    @AutoGenerated(locked = true)
    public ClinicTypeEnum getClinicType() {
        return this.clinicType;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getDispenseStaffId() {
        return this.dispenseStaffId;
    }

    @AutoGenerated(locked = true)
    public String getDispenseStorageCode() {
        return this.dispenseStorageCode;
    }

    @AutoGenerated(locked = true)
    public Date getDispenseTime() {
        return this.dispenseTime;
    }

    @AutoGenerated(locked = true)
    public String getDispenseWindowId() {
        return this.dispenseWindowId;
    }

    @AutoGenerated(locked = true)
    public String getDispensingPrepareId() {
        return this.dispensingPrepareId;
    }

    @AutoGenerated(locked = true)
    public Set<DrugPrescriptionDispenseDetailBO> getDrugPrescriptionDispenseDetailBOSet() {
        return this.drugPrescriptionDispenseDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public Boolean getEmergencyFlag() {
        return this.emergencyFlag;
    }

    @AutoGenerated(locked = true)
    public Long getHerbRefundRegimen() {
        return this.herbRefundRegimen;
    }

    @AutoGenerated(locked = true)
    public Long getHerbRefundingRegimen() {
        return this.herbRefundingRegimen;
    }

    @AutoGenerated(locked = true)
    public Long getHerbRegimen() {
        return this.herbRegimen;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public Boolean getInvalidFlag() {
        return this.invalidFlag;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public OrderClassEnum getOrderClass() {
        return this.orderClass;
    }

    @AutoGenerated(locked = true)
    public String getOutpSettleId() {
        return this.outpSettleId;
    }

    @AutoGenerated(locked = true)
    public String getOutpVisitId() {
        return this.outpVisitId;
    }

    @AutoGenerated(locked = true)
    public String getPatientId() {
        return this.patientId;
    }

    @AutoGenerated(locked = true)
    public String getPrepareStaffId() {
        return this.prepareStaffId;
    }

    @AutoGenerated(locked = true)
    public String getPrescriptionId() {
        return this.prescriptionId;
    }

    @AutoGenerated(locked = true)
    public Long getPrintCount() {
        return this.printCount;
    }

    @AutoGenerated(locked = true)
    public Boolean getPrintFlag() {
        return this.printFlag;
    }

    @AutoGenerated(locked = true)
    public String getRefundApplyStaffId() {
        return this.refundApplyStaffId;
    }

    @AutoGenerated(locked = true)
    public String getRefundDispenseId() {
        return this.refundDispenseId;
    }

    @AutoGenerated(locked = true)
    public Boolean getRefundFlag() {
        return this.refundFlag;
    }

    @AutoGenerated(locked = true)
    public String getRemark() {
        return this.remark;
    }

    @AutoGenerated(locked = true)
    public String getSecondPrepareStaffId() {
        return this.secondPrepareStaffId;
    }

    @AutoGenerated(locked = true)
    public PreparationDispenseStatusEnum getStatus() {
        return this.status;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setAuditStaffId(String auditStaffId) {
        this.auditStaffId = auditStaffId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setCheckStaffId(String checkStaffId) {
        this.checkStaffId = checkStaffId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setClinicType(ClinicTypeEnum clinicType) {
        this.clinicType = clinicType;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setDispenseStaffId(String dispenseStaffId) {
        this.dispenseStaffId = dispenseStaffId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setDispenseStorageCode(String dispenseStorageCode) {
        this.dispenseStorageCode = dispenseStorageCode;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setDispenseTime(Date dispenseTime) {
        this.dispenseTime = dispenseTime;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setDispenseWindowId(String dispenseWindowId) {
        this.dispenseWindowId = dispenseWindowId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setDispensingPrepareId(String dispensingPrepareId) {
        this.dispensingPrepareId = dispensingPrepareId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugPrescriptionDispenseDetailBOSet(
            Set<DrugPrescriptionDispenseDetailBO> drugPrescriptionDispenseDetailBOSet) {
        this.drugPrescriptionDispenseDetailBOSet = drugPrescriptionDispenseDetailBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setEmergencyFlag(Boolean emergencyFlag) {
        this.emergencyFlag = emergencyFlag;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setHerbRefundRegimen(Long herbRefundRegimen) {
        this.herbRefundRegimen = herbRefundRegimen;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setHerbRefundingRegimen(Long herbRefundingRegimen) {
        this.herbRefundingRegimen = herbRefundingRegimen;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setHerbRegimen(Long herbRegimen) {
        this.herbRegimen = herbRegimen;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setId(String id) {
        this.id = id;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setInvalidFlag(Boolean invalidFlag) {
        this.invalidFlag = invalidFlag;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setOrderClass(OrderClassEnum orderClass) {
        this.orderClass = orderClass;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setOutpSettleId(String outpSettleId) {
        this.outpSettleId = outpSettleId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setOutpVisitId(String outpVisitId) {
        this.outpVisitId = outpVisitId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setPatientId(String patientId) {
        this.patientId = patientId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setPrepareStaffId(String prepareStaffId) {
        this.prepareStaffId = prepareStaffId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setPrescriptionId(String prescriptionId) {
        this.prescriptionId = prescriptionId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setPrintCount(Long printCount) {
        this.printCount = printCount;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setPrintFlag(Boolean printFlag) {
        this.printFlag = printFlag;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setRefundApplyStaffId(String refundApplyStaffId) {
        this.refundApplyStaffId = refundApplyStaffId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setRefundDispenseId(String refundDispenseId) {
        this.refundDispenseId = refundDispenseId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setRefundFlag(Boolean refundFlag) {
        this.refundFlag = refundFlag;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setRemark(String remark) {
        this.remark = remark;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setSecondPrepareStaffId(String secondPrepareStaffId) {
        this.secondPrepareStaffId = secondPrepareStaffId;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setStatus(PreparationDispenseStatusEnum status) {
        this.status = status;
        return (DrugPrescriptionDispenseBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugPrescriptionDispenseBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugPrescriptionDispenseBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
