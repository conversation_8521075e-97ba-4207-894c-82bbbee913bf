package com.pulse.medication_dispensing.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.medication_dispensing.manager.DrugPrescriptionDispensingPrepareBaseDtoManager;
import com.pulse.medication_dispensing.manager.converter.DrugPrescriptionDispensingPrepareBaseDtoConverter;
import com.pulse.medication_dispensing.manager.dto.DrugPrescriptionDispensingPrepareBaseDto;
import com.pulse.medication_dispensing.persist.dos.DrugPrescriptionDispensingPrepare;
import com.pulse.medication_dispensing.persist.mapper.DrugPrescriptionDispensingPrepareDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "d3bd8af1-f153-46b6-b1a3-f51b778ae226|DTO|BASE_MANAGER_IMPL")
public abstract class DrugPrescriptionDispensingPrepareBaseDtoManagerBaseImpl
        implements DrugPrescriptionDispensingPrepareBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugPrescriptionDispensingPrepareBaseDtoConverter
            drugPrescriptionDispensingPrepareBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugPrescriptionDispensingPrepareDao drugPrescriptionDispensingPrepareDao;

    @AutoGenerated(locked = true, uuid = "04327f4d-b6f5-3bf3-b8bb-403e6f2d0bbe")
    @Override
    public DrugPrescriptionDispensingPrepareBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispensingPrepareBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugPrescriptionDispensingPrepareBaseDto drugPrescriptionDispensingPrepareBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugPrescriptionDispensingPrepareBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "1d81ae24-5194-3e17-a727-66fbf6e041a7")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByPrescriptionDispensingIds(
            List<String> prescriptionDispensingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(prescriptionDispensingId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareList =
                drugPrescriptionDispensingPrepareDao.getByPrescriptionDispensingIds(
                        prescriptionDispensingId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingPrepareList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                drugPrescriptionDispensingPrepareList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2a6f9e8d-e9b6-35ac-a46f-2fffb0f5152c")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByPrepareWindowId(
            String prepareWindowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispensingPrepareBaseDto>
                drugPrescriptionDispensingPrepareBaseDtoList =
                        getByPrepareWindowIds(Arrays.asList(prepareWindowId));
        return drugPrescriptionDispensingPrepareBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "367b4b6a-0c8b-3ccc-81c0-da4e525d2f8e")
    @Override
    public DrugPrescriptionDispensingPrepareBaseDto getByPrescriptionDispensingId(
            String prescriptionDispensingId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispensingPrepareBaseDto> ret =
                getByPrescriptionDispensingIds(Arrays.asList(prescriptionDispensingId));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugPrescriptionDispensingPrepareBaseDto drugPrescriptionDispensingPrepareBaseDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugPrescriptionDispensingPrepareBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3c1fc11a-5f37-3883-aa8a-b4b70a435c16")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getBySecondPrepareStaffId(
            String secondPrepareStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispensingPrepareBaseDto>
                drugPrescriptionDispensingPrepareBaseDtoList =
                        getBySecondPrepareStaffIds(Arrays.asList(secondPrepareStaffId));
        return drugPrescriptionDispensingPrepareBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "438493e5-2eab-3728-91ea-4dd938a63156")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByPrepareWindowIds(
            List<String> prepareWindowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(prepareWindowId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareList =
                drugPrescriptionDispensingPrepareDao.getByPrepareWindowIds(prepareWindowId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingPrepareList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                drugPrescriptionDispensingPrepareList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "4f2773ed-583f-3abd-b302-e15e9a3a2529")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByPrintStaffIds(
            List<String> printStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(printStaffId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareList =
                drugPrescriptionDispensingPrepareDao.getByPrintStaffIds(printStaffId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingPrepareList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                drugPrescriptionDispensingPrepareList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "689d2713-7512-3211-81e0-c54ff66fece5")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByPrepareStaffId(
            String prepareStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispensingPrepareBaseDto>
                drugPrescriptionDispensingPrepareBaseDtoList =
                        getByPrepareStaffIds(Arrays.asList(prepareStaffId));
        return drugPrescriptionDispensingPrepareBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "874bde32-4e2c-3c28-a532-073896916747")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getBySecondPrepareStaffIds(
            List<String> secondPrepareStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(secondPrepareStaffId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareList =
                drugPrescriptionDispensingPrepareDao.getBySecondPrepareStaffIds(
                        secondPrepareStaffId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingPrepareList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                drugPrescriptionDispensingPrepareList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ae942c6b-81b7-327f-8c03-c335a602f3be")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByPrepareStaffIds(
            List<String> prepareStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(prepareStaffId)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareList =
                drugPrescriptionDispensingPrepareDao.getByPrepareStaffIds(prepareStaffId);
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingPrepareList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                drugPrescriptionDispensingPrepareList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b579e721-f3bd-30a3-9643-6ceea8ea2c27")
    public List<DrugPrescriptionDispensingPrepareBaseDto>
            doConvertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                    List<DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingPrepareList)) {
            return Collections.emptyList();
        }

        Map<String, DrugPrescriptionDispensingPrepareBaseDto> dtoMap =
                drugPrescriptionDispensingPrepareBaseDtoConverter
                        .convertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                                drugPrescriptionDispensingPrepareList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugPrescriptionDispensingPrepareBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugPrescriptionDispensingPrepareBaseDto>
                drugPrescriptionDispensingPrepareBaseDtoList = new ArrayList<>();
        for (DrugPrescriptionDispensingPrepare i : drugPrescriptionDispensingPrepareList) {
            DrugPrescriptionDispensingPrepareBaseDto drugPrescriptionDispensingPrepareBaseDto =
                    dtoMap.get(i.getId());
            if (drugPrescriptionDispensingPrepareBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugPrescriptionDispensingPrepareBaseDtoList.add(
                    drugPrescriptionDispensingPrepareBaseDto);
        }
        return drugPrescriptionDispensingPrepareBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "ef6ef35c-7e8f-304a-8657-72b0daf7b8ce")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareList =
                drugPrescriptionDispensingPrepareDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugPrescriptionDispensingPrepareList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugPrescriptionDispensingPrepare> drugPrescriptionDispensingPrepareMap =
                drugPrescriptionDispensingPrepareList.stream()
                        .collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugPrescriptionDispensingPrepareList =
                id.stream()
                        .map(i -> drugPrescriptionDispensingPrepareMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugPrescriptionDispensingPrepareToDrugPrescriptionDispensingPrepareBaseDto(
                drugPrescriptionDispensingPrepareList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "f6a9276b-9b52-32da-bbe7-c6901969c734")
    @Override
    public List<DrugPrescriptionDispensingPrepareBaseDto> getByPrintStaffId(String printStaffId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugPrescriptionDispensingPrepareBaseDto>
                drugPrescriptionDispensingPrepareBaseDtoList =
                        getByPrintStaffIds(Arrays.asList(printStaffId));
        return drugPrescriptionDispensingPrepareBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
