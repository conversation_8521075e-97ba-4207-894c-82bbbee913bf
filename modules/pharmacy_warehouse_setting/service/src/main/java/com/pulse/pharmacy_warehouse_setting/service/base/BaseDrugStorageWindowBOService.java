package com.pulse.pharmacy_warehouse_setting.service.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;

import com.pulse.pharmacy_warehouse_setting.manager.bo.*;
import com.pulse.pharmacy_warehouse_setting.manager.bo.DrugStorageWindowBO;
import com.pulse.pharmacy_warehouse_setting.persist.dos.DrugStorageWindow;
import com.pulse.pharmacy_warehouse_setting.service.base.BaseDrugStorageWindowBOService.DeleteStorageWindowBoResult;
import com.pulse.pharmacy_warehouse_setting.service.base.BaseDrugStorageWindowBOService.DisableStorageWindowBoResult;
import com.pulse.pharmacy_warehouse_setting.service.base.BaseDrugStorageWindowBOService.MergeStorageWindowBoResult;
import com.pulse.pharmacy_warehouse_setting.service.bto.DeleteStorageWindowBto;
import com.pulse.pharmacy_warehouse_setting.service.bto.DisableStorageWindowBto;
import com.pulse.pharmacy_warehouse_setting.service.bto.MergeStorageWindowBto;
import com.vs.bo.AddedBto;
import com.vs.bo.BaseBoResult;
import com.vs.bo.DeletedBto;
import com.vs.bo.UnmodifiedBto;
import com.vs.bo.UpdatedBto;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.ox.common.exception.IgnoredException;
import com.vs.persist.transactional.TransactionalSessionFactory;
import com.vs.sqlmapper.core.OracleIdGenerator;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Set;

import javax.annotation.Resource;

@DoNotModify
@Slf4j
@Component
@AutoGenerated(locked = true, uuid = "78fd6569-597c-3443-8887-b882a290e327")
public class BaseDrugStorageWindowBOService {
    @AutoGenerated(locked = true)
    @Resource(name = "oracleIdGenerator")
    private OracleIdGenerator idGenerator;

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugStorageWindowBO createDisableStorageWindowOnDuplicateUpdate(
            BaseDrugStorageWindowBOService.DisableStorageWindowBoResult boResult,
            DisableStorageWindowBto disableStorageWindowBto) {
        DrugStorageWindowBO drugStorageWindowBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (disableStorageWindowBto.getId() == null);
        if (!allNull && !found) {
            drugStorageWindowBO = DrugStorageWindowBO.getById(disableStorageWindowBto.getId());
            if (drugStorageWindowBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugStorageWindowBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugStorageWindowBO.convertToDrugStorageWindow());
                updatedBto.setBto(disableStorageWindowBto);
                updatedBto.setBo(drugStorageWindowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        disableStorageWindowBto, "__$validPropertySet"),
                        "enableFlag")) {
                    drugStorageWindowBO.setEnableFlag(disableStorageWindowBto.getEnableFlag());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugStorageWindowBO.convertToDrugStorageWindow());
                updatedBto.setBto(disableStorageWindowBto);
                updatedBto.setBo(drugStorageWindowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        disableStorageWindowBto, "__$validPropertySet"),
                        "enableFlag")) {
                    drugStorageWindowBO.setEnableFlag(disableStorageWindowBto.getEnableFlag());
                }
            }
        } else {
            drugStorageWindowBO = new DrugStorageWindowBO();
            if (pkExist) {
                drugStorageWindowBO.setId(disableStorageWindowBto.getId());
            } else {
                drugStorageWindowBO.setId(
                        String.valueOf(this.idGenerator.allocateId("drug_storage_window")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(
                                    disableStorageWindowBto, "__$validPropertySet"),
                    "enableFlag")) {
                drugStorageWindowBO.setEnableFlag(disableStorageWindowBto.getEnableFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(disableStorageWindowBto);
            addedBto.setBo(drugStorageWindowBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugStorageWindowBO;
    }

    /** 数据库创建一行, 数据库创建一行 */
    @AutoGenerated(locked = true)
    private DrugStorageWindowBO createMergeStorageWindowOnDuplicateUpdate(
            BaseDrugStorageWindowBOService.MergeStorageWindowBoResult boResult,
            MergeStorageWindowBto mergeStorageWindowBto) {
        DrugStorageWindowBO drugStorageWindowBO = null;
        boolean found = false;
        boolean allNull = false;
        boolean pkMatched = false;
        boolean pkExist = false;
        String matchedUkName = "";
        allNull = (mergeStorageWindowBto.getId() == null);
        if (!allNull && !found) {
            drugStorageWindowBO = DrugStorageWindowBO.getById(mergeStorageWindowBto.getId());
            if (drugStorageWindowBO != null) {
                matchedUkName += "(";
                matchedUkName += "'id'";
                matchedUkName += ")";
                found = true;
            }
            pkMatched = found;
            pkExist = true;
        }
        if (drugStorageWindowBO != null) {
            if (pkMatched) {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugStorageWindowBO.convertToDrugStorageWindow());
                updatedBto.setBto(mergeStorageWindowBto);
                updatedBto.setBo(drugStorageWindowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugStorageWindowBO.setStorageCode(mergeStorageWindowBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "windowName")) {
                    drugStorageWindowBO.setWindowName(mergeStorageWindowBto.getWindowName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "sortOrder")) {
                    drugStorageWindowBO.setSortOrder(mergeStorageWindowBto.getSortOrder());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "managePropertyList")) {
                    drugStorageWindowBO.setManagePropertyList(
                            mergeStorageWindowBto.getManagePropertyList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "windowType")) {
                    drugStorageWindowBO.setWindowType(mergeStorageWindowBto.getWindowType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "windowPurpose")) {
                    drugStorageWindowBO.setWindowPurpose(mergeStorageWindowBto.getWindowPurpose());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "remark")) {
                    drugStorageWindowBO.setRemark(mergeStorageWindowBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "dispenseMachineFlag")) {
                    drugStorageWindowBO.setDispenseMachineFlag(
                            mergeStorageWindowBto.getDispenseMachineFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "onlineFlag")) {
                    drugStorageWindowBO.setOnlineFlag(mergeStorageWindowBto.getOnlineFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "autoPrintFlag")) {
                    drugStorageWindowBO.setAutoPrintFlag(mergeStorageWindowBto.getAutoPrintFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "erpFlag")) {
                    drugStorageWindowBO.setErpFlag(mergeStorageWindowBto.getErpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "formCodeList")) {
                    drugStorageWindowBO.setFormCodeList(mergeStorageWindowBto.getFormCodeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "macAddress")) {
                    drugStorageWindowBO.setMacAddress(mergeStorageWindowBto.getMacAddress());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "computerName")) {
                    drugStorageWindowBO.setComputerName(mergeStorageWindowBto.getComputerName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "prescriptionNature")) {
                    drugStorageWindowBO.setPrescriptionNature(
                            mergeStorageWindowBto.getPrescriptionNature());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "formalWindowFlag")) {
                    drugStorageWindowBO.setFormalWindowFlag(
                            mergeStorageWindowBto.getFormalWindowFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "enableFlag")) {
                    drugStorageWindowBO.setEnableFlag(mergeStorageWindowBto.getEnableFlag());
                }
            } else {
                UpdatedBto updatedBto = new UpdatedBto();
                updatedBto.setEntity(drugStorageWindowBO.convertToDrugStorageWindow());
                updatedBto.setBto(mergeStorageWindowBto);
                updatedBto.setBo(drugStorageWindowBO);
                boResult.getUpdatedList().add(updatedBto);
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "storageCode")) {
                    drugStorageWindowBO.setStorageCode(mergeStorageWindowBto.getStorageCode());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "windowName")) {
                    drugStorageWindowBO.setWindowName(mergeStorageWindowBto.getWindowName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "sortOrder")) {
                    drugStorageWindowBO.setSortOrder(mergeStorageWindowBto.getSortOrder());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "managePropertyList")) {
                    drugStorageWindowBO.setManagePropertyList(
                            mergeStorageWindowBto.getManagePropertyList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "windowType")) {
                    drugStorageWindowBO.setWindowType(mergeStorageWindowBto.getWindowType());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "windowPurpose")) {
                    drugStorageWindowBO.setWindowPurpose(mergeStorageWindowBto.getWindowPurpose());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "remark")) {
                    drugStorageWindowBO.setRemark(mergeStorageWindowBto.getRemark());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "dispenseMachineFlag")) {
                    drugStorageWindowBO.setDispenseMachineFlag(
                            mergeStorageWindowBto.getDispenseMachineFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "onlineFlag")) {
                    drugStorageWindowBO.setOnlineFlag(mergeStorageWindowBto.getOnlineFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "autoPrintFlag")) {
                    drugStorageWindowBO.setAutoPrintFlag(mergeStorageWindowBto.getAutoPrintFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "erpFlag")) {
                    drugStorageWindowBO.setErpFlag(mergeStorageWindowBto.getErpFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "formCodeList")) {
                    drugStorageWindowBO.setFormCodeList(mergeStorageWindowBto.getFormCodeList());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "macAddress")) {
                    drugStorageWindowBO.setMacAddress(mergeStorageWindowBto.getMacAddress());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "computerName")) {
                    drugStorageWindowBO.setComputerName(mergeStorageWindowBto.getComputerName());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "prescriptionNature")) {
                    drugStorageWindowBO.setPrescriptionNature(
                            mergeStorageWindowBto.getPrescriptionNature());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "formalWindowFlag")) {
                    drugStorageWindowBO.setFormalWindowFlag(
                            mergeStorageWindowBto.getFormalWindowFlag());
                }
                if (CollectionUtil.contains(
                        (Set<String>)
                                ReflectUtil.getFieldValue(
                                        mergeStorageWindowBto, "__$validPropertySet"),
                        "enableFlag")) {
                    drugStorageWindowBO.setEnableFlag(mergeStorageWindowBto.getEnableFlag());
                }
            }
        } else {
            drugStorageWindowBO = new DrugStorageWindowBO();
            if (pkExist) {
                drugStorageWindowBO.setId(mergeStorageWindowBto.getId());
            } else {
                drugStorageWindowBO.setId(
                        String.valueOf(this.idGenerator.allocateId("drug_storage_window")));
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "storageCode")) {
                drugStorageWindowBO.setStorageCode(mergeStorageWindowBto.getStorageCode());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "windowName")) {
                drugStorageWindowBO.setWindowName(mergeStorageWindowBto.getWindowName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "sortOrder")) {
                drugStorageWindowBO.setSortOrder(mergeStorageWindowBto.getSortOrder());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "managePropertyList")) {
                drugStorageWindowBO.setManagePropertyList(
                        mergeStorageWindowBto.getManagePropertyList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "windowType")) {
                drugStorageWindowBO.setWindowType(mergeStorageWindowBto.getWindowType());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "windowPurpose")) {
                drugStorageWindowBO.setWindowPurpose(mergeStorageWindowBto.getWindowPurpose());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "remark")) {
                drugStorageWindowBO.setRemark(mergeStorageWindowBto.getRemark());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "dispenseMachineFlag")) {
                drugStorageWindowBO.setDispenseMachineFlag(
                        mergeStorageWindowBto.getDispenseMachineFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "onlineFlag")) {
                drugStorageWindowBO.setOnlineFlag(mergeStorageWindowBto.getOnlineFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "autoPrintFlag")) {
                drugStorageWindowBO.setAutoPrintFlag(mergeStorageWindowBto.getAutoPrintFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "erpFlag")) {
                drugStorageWindowBO.setErpFlag(mergeStorageWindowBto.getErpFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "formCodeList")) {
                drugStorageWindowBO.setFormCodeList(mergeStorageWindowBto.getFormCodeList());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "macAddress")) {
                drugStorageWindowBO.setMacAddress(mergeStorageWindowBto.getMacAddress());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "computerName")) {
                drugStorageWindowBO.setComputerName(mergeStorageWindowBto.getComputerName());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "prescriptionNature")) {
                drugStorageWindowBO.setPrescriptionNature(
                        mergeStorageWindowBto.getPrescriptionNature());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "formalWindowFlag")) {
                drugStorageWindowBO.setFormalWindowFlag(
                        mergeStorageWindowBto.getFormalWindowFlag());
            }
            if (CollectionUtil.contains(
                    (Set<String>)
                            ReflectUtil.getFieldValue(mergeStorageWindowBto, "__$validPropertySet"),
                    "enableFlag")) {
                drugStorageWindowBO.setEnableFlag(mergeStorageWindowBto.getEnableFlag());
            }
            AddedBto addedBto = new AddedBto();
            addedBto.setBto(mergeStorageWindowBto);
            addedBto.setBo(drugStorageWindowBO);
            boResult.getAddBtoList().add(addedBto);
        }
        return drugStorageWindowBO;
    }

    /** 删除对象:deleteStorageWindow,如果不存在就抛出异常 */
    @AutoGenerated(locked = true)
    private DrugStorageWindowBO deleteDeleteStorageWindowOnMissThrowEx(
            BaseDrugStorageWindowBOService.DeleteStorageWindowBoResult boResult,
            DeleteStorageWindowBto deleteStorageWindowBto) {
        DrugStorageWindowBO drugStorageWindowBO = null;
        boolean found = false;
        boolean allNull = false;
        allNull = (deleteStorageWindowBto.getId() == null);
        if (!allNull && !found) {
            drugStorageWindowBO = DrugStorageWindowBO.getById(deleteStorageWindowBto.getId());
            found = true;
        }
        if (drugStorageWindowBO == null) {
            throw new IgnoredException(400, "删除失败，无法找到原对象！");
        } else {
            TransactionalSessionFactory.getSession().delete(drugStorageWindowBO);
            TransactionalSessionFactory.getSession().flush();
            DeletedBto deletedBto = new DeletedBto();
            deletedBto.setBto(deleteStorageWindowBto);
            deletedBto.setEntity(drugStorageWindowBO.convertToDrugStorageWindow());
            boResult.getDeletedBtoList().add(deletedBto);
            return drugStorageWindowBO;
        }
    }

    /** 功能：删除药房窗口 */
    @AutoGenerated(locked = true)
    protected DeleteStorageWindowBoResult deleteStorageWindowBase(
            DeleteStorageWindowBto deleteStorageWindowBto) {
        if (deleteStorageWindowBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DeleteStorageWindowBoResult boResult = new DeleteStorageWindowBoResult();
        DrugStorageWindowBO drugStorageWindowBO =
                deleteDeleteStorageWindowOnMissThrowEx(boResult, deleteStorageWindowBto);
        boResult.setRootBo(drugStorageWindowBO);
        return boResult;
    }

    /** 停用窗口 */
    @AutoGenerated(locked = true)
    protected DisableStorageWindowBoResult disableStorageWindowBase(
            DisableStorageWindowBto disableStorageWindowBto) {
        if (disableStorageWindowBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        DisableStorageWindowBoResult boResult = new DisableStorageWindowBoResult();
        DrugStorageWindowBO drugStorageWindowBO =
                createDisableStorageWindowOnDuplicateUpdate(boResult, disableStorageWindowBto);
        boResult.setRootBo(drugStorageWindowBO);
        return boResult;
    }

    /** 功能：保存药房窗口 */
    @AutoGenerated(locked = true)
    protected MergeStorageWindowBoResult mergeStorageWindowBase(
            MergeStorageWindowBto mergeStorageWindowBto) {
        if (mergeStorageWindowBto == null) {
            throw new IgnoredException(400, "bto 不能为null!");
        }
        MergeStorageWindowBoResult boResult = new MergeStorageWindowBoResult();
        DrugStorageWindowBO drugStorageWindowBO =
                createMergeStorageWindowOnDuplicateUpdate(boResult, mergeStorageWindowBto);
        boResult.setRootBo(drugStorageWindowBO);
        return boResult;
    }

    public static class MergeStorageWindowBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugStorageWindowBO getRootBo() {
            return (DrugStorageWindowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<MergeStorageWindowBto, DrugStorageWindowBO> getCreatedBto(
                MergeStorageWindowBto mergeStorageWindowBto) {
            return this.getAddedResult(mergeStorageWindowBto);
        }

        @AutoGenerated(locked = true)
        public DrugStorageWindow getDeleted_DrugStorageWindow() {
            return (DrugStorageWindow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStorageWindow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<MergeStorageWindowBto, DrugStorageWindow, DrugStorageWindowBO>
                getUpdatedBto(MergeStorageWindowBto mergeStorageWindowBto) {
            return super.getUpdatedResult(mergeStorageWindowBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<MergeStorageWindowBto, DrugStorageWindowBO> getUnmodifiedBto(
                MergeStorageWindowBto mergeStorageWindowBto) {
            return super.getUnmodifiedResult(mergeStorageWindowBto);
        }
    }

    public static class DeleteStorageWindowBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugStorageWindowBO getRootBo() {
            return (DrugStorageWindowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DeleteStorageWindowBto, DrugStorageWindowBO> getCreatedBto(
                DeleteStorageWindowBto deleteStorageWindowBto) {
            return this.getAddedResult(deleteStorageWindowBto);
        }

        @AutoGenerated(locked = true)
        public DrugStorageWindow getDeleted_DrugStorageWindow() {
            return (DrugStorageWindow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStorageWindow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DeleteStorageWindowBto, DrugStorageWindow, DrugStorageWindowBO>
                getUpdatedBto(DeleteStorageWindowBto deleteStorageWindowBto) {
            return super.getUpdatedResult(deleteStorageWindowBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DeleteStorageWindowBto, DrugStorageWindowBO> getUnmodifiedBto(
                DeleteStorageWindowBto deleteStorageWindowBto) {
            return super.getUnmodifiedResult(deleteStorageWindowBto);
        }
    }

    public static class DisableStorageWindowBoResult extends BaseBoResult {

        @AutoGenerated(locked = true)
        public DrugStorageWindowBO getRootBo() {
            return (DrugStorageWindowBO) this.rootBo;
        }

        @AutoGenerated(locked = true)
        public AddedBto<DisableStorageWindowBto, DrugStorageWindowBO> getCreatedBto(
                DisableStorageWindowBto disableStorageWindowBto) {
            return this.getAddedResult(disableStorageWindowBto);
        }

        @AutoGenerated(locked = true)
        public DrugStorageWindow getDeleted_DrugStorageWindow() {
            return (DrugStorageWindow)
                    CollectionUtil.getFirst(this.getDeletedEntityList(DrugStorageWindow.class));
        }

        @AutoGenerated(locked = true)
        public UpdatedBto<DisableStorageWindowBto, DrugStorageWindow, DrugStorageWindowBO>
                getUpdatedBto(DisableStorageWindowBto disableStorageWindowBto) {
            return super.getUpdatedResult(disableStorageWindowBto);
        }

        @AutoGenerated(locked = true)
        public UnmodifiedBto<DisableStorageWindowBto, DrugStorageWindowBO> getUnmodifiedBto(
                DisableStorageWindowBto disableStorageWindowBto) {
            return super.getUnmodifiedResult(disableStorageWindowBto);
        }
    }
}
