package com.pulse.pharmacy_warehouse_setting.service.bto;

import com.pulse.drug_dictionary.common.enums.DrugTypeEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.PrescriptionNatureEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.WindowPurposeEnum;
import com.pulse.pharmacy_warehouse_setting.common.enums.WindowTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> DrugStorageWindow
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_UPDATE
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "aa9007f8-c704-4551-8c6b-bda5b3257407|BTO|DEFINITION")
public class MergeStorageWindowBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 自动打印标志 */
    @AutoGenerated(locked = true, uuid = "3e8fe159-2bc2-49db-b82b-42c3f81dc023")
    private Boolean autoPrintFlag;

    /** 计算机名称 */
    @AutoGenerated(locked = true, uuid = "b3e65c44-2c50-4faa-b16d-4f5d20ee4c03")
    private String computerName;

    /** 启用发药机标识 */
    @AutoGenerated(locked = true, uuid = "6aa07ce9-9733-4a3d-9304-acf0cb0823e9")
    private Boolean dispenseMachineFlag;

    /** 启用标志 */
    @AutoGenerated(locked = true, uuid = "92b342c1-738b-4251-84bb-7a30d3552b39")
    private Boolean enableFlag;

    /** 急诊窗口标志 */
    @AutoGenerated(locked = true, uuid = "c0d6dd0e-3955-4296-ac7d-e0772fc8d1bf")
    private Boolean erpFlag;

    /** 剂型 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b5d88e67-809a-4e2d-a65d-231d4c02df6c")
    private List<String> formCodeList;

    /** 正式窗口标志 */
    @AutoGenerated(locked = true, uuid = "02d439c2-0aaa-4cb7-aba5-4efd2b02ba70")
    private Boolean formalWindowFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "77674645-07ff-4ba5-b243-61b4d06423a9")
    private String id;

    /** MAC地址 */
    @AutoGenerated(locked = true, uuid = "5fde0e6d-da10-4336-ae78-8b94fa1b2ba6")
    private String macAddress;

    /** 管理属性 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c2be02f6-5b5f-40da-b35e-14c6f87b47d8")
    private List<DrugTypeEnum> managePropertyList;

    /** 已联机标识 */
    @AutoGenerated(locked = true, uuid = "2d82610a-0a9e-4100-a772-b1fdae6cc192")
    private Boolean onlineFlag;

    /** 处方性质 */
    @AutoGenerated(locked = true, uuid = "fb39aa3a-c56d-4767-87c6-15a7da1f1e20")
    private PrescriptionNatureEnum prescriptionNature;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "efb580b8-104c-48f8-b7bd-829ae7392e5a")
    private String remark;

    /** 顺序号 */
    @AutoGenerated(locked = true, uuid = "4aeaf22d-d2c3-4222-a1d4-3a1cf220bf05")
    private Long sortOrder;

    /** 所属发药药房编码 */
    @AutoGenerated(locked = true, uuid = "cb8be751-7932-4b5c-9cfe-63a9f7a83e63")
    private String storageCode;

    /** 窗口名称 */
    @AutoGenerated(locked = true, uuid = "f7b5ec52-8d78-4383-9ee5-cad5a3f2eb4c")
    private String windowName;

    /** 窗口用途 1:发药窗口 2:配药窗口 3:查询窗口 */
    @AutoGenerated(locked = true, uuid = "a1478d35-f8b7-4fbb-a42c-************")
    private WindowPurposeEnum windowPurpose;

    /** 窗口类型 普通、毒麻、出院带药、饮片、颗粒、膏方、协定处方 */
    @AutoGenerated(locked = true, uuid = "578d3b1f-d88c-4090-a5db-a938b1be06b7")
    private WindowTypeEnum windowType;

    @AutoGenerated(locked = true)
    public void setAutoPrintFlag(Boolean autoPrintFlag) {
        this.__$validPropertySet.add("autoPrintFlag");
        this.autoPrintFlag = autoPrintFlag;
    }

    @AutoGenerated(locked = true)
    public void setComputerName(String computerName) {
        this.__$validPropertySet.add("computerName");
        this.computerName = computerName;
    }

    @AutoGenerated(locked = true)
    public void setDispenseMachineFlag(Boolean dispenseMachineFlag) {
        this.__$validPropertySet.add("dispenseMachineFlag");
        this.dispenseMachineFlag = dispenseMachineFlag;
    }

    @AutoGenerated(locked = true)
    public void setEnableFlag(Boolean enableFlag) {
        this.__$validPropertySet.add("enableFlag");
        this.enableFlag = enableFlag;
    }

    @AutoGenerated(locked = true)
    public void setErpFlag(Boolean erpFlag) {
        this.__$validPropertySet.add("erpFlag");
        this.erpFlag = erpFlag;
    }

    @AutoGenerated(locked = true)
    public void setFormCodeList(List<String> formCodeList) {
        this.__$validPropertySet.add("formCodeList");
        this.formCodeList = formCodeList;
    }

    @AutoGenerated(locked = true)
    public void setFormalWindowFlag(Boolean formalWindowFlag) {
        this.__$validPropertySet.add("formalWindowFlag");
        this.formalWindowFlag = formalWindowFlag;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setMacAddress(String macAddress) {
        this.__$validPropertySet.add("macAddress");
        this.macAddress = macAddress;
    }

    @AutoGenerated(locked = true)
    public void setManageProperty(List<DrugTypeEnum> manageProperty) {
        this.__$validPropertySet.add("managePropertyList");
        this.managePropertyList = manageProperty;
    }

    @AutoGenerated(locked = true)
    public void setManagePropertyList(List<DrugTypeEnum> managePropertyList) {
        this.__$validPropertySet.add("managePropertyList");
        this.managePropertyList = managePropertyList;
    }

    @AutoGenerated(locked = true)
    public void setOnlineFlag(Boolean onlineFlag) {
        this.__$validPropertySet.add("onlineFlag");
        this.onlineFlag = onlineFlag;
    }

    @AutoGenerated(locked = true)
    public void setPrescriptionNature(PrescriptionNatureEnum prescriptionNature) {
        this.__$validPropertySet.add("prescriptionNature");
        this.prescriptionNature = prescriptionNature;
    }

    @AutoGenerated(locked = true)
    public void setRemark(String remark) {
        this.__$validPropertySet.add("remark");
        this.remark = remark;
    }

    @AutoGenerated(locked = true)
    public void setSortOrder(Long sortOrder) {
        this.__$validPropertySet.add("sortOrder");
        this.sortOrder = sortOrder;
    }

    @AutoGenerated(locked = true)
    public void setStorageCode(String storageCode) {
        this.__$validPropertySet.add("storageCode");
        this.storageCode = storageCode;
    }

    @AutoGenerated(locked = true)
    public void setWindowName(String windowName) {
        this.__$validPropertySet.add("windowName");
        this.windowName = windowName;
    }

    @AutoGenerated(locked = true)
    public void setWindowPurpose(WindowPurposeEnum windowPurpose) {
        this.__$validPropertySet.add("windowPurpose");
        this.windowPurpose = windowPurpose;
    }

    @AutoGenerated(locked = true)
    public void setWindowType(WindowTypeEnum windowType) {
        this.__$validPropertySet.add("windowType");
        this.windowType = windowType;
    }
}
