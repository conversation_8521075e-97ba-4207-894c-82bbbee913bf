<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>
    <groupId>com.pulse</groupId>
    <artifactId>pulse</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <logback.classic.version>1.2.3</logback.classic.version>
        <flyway.version>5.2.4</flyway.version>
        <cn.hutool.all>5.7.4</cn.hutool.all>
        <javax.persistence-api>2.2</javax.persistence-api>
        <elasticsearch.version>7.3.1</elasticsearch.version>
        <spring-boot-dependencies.version>2.6.2</spring-boot-dependencies.version>
        <commons-logging.version>1.2</commons-logging.version>
        <hession.version>4.0.63</hession.version>
        <netty-all.version>4.1.48.Final</netty-all.version>
        <gson.version>2.8.6</gson.version>
        <spring-boot.version>2.2.6.RELEASE</spring-boot.version>
        <mybatis-spring-boot-starter.version>2.1.2</mybatis-spring-boot-starter.version>
        <mysql-connector-java.version>8.0.11</mysql-connector-java.version>
        <slf4j-api.version>1.7.30</slf4j-api.version>
        <junit.version>4.13</junit.version>
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>
        <groovy.version>3.0.3</groovy.version>
        <spring.version>5.2.5.RELEASE</spring.version>
        <hibernate.version>5.5.7.Final</hibernate.version>
        <lobmook.version>1.18.20</lobmook.version>
        <rocketmq-tools.version>4.7.1</rocketmq-tools.version>
        <rocketmq-spring-boot.version>2.2.0</rocketmq-spring-boot.version>
        <hutool-all.version>5.7.4</hutool-all.version>
        <pinyin4j.version>2.5.0</pinyin4j.version>
        <javax.annotation.version>1.3.2</javax.annotation.version>
        <javax.transaction-api.version>1.3</javax.transaction-api.version>
        <hibernate-validator.version>6.1.6.Final</hibernate-validator.version>
        <spring-boot-starter-data-jpa.version>2.6.3</spring-boot-starter-data-jpa.version>
        <spring-web.version>5.3.14</spring-web.version>
        <toco-common.version>1.0.0-SNAPSHOT</toco-common.version>
        <jedis.version>3.3.0</jedis.version>
        <spring-boot-autoconfigure.version>2.7.18</spring-boot-autoconfigure.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <org.aspectj.version>1.9.6</org.aspectj.version>
        <spring-context.version>5.3.14</spring-context.version>
        <xxl-job-core.version>2.2.0</xxl-job-core.version>
        <cglib-nodep.version>3.3.0</cglib-nodep.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <springfox-swagger-ui.version>2.9.2</springfox-swagger-ui.version>
        <springfox-swagger2.version>2.9.2</springfox-swagger2.version>
        <swagger-annotations.version>1.5.22</swagger-annotations.version>
        <javax.servlet-api.version>4.0.1</javax.servlet-api.version>
        <jackson-databind.version>2.13.0</jackson-databind.version>
        <httpclient.version>4.5.13</httpclient.version>
        <guava.version>23.0</guava.version>
        <opensearch.version>2.5.0</opensearch.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <transmittable-thread-local.version>2.12.3</transmittable-thread-local.version>
        <spring.web.mvc.version>5.3.14</spring.web.mvc.version>
        <httpasyncclient.version>4.1.5</httpasyncclient.version>
        <rocketmq-client.version>4.7.1</rocketmq-client.version>
        <org.json.version>20170516</org.json.version>
        <elasticsearch-core.version>7.3.1</elasticsearch-core.version>
        <vs.debug.version>1.0.0-SNAPSHOT</vs.debug.version>
        <vs.mock.spring>1.0.0-SNAPSHOT</vs.mock.spring>
        <toco.version>1.0.0-SNAPSHOT</toco.version>
        <log4j.version>2.23.1</log4j.version>
        <org.hibernate.core>5.5.7.Final</org.hibernate.core>
        <ox.sprintboot.web.starter.version>1.0-SNAPSHOT</ox.sprintboot.web.starter.version>
        <ox.basidc.common>1.0-SNAPSHOT</ox.basidc.common>
        <vs.sqlmapper.spring>1.0.0-SNAPSHOT</vs.sqlmapper.spring>
        <oracle.version>12.2.0.1</oracle.version>
        <db.hiernate.dialect.version>8.1.3.140</db.hiernate.dialect.version>
        <!-- 2.1.2版本以上支持纯净版客户端 -->
        <nacos.version>2.4.2</nacos.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>${javax.persistence-api}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.classic.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${org.json.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient</artifactId>
                <version>${httpasyncclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.web.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.toco</groupId>
                <artifactId>ox-bo-common</artifactId>
                <version>${toco-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet-api.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.toco</groupId>
                <artifactId>common</artifactId>
                <version>${toco-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.opensearch.client</groupId>
                <artifactId>opensearch-rest-high-level-client</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.21</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch-x-content</artifactId>
                <version>7.3.1</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${springfox-swagger-ui.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox-swagger2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>${cglib-nodep.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.toco</groupId>
                <artifactId>common-rpc</artifactId>
                <version>${toco-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.toco</groupId>
                <artifactId>vs-sqlmanager-basic</artifactId>
                <version>${toco-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring-context.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${org.aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring-boot-autoconfigure.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.toco</groupId>
                <artifactId>vs-sqlmapper-spring</artifactId>
                <version>${toco-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.toco</groupId>
                <artifactId>ox-jsqlparser</artifactId>
                <version>${toco-common.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring-web.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-jpa</artifactId>
                <version>${spring-boot-starter-data-jpa.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.transaction</groupId>
                <artifactId>javax.transaction-api</artifactId>
                <version>${javax.transaction-api.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${javax.annotation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot</artifactId>
                <version>${rocketmq-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-tools</artifactId>
                <version>${rocketmq-tools.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lobmook.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>${commons-logging.version}</version>
            </dependency>
            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>${hession.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>transport</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client-sniffer</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>7.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch-core</artifactId>
                <version>${elasticsearch-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.opensearch.client</groupId>
                <artifactId>opensearch-rest-high-level-client</artifactId>
                <version>${opensearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.toco</groupId>
            <artifactId>toco-all</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>javax.el</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.17.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.7</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-common</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-api</artifactId>
            <version>${nacos.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>[11.0,21.0)</version>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>3.8</version>
                                </requireMavenVersion>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.43.0</version>
                <configuration>
                    <java>
                        <googleJavaFormat>
                            <groupArtifact>com.google.googlejavaformat:google-java-format</groupArtifact>
                            <version>1.22.0</version>
                            <style>AOSP</style>
                            <reorderImports>true</reorderImports>
                            <reflowLongStrings>true</reflowLongStrings>
                            <formatJavadoc>true</formatJavadoc>
                        </googleJavaFormat>
                    </java>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>entrance</module>
        <module>common</module>
        <module>modules/organization</module>
        <module>modules/dictionary_basic</module>
        <module>modules/user</module>
        <module>modules/parameter</module>
        <module>modules/dictionary_business</module>
        <module>modules/consulting_room</module>
        <module>modules/billing_public_config</module>
        <module>modules/application</module>
        <module>modules/permission</module>
        <module>modules/drug_dictionary</module>
        <module>modules/file</module>
        <module>modules/patient_information</module>
        <module>modules/drug_purchasing_plan</module>
        <module>modules/drug_inventory</module>
        <module>modules/drug_quality</module>
        <module>modules/pharmacy_warehouse_setting</module>
        <module>modules/drug_report</module>
        <module>modules/patient_safety</module>
        <module>modules/drug_financial</module>
        <module>modules/special_drug</module>
        <module>modules/drug_permission</module>
        <module>modules/drug_circulation</module>
        <module>modules/pivas_preparation</module>
        <module>modules/medication_dispensing</module>
        <module>modules/medication_dispensing_review</module>
        <module>modules/medication_return</module>
        <module>modules/appointment_schedule</module>
        <module>modules/appointment_booking</module>
        <module>modules/orders</module>
        <module>modules/certificate</module>
        <module>modules/visit</module>
        <module>modules/treatment</module>
        <module>modules/tag</module>
        <module>modules/log</module>
        <module>modules/diagnosis</module>
        <module>modules/rule_engine</module>
        <module>modules/rule_engine_common</module>
    </modules>
</project>